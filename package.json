{"name": "mdcu", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 8080", "lint": "next lint", "postbuild": "next-sitemap"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "axios": "^1.4.0", "bootstrap": "^5.3.1", "chart.js": "^3.7.1", "cookie": "^0.5.0", "crypto": "^1.0.1", "form-data": "^4.0.0", "install": "^0.13.0", "jsonwebtoken": "8.5.1", "jsrsasign": "^10.8.6", "lodash": "^4.17.21", "moment": "^2.29.3", "moment-timezone": "^0.5.34", "next": "^12.1.5", "next-auth": "^4.3.4", "next-optimized-images": "^1.4.2", "next-sitemap": "^3.0.5", "nodemailer": "^6.7.5", "nookies": "^2.5.2", "openai": "^4.84.0", "react": "^18.0.0", "react-chartjs-2": "^4.1.0", "react-cookie": "^4.1.1", "react-datepicker": "^4.8.0", "react-dom": "^18.0.0", "react-hook-form": "^7.31.1", "react-icons": "^4.3.1", "react-markdown": "^9.0.3", "react-modal": "^3.15.1", "react-moment": "^1.1.2", "react-number-format": "^4.9.3", "react-pdf": "^7.0.2", "react-player": "^2.10.1", "react-rating-stars-component": "^2.2.0", "react-switch": "^7.0.0", "react-use-draggable-scroll": "^0.4.7", "remark-breaks": "^4.0.0", "remove": "^0.1.5", "screenfull": "^6.0.2", "semantic-ui-css": "^2.4.1", "semantic-ui-react": "^2.1.2", "styled-components": "^5.3.5", "sweetalert2": "^11.4.8", "swiper": "^8.1.4", "swr": "^1.3.0", "url-loader": "^4.1.1", "webp-loader": "^0.4.0"}, "devDependencies": {"@types/node": "22.13.2", "eslint": "8.13.0", "eslint-config-next": "12.1.5", "resolve-url-loader": "^5.0.0", "typescript": "5.7.3"}}
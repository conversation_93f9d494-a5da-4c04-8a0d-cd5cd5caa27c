import React from "react";
import <PERSON>ript from "next/script";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../../../../api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";


export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const params = context.query;
  const utoken = params.utoken;
  const orderId = params.orderId;
  const price = params.price;
  const type = params.type;
  const user = await getUser(utoken);
  const payment = {
    payment_callback: process.env.PAYMENT_CALLBACK_URL,
    payment_check_callback: process.env.PAYMENT_CHECK_CALLBACK_URL,
    payment_sdk: process.env.PAYMENT_SDK,
    payment_pkey: process.env.PAYMENT_PKEY,
    payment_mid: process.env.PAYMENT_MID,
    payment_name: process.env.PAYMENT_NAME,
  };
  return {
    props: {
      session: await getSession(context),
      params,
      user,
      utoken,
      payment,
      orderId,
      price,
      type
    },
  };
}

export default function Profile({
  params,
  user,
  utoken,
  payment,
  orderId,
  price,
  type
}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  const [payPrice, setPayPrice] = React.useState(price);
  const [loadPay, setLoadPay] = React.useState(false);
  const [firstLoad, setFirstLoad] = React.useState(true);
  const [payType, setPayType] = useState(type);
  const [qrId, setQrId] = useState(null);

  useEffect(() => {
    if(firstLoad){
      setFirstLoad(false);
      appContext.setLoading(true);
      if(payType==1){
        const formData = new URLSearchParams();
        formData.append("order_id", orderId);
        formData.append("utoken", utoken);
        appContext.sendApiToken(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/kpayOrder",
          formData,
          (res) => {
            if (res["status"] == "success") {
              setPayType('qr');
              setQrId(res['qr_id']);
              setLoadPay(false);
              setTimeout(() => {
                appContext.setLoading(false);
                setLoadPay(true);
              }, "100");
              setTimeout(() => {
                var kpayemnt_form = document.getElementById('kpayemnt_form');
                var _path_action = kpayemnt_form.getAttribute('action-url')

                kpayemnt_form.setAttribute('action',_path_action+'/'+ orderId);
                
                var element = document.getElementsByClassName('pay-button');
                
                try{
                  element[0].click();
                }catch(e){
                  // console.log('kpayment not active')
                }
              }, "1000");
            }
          }
        );
      }else{
        const formData = new URLSearchParams();
        formData.append("order_id", orderId);
        formData.append("utoken", utoken);
        appContext.sendApiToken(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/kpayOrderStamp",
          formData,
          (res) => {
            if (res["status"] == "success") {
              setPayType('card');
              setQrId(null);
              setLoadPay(false);
              setTimeout(() => {
                appContext.setLoading(false);
                setLoadPay(true);
              }, "100");
              setTimeout(() => {
                var kpayemnt_form = document.getElementById('kpayemnt_form');
                var _path_action = kpayemnt_form.getAttribute('action-url')

                kpayemnt_form.setAttribute('action',_path_action+'/'+ orderId);
                
                var element = document.getElementsByClassName('pay-button');
                
                try{
                  element[0].click();
                }catch(e){
                  // console.log('kpayment not active')
                }
              }, "1000");
            }
          }
        );
      }
    }
  }, [firstLoad]);
  return (
    <>
      <style>{`
        #onetrust-consent-sdk{
          display:none!important;
        }
        .full_ebook.single_page .preview-pdf-block{
          width: 100%!important;
        }
      `}</style>
      <div className="main-all">
        <div className="main-body">
          {loadPay ? (
            payType == 'qr' ? (
              <>
                <form method="POST" action={payment.payment_check_callback} action-url={payment.payment_check_callback} id="kpayemnt_form">
                  <script type="text/javascript"
                    async
                    src={payment.payment_sdk}
                    data-apikey={payment.payment_pkey}
                    data-amount={payPrice}
                    data-currency="THB"
                    data-order-id={qrId}
                    data-payment-methods={payType}
                    data-name={payment.payment_name}
                    data-mid={payment.payment_mid}
                  >
                  </script>
                </form>
                <Script
                  strategy="afterInteractive"
                  src={payment.payment_sdk}
                  data-apikey={payment.payment_pkey}
                  data-amount={payPrice}
                  data-currency="THB"
                  data-order-id={qrId}
                  data-payment-methods={payType}
                  data-name={payment.payment_name}
                  data-mid={payment.payment_mid}
                  id={"kpayment"}
                  onLoad={() => {
                    // console.log('KPayment loaded')
                    window.KPayment.create();
                    var payment_show = 0;
                    window.setInterval( function() {  
                      var elem = document.getElementsByClassName('payment-container');
                      var lastClassName = elem[0].className;
                      if ( (" " + lastClassName + " ").replace(/[\n\t]/g, " ").indexOf(" show ") > -1 ){
                        if(payment_show==0){
                          payment_show = 1;
                        }
                      }else{
                        if(payment_show==1){
                          payment_show = 2;
                          setTimeout(() => {
                            window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
                          }, "1000");
                        }
                      }
                    },1000);
                  }}
                />
              </>
            ):(
              <>
                <form method="POST" action={payment.payment_callback} action-url={payment.payment_callback} id="kpayemnt_form">
                  <script type="text/javascript"
                    async
                    src={payment.payment_sdk}
                    data-apikey={payment.payment_pkey}
                    data-amount={payPrice}
                    data-currency="THB"
                    data-payment-methods={payType}
                    data-name={payment.payment_name}
                    data-mid={payment.payment_mid}
                  >
                  </script>
                </form>
                <Script
                  strategy="afterInteractive"
                  src={payment.payment_sdk}
                  data-apikey={payment.payment_pkey}
                  data-amount={payPrice}
                  data-currency="THB"
                  data-payment-methods={payType}
                  data-name={payment.payment_name}
                  data-mid={payment.payment_mid}
                  id={"kpayment"}
                  onLoad={() => {
                    // console.log('KPayment loaded')
                    window.KPayment.create();
                    var payment_show = 0;
                    window.setInterval( function() {  
                      var elem = document.getElementsByClassName('payment-container');
                      var lastClassName = elem[0].className;
                      if ( (" " + lastClassName + " ").replace(/[\n\t]/g, " ").indexOf(" show ") > -1 ){
                        if(payment_show==0){
                          payment_show = 1;
                        }
                      }else{
                        if(payment_show==1){
                          payment_show = 2;
                          setTimeout(() => {
                            window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
                          }, "1000");
                        }
                      }
                    },1000);
                  }}
                />
              </>
            )
          ):null}
        </div>
      </div>
    </>
  );
}

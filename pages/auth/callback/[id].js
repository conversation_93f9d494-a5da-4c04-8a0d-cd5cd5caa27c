import nookies from 'nookies'
import { parseCookies, setC<PERSON>ie, destroyCookie } from 'nookies'

import { useSession, getSession } from "next-auth/react"
import { useState,useContext,useEffect } from "react";
import { useRouter } from "next/router";



import Error from "next";
import { URLSearchParams } from "url";
import AppContext from '/libs/contexts/AppContext';


 
export async function getServerSideProps(context) {
  const cookies = nookies.get(context)
  const params = context.query;
  const formData = new URLSearchParams(); 
  const session = await getSession(context);

  var bol = false;
  if(session){
    if(params.id=='line'){
      formData.append("line_id", session.user.id);
      formData.append("name", session.user.name);
      formData.append("avatar", session.user.image);
      formData.append("login_type",'line');
      bol = true;
    }else if(params.id=='facebook'){
      formData.append("facebook_id", session.user.id);
      formData.append("name", session.user.name);
      formData.append("avatar", session.user.image);
      formData.append("login_type",'facebook');
      bol = true;
    }else if(params.id=='google'){
      formData.append("google_id", session.user.id);
      formData.append("name", session.user.name);
      formData.append("avatar", session.user.image);
      formData.append("login_type",'google');
      bol = true;
    }
  }
 
  if(bol){
    const res = await fetch(
      process.env.NEXT_PUBLIC_API + "/api/user/login",
      {
        body: formData,
        method: "POST",
      }
    );
    const errorCode = res.ok ? false : res.statusCode;
    const data = await res.json(); 
    
    if(data['status']=='success'){
      nookies.set(context, process.env.NEXT_PUBLIC_APP+'_token', data['utoken'], {maxAge: 30 * 24 * 60 * 60,path: '/',secure: true,sameSite: 'lax'})
    }else{
      nookies.destroy(context, process.env.NEXT_PUBLIC_APP+'_token')
      return {
        redirect: { destination: '/' }
      };
    }
    return {
      // redirect: { destination: '/' },
      props: { session: await getSession(context),errorCode, data, params },
    };
  }else{
    if(params.id=='token'){
      nookies.set(context, process.env.NEXT_PUBLIC_APP+'_token', params['utoken'], {maxAge: 30 * 24 * 60 * 60,path: '/',secure: true,sameSite: 'lax'})

      const errorCode = false;
      const data =  []; 
      return {
        // redirect: { destination: '/' },
        props: { session: await getSession(context),errorCode, data, params },
      };
    }
    return {
      redirect: { destination: '/' }
    };
  }
  
}



export default function Callback({ errorCode, data, params }) {
  const router = useRouter()
  const { data: session, status: session_status  } = useSession()
  const appContext = useContext(AppContext);
  // console.log("xxxxxxxxxxxx");
  // console.log(params);
  // console.log(session);
  // console.log(data);
  
  // console.log(cookies)
  // console.log("xxxxxxxxxxxx");
  useEffect(() => {
    const cookies = parseCookies()
    if(cookies[process.env.NEXT_PUBLIC_APP+'_token']){
      appContext.setToken(cookies[process.env.NEXT_PUBLIC_APP+'_token'])
    }else{
      appContext.setToken(null)
    }
    if(data['status']=='success' && data['is_internal_email']=='false'){
      router.replace('/register?status=internal_email')
    }else{
      if(data['status']=='success' && data['is_complete']=='false'){
        router.replace('/register')
      }else{
        if(data['status']=='success' && data['is_confirm']=='false'){
          router.replace('/register')
        }else{
          if(localStorage.getItem('backgift')&&localStorage.getItem('backgift')!=''){
            router.replace(localStorage.getItem('backgift'))
          }else{
            if(localStorage.getItem('backpath')){
              router.replace(localStorage.getItem('backpath'))
            }else{
              router.replace('/')
            }
          }
        }
      }
    }
  })

  if (errorCode) {
    return <Error statusCode={errorCode} />;
  }

  return null;
}

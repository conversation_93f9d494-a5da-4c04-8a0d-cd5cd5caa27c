import nookies from 'nookies'
import { parseCookies, setCookie, destroyCookie } from 'nookies'
import { getProviders, signOut, getSession, useSession } from "next-auth/react"; 
import { useState ,useEffect} from "react";

 

function SignOutPage({ params }) {
  

  const cookies = parseCookies()
  // console.log({ cookies })
  // console.log(providers)

  // console.log('getServerSideProps')
  // console.log(params)
  
 
  destroyCookie(null,process.env.NEXT_PUBLIC_APP + "_token", {
    path: '/', // THE KEY IS TO SET THE SAME PATH
  })

  signOut({redirect: true, callbackUrl: "/"})
  // console.log('xxx')
  return (
    null
  );
}

export default SignOutPage;

export async function getServerSideProps(context) {
    const cookies = nookies.get(context)
    const { req } = context;
    const session = await getSession({ req });

    const params = context.query;
    nookies.destroy(context, process.env.NEXT_PUBLIC_APP+'_token')

    return {
      props: {params},
    };
  }
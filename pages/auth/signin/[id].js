import { getProviders, signIn, getSession, useSession } from "next-auth/react";
import { useState } from "react";

function SignInPage({ providers, params }) {
  // console.log(providers)

  // console.log('getServerSideProps')
  // console.log(params)

  useState(() => {
    if (typeof window !== "undefined") {
      signIn(params.id);
    }
  }, []);

  return null;
}

export default SignInPage;

export async function getServerSideProps(context) {
  const { req } = context;
  const session = await getSession({ req });

  const params = context.query;

  if (session) {
    return {
      redirect: { destination: "/auth/callback/" + params.id },
    };
  }

  return {
    props: {
      session: await getSession(context),
      providers: await getProviders(),
      params,
    },
  };
}

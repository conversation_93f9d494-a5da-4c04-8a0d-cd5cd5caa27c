import Image from "next/image";
import Link from "next/link";
import React, {
  useState,
  useRef,
  useEffect,
  useContext,
  useReducer,
} from "react";
import { useDraggable } from 'react-use-draggable-scroll';
import Router, { useRouter } from "next/router";
import AppContext from "/libs/contexts/AppContext";
import Head from "next/head";
import Header from "../../../themes/header/header";
import Footer from "../../../themes/footer/footer";

import { Document, Page,pdfjs } from 'react-pdf';
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;


// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { URLSearchParams } from "url";
import { useSession, getSession } from "next-auth/react";
import { getUser } from "../../api/user";
// Serverside & Api fetching

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";
  const lang = context.locale || "th";
  //SEO
  const seo_res = await fetch(process.env.NEXT_PUBLIC_API + "/api/seo/article", {
    method: "POST",
  });
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  // Auth
  const user = await getUser(utoken);
  if (!user) {
    // return {
    //   redirect: { destination: "/" },
    // };
  }
  // Auth

  return {
    props: {
      utoken,
      seo_data,
      user,
    },
  };
}

export default function SamplePDF({ utoken, seo_data, user }) {
    const appContext = useContext(AppContext);
    const [file, setFile] = useState("http://localhost:8000/sample/pdf");
    const [numPages, setNumPages] = useState(null);
    const [pageNumber, setPageNumber] = useState(1);

    function onFileChange(event) {
        setFile(event.target.files[0]);
    }

    function onDocumentLoadSuccess({ numPages: nextNumPages }) {
        setNumPages(nextNumPages);
    }


    function goToPreviousPage() {
        if (pageNumber > 1) {
        setPageNumber(pageNumber - 1);
        }
    }

    function goToNextPage() {
        if (pageNumber < numPages) {
        setPageNumber(pageNumber + 1);
        }
    }
    
    const ref = useRef(null);
    const { events: events } = useDraggable(ref, {
        applyRubberBandEffect: true,
    });
  
  return (
    <div className="main-all">
     <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div ref={ref} {...events} className={`container custom-container space-between-content react-pdf-container`}>
            <Document file={file} onLoadSuccess={onDocumentLoadSuccess}>
                {Array.from({ length: numPages }, (_, index) => (
                    index<5?(
                        <Page
                            key={`page_${index + 1}`}
                            pageNumber={index + 1}
                            renderAnnotationLayer={false}
                            renderTextLayer={false}
                        />
                    ):null
                ))}
            </Document>
            {/* <Document file={file} onLoadSuccess={onDocumentLoadSuccess}>
                <Page
                    key={`page_${pageNumber}`}
                    pageNumber={pageNumber}
                    renderAnnotationLayer={false}
                    renderTextLayer={false}
                />
            </Document> */}
    
            {/* <div>
                <button onClick={goToPreviousPage} disabled={pageNumber === 1}>
                    Previous
                </button>
                <span>
                    Page {pageNumber} of {numPages}
                </span>
                <button onClick={goToNextPage} disabled={pageNumber === numPages}>
                    Next
                </button>
            </div> */}
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
}

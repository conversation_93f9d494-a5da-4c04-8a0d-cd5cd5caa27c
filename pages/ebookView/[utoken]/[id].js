import React from "react";
import Image from "next/image";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import Router, { useRouter } from "next/router";
import { getUser } from "../../api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import { useForm } from "react-hook-form";
import Head from "next/head";
import Link from "next/link";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import CourseDetailIntro from "/themes/components/courseDetailIntro";
import CourseDetailDescription from "/themes/components/courseDetailDescription";
import CourseDetailPrice from "/themes/components/courseDetailPrice";
import CourseDetailCode from "/themes/components/courseDetailCode";
import CourseDetailScholarship from "/themes/components/courseDetailScholarship";
import GroupCategory from "/themes/components/groupCategory";
import CommentZone from "/themes/components/commentZone";
import ListVdoModal from "/themes/components/listVdoModal";
import GroupAudioList from "/themes/components/groupAudioList";
import ListVdoEp from "/themes/components/listVdoEp";
import VdoModal from "/themes/components/vdoModal";
import CourseBanner from "/themes/components/courseBanner";
import EbookBanner from "/themes/components/ebookBanner";
import RankingViewHistory from "/themes/components/rankingViewHistory";
import ReactPlayer from "react-player";
import NumberFormat from "react-number-format";
import ListTags from "/themes/components/listTagsCourse";
import { IoMdDownload } from "react-icons/io";
import { useDraggable } from 'react-use-draggable-scroll';
import { Document, Page,pdfjs } from 'react-pdf';
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import { Pie } from "react-chartjs-2";
ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

import {
  Menu,
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
  Accordion,
  Tab,
  Popup,
} from "semantic-ui-react";
import "semantic-ui-css/semantic.min.css";
import stylesModal from "/public/assets/css/component/listQuizModal.module.css";
import styles from "/public/assets/css/pages/course.module.css";
import Swal from "sweetalert2";

import moment from "moment";
// import { gsap } from "gsap";
// import { ScrollTrigger } from "gsap/dist/ScrollTrigger";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const params = context.query;
  const utoken = params.utoken;
  const user = await getUser(utoken);
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/course/get/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();
  if (data["status"] == "false" || data["data"]["type"] != "Ebook") {
    return {
      redirect: { destination: "/" },
    };
  }

  return {
    props: {
      session: await getSession(context),
      data,
      utoken,
    },
  };
}

export default function Course({
  data,
  utoken,
}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [epIndex, setEpIndex] = useState(parseInt(router.query.ep)||null);
  const [lang, setLang] = useState(locale);
  const [file, setFile] = useState(null);
  const [firstLoad, setFirstLoad] = useState(true);
  const [numPages, setNumPages] = useState(null);
  const [pagePreview, setPagePreview] = useState([]);
  const [nowPage, setNowPage] = useState(0);
  const [showControl, setShowControl] = useState(false);
  const [allPages, setAllPages] = useState(1);
  useEffect(() => {
    if (firstLoad && epIndex && epIndex>0) {
      setFirstLoad(false);
      if(data["documentList"] && data["documentList"].length>0 && data["documentList"].length>=epIndex){
        setFile(process.env.NEXT_PUBLIC_API+"/ebook_preview/"+data["documentList"][(epIndex-1)]['id_encrypt']);
        if(data["documentList"][(epIndex-1)]["preview_page"]!=null && data["documentList"][(epIndex-1)]["preview_page"]!='' 
        && data["documentList"][(epIndex-1)]["preview_page"]!='null'){
          setPagePreview(data["documentList"][(epIndex-1)]["preview_page"].split(","));
        }else{
          setPagePreview([]);
        }
      }else{
        setFile(null);
        setPagePreview([]);
      }
    }
  }, [firstLoad,epIndex]);
  function ebookScrollRight() {
    var elements = document.getElementsByClassName('react-pdf__Page');
    var obj_width = elements[0].offsetWidth;
    ref.current.scrollLeft = obj_width * (nowPage+1);
  }
  function ebookScrollLeft() {
    var elements = document.getElementsByClassName('react-pdf__Page');
    var obj_width = elements[0].offsetWidth;
    ref.current.scrollLeft = obj_width * (nowPage-1);
  }
  function onDocumentLoadSuccess({ numPages: nextNumPages }) {
    setNumPages(nextNumPages);
  }
  const ref = useRef(null);
  const { events: events } = useDraggable(ref, {
      applyRubberBandEffect: true,
  });
  const [fullScreen, setFullScreen] = useState(true);
  const [toggleZoom, setToggleZoom] = useState(false);
  const [singlePage, setSinglePage] = useState(true);
  function checkFree(){
    if((data.data.price == 0 || (data.data.is_promotion == 1 && data.data.pro_price == 0) || data.data.is_internal || data.data.is_subscription || data.data.is_volume)&&discountCode==''){
      return true;
    }else{
      return false;
    }
  }
  const [popupShow, setPopupShow] = useState(false);
  function handleScroll() {
    try {
      var elements = document.getElementsByClassName('react-pdf__Page');
      var all_page = Math.ceil(ref.current.scrollWidth / (elements[0].offsetWidth));
      setAllPages(all_page);
      var obj_width = elements[0].offsetWidth;
      setNowPage(Math.ceil(ref.current.scrollLeft / obj_width));
    }
    catch(err) {
    }
    if(!popupShow && !data["data"]["allowed"]){
      var last_pos = ref.current.scrollWidth - (elements[0].offsetWidth*1.1);
      if(last_pos<=ref.current.scrollLeft){
        setPopupShow(true);
        if (checkFree()) {
          Swal.fire({
            text: translateEng('รับ E-Book เล่มนี้ฟรี เพื่ออ่านต่อ'),
            icon: 'info',
            showCancelButton: false,
            confirmButtonColor: '#648d2f',
            confirmButtonText: translateEng('รับเลย')
          }).then((result) => {
            if (result.isConfirmed) {
              window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
            }
          })
        }else{
          Swal.fire({
            text: translateEng('ซื้อ E-Book เล่มนี้เพื่ออ่านต่อ'),
            icon: 'info',
            showCancelButton: false,
            confirmButtonColor: '#648d2f',
            confirmButtonText: translateEng('ซื้อเลย')
          }).then((result) => {
            if (result.isConfirmed) {
              window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
            }
          })
        }
      }
    }
  }
  function translateEng(_value) {
    if(lang=='en'){
      if(_value=='ฟรี'){
        return "Free";
      }else if(_value=='บาท'){
        return "Baht";
      }else if(_value=='อ่าน'){
        return "Read";
      }else if(_value=='คะแนน'){
        return "Points";
      }else if(_value=='ปกติ'){
        return "Normal";
      }else if(_value=='ราคา'){
        return "Price";
      }else if(_value=='สถิติ'){
        return "Statistics";
      }else if(_value=='ภาพรวมการทำแบบทดสอบ'){
        return "Examination overview";
      }else if(_value=='แบบทดสอบทั้งหมด'){
        return "All Examinations";
      }else if(_value=='ชุด'){
        return "Sets";
      }else if(_value=='ทำแล้ว'){
        return "Done";
      }else if(_value=='ผ่าน'){
        return "Pass";
      }else if(_value=='ไม่ผ่าน'){
        return "Fail";
      }else if(_value=='ยังไม่ได้ทำ'){
        return "Not yet";
      }else if(_value=='เฉลี่ยคะแนนที่ทำได้'){
        return "Average score";
      }else if(_value=='คะแนนสูงสุด'){
        return "Highest score";
      }else if(_value=='คะแนนที่ทำได้'){
        return "Score";
      }else if(_value=='คะแนนต่ำสุด'){
        return "Lowest score";
      }else if(_value=='ผลคะแนน'){
        return "Result";
      }else if(_value=='อันดับของคุณ จากผู้ทำแบบทดสอบทั้งหมด'){
        return "Your ranking";
      }else if(_value=='อันดับ'){
        return "Ranking";
      }else if(_value=='ชื่อ'){
        return "Name";
      }else if(_value=='วันที่ทำ'){
        return "Date";
      }else if(_value=='วันที่'){
        return "Date";
      }else if(_value=='รอการตรวจสอบ'){
        return "Pending";
      }else if(_value=='ประวัติการทำแบบทดสอบหลังเรียน'){
        return "Post-Examination history";
      }else if(_value=='ประวัติการทำแบบทดสอบก่อนเรียน'){
        return "Pre-Examination history";
      }else if(_value=='ทดสอบครั้งที่'){
        return "Round";
      }else if(_value=='ชื่อแบบทดสอบ'){
        return "Examination Title";
      }else if(_value=='สถานะ'){
        return "Status";
      }else if(_value=='ผลการทดสอบ'){
        return "Result";
      }else if(_value=='ตรวจแล้ว'){
        return "Checked";
      }else if(_value=='ส่งคำตอบสำเร็จ'){
        return "Submitted";
      }else if(_value=='ปิด'){
        return "Close";
      }else if(_value=='กรุณาตอบคำถามให้ครบถ้วน'){
        return "Please answer questions completely";
      }else if(_value=='คุณต้องการเริ่มเรียนใช่หรือไม่?'){
        return "Do you want to start studying?";
      }else if(_value=='คุณต้องการรับ E-Book ฟรีใช่หรือไม่?'){
        return "Do you want to get a free E-Book?";
      }else if(_value=='ใช่'){
        return "Yes";
      }else if(_value=='ไม่'){
        return "No";
      }else if(_value=='กรุณาซื้อคอร์สนี้ก่อนค่ะ'){
        return "Please buy this course";
      }else if(_value=='กรุณาซื้อ E-Book นี้ก่อนค่ะ'){
        return "Please buy this E-Book";
      }else if(_value=='คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'){
        return "You have another item in your cart<br>Want to buy this product?";
      }else if(_value=='ยืนยัน'){
        return "Submit";
      }else if(_value=='ยกเลิก'){
        return "Cancel";
      }else if(_value=='พบข้อผิดพลาด กรุณาลองอีกครั้ง'){
        return "Found an error, please try again";
      }else if(_value=='กรุณาเข้าสู่ระบบค่ะ'){
        return "Please login";
      }else if(_value=='กรุณาเลือกเพลย์ลิสต์'){
        return "Choose playlist";
      }else if(_value=='ยินดีด้วย ใช้โค้ดส่วนลดสำเร็จ'){
        return "Congratulations, the discount code has been used successfully";
      }else if(_value=='ยินดีด้วย คุณได้รับการสนับสนุน'){
        return "Congratulations, you are supported";
      }else if(_value=='ขออภัย ไม่พบโค้ดส่วนลด'){
        return "Sorry, the discount code could not be found";
      }else if(_value=='ขออภัย ไม่พบผู้สนับสนุน'){
        return "Sorry, the supporter could not be found";
      }else if(_value=='บทเรียนทั้งหมด'){
        return "All lessons";
      }else if(_value=='เอกสารประกอบการเรียน'){
        return "Documents";
      }else if(_value=='ดาวน์โหลดไฟล์ในบทเรียน'){
        return "Download";
      }else if(_value=='รายละเอียด'){
        return "Details";
      }else if(_value=='ถาม-ตอบ'){
        return "Q & A";
      }else if(_value=='แบบทดสอบ'){
        return "Examination";
      }else if(_value=='คะแนนล่าสุดของคุณ'){
        return "Recently score";
      }else if(_value=='เริ่มใหม่'){
        return "Restart";
      }else if(_value=='แบบทดสอบของคุณอยู่ระหว่างตรวจสอบ'){
        return "Your test is under review";
      }else if(_value=='โค้ดส่วนลด'){
        return "Discount code";
      }else if(_value=='เลือกรับ Scholarship จากผู้สนับสนุน'){
        return "Select scholarship";
      }else if(_value=='เพิ่มไปยังเพลย์ลิสต์ของคุณ'){
        return "Add to playlist";
      }else if(_value=='สร้างเพลย์ลิสต์'){
        return "Create playlist";
      }else if(_value=='เพลย์ลิสต์ของคุณ'){
        return "Your playlist";
      }else if(_value=='ป้อนชื่อเพลย์ลิสต์'){
        return "Enter plalist title";
      }else if(_value=='เริ่มเรียน'){
        return "Start learning";
      }else if(_value=='รับฟรี'){
        return "Get Free";
      }else if(_value=='ซื้อคอร์สนี้'){
        return "Buy this course";
      }else if(_value=='ซื้อ E-Book'){
        return "Buy E-Book";
      }else if(_value=='คอร์สเกี่ยวข้อง'){
        return "Related course";
      }else if(_value=='E-Book ที่เกี่ยวข้อง'){
        return "Related E-Book";
      }else if(_value=='ข้อที่'){
        return "No.";
      }else if(_value=='ส่งคำตอบ'){
        return "Submit answer";
      }else if(_value=='ข้าม'){
        return "Skip";
      }else if(_value=='ต่อไป'){
        return "Next";
      }else if(_value=='แบบประเมินความพึงพอใจ (ใช้เวลาไม่ถึง 1 นาที)'){
        return "Assessment (It takes less than 1 minute.)";
      }else if(_value=='คำตอบ'){
        return "Answer";
      }else if(_value=='คำตอบที่ถูก'){
        return "Correct";
      }else if(_value=='คำอธิบาย'){
        return "Explain";
      }else if(_value=='ดูคำตอบ'){
        return "View";
      }else if(_value=='ยินดีด้วย!'){
        return "Congratulations!";
      }else if(_value=='คุณปลดล็อกใบประกาศ'){
        return "You unlocked the certificate";
      }else if(_value=='สามารถดาวน์โหลดใบประกาศได้ที่โปรไฟล์ของท่าน'){
        return "Certificate can be download at your profile";
      }else if(_value=='หรือคลิกที่นี่'){
        return "Or click here";
      }else if(_value=='คอร์ส'){
        return "Course";
      }else if(_value=='ดาวน์โหลด'){
        return "Download";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  return (
    <>
      <style>{`
        #onetrust-consent-sdk{
          display:none!important;
        }
        .full_ebook.single_page .preview-pdf-block{
          width: 100%!important;
        }
      `}</style>
      <div className={`main-all page-course ${fullScreen ? "full_ebook":""} ${singlePage ? "single_page":""} ${toggleZoom?'is_zoom':''}`}>
        <div className="main-body bg-white">
          <div className={`container custom-container space-between-content `}>
            <div className="podcast-page ebook-page">
              {fullScreen?(
                <div className="left-ebook-page" onClick={() => {ebookScrollLeft()}}>
                  {nowPage>0?(
                    <i className="icon-ic-left"></i>
                  ):null}
                </div>
              ):null}
              <div className="row ebook-price">
                {!data["data"]["allowed"] ?(
                  <div className="preview-pdf-block">
                    <div ref={ref} onScroll={() => {handleScroll()}} {...events} className={`react-pdf-container`}>
                      {file!=null && file!='' && file!='null' ?(
                        <Document file={file} onLoadSuccess={onDocumentLoadSuccess}>
                          {Array.from({ length: numPages }, (_, index) => (
                            (pagePreview.indexOf((index+1).toString()))>-1?(
                              <div className="block-page-pdf">
                                <Page
                                  onClick={() => {setFullScreen(true)}}
                                  key={`page_${index + 1}`}
                                  pageNumber={index + 1}
                                  renderAnnotationLayer={false}
                                  renderTextLayer={false}
                                />
                              </div>
                            ):null
                          ))}
                        </Document>
                      ):null}
                    </div>
                  </div>
                ):(
                  <div className={`preview-pdf-block`}>
                    <div ref={ref} onScroll={() => {handleScroll()}} {...events} className={`react-pdf-container`}>
                      {file!=null && file!='' && file!='null' ?(
                        <Document file={file} onLoadSuccess={onDocumentLoadSuccess}>
                          {Array.from({ length: numPages }, (_, index) => (
                            <div className="block-page-pdf">
                              <Page
                                onClick={() => {setFullScreen(true)}}
                                key={`page_${index + 1}`}
                                pageNumber={index + 1}
                                renderAnnotationLayer={false}
                                renderTextLayer={false}
                              />
                              <div className="number-pdf-page">
                                <p>หน้า {(index+1)+' จาก '+(numPages)}</p>
                              </div>
                            </div>
                          ))}
                        </Document>
                      ):null}
                    </div>
                  </div>
                )}
              </div>
              {fullScreen?(
                <div className="right-ebook-page">
                  {(singlePage&&(nowPage+1)<allPages)||(!singlePage&&(nowPage+2)<allPages)||(!singlePage && (nowPage+1)<allPages)||allPages==1?(
                    <div className="outer-next-ebook" onClick={() => {ebookScrollRight()}}>
                      <i className="icon-ic-right"></i>
                    </div>
                  ):null}
                </div>
              ):null}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

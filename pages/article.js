import React ,{ useLayoutEffect, useRef } from "react";
// Serverside & Api fetching
import nookies from 'nookies'
import { parseCookies, setCookie, destroyCookie } from 'nookies'
import { getUser } from "./api/user";
import { useState, useEffect,useContext } from "react";
import AppContext from '/libs/contexts/AppContext';
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react"
import Error from "next";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation,Lazy,FreeMode,Mousewheel } from "swiper";
import Head from "next/head";
import Header from "../themes/header/header";
import Footer from "../themes/footer/footer";
import GroupCardNews from "../themes/components/groupCardNews";
import Router, { useRouter } from "next/router";
import "swiper/css/navigation";
import "swiper/css";
import "swiper/css/lazy";
export async function getServerSideProps(context) {
  const cookies = nookies.get(context)
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP+'_token']);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP+'_token'] || '';
  //SEO
  const seo_res = await fetch(process.env.NEXT_PUBLIC_API + "/api/seo/article", {
    method: "POST",
  });
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO
  return {
    props: {seo_data,utoken,user},
  };
}

export default function Article({seo_data,utoken,user}) {
  const { data: session, status: session_status  } = useSession()
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const [listData, setListData] = React.useState(null);
  const [reload, setReload] = React.useState(true);
  useEffect(() => {
      if(reload){
        setReload(false)
        appContext.loadApi(process.env.NEXT_PUBLIC_API +"/api/mdcu/getNewsList",null,(data)=>{
          setListData(data)
        });
      }
  }, [reload]);

  const swipperBig = useRef();
  const [reloadInfo, setReloadInfo] = useState(true);
  const [infographicData, setInfographicData] = useState(null);

  const [reloadSlide, setReloadSlide] = useState(false);
  const [swiperSet, setSwiperSet] = useState(0);
  const [swiperSlide, setSwiperSlide] = useState(0);
  const [swiperData, setSwiperData] = useState(null);
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);
  useEffect(() => {
    if(reloadInfo){
      setReloadInfo(false)
      // appContext.loadApi(process.env.NEXT_PUBLIC_API +"/api/mdcu/getInfographic",null,(data)=>{
      //   setInfographicData(data)
      //   if(data && data['infographic_data']){
      //     setSwiperData(data['infographic_data'][swiperSet]['data'])
      //   }
      // });
    }
    if(reloadSlide){
      setReloadSlide(false)
      if(infographicData && infographicData['infographic_data']){
        setSwiperData(infographicData['infographic_data'][swiperSet]['data'])
      }
      swipperBig.current.slideTo(swiperSlide)
      window.scrollTo({
        top: document.getElementById("top_infographic").offsetTop,
        behavior: 'smooth',
      })
    }
  }, [reloadInfo,reloadSlide]);
  function goToSwiper(_set,_index){
    setSwiperSet(_set)
    setSwiperSlide(_index)
    setReloadSlide(true)
  }
  return (
    <div className="main-all">
     <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className={`container custom-container space-between-content`}>
          <div className="news-page">
            <div className="news-list">
              {listData && listData['newsList'] && listData['newsList'].length>0 ? (
                <div>
                  <div className="news-list-title">
                    <h3>
                      <span>ข่าวสาร</span>
                    </h3>
                  </div>
                  <div className="row row-a-stretch">
                    <GroupCardNews
                      lang={lang}
                      data={listData['newsList']}
                    ></GroupCardNews>
                  </div>
                </div>
              ) : (
                null
              )}
              {listData && listData['articleList'] && listData['articleList'].length>0 ? (
                <div>
                  <div className="news-list-title">
                    <h3>
                      <span>บทความ</span>
                    </h3>
                  </div>
                  <div className="row row-a-stretch">
                    <GroupCardNews
                      lang={lang}
                      data={listData['articleList']}
                    ></GroupCardNews>
                  </div>
                </div>
              ) : (
                null
              )}
            </div>
          </div>
        </div>
        {/* <div id="top_infographic" className="infographic-page">
          <div className={`container custom-container space-between-content`}>
            <div className="row">
              {swiperData && swiperData.length>0 ? (
                <div className="col-12">
                  <h2>
                    Infographic
                  </h2>
                  <div className="block-infographic size-big">
                    <Swiper freeMode={true} lazy={true} mousewheel={{
                forceToAxis: true,
                enabled: true,
              }} modules={[FreeMode,Lazy,Navigation,Mousewheel]} onSwiper={(swiper) => {swipperBig.current = swiper;}} navigation={true} className="mySwiper">
                      {swiperData.map((val, key) => (
                        <SwiperSlide key={key}>
                          <div className="card-img">
                            <a href={`/infographic/${val.slug}`}>
                              <img className="img-thumb cursor-pointer swiper-lazy" data-src={val.image} alt={val.title_th} />
                              <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                            </a>
                          </div>
                        </SwiperSlide>
                      ))}
                    </Swiper>
                  </div>
                </div>
              ):(null)}
              {infographicData && infographicData['infographic_data'] && infographicData['infographic_data'].length>0 ? (
                infographicData['infographic_data'].map((val, key) => (
                  <div className="col-12" key={key}>
                    <h2>
                      {val.group_title}
                    </h2>
                    <div className="block-infographic">
                      <Swiper
                        freeMode={true}
                        lazy={true}
                        mousewheel={{
                          forceToAxis: true,
                          enabled: true,
                        }}
                        modules={[Lazy,FreeMode,Mousewheel]}
                        spaceBetween={10}
                        breakpoints={{
                          0: {
                            slidesPerView: 2.3,
                          },
                          768: {
                            slidesPerView: 4.5,
                          },
                          992: {
                            slidesPerView: 6.5,
                          },
                        }}
                      >
                        {val.data.map((val_data, key_data) => (
                          <SwiperSlide key={key_data}>
                            <div className="card-img">
                              <img onClick={() => goToSwiper(key,key_data)} className="img-thumb cursor-pointer swiper-lazy" data-src={val_data.image} alt={val.title_th} />
                              <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                            </div>
                          </SwiperSlide>
                        ))}
                      </Swiper>
                    </div>
                  </div>
                ))
              ):(null)}
            </div>
          </div>
        </div> */}
      </div>
      <Footer></Footer>
    </div>
  );
}

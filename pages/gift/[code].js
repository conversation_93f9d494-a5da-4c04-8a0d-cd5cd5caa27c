import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { useRouter } from "next/router";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import { useState, useEffect, useContext } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import Link from "next/link";
export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("code", params.code);
  formData.append("utoken", cookies[process.env.NEXT_PUBLIC_APP + "_token"]);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/user/gift/" + params.code,
    {
      body: formData,
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const resdata = await res;
  const data = await res.json();

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/gift",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  if (data.status == "false") {
    return {
      props: {
        session: await getSession(context),
        seo_data,
        errorCode,
        data,
        params,
        user
      },
    };
  }
  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      user
    },
  };
}
export default function Gift({ seo_data, errorCode, data, params, user}) {
  const router = useRouter()
  const { data: session, status: session_status  } = useSession()
  const appContext = useContext(AppContext);
  useEffect(() => {
    if (!user) {
      localStorage.setItem("backgift", router.asPath);
    }else{
      localStorage.setItem("backgift", '');
    }
  }, [])
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div
          className={`container custom-container space-between-content thanks-page-container`}
        >
          {data.status == "success" ? (
            <div className="thanks-page white">
              <i className="icon-ic-tick-thanks"></i>
              <h3>ยินดีด้วย คุณได้รับของขวัญเรียบร้อยแล้ว</h3>
              <p>
                สามารถตรวจสอบได้ที่หน้าโปรไฟล์ของคุณ
              </p>
            </div>
          ) : (
            data.status == "limit" ? (
              <div className="thanks-page white">
                <i className="icon-ic-tick-thanks"></i>
                <h3>คุณเคยรับของขวัญนี้เรียบร้อยแล้ว</h3>
                <p>
                  สามารถตรวจสอบได้ที่หน้าโปรไฟล์ของคุณ
                </p>
              </div>
            ) : (
              data.status == "not-login" ? (
                <div className="thanks-page white alert">
                  <i className="icon-ic-remove-cart"></i>
                  <h3>คุณยังไม่ได้เข้าสู่ระบบ</h3>
                  <p>
                  กรุณาสมัครสมาชิก หรือเข้าสู่ระบบเพื่อรับของขวัญ
                  </p>
                </div>
              ) : (
                <div className="thanks-page white alert">
                  <i className="icon-ic-remove-cart"></i>
                  <h3>ขออภัย พบข้อผิดพลาด</h3>
                  <p>
                    กรุณาตรวจสอบลิงค์ที่ได้รับจากอีเมล <br></br>
                    หรือติดต่อสอบถามเพิ่มเติมได้ที่ 0 2256 4183 ,0 2256 4000
                    (รพ.จุฬาฯ)
                  </p>
                </div>
              )
            )
          )}
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
}

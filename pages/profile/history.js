import React from "react";
import Script from "next/script";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { render } from "react-dom";
import Swal from "sweetalert2";
import NumberFormat from "react-number-format";

import Head from "next/head";
import Header from "../../themes/header/header";
import Footer from "../../themes/footer/footer";
import ChooseTypeDataList from "../../themes/components/chooseTypeDataList";
import TableHistoryCourse from "../../themes/components/tableHistoryCourse";
import BlockProfileInfo from "../../themes/components/blockProfileInfo";

import Link from "next/link";
import Image from "next/image";

import "semantic-ui-css/semantic.min.css";

import styles from "/public/assets/css/pages/profile.module.css";

import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";


export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  // Auth
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }
  // Auth
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";
  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);
  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/user/profile/main",
    {
      body: formData,
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["status"] != "success") {
    return {
      redirect: { destination: "/" },
    };
  }
  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/history",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO
  const payment = {
    payment_callback: process.env.PAYMENT_CALLBACK_URL,
    payment_check_callback: process.env.PAYMENT_CHECK_CALLBACK_URL,
    payment_sdk: process.env.PAYMENT_SDK,
    payment_pkey: process.env.PAYMENT_PKEY,
    payment_mid: process.env.PAYMENT_MID,
    payment_name: process.env.PAYMENT_NAME,
  };
  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      user,
      utoken,
      payment
    },
  };
}

export default function Profile({
  seo_data,
  errorCode,
  data,
  params,
  user,
  utoken,
  payment
}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const [listData, setListData] = React.useState(null);
  const [menuData, setMenuData] = React.useState([]);
  const [reload, setReload] = React.useState(true);
  const [payPrice, setPayPrice] = React.useState(0);
  const [loadPay, setLoadPay] = React.useState(false);
  const [payType, setPayType] = useState('card');
  const [qrId, setQrId] = useState(null);
  useEffect(() => {
    if (reload) {
      setReload(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/user/myOrder",
        null,
        (data) => {
          setListData(data);
          var list_menu = [];
          list_menu.push({ value: "course", link: "/profile", text: "คอร์สของฉัน" });
          list_menu.push({ value: "live", link: "/profile/live", text: "Live ของฉัน" });
           list_menu.push({ value: "curriculum", link: "/profile/curriculum", text: "หลักสูตรของฉัน" });
          list_menu.push({ value: "ebook", link: "/profile/ebook", text: "Ebook ของฉัน" });
          list_menu.push({ value: "ticket", link: "/profile/ticket", text: "Ticket ของฉัน" });
          list_menu.push({ value: "subscription", link: "/profile/yearly-member", text: "Yearly Member" });
          list_menu.push({ value: "redemption", link: "/profile/redemption", text: "Point Redemption" });
          list_menu.push({ value: "history", link: "/profile/history", text: "ประวัติการสั่งซื้อ" });
          list_menu.push({ value: "favourite", link: "/profile/favourite", text: "รายการโปรด" });
          list_menu.push({ value: "my-list", link: "/profile/my-list", text: "เพลย์ลิสต์ของฉัน" });
          list_menu.push({ value: "certificate", link: "/profile/certificate", text: "ใบประกาศนียบัตร" });
          if(data.is_med){
            list_menu.push({ value: "cme-report", link: "/profile/cme-report", text: "CME Report" });
          }
          if(data.company_count>0){
            list_menu.push({ value: "company", link: "/profile/company", text: "Company" });
          }
          if(data.oculus_count>0){
            list_menu.push({ value: "oculus", link: "/profile/oculus", text: "Oculus" });
          }
          list_menu.push({ value: "chula-lifelong-learning", link: "", text: "Chula Lifelong Learning" });
          list_menu.push({ value: "immersive", link: "", text: "Immersive Courses" });
          list_menu.push({ value: "logout", link: "/auth/signout", text: "ออกจากระบบ" });
          setMenuData(list_menu);
        }
      );
    }
  }, [reload]);
  const handleOnChange = (e, data) => {
    var selectItem = menuData.find(item => item.value == data.value);
    if (selectItem) {
      if(selectItem.value=='immersive'){
        appContext.submitImmersive();
      }else if(selectItem.value=='chula-lifelong-learning'){
        appContext.creditBankPopup();
      }else{
        window.location.href = selectItem.link;
      }
    }
  };
  function cancelOrder(_id) {
    Swal.fire({
      title: 'คุณแน่ใจ?',
      html: "ต้องการยกเลิกคำสั่งซื้อนี้ใช่หรือไม่ ?",
      icon: "info",
      showCancelButton: true,
      confirmButtonColor: '#648d2f',
      cancelButtonColor: '#d33',
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก'
    }).then((result) => {
      if (result.value) {
        const formData = new URLSearchParams();
        formData.append("order_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/cancelOrder",
          formData,
          (data) => {
            if(data['status']=='success'){
              location.reload();
            }
          }
        );
      }
    });
  }
  function kPaymentSubmit(_id,_price,_type,_qr_id){
    appContext.setLoading(true);
    setPayPrice(_price);
    if(_type==1){
      const formData = new URLSearchParams();
      formData.append("order_id", _id);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/kpayOrder",
        formData,
        (res) => {
          if (res["status"] == "success") {
            setPayType('qr');
            setQrId(res['qr_id']);
            setLoadPay(false);
            setTimeout(() => {
              appContext.setLoading(false);
              setLoadPay(true);
            }, "100");
            setTimeout(() => {
              var kpayemnt_form = document.getElementById('kpayemnt_form');
              var _path_action = kpayemnt_form.getAttribute('action-url')

              kpayemnt_form.setAttribute('action',_path_action+'/'+ _id);
              
              var element = document.getElementsByClassName('pay-button');
              
              try{
                element[0].click();
              }catch(e){
               
              }
            }, "1000");
          }
        }
      );
    }else{
      const formData = new URLSearchParams();
      formData.append("order_id", _id);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/kpayOrderStamp",
        formData,
        (res) => {
          if (res["status"] == "success") {
            setPayType('card');
            setQrId(null);
            setLoadPay(false);
            setTimeout(() => {
              appContext.setLoading(false);
              setLoadPay(true);
            }, "100");
            setTimeout(() => {
              var kpayemnt_form = document.getElementById('kpayemnt_form');
              var _path_action = kpayemnt_form.getAttribute('action-url')

              kpayemnt_form.setAttribute('action',_path_action+'/'+ _id);
              
              var element = document.getElementsByClassName('pay-button');
              
              try{
                element[0].click();
              }catch(e){
                // console.log('kpayment not active')
              }
            }, "1000");
          }
        }
      );
    }
  }
  function payment2c2pSubmit(_id, _price, _type, _qr_id) {
    appContext.setLoading(true);
    setPayPrice(_price);
    
    // ตรวจสอบว่า _id ไม่เป็น null หรือ undefined
    if (!_id) {
      appContext.setLoading(false);
      Swal.fire({
        text: "ไม่พบข้อมูลคำสั่งซื้อ",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f",
      });
      return;
    }
    
    // กำหนดประเภทการชำระเงินตาม _type
    const paymentType = _type == 1 ? "QR" : "CARD";
    
    // สร้าง FormData สำหรับ API request
    const formData = new FormData();
    formData.append("order_id", _id);
    formData.append("payment_type", paymentType);
    
    // ตรวจสอบว่ามี utoken หรือไม่
    const token = localStorage.getItem("utoken") || sessionStorage.getItem("utoken") || utoken;
    if (!token) {
      appContext.setLoading(false);
      Swal.fire({
        text: "กรุณาเข้าสู่ระบบใหม่อีกครั้ง",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f",
      });
      return;
    }
    
    formData.append("utoken", token);
    
    // เรียก API เพื่อเตรียมข้อมูลการชำระเงิน
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/preparePayment",
      formData,
      (res) => {
        
        if (res.status === "success") {
          try {
            // สร้าง body ข้อมูลสำหรับ fetch API
            const jwtRequestData = JSON.stringify({
              order_id: _id,
              payment_type: paymentType,
              locale: "th"
            });
            
            // console.log("Sending JWT request:", {
            //   url: res.jwt_url,
            //   data: JSON.parse(jwtRequestData)
            // });
            
            // Step 1: Get JWT payload using fetch
            fetch(res.jwt_url, {
              method: "POST",
              headers: {
                'Content-Type': 'application/json',
              },
              body: jwtRequestData
            })
            .then(response => response.json())
            .then(jwtRes => {
              
              if (jwtRes.status === "success" && jwtRes.payload) {
                // Step 2: Get payment token using fetch
                const tokenRequestData = JSON.stringify({
                  payload: jwtRes.payload,
                  payment_order_id: _id,
                  payment_type: paymentType
                });
                
                // console.log("Sending token request:", {
                //   url: res.token_url,
                //   data: JSON.parse(tokenRequestData)
                // });
                
                return fetch(res.token_url, {
                  method: "POST",
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: tokenRequestData
                });
              } else {
                throw new Error(jwtRes.message || "ไม่สามารถสร้าง JWT ได้");
              }
            })
            .then(response => response.json())
            .then(tokenRes => {
              // console.log("Token response:", tokenRes);
              
              appContext.setLoading(false);
              
              if (tokenRes.status === "success") {
                // จัดการตามประเภทการชำระเงิน
                if (paymentType === "QR") { // QR Code
                  if (tokenRes.qrImageUrl) {
                    // แสดง QR Code
                    showQrCodePayment(tokenRes.qrImageUrl, _price, _id, res.check_url);
                  } else if (tokenRes.webPaymentUrl || tokenRes.paymentOptionUrl) {
                    // กรณี QR แต่ต้อง redirect
                    window.location.href = tokenRes.webPaymentUrl || tokenRes.paymentOptionUrl;
                  } else {
                    throw new Error("ไม่พบ QR Code หรือ URL สำหรับการชำระเงิน");
                  }
                } else { // Credit Card
                  if (tokenRes.webPaymentUrl || tokenRes.paymentOptionUrl) {
                    // Redirect ไปยังหน้าชำระเงิน
                    window.location.href = tokenRes.webPaymentUrl || tokenRes.paymentOptionUrl;
                  } else {
                    throw new Error("ไม่พบ URL สำหรับการชำระเงิน");
                  }
                }
              } else {
                throw new Error(tokenRes.message || "เกิดข้อผิดพลาดในการชำระเงิน");
              }
            })
            .catch(error => {
              console.error("Error in payment process:", error);
              appContext.setLoading(false);
              Swal.fire({
                text: error.message || "เกิดข้อผิดพลาดในการเชื่อมต่อ กรุณาลองใหม่อีกครั้ง",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f",
              });
            });
          } catch (error) {
            console.error("Error processing payment data:", error);
            appContext.setLoading(false);
            Swal.fire({
              text: "เกิดข้อผิดพลาดในการเตรียมการชำระเงิน กรุณาลองใหม่อีกครั้ง",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f",
            });
          }
        } else {
          appContext.setLoading(false);
          Swal.fire({
            text: res.message || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง",
            icon: "error",
            confirmButtonText: "ปิด",
            confirmButtonColor: "#648d2f",
          });
        }
      },
      (error) => {
        console.error("API error:", error);
        appContext.setLoading(false);
        Swal.fire({
          text: "เกิดข้อผิดพลาดในการเชื่อมต่อ กรุณาลองใหม่อีกครั้ง",
          icon: "error",
          confirmButtonText: "ปิด",
          confirmButtonColor: "#648d2f",
        });
      }
    );
  }
  
  function showQrCodePayment(qrImageUrl, price, orderId, checkUrl) {
    // สร้าง element สำหรับแสดง QR Code
    const qrElement = document.createElement('div');
    qrElement.id = 'qrContainer';
    qrElement.style.cssText = 'position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.8);z-index:10000;display:flex;align-items:center;justify-content:center;flex-direction:column;padding:20px;';
    
    qrElement.innerHTML = `
      <div style="background:white;border-radius:10px;padding:30px;max-width:400px;width:100%;text-align:center;">
        <h2 style="margin-bottom:20px;color:#648d2f;">สแกน QR Code เพื่อชำระเงิน</h2>
        <div style="width:250px;height:250px;margin:0 auto;">
          <img src="${qrImageUrl}" id="qrImage" style="width:100%;height:100%;object-fit:contain;" />
        </div>
        <p style="margin-top:20px;color:#666;">จำนวนเงิน: ${price} บาท</p>
        <p style="margin-top:5px;color:#666;">กรุณารอสักครู่ ระบบกำลังตรวจสอบการชำระเงิน...</p>
        <div id="paymentStatus" style="margin-top:15px;font-weight:bold;"></div>
        <button onclick="document.getElementById('qrContainer').remove();" style="margin-top:20px;padding:10px 20px;background:#648d2f;color:white;border:none;border-radius:5px;cursor:pointer;">ปิด</button>
      </div>
    `;
    
    document.body.appendChild(qrElement);
    
    // เริ่มตรวจสอบสถานะทุก 5 วินาที
    const statusElement = document.getElementById('paymentStatus');
    checkPaymentStatus(orderId, statusElement, checkUrl);
  }

  function checkPaymentStatus(orderId, statusElement, checkUrl) {
    const maxCheckTime = 15 * 60 * 1000;
    const checkStartTime = Date.now();

    const checkInterval = setInterval(() => {
      if (Date.now() - checkStartTime > maxCheckTime) {
        clearInterval(checkInterval);
        statusElement.innerHTML = '<span style="color:orange;">หมดเวลาการชำระเงิน กรุณาลองใหม่อีกครั้ง</span>';
        return;
      }
      
      // ส่งข้อมูลในรูปแบบ JSON ด้วย fetch API
      fetch(checkUrl, {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          order_id: orderId
        })
      })
      .then(response => response.json())
      .then(checkResponse => {
        // console.log("Payment status check:", checkResponse);
        
        if (checkResponse.payment_status === 'completed') {
          clearInterval(checkInterval);
          statusElement.innerHTML = '<span style="color:green;">การชำระเงินสำเร็จ! กำลังนำท่านไปยังหน้าประวัติการสั่งซื้อ...</span>';
          setTimeout(() => {
            window.location.reload();
          }, 3000);
        } else if (checkResponse.payment_status === 'failed') {
          clearInterval(checkInterval);
          statusElement.innerHTML = '<span style="color:red;">การชำระเงินไม่สำเร็จ กรุณาลองอีกครั้ง</span>';
        } else {
          statusElement.textContent = 'กำลังรอการชำระเงิน...';
        }
      })
      .catch(error => {
        console.error("Error checking payment status:", error);
        statusElement.innerHTML = '<span style="color:orange;">ไม่สามารถตรวจสอบสถานะได้ กรุณารอสักครู่...</span>';
      });
    }, 5000);
  }
  
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className="profile-page">
          <div className="container custom-container">
            <div className="row">
              {listData ? (
                <BlockProfileInfo user={user} count={listData['count']}></BlockProfileInfo>
              ):null}
              <div className="block-profile-my-description">
                <div className="description-action">
                  <div className="inner">
                    {/* {infoTypeOptions} */}

                    {/* <Select className="fm-control" options={infoTypeOptions} /> */}
                    {menuData ? (
                      <ChooseTypeDataList
                        type="history"
                        data={menuData}
                        submitImmersive={appContext.submitImmersive}
                        creditBankPopup={appContext.creditBankPopup}
                      ></ChooseTypeDataList>
                    ):null}
                  </div>
                </div>
                <div className="choose_type_data_select">
                  <div className="inner_data_select">
                    {menuData ? (
                      <Select
                        className="data_select"
                        onChange={handleOnChange}
                        options={menuData}
                        defaultValue={'history'}
                      />
                    ):null}
                  </div>
                </div>
                <div className="description-detail">
                  <div className="inner">
                    <div className={`${styles.titleListCourseGreenLine}`}>
                      <h3>
                        <span>ประวัติการสั่งซื้อ</span>
                      </h3>
                      <div className={`${styles.my_point}`}>
                        <p>
                          คุณมี
                        </p>
                        <div className={`${styles.coin}`}>
                          <Image alt="" width={32} height={32} src={'/assets/images/coin.png'} objectFit='contain' />
                        </div>
                        {listData ? (
                          <h3>
                            <NumberFormat
                              value={listData['reward_point']}
                              displayType={"text"}
                              thousandSeparator={true}
                            />
                          </h3>
                        ):(
                          <h3>
                            0
                          </h3>
                        )}
                      </div>
                    </div>
                    {listData &&
                    listData["data"] &&
                    listData["data"].length > 0 ? (
                      <TableHistoryCourse
                        data={listData["data"]}
                        callback={cancelOrder}
                        payment={payment2c2pSubmit} // เปลี่ยนจาก kPaymentSubmit เป็น payment2c2pSubmit
                      ></TableHistoryCourse>
                    ) : null}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {loadPay ? (
          payType == 'qr' ? (
            <>
            </>
          ):(
            <>
            </>
          )
        ):null}
        
      </div>
      <Footer></Footer>
    </div>
  );
}

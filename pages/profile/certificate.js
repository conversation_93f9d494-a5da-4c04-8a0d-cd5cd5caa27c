import React, { Component } from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import { useState, useEffect, useContext, useRef } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import { render } from "react-dom";

import { URLSearchParams } from "url";
import NumberFormat from "react-number-format";

import Head from "next/head";
import Header from "../../themes/header/header";
import Footer from "../../themes/footer/footer";
import ChooseTypeDataList from "../../themes/components/chooseTypeDataList";
import CardCertificate from "../../themes/components/cardCertificate";
import ChooseTypeDataSelect from "../../themes/components/chooseTypeDataSelect";
import BlockProfileInfo from "../../themes/components/blockProfileInfo";
import { useForm } from "react-hook-form";
import Link from "next/link";
import Image from "next/image";

import "semantic-ui-css/semantic.min.css";

import styles from "/public/assets/css/pages/profile.module.css";

import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";
import Swal from "sweetalert2";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  // Auth
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }
  // Auth
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";
  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);
  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/user/profile/main",
    {
      body: formData,
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["status"] != "success") {
    return {
      redirect: { destination: "/" },
    };
  }
  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/certificate",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      user,
      utoken
    },
  };
}

export default function Profile({
  seo_data,
  errorCode,
  data,
  params,
  user,
  utoken,
}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const [listData, setListData] = React.useState(null);
  const [menuData, setMenuData] = React.useState([]);
  const [reload, setReload] = React.useState(true);
  const [certName, setCertName] = useState(false);
  const { register, setValue, getValues } = useForm({
    defaultValues: {},
  });
  const [errorArray, setErrorArray] = useState([]);
  const [certNameTh, setCertNameTh] = React.useState(null);
  const [certNameEn, setCertNameEn] = React.useState(null);
  const [haveName, setHaveName] = useState(false);
  const [reloadCert, setReloadCert] = useState(false);
  useEffect(() => {
    if (appContext.user) {
      setValue("cert_name_th", appContext.user.cert_name_th);
      setValue("cert_name_en", appContext.user.cert_name_en);
      setCertNameTh(appContext.user.cert_name_th);
      setCertNameEn(appContext.user.cert_name_en);
      if(appContext.user.cert_name_th!=null&&appContext.user.cert_name_th!=''&&appContext.user.cert_name_th!='null'
      &&appContext.user.cert_name_en!=null&&appContext.user.cert_name_en!=''&&appContext.user.cert_name_en!='null'){
        setHaveName(true);
      }
    }
  }, [appContext.user]);
  useEffect(() => {
    if (reload) {
      setReload(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/user/myCertificate",
        null,
        (data) => {
          setListData(data);
          var list_menu = [];
          list_menu.push({ value: "course", link: "/profile", text: "คอร์สของฉัน" });
          list_menu.push({ value: "live", link: "/profile/live", text: "Live ของฉัน" });
          list_menu.push({ value: "curriculum", link: "/profile/curriculum", text: "หลักสูตรของฉัน" });
          list_menu.push({ value: "ebook", link: "/profile/ebook", text: "Ebook ของฉัน" });
          list_menu.push({ value: "ticket", link: "/profile/ticket", text: "Ticket ของฉัน" });
          list_menu.push({ value: "subscription", link: "/profile/yearly-member", text: "Yearly Member" });
          list_menu.push({ value: "redemption", link: "/profile/redemption", text: "Point Redemption" });
          list_menu.push({ value: "history", link: "/profile/history", text: "ประวัติการสั่งซื้อ" });
          list_menu.push({ value: "favourite", link: "/profile/favourite", text: "รายการโปรด" });
          list_menu.push({ value: "my-list", link: "/profile/my-list", text: "เพลย์ลิสต์ของฉัน" });
          list_menu.push({ value: "certificate", link: "/profile/certificate", text: "ใบประกาศนียบัตร" });
          if(data.is_med){
            list_menu.push({ value: "cme-report", link: "/profile/cme-report", text: "CME Report" });
          }
          if(data.company_count>0){
            list_menu.push({ value: "company", link: "/profile/company", text: "Company" });
          }
          if(data.oculus_count>0){
            list_menu.push({ value: "oculus", link: "/profile/oculus", text: "Oculus" });
          }
          list_menu.push({ value: "chula-lifelong-learning", link: "", text: "Chula Lifelong Learning" });
          list_menu.push({ value: "immersive", link: "", text: "Immersive Courses" });
          list_menu.push({ value: "logout", link: "/auth/signout", text: "ออกจากระบบ" });
          setMenuData(list_menu);
          setReloadCert(true);
        }
      );
    }
  }, [reload]);
  function submitName() {
    let register_data = getValues();
    let bol = true;
    var errarr = [];
    if (appContext.isNull(register_data["cert_name_th"])) {
      bol = false;
      errarr.push("cert_name_th");
    }
    if (appContext.isNull(register_data["cert_name_en"])) {
      bol = false;
      errarr.push("cert_name_en");
    }

    setErrorArray(errarr);
    if (bol) {
      let register_data = getValues();
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/user/cert_name",
        register_data,
        (data) => {
          if (data["status"] == "success") {
            setCertNameTh(data["name_th"]);
            setCertNameEn(data["name_en"]);
            setValue("cert_name_th", data["name_th"]);
            setValue("cert_name_en", data["name_en"]);
            Swal.fire({
              html: 'แก้ไขสำเร็จ',
              icon: "success",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              setHaveName(true);
              setCertName(false);
              setReloadCert(false);
              setTimeout(() => {
                setReloadCert(true);
              }, "0");
            });
          } else {
            Swal.fire({
              text: "พบข้อผิดพลาด กรุณาลองอีกครั้ง",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          }
        }
      );
    } else {
      Swal.fire({
        text: "กรุณากรอกชื่อภาษาไทย และภาษาอังกฤษ",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f"
      });
    }
  }
  function modalClose() {
    setCertName(false);
    setValue("cert_name_th", certNameTh);
    setValue("cert_name_en", certNameEn);
  }
  function modalOpen() {
    setCertName(true);
    setValue("cert_name_th", certNameTh);
    setValue("cert_name_en", certNameEn);
  }
  function checkName() {
    if(certNameTh!=null&&certNameTh!=''&&certNameTh!='null'&&certNameEn!=null&&certNameEn!=''&&certNameEn!='null'){
      Swal.fire({
        text: "ขออภัย ไม่สามารถแก้ไขชื่อซ้ำได้",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f"
      });
    }else{
      setCertName(true);
    }
    
  }
  const handleOnChange = (e, data) => {
    var selectItem = menuData.find(item => item.value == data.value);
    if (selectItem) {
      if(selectItem.value=='immersive'){
        appContext.submitImmersive();
      }else if(selectItem.value=='chula-lifelong-learning'){
        appContext.creditBankPopup();
      }else{
        window.location.href = selectItem.link;
      }
    }
  };
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className="profile-page">
          <div className="container custom-container">
            <div className="row">
              {listData ? (
                <BlockProfileInfo user={user} count={listData['count']}></BlockProfileInfo>
              ):null}
              <div className="block-profile-my-description">
                <div className="description-action">
                  <div className="inner">
                    {/* {infoTypeOptions} */}

                    {/* <Select className="fm-control" options={infoTypeOptions} /> */}
                    {menuData ? (
                      <ChooseTypeDataList
                        type="certificate"
                        data={menuData}
                        submitImmersive={appContext.submitImmersive}
                        creditBankPopup={appContext.creditBankPopup}
                      ></ChooseTypeDataList>
                    ):null}
                  </div>
                </div>
                <div className="choose_type_data_select">
                  <div className="inner_data_select">
                    {menuData ? (
                      <Select
                        className="data_select"
                        onChange={handleOnChange}
                        options={menuData}
                        defaultValue={'certificate'}
                      />
                    ):null}
                  </div>
                </div>
                <div className="description-detail">
                  <div className="inner">
                    <div className={`${styles.titleListCourseGreenLine} ${styles.mb_0}`}>
                      <h3>
                        <span>ใบประกาศนียบัตร</span>
                      </h3>
                      <Button className="green-button icon mb_mg_bot" onClick={() => checkName()}>
                        <i className="icon-ic-edit"></i>
                        <span className="name">แก้ไขชื่อ</span>
                      </Button>
                      <div className={`${styles.my_point}`}>
                        <p>
                          คุณมี
                        </p>
                        <div className={`${styles.coin}`}>
                          <Image alt="" width={32} height={32} src={'/assets/images/coin.png'} objectFit='contain' />
                        </div>
                        {listData ? (
                          <h3>
                            <NumberFormat
                              value={listData['reward_point']}
                              displayType={"text"}
                              thousandSeparator={true}
                            />
                          </h3>
                        ):(
                          <h3>
                            0
                          </h3>
                        )}
                      </div>
                    </div>

                    {listData &&
                    listData["data"] &&
                    listData["data"].length > 0 &&
                    reloadCert ? (
                      <CardCertificate
                        display="normal"
                        type="desktop"
                        data={listData["data"]}
                        haveName={haveName}
                        alert={checkName}
                      ></CardCertificate>
                    ) : null}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        className='modalCreateList'
        onClose={() => modalClose()}
        onOpen={() => modalOpen()}
        open={certName}>
        <Modal.Content className="modalCreateListContent">
          <div className="block-modal-CreateList">
              <div className="inner">
                <h3>กรอกชื่อที่ต้องการให้แสดงบนใบประกาศนียบัตร</h3>
              </div>
              <div className='fm-CreateList-add active'>
                <div className="item-fm">
                  <Input
                    type="text"
                    className="fm-control"
                    placeholder="ชื่อภาษาไทย"
                  >
                    <input
                      {...register("cert_name_th")}
                      maxLength={100}
                      data-type="textaddress"
                      onInput={appContext.diFormPattern}
                    />
                  </Input>
                </div>
              </div>
              <div className='fm-CreateList-add active'>
                <div className="item-fm">
                  <Input
                    type="text"
                    className="fm-control"
                    placeholder="ชื่อภาษาอังกฤษ"
                  >
                    <input
                      {...register("cert_name_en")}
                      maxLength={100}
                      data-type="textaddress"
                      onInput={appContext.diFormPattern}
                    />
                  </Input>
                </div>
              </div>
              <div className='fm-CreateList-action'>
                <button className='btn-add-list' onClick={() => submitName()}>
                  <span>ตกลง</span>
                </button>
              </div>
          </div>
        </Modal.Content>
      </Modal>
      <Footer></Footer>
    </div>
  );
}

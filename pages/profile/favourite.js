import React, { Component } from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import { useState, useEffect, useContext, useRef } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import { render } from "react-dom";

import NumberFormat from "react-number-format";

import Head from "next/head";
import Header from "../../themes/header/header";
import Footer from "../../themes/footer/footer";
import ChooseTypeDataList from "../../themes/components/chooseTypeDataList";
import CardImagetGroup from "../../themes/components/cardImageGroup";
import ChooseTypeDataSelect from "../../themes/components/chooseTypeDataSelect";
import BlockProfileInfo from "../../themes/components/blockProfileInfo";
import Link from "next/link";
import Image from "next/image";

import "semantic-ui-css/semantic.min.css";

import styles from "/public/assets/css/pages/profile.module.css";

import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";

const sortOptions = [
  { value: "date", text: "by Upload date" },
  { value: "price", text: "by Price" },
  { value: "popular", text: "Popular" },
  { value: "atoz", text: "by A to Z" },
];


export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  // Auth
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }
  // Auth
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";
  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);
  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/user/profile/main",
    {
      body: formData,
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["status"] != "success") {
    return {
      redirect: { destination: "/" },
    };
  }
  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/favourite",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO
  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      user,
      utoken
    },
  };
}

export default function Profile({
  seo_data,
  errorCode,
  data,
  params,
  user,
  utoken,
}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const [listData, setListData] = React.useState(null);
  const [menuData, setMenuData] = React.useState([]);
  const [reload, setReload] = React.useState(true);
  const [orderby, setOrderby] = React.useState("");
  useEffect(() => {
    if (reload) {
      setReload(false);
      const formData = new URLSearchParams();
      formData.append("orderby", orderby);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/user/myFavourite",
        formData,
        (data) => {
          // console.log(data);
          setListData(data);
          var list_menu = [];
          list_menu.push({ value: "course", link: "/profile", text: "คอร์สของฉัน" });
          list_menu.push({ value: "live", link: "/profile/live", text: "Live ของฉัน" });
           list_menu.push({ value: "curriculum", link: "/profile/curriculum", text: "หลักสูตรของฉัน" });
          list_menu.push({ value: "ebook", link: "/profile/ebook", text: "Ebook ของฉัน" });
          list_menu.push({ value: "ticket", link: "/profile/ticket", text: "Ticket ของฉัน" });
          list_menu.push({ value: "subscription", link: "/profile/yearly-member", text: "Yearly Member" });
          list_menu.push({ value: "redemption", link: "/profile/redemption", text: "Point Redemption" });
          list_menu.push({ value: "history", link: "/profile/history", text: "ประวัติการสั่งซื้อ" });
          list_menu.push({ value: "favourite", link: "/profile/favourite", text: "รายการโปรด" });
          list_menu.push({ value: "my-list", link: "/profile/my-list", text: "เพลย์ลิสต์ของฉัน" });
          list_menu.push({ value: "certificate", link: "/profile/certificate", text: "ใบประกาศนียบัตร" });
          if(data.is_med){
            list_menu.push({ value: "cme-report", link: "/profile/cme-report", text: "CME Report" });
          }
          if(data.company_count>0){
            list_menu.push({ value: "company", link: "/profile/company", text: "Company" });
          }
          if(data.oculus_count>0){
            list_menu.push({ value: "oculus", link: "/profile/oculus", text: "Oculus" });
          }
          list_menu.push({ value: "chula-lifelong-learning", link: "", text: "Chula Lifelong Learning" });
          list_menu.push({ value: "immersive", link: "", text: "Immersive Courses" });
          list_menu.push({ value: "logout", link: "/auth/signout", text: "ออกจากระบบ" });
          setMenuData(list_menu);
        }
      );
    }
  }, [reload]);
  const orderChange = (e, data) => {
    setOrderby(data.value);
    setReload(true);
  };
  const handleOnChange = (e, data) => {
    var selectItem = menuData.find(item => item.value == data.value);
    if (selectItem) {
      if(selectItem.value=='immersive'){
        appContext.submitImmersive();
      }else if(selectItem.value=='chula-lifelong-learning'){
        appContext.creditBankPopup();
      }else{
        window.location.href = selectItem.link;
      }
    }
  };
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className="profile-page">
          <div className="container custom-container">
            <div className="row">
              {listData ? (
                <BlockProfileInfo user={user} count={listData['count']}></BlockProfileInfo>
              ):null}
              <div className="block-profile-my-description">
                <div className="description-action">
                  <div className="inner">
                    {/* {infoTypeOptions} */}

                    {/* <Select className="fm-control" options={infoTypeOptions} /> */}
                    {menuData ? (
                      <ChooseTypeDataList
                        type="favourite"
                        data={menuData}
                        submitImmersive={appContext.submitImmersive}
                        creditBankPopup={appContext.creditBankPopup}
                      ></ChooseTypeDataList>
                    ):null}
                  </div>
                </div>
                <div className="choose_type_data_select">
                  <div className="inner_data_select">
                    {menuData ? (
                      <Select
                        className="data_select"
                        onChange={handleOnChange}
                        options={menuData}
                        defaultValue={'favourite'}
                      />
                    ):null}
                  </div>
                </div>
                <div className="description-detail">
                  <div className="inner">
                    <div className={`${styles.titleListCourseGreenLine}`}>
                      <h3>
                        <span>รายการโปรด</span>
                      </h3>
                      <div className={`${styles.my_point}`}>
                        <p>
                          คุณมี
                        </p>
                        <div className={`${styles.coin}`}>
                          <Image alt="" width={32} height={32} src={'/assets/images/coin.png'} objectFit='contain' />
                        </div>
                        {listData ? (
                          <h3>
                            <NumberFormat
                              value={listData['reward_point']}
                              displayType={"text"}
                              thousandSeparator={true}
                            />
                          </h3>
                        ):(
                          <h3>
                            0
                          </h3>
                        )}
                      </div>
                    </div>
                    <div className="col-12 col-md-6 col-lg-3 col-xl-3 col-sort">
                      <div className="course-sort">
                        <div className="item-fm">
                          <Select
                            className="fm-control green"
                            placeholder="เลือกหัวข้อ"
                            onChange={orderChange}
                            options={sortOptions}
                          />
                        </div>
                      </div>
                    </div>
                    {listData &&
                    listData["data"] &&
                    listData["data"].length > 0 ? (
                      <CardImagetGroup
                        display="normal"
                        type="desktop"
                        data={listData["data"]}
                      ></CardImagetGroup>
                    ) : null}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
}

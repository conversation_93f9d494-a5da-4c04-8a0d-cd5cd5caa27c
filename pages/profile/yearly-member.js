import React, { Component } from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import { useState, useEffect, useContext, useRef } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import { render } from "react-dom";

import { useRouter } from "next/router";
import NumberFormat from "react-number-format";

import Head from "next/head";
import Header from "../../themes/header/header";
import Footer from "../../themes/footer/footer";
import ChooseTypeDataList from "../../themes/components/chooseTypeDataList";
import ChooseTypeDataSelect from "../../themes/components/chooseTypeDataSelect";
import BlockProfileInfo from "../../themes/components/blockProfileInfo";

import ItemListCourse from "/themes/components/itemListCourse";
import Swal from "sweetalert2";
import Link from "next/link";
import Image from "next/image";

import "semantic-ui-css/semantic.min.css";

import styles from "/public/assets/css/pages/profile.module.css";

import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";


export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  // Auth
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }
  // Auth
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";
  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);
  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/user/profile/main",
    {
      body: formData,
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["status"] != "success") {
    return {
      redirect: { destination: "/" },
    };
  }
  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/subscription",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      user,
      utoken
    },
  };
}

export default function Profile({
  seo_data,
  errorCode,
  data,
  params,
  user,
  utoken,
}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);
  function translateEng(_value) {
    if(lang=='en'){
      if(_value=='ปิด'){
        return "Close";
      }else if(_value=='พบข้อผิดพลาด กรุณาลองอีกครั้ง'){
        return "Found an error, please try again";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  appContext.setToken(utoken);
  const [listData, setListData] = React.useState(null);
  const [menuData, setMenuData] = React.useState([]);
  const [reload, setReload] = React.useState(true);
  const [addCoupon, setAddCoupon] = useState(false);
  const [inputCode, setInputCode] = useState(null);
  useEffect(() => {
    if (reload) {
      setReload(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/user/mySubscription",
        null,
        (data) => {
          setListData(data);
          var list_menu = [];
          list_menu.push({ value: "course", link: "/profile", text: "คอร์สของฉัน" });
          list_menu.push({ value: "live", link: "/profile/live", text: "Live ของฉัน" });
           list_menu.push({ value: "curriculum", link: "/profile/curriculum", text: "หลักสูตรของฉัน" });
          list_menu.push({ value: "ebook", link: "/profile/ebook", text: "Ebook ของฉัน" });
          list_menu.push({ value: "ticket", link: "/profile/ticket", text: "Ticket ของฉัน" });
          list_menu.push({ value: "subscription", link: "/profile/yearly-member", text: "Yearly Member" });
          list_menu.push({ value: "redemption", link: "/profile/redemption", text: "Point Redemption" });
          list_menu.push({ value: "history", link: "/profile/history", text: "ประวัติการสั่งซื้อ" });
          list_menu.push({ value: "favourite", link: "/profile/favourite", text: "รายการโปรด" });
          list_menu.push({ value: "my-list", link: "/profile/my-list", text: "เพลย์ลิสต์ของฉัน" });
          list_menu.push({ value: "certificate", link: "/profile/certificate", text: "ใบประกาศนียบัตร" });
          if(data.is_med){
            list_menu.push({ value: "cme-report", link: "/profile/cme-report", text: "CME Report" });
          }
          if(data.company_count>0){
            list_menu.push({ value: "company", link: "/profile/company", text: "Company" });
          }
          if(data.oculus_count>0){
            list_menu.push({ value: "oculus", link: "/profile/oculus", text: "Oculus" });
          }
          list_menu.push({ value: "chula-lifelong-learning", link: "", text: "Chula Lifelong Learning" });
          list_menu.push({ value: "immersive", link: "", text: "Immersive Courses" });
          list_menu.push({ value: "logout", link: "/auth/signout", text: "ออกจากระบบ" });
          setMenuData(list_menu);
        }
      );
    }
  }, [reload]);
  const handleOnChange = (e, data) => {
    var selectItem = menuData.find(item => item.value == data.value);
    if (selectItem) {
      if(selectItem.value=='immersive'){
        appContext.submitImmersive();
      }else if(selectItem.value=='chula-lifelong-learning'){
        appContext.creditBankPopup();
      }else{
        window.location.href = selectItem.link;
      }
    }
  };

  function groupCategoryCallback(_type, _id ,_title) {
    if (user) {
      if (_type == "subscription") {
              // const str="ขออภัย MDCU MedUMORE\n" +
              // "ปิดปรับปรุงระบบชำระเงินชั่วคราว\n"+
              // "ท่านจะได้รับการแจ้งเตือน\n"+
              // "เมื่อการดำเนินการเสร็จสิ้น";
              // Swal.fire({
              //   html: '<pre style="font-family: \'Kanit\'">' + str + '</pre>',
              //   icon: "warning",
              //   confirmButtonText: "ตกลง",
              //   confirmButtonColor: "#648d2f"
              // });
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "subscription");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
          formData,
          (res_check) => {
            if(res_check['status']=='success'){
              if(res_check['count']==0){
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                  formData,
                  (data) => {
                    if(data['status']=='success'){
                      appContext.setReloadCart(true);
                      appContext.setReloadCourse(true);
          
                      document
                        .getElementsByClassName("main-header")[0]
                        .classList.add("active_cart");
                      document
                        .getElementsByClassName("group-menu-cart")[0]
                        .classList.add("on_show");
                      document.body.classList.add("open_cart");
                      document
                        .getElementsByClassName("group-menu-f-mobile")[0]
                        .classList.remove("on_show");
                    }else if(data['status']=='limit'){
                      Swal.fire({
                        text: "ขออภัยค่ะ คุณไม่สามารถต่ออายุเกิน 2 ปีได้",
                        icon: "info",
                        confirmButtonText: "ปิด",
                        confirmButtonColor: "#648d2f"
                      });
                    }else if(data['status']=='limit_buy'){
                      Swal.fire({
                        text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                        icon: "info",
                        confirmButtonText: "ปิด",
                        confirmButtonColor: "#648d2f"
                      });
                    }
                  }
                );
              }else{
                Swal.fire({
                  html: "คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?",
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: 'ยืนยัน',
                  cancelButtonText: 'ยกเลิก'
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                      formData,
                      (res_clear) => {
                        if(res_clear['status']=='success'){
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                            formData,
                            (data) => {
                              if(data['status']=='success'){
                                appContext.setReloadCart(true);
                                appContext.setReloadCourse(true);
                    
                                document
                                  .getElementsByClassName("main-header")[0]
                                  .classList.add("active_cart");
                                document
                                  .getElementsByClassName("group-menu-cart")[0]
                                  .classList.add("on_show");
                                document.body.classList.add("open_cart");
                                document
                                  .getElementsByClassName("group-menu-f-mobile")[0]
                                  .classList.remove("on_show");
                              }else if(data['status']=='limit'){
                                Swal.fire({
                                  text: "ขออภัยค่ะ คุณไม่สามารถต่ออายุเกิน 2 ปีได้",
                                  icon: "info",
                                  confirmButtonText: "ปิด",
                                  confirmButtonColor: "#648d2f"
                                });
                              }else if(data['status']=='limit_buy'){
                                Swal.fire({
                                  text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                                  icon: "info",
                                  confirmButtonText: "ปิด",
                                  confirmButtonColor: "#648d2f"
                                });
                              }
                            }
                          );
                        }
                      }
                    );
                  }
                });
              }
            }
          }
        );
      }else if (_type == "subscription_free") {
        const formData = new URLSearchParams();
        formData.append("subscription_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addSubscriptionFree",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              Swal.fire({
                text: "ลงทะเบียนสำเร็จ เริ่มเรียนได้เลย",
                icon: "success",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              }).then((result) => {
                if(lang=='en'){
                  location.href="/en/profile/yearly-member";
                }else{
                  location.href="/profile/yearly-member";
                }
              });
            } else if(obj["status"] == "not_expire"){
              Swal.fire({
                text: translateEng('ขออภัยคุณเป็น Yearly Member แล้ว'),
                icon: "info",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      }
    } else {
      Swal.fire({
        text: "กรุณาเข้าสู่ระบบค่ะ",
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }

  function submitCode() {
    if(inputCode!=null&&inputCode!=''){
      const formData = new URLSearchParams();
      formData.append("code", inputCode);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addSubscription",
        formData,
        (data) => {
          if(data['status']=='success'){
            setAddCoupon(false);
            Swal.fire({
              text: "ใช้ Coupon Code สำเร็จ",
              icon: "success",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              window.location.reload();
            });
          }else if(data['status']=='limit'){
            Swal.fire({
              text: "คุณได้ใช้ Coupon Code ครบกำหนดแล้ว",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          }else{
            Swal.fire({
              text: "ไม่พบ Coupon Code",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          }
        }
      );
    }else{
      Swal.fire({
        text: "กรุณากรอก Coupon Code",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f"
      });
    }
  }

  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className="profile-page">
          <div className="container custom-container">
            <div className="row">
              {listData ? (
                <BlockProfileInfo user={user} count={listData['count']}></BlockProfileInfo>
              ):null}
              <div className="block-profile-my-description">
                <div className="description-action">
                  <div className="inner">
                    {/* {infoTypeOptions} */}

                    {/* <Select className="fm-control" options={infoTypeOptions} /> */}
                    {menuData ? (
                      <ChooseTypeDataList
                        type="subscription"
                        data={menuData}
                        submitImmersive={appContext.submitImmersive}
                        creditBankPopup={appContext.creditBankPopup}
                      ></ChooseTypeDataList>
                    ):null}
                  </div>
                </div>
                <div className="choose_type_data_select">
                  <div className="inner_data_select">
                    {menuData ? (
                      <Select
                        className="data_select"
                        onChange={handleOnChange}
                        options={menuData}
                        defaultValue={'subscription'}
                      />
                    ):null}
                  </div>
                </div>
                {listData && listData['subscription'] ? (
                  listData['is_buy'] ? (
                    <div className="description-detail">
                      <div className="inner">
                        <div className={`${styles.titleListCourseGreenLine} ${styles.mb_0}`}>
                          {listData['expire_count']!=''?(
                            <h3>
                              <span>Yearly Member</span> <br className={`${styles.mobile}`}></br><span className={`${styles.small}`}>(สถานะ : เปิดใช้งาน</span><span className={`${styles.small}`}>วันหมดอายุ : {listData['expire_count']} วัน)</span>
                            </h3>
                          ):(
                            <h3>
                              <span>Yearly Member</span>
                            </h3>
                          )}
                          <Button className="green-button mb_mg_bot" onClick={() => setAddCoupon(true)}>
                            <Image
                              className="avatar ItemsRedeemImgSize"
                              src="/assets/images/subscription_code.png"
                              alt=""
                              layout="fill"
                              objectFit="contain"
                            />
                            <span className="name">Redeem</span>
                          </Button>
                          <div className={`${styles.my_point}`}>
                            <p>
                              คุณมี
                            </p>
                            <div className={`${styles.coin}`}>
                              <Image alt="" width={32} height={32} src={'/assets/images/coin.png'} objectFit='contain' />
                            </div>
                            {listData ? (
                              <h3>
                                <NumberFormat
                                  value={listData['reward_point']}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                />
                              </h3>
                            ):(
                              <h3>
                                0
                              </h3>
                            )}
                          </div>
                        </div>
                        {listData &&
                        listData["data"] &&
                        listData["data"].length > 0 ? (
                          <ItemListCourse data={listData["data"]}></ItemListCourse>
                        ) : null}
                      </div>
                    </div>
                  ):(
                    <div className="description-detail">
                      <div className="inner">
                        <div className={`${styles.titleListCourseGreenLine} ${styles.mb_0}`}>
                          {listData['expire_count']!=''?(
                            <h3>
                              <span>Yearly Member</span> <br className={`${styles.mobile}`}></br><span className={`${styles.small}`}>(สถานะ : เปิดใช้งาน</span><span className={`${styles.small}`}>วันหมดอายุ : {listData['expire_count']} วัน)</span>
                            </h3>
                          ):(
                            <h3>
                              <span>Yearly Member</span>
                            </h3>
                          )}
                          <Button className="green-button mb_mg_bot" onClick={() => setAddCoupon(true)}>
                              <Image
                                className="avatar ItemsRedeemImgSize"
                                src="/assets/images/subscription_code.png"
                                alt=""
                                layout="fill"
                                objectFit="contain"
                              />
                            <span className="name">Redeem</span>
                          </Button>
                          <div className={`${styles.my_point}`}>
                            <p>
                              คุณมี
                            </p>
                            <div className={`${styles.coin}`}>
                              <Image alt="" width={32} height={32} src={'/assets/images/coin.png'} objectFit='contain' />
                            </div>
                            {listData ? (
                              <h3>
                                <NumberFormat
                                  value={listData['reward_point']}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                />
                              </h3>
                            ):(
                              <h3>
                                0
                              </h3>
                            )}
                          </div>
                        </div>
                        <div className="block-profile-subscription">
                          <div className="inner-subscription">
                            <div className="subscription-content">
                              <h3>
                                {listData['subscription']['title']}
                              </h3>
                              <div dangerouslySetInnerHTML={{
                                __html: listData['subscription']['details'],
                              }}></div>
                              {listData['subscription']['cta']!=null&&listData['subscription']['cta']!=''&&listData['subscription']['cta']!='null'
                              &&listData['subscription']['link']!=null&&listData['subscription']['link']!=''&&listData['subscription']['link']!='null' ? (
                                <a href={listData['subscription']['link']}>{listData['subscription']['cta']}</a>
                              ):null}
                            </div>
                            <div className="subscription-action">
                            {user && (user.user_type==1||user.user_type==3||user.user_type==4) ? (
                              user.internal_status==1||user.internal_status==2?(
                                <button onClick={() => groupCategoryCallback("subscription_free", listData['subscription']['id'], "")} className="btn-subscription-buy"><span>ฟรี</span></button>
                              ):(
                                <button onClick={() => groupCategoryCallback("subscription", listData['subscription']['id'], "")} className="btn-subscription-buy"><span>ซื้อแพ็คเกจ</span></button>
                              )
                            ):(
                              null
                              // <button className="btn-subscription-buy"><span>เร็ว ๆ นี้</span></button>
                            )} 
                              <NumberFormat
                                value={listData['subscription']['price']}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <h2 {...props}>
                                    ราคา {value} บาท
                                  </h2>
                                )}
                              />
                              <p>{listData['subscription']['remark']}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                ):(
                  listData ? (
                    <div className="description-detail">
                      <div className="inner">
                        <div className={`${styles.titleListCourseGreenLine} ${styles.mb_0}`}>
                          {listData['expire_count']!=''?(
                            <h3>
                              <span>Yearly Member</span> <br className={`${styles.mobile}`}></br><span className={`${styles.small}`}>(สถานะ : เปิดใช้งาน</span><span className={`${styles.small}`}>วันหมดอายุ : {listData['expire_count']} วัน)</span>
                            </h3>
                          ):(
                            <h3>
                              <span>Yearly Member</span>
                            </h3>
                          )}
                          <Button className="green-button mb_mg_bot" onClick={() => setAddCoupon(true)}>
                            <Image
                              className="avatar ItemsRedeemImgSize"
                              src="/assets/images/subscription_code.png"
                              alt=""
                              layout="fill"
                                objectFit="contain"
                            />
                            <span className="name">Redeem</span>
                          </Button>
                          <div className={`${styles.my_point}`}>
                            <p>
                              คุณมี
                            </p>
                            <div className={`${styles.coin}`}>
                              <Image alt="" width={32} height={32} src={'/assets/images/coin.png'} objectFit='contain' />
                            </div>
                            {listData ? (
                              <h3>
                                <NumberFormat
                                  value={listData['reward_point']}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                />
                              </h3>
                            ):(
                              <h3>
                                0
                              </h3>
                            )}
                          </div>
                        </div>
                        {listData &&
                        listData["data"] &&
                        listData["data"].length > 0 ? (
                          <ItemListCourse data={listData["data"]}></ItemListCourse>
                        ) : null}
                      </div>
                    </div>
                  ):null
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        className='modalCreateList'
        onClose={() => setAddCoupon(false)}
        onOpen={() => setAddCoupon(true)}
        open={addCoupon}>
        <Modal.Content className="modalCreateListContent">
          <div className="block-modal-CreateList">
              <div className="inner">
                <h3>กรอก Coupon Code</h3>
              </div>
              <div className='fm-CreateList-add active'>
                <div className="item-fm">
                <Input
                  id="create_list"
                  type="text"
                  className="fm-control"
                  placeholder="Coupon Code"
                  onChange={(event) =>
                    setInputCode(event.target.value)
                  }
                ></Input>
                </div>
              </div>
              <div className='fm-CreateList-action'>
                <button className='btn-add-list' onClick={() => submitCode()}>
                  <span>ตกลง</span>
                </button>
              </div>
          </div>
        </Modal.Content>
      </Modal>
      <Footer></Footer>
    </div>
  );
}

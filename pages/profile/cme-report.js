import React, { Component } from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import { useState, useEffect, useContext } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";

import { render } from "react-dom";

import NumberFormat from "react-number-format";

import Head from "next/head";
import Header from "../../themes/header/header";
import Footer from "../../themes/footer/footer";
import ChooseTypeDataList from "../../themes/components/chooseTypeDataList";
import ItemListCourse from "../../themes/components/itemListCourse";
import ChooseTypeDataSelect from "../../themes/components/chooseTypeDataSelect";
import BlockProfileInfo from "../../themes/components/blockProfileInfo";

import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import moment from 'moment'; 
import 'moment/locale/th'; 

import Link from "next/link";
import Image from "next/image";

import "semantic-ui-css/semantic.min.css";

import styles from "/public/assets/css/pages/profile.module.css";
import { IoMdArrowDropdown } from "react-icons/io";


import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";




export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  // Auth
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }
  // Auth
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";
  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);
  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/user/profile/main",
    {
      body: formData,
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["status"] != "success") {
    return {
      redirect: { destination: "/" },
    };
  }
  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/profile",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      user,
      utoken
    },
  };
}

export default function Profile({
  seo_data,
  errorCode,
  data,
  params,
  user,
  utoken,
}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const [listData, setListData] = React.useState(null);
  const [menuData, setMenuData] = React.useState([]);
  const [reload, setReload] = React.useState(true);
  const [loadReport, setLoadReport] = React.useState(true);
  const [reportList, setReportList] = React.useState([]);
  const [reportSum, setReportSum] = React.useState(0);

  const [startDate, setStartDate] = React.useState(null);
  const [endDate, setEndDate] = React.useState(null);

  useEffect(() => {
    if (reload) {
      setReload(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/user/cmeReport",
        null,
        (data) => {
          setListData(data);
          var list_menu = [];
          list_menu.push({ value: "course", link: "/profile", text: "คอร์สของฉัน" });
          list_menu.push({ value: "live", link: "/profile/live", text: "Live ของฉัน" });
           list_menu.push({ value: "curriculum", link: "/profile/curriculum", text: "หลักสูตรของฉัน" });
          list_menu.push({ value: "ebook", link: "/profile/ebook", text: "Ebook ของฉัน" });
          list_menu.push({ value: "ticket", link: "/profile/ticket", text: "Ticket ของฉัน" });
          list_menu.push({ value: "subscription", link: "/profile/yearly-member", text: "Yearly Member" });
          list_menu.push({ value: "redemption", link: "/profile/redemption", text: "Point Redemption" });
          list_menu.push({ value: "history", link: "/profile/history", text: "ประวัติการสั่งซื้อ" });
          list_menu.push({ value: "favourite", link: "/profile/favourite", text: "รายการโปรด" });
          list_menu.push({ value: "my-list", link: "/profile/my-list", text: "เพลย์ลิสต์ของฉัน" });
          list_menu.push({ value: "certificate", link: "/profile/certificate", text: "ใบประกาศนียบัตร" });
          if(data.is_med){
            list_menu.push({ value: "cme-report", link: "/profile/cme-report", text: "CME Report" });
          }
          if(data.company_count>0){
            list_menu.push({ value: "company", link: "/profile/company", text: "Company" });
          }
          if(data.oculus_count>0){
            list_menu.push({ value: "oculus", link: "/profile/oculus", text: "Oculus" });
          }
          list_menu.push({ value: "chula-lifelong-learning", link: "", text: "Chula Lifelong Learning" });
          list_menu.push({ value: "immersive", link: "", text: "Immersive Courses" });
          list_menu.push({ value: "logout", link: "/auth/signout", text: "ออกจากระบบ" });
          setMenuData(list_menu);
        }
      );
    }
  }, [reload]);

  useEffect(() => {
    if (loadReport) {
      setLoadReport(false);
      const formData = new URLSearchParams();
      formData.append("from", !appContext.isNull(startDate)?moment(startDate).format('YYYY-MM-DD'):'');
      formData.append("to", !appContext.isNull(endDate)?moment(endDate).format('YYYY-MM-DD'):'');
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/user/cmeFilter",
        formData,
        (data) => {
          setReportList(data.data);
          setReportSum(data.data.reduce((sum, course) => sum + parseFloat(course.point), 0));
        }
      );
    }
  }, [loadReport]);
  const handleOnChange = (e, data) => {
    var selectItem = menuData.find(item => item.value == data.value);
    if (selectItem) {
      if(selectItem.value=='immersive'){
        appContext.submitImmersive();
      }else if(selectItem.value=='chula-lifelong-learning'){
        appContext.creditBankPopup();
      }else{
        window.location.href = selectItem.link;
      }
    }
  };
  const exportPdf = ()=>{
    if(!process.env.NEXT_PUBLIC_APPDOMAIN||(process.env.NEXT_PUBLIC_APPDOMAIN&&process.env.NEXT_PUBLIC_APPDOMAIN!='https://www.medumore.org')){
      var url = 'https://coreuat.medumore.org/cme-report/'+appContext.token+'/'+(!appContext.isNull(startDate)?moment(startDate).format('YYYY-MM-DD'):'null')+'/'+(!appContext.isNull(endDate)?moment(endDate).format('YYYY-MM-DD'):'null');
      window.open(url, '_blank');
    }else{
      var url = 'https://core.medumore.org/cme-report/'+appContext.token+'/'+(!appContext.isNull(startDate)?moment(startDate).format('YYYY-MM-DD'):'null')+'/'+(!appContext.isNull(endDate)?moment(endDate).format('YYYY-MM-DD'):'null');
      window.open(url, '_blank');
    }
  }
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className="profile-page">
          <div className="container custom-container">
            <div className="row">
              {listData ? (
                <BlockProfileInfo user={user} count={listData['count']}></BlockProfileInfo>
              ):null}
              <div className="block-profile-my-description">
                <div className="description-action">
                  <div className="inner">
                    {/* {infoTypeOptions} */}

                    {/* <Select className="fm-control" options={infoTypeOptions} /> */}
                    {menuData ? (
                      <ChooseTypeDataList
                        type="cme-report"
                        data={menuData}
                        submitImmersive={appContext.submitImmersive}
                        creditBankPopup={appContext.creditBankPopup}
                      ></ChooseTypeDataList>
                    ):null}
                  </div>
                </div>
                <div className="choose_type_data_select">
                  <div className="inner_data_select">
                    {menuData ? (
                      <Select
                        className="data_select"
                        onChange={handleOnChange}
                        options={menuData}
                        defaultValue={'cme-report'}
                      />
                    ):null}
                  </div>
                </div>
                <div className="description-detail">
                  <div className="inner">
                    <div className={`${styles.titleListCourseGreenLine}`}>
                      <h3>
                        <span>CME Report</span>
                      </h3>
                      <div className={`${styles.my_point}`}>
                        <p> คุณมี </p>
                        <div className={`${styles.coin}`}>
                          <Image alt="" width={32} height={32} src={'/assets/images/coin.png'} objectFit='contain' />
                        </div>
                        {listData ? (
                          <h3>
                            <NumberFormat
                              value={listData['reward_point']}
                              displayType={"text"}
                              thousandSeparator={true}
                            />
                          </h3>
                        ):(
                          <h3>
                            0
                          </h3>
                        )}
                      </div>
                      <div className={`${styles.cmeReportGroupBtn}`}>
                          <Button className={`${styles.cmeReportBtn}`} onClick={() => setLoadReport(true)}>ค้นหา</Button>
                          <Button className={`${styles.cmeReportBtn} ${styles.cmePrint}`} onClick={() => exportPdf()}>Print</Button>
                      </div>
                    </div>
                          
                          {/* ======= Update =======*/}
                          <>
                            <div  className={`${styles.cmeReportDataHead}`}>
                              {/* <div className={`${styles.tDataTopTitle}`}></div>
                              <div className={`${styles.tDataTopAction}`}></div> */}
                              {/* <Button></Button>
                              <Button></Button> */}
                              <div className="item-fm fm-DatePicker">
                                <DatePicker
                                  className="Ui_DatePicker"
                                  labelClassName="Ui_DatePicker_label"
                                  calendarClassName="Ui_DatePicker_calendar"
                                  popperClassName="Ui_DatePicker_popper"
                                  wrapperClassName="Ui_DatePicker_wrapper"
                                  selected={startDate}
                                  onChange={(date) => setStartDate(date)}
                                  selectsStart
                                  startDate={startDate}
                                  endDate={endDate}
                                  placeholderText="From"
                                  dateFormat="dd/MM/yyyy"
                                />
                                  <IoMdArrowDropdown className="Ui_DatePicker_calendarIcon" />
                              </div>
                              <div className="item-fm fm-DatePicker">
                                <DatePicker
                                    icon={"fa fa-calendar"}
                                    className="Ui_DatePicker"
                                    labelClassName="Ui_DatePicker_label"
                                    calendarClassName="Ui_DatePicker_calendar"
                                    popperClassName="Ui_DatePicker_popper"
                                    wrapperClassName="Ui_DatePicker_wrapper"
                                    selected={endDate}
                                    onChange={(date) => setEndDate(date)}
                                    selectsEnd
                                    startDate={startDate}
                                    endDate={endDate}
                                    minDate={startDate}
                                    placeholderText="To"
                                    dateFormat="dd/MM/yyyy"
                                />
                                <IoMdArrowDropdown className="Ui_DatePicker_calendarIcon" />
                              </div>
                            </div>
                            <div  className={`${styles.cmeReportDataBody}`}>
                              <table className={`${styles.tableReport}`}>
                                <thead>
                                  <tr>
                                    <th>Date</th>
                                    <th>Course</th>
                                    <th>CME Credits</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {reportList.length>0?(
                                    reportList.map((report, index) => (
                                      <tr key={index}>
                                        <td width={'30%'}>{moment(report.created_at).format('DD/MM/YYYY')}</td>
                                        <td width={'50%'}>{report.course_name}</td>
                                        <td width={'20%'}>{report.point}</td>
                                      </tr>
                                    ))
                                  ):(
                                    <tr>
                                      <td width={'30%'}>-</td>
                                      <td width={'50%'}>-</td>
                                      <td width={'20%'}>-</td>
                                    </tr>
                                  )}
                                  {/* <tr><td></td><td></td><td></td></tr> */}
                                </tbody>
                              </table>
                              <table className={`${styles.tableReport} ${styles.total}`}>
                              <thead>
                                  <tr>
                                    <th width={''}></th>
                                    <th width={'15%'}>Total</th>
                                    <th width={'20%'}>{reportSum}</th>
                                  </tr>
                                </thead>
                              </table>
                            </div>
                          </>
                          {/* ======= Update =======*/}

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
}

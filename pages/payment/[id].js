import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";

import { useState, useEffect, useContext, useRef, useReducer } from "react";

import { useRouter } from "next/router";
import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import Link from "next/link";
import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";
import { useForm } from "react-hook-form";
// Serverside & Api fetching
import Swal from "sweetalert2";

import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/order/get/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();
  if (data.status == "false") {
    return {
      redirect: { destination: "/" },
    };
  } else {
    if (data.payment != 3) {
      return {
        redirect: { destination: "/" },
      };
    }
  }
  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/payment",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      utoken,
      user,
    },
  };
}

const typeoption = [
  {
    key: "ธนาคารกรุงเทพ",
    value: "ธนาคารกรุงเทพ",
    text: "ธนาคารกรุงเทพ",
  },
  {
    key: "ธนาคารกรุงไทย",
    value: "ธนาคารกรุงไทย",
    text: "ธนาคารกรุงไทย",
  },
  {
    key: "ธนาคารกรุงศรีอยุธยา",
    value: "ธนาคารกรุงศรีอยุธยา",
    text: "ธนาคารกรุงศรีอยุธยา",
  },
  {
    key: "ธนาคารกสิกรไทย",
    value: "ธนาคารกสิกรไทย",
    text: "ธนาคารกสิกรไทย",
  },
  {
    key: "ธนาคารเกียรตินาคิน",
    value: "ธนาคารเกียรตินาคิน",
    text: "ธนาคารเกียรตินาคิน",
  },
  {
    key: "ธนาคารซีไอเอ็มบี ไทย",
    value: "ธนาคารซีไอเอ็มบี ไทย",
    text: "ธนาคารซีไอเอ็มบี ไทย",
  },
  {
    key: "ธนาคารทหารไทยธนชาต",
    value: "ธนาคารทหารไทยธนชาต",
    text: "ธนาคารทหารไทยธนชาต",
  },
  {
    key: "ธนาคารทิสโก้",
    value: "ธนาคารทิสโก้",
    text: "ธนาคารทิสโก้",
  },
  {
    key: "ธนาคารไทยพาณิชย์",
    value: "ธนาคารไทยพาณิชย์",
    text: "ธนาคารไทยพาณิชย์",
  },
  {
    key: "ธนาคารยูโอบี",
    value: "ธนาคารยูโอบี",
    text: "ธนาคารยูโอบี",
  },
  {
    key: "ธนาคารแลนด์ แอนด์ เฮ้าส์",
    value: "ธนาคารแลนด์ แอนด์ เฮ้าส์",
    text: "ธนาคารแลนด์ แอนด์ เฮ้าส์",
  },
  {
    key: "ธนาคารสแตนดาร์ดชาร์เตอร์ด (ไทย)",
    value: "ธนาคารสแตนดาร์ดชาร์เตอร์ด (ไทย)",
    text: "ธนาคารสแตนดาร์ดชาร์เตอร์ด (ไทย)",
  },
  {
    key: "ธนาคารไอซีบีซี (ไทย)",
    value: "ธนาคารไอซีบีซี (ไทย)",
    text: "ธนาคารไอซีบีซี (ไทย)",
  },
];

const bankoption = [
  { key: "ธนาคารกสิกรไทย", value: "ธนาคารกสิกรไทย", text: "ธนาคารกสิกรไทย" },
];

export default function Payment({
  seo_data,
  errorCode,
  data,
  params,
  utoken,
  user,
}) {
  // console.log(data)
  const router = useRouter();
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [errorArray, setErrorArray] = useState([]);
  const [reload, setReload] = useState(true);
  const { register, setValue, getValues } = useForm({
    defaultValues: {},
  });

  const [startDate, setStartDate] = useState();
  function changeDate(_date) {
    setStartDate(_date);
    setValue("t_date", moment(_date).format("yyyy-MM-D HH:mm:ss"));
  }

  useEffect(() => {
    if (reload) {
      setReload(false);
      setValue("order_no", data.order.order_no);
      setValue("amount", data.order.total_price);
      setValue("to_bank", "ธนาคารกสิกรไทย");
    }
  }, data);

  const onSubmit = () => {
    let register_data = getValues();
    let bol = true;
    var errarr = [];
    if (appContext.isNull(register_data["order_no"])) {
      bol = false;
      errarr.push("order_no");
    }
    if (appContext.isNull(register_data["by_bank"])) {
      bol = false;
      errarr.push("by_bank");
    }
    if (appContext.isNull(register_data["to_bank"])) {
      bol = false;
      errarr.push("to_bank");
    }
    if (appContext.isNull(register_data["t_date"])) {
      bol = false;
      errarr.push("t_date");
    }
    if (appContext.isNull(register_data["amount"])) {
      bol = false;
      errarr.push("amount");
    }
    if (appContext.isNull(register_data["image_slip"])) {
      bol = false;
      errarr.push("image_slip");
    }
    setErrorArray(errarr);
    if (bol) {
      let register_data = getValues();
      const formData = new URLSearchParams();
      formData.append("data", JSON.stringify(register_data));
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/updateOrder",
        formData,
        (obj) => {
          if (obj["status"] == "success") {
            setTimeout(() => {
              Swal.fire({
                text: "แจ้งชำระเงินสำเร็จ",
                icon: "success",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              }).then((result) => {
                location.href = "/profile/history";
              });
            }, "0");
          }
        }
      );
    } else {
      Swal.fire({
        text: "กรุณาตรวจสอบข้อมูล",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f"
      });
    }
  };

  const checkMyError = (_val) => {
    if (errorArray.indexOf(_val) > -1) {
      return true;
    }
    return false;
  };

  const previewFile = (event) => {
    event.preventDefault();
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      function () {
        if (_.startsWith(reader.result, "data:")) {
          appContext.loadApi(
            process.env.NEXT_PUBLIC_API + "/api/image/save",
            { image: reader.result },
            (data) => {
              if (data["status"] == "true") {
                setValue("image_slip", data["path"]);
                document.getElementById("send_payment_btn").classList.remove("disabled");
              }
            }
          );
        }
      },
      false
    );
    if (file) {
      reader.readAsDataURL(file);
    }
  };
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className="register-page">
          <div className="register-block-form">
            <div className="block-form-inner">
              {/* ===== */}
              <div className="row">
                <div className="col-12">
                  <div className="register-block-form-title">
                    <h3>PAYMENT</h3>
                    <p>แจ้งชำระเงิน</p>
                  </div>
                </div>
              </div>
              {/* ===== */}
              <div className="row-fm-register row">
                <div className="col-fm-register col-12 col-md-12">
                  <div className="item-fm">
                    <p className="fm-title">รหัสการสั่งซื้อ</p>
                    <Input type="text" className="fm-control">
                      <input
                        {...register("order_no")}
                        maxLength={100}
                        disabled
                        data-type="textaddress"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("order_no") ? "error" : ""}
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <p className="fm-title">ชำระเงินโดย</p>
                    <Select
                      placeholder="เลือกธนาคาร"
                      options={typeoption}
                      className={
                        "fm-control " + (checkMyError("by_bank") ? "error" : "")
                      }
                      onChange={(event, data) => {
                        setValue("by_bank", data.value);
                      }}
                    />
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <p className="fm-title">ชำระไปที่</p>
                    <Select
                      placeholder="ธนาคารกสิกรไทย"
                      options={bankoption}
                      disabled
                      className={
                        "fm-control " + (checkMyError("to_bank") ? "error" : "")
                      }
                      onChange={(event, data) => {
                        setValue("to_bank", data.value);
                      }}
                    />
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <p className="fm-title">วันที่และเวลา</p>
                    <DatePicker
                      className="fm-datetime"
                      timeInputLabel="Time:"
                      dateFormat="dd/MM/yyyy h:mm aa"
                      showTimeInput
                      selected={startDate}
                      onChange={(date) => changeDate(date)}
                    />
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <p className="fm-title">จำนวนเงิน</p>
                    <Input type="text" className="fm-control">
                      <input
                        {...register("amount")}
                        maxLength={10}
                        disabled
                        data-type="number"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("amount") ? "error" : ""}
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-12">
                  <div className="item-fm">
                    <p className="fm-title">แนบไฟล์สลิปการชำระเงินที่นี่</p>
                    <input
                      className="fm-upload"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/gif,image/xbm,image/xpm,image/wbmp,image/webp,image/bmp"
                      onChange={(e) => {
                        previewFile(e);
                      }}
                    />
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-12">
                  <div className="item-fm">
                    <p className="fm-title">หมายเหตุ</p>
                    <textarea
                      {...register("remark")}
                      className={
                        "fm-control " + (checkMyError("remark") ? "error" : "")
                      }
                      onInput={appContext.diFormPattern}
                      data-type="textaddress"
                      rows="3"
                    ></textarea>
                  </div>
                </div>
                <div className="col-12 col-md-6 offset-md-3">
                  <button
                    id="send_payment_btn"
                    onClick={onSubmit}
                    className="btn-default btn-register disabled"
                  >
                    <span>แจ้งชำระเงิน</span>
                  </button>
                </div>
              </div>
              {/* ===== */}
            </div>
          </div>
        </div>
      </div>

      <Footer></Footer>
    </div>
  );
}

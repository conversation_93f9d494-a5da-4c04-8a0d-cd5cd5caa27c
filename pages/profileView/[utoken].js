import React from "react";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import _ from "lodash";

import { useRouter } from "next/router";
import Head from "next/head";
import Header from "../../themes/header/header";
import Footer from "../../themes/footer/footer";
import Link from "next/link";
import Image from "next/image";
import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";
import { useForm } from "react-hook-form";
// Serverside & Api fetching
import Error from "next";
// Serverside & Api fetching
// Auth
import nookies from "nookies";
import { getUser } from "/pages/api/user";
import { useSession, getSession } from "next-auth/react";
// Auth
import AppContext from "/libs/contexts/AppContext";
import Swal from "sweetalert2";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const params = context.query;
  const utoken = params.utoken;
  const user = await getUser(utoken);
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);
  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/user/profile/main",
    {
      body: formData,
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["status"] != "success") {
    return {
      redirect: { destination: "/" },
    };
  }
  return {
    props: {
      session: await getSession(context),
      data,
      params,
      user,
      utoken
    },
  };
}
export default function Edit({ data, params, user,utoken }) {
  const router = useRouter();
  const appContext = useContext(AppContext);
  const inputFiledata = useRef(null);
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [reload, setReload] = React.useState(true);
  const [reloadDistrict, setReloadDistrict] = React.useState(true);
  const [reloadSubDistrict, setReloadSubDistrict] = React.useState(true);
  const [provinceOptions, setProvinceOptions] = useState(null);
  const [districtOptions, setDistrictOptions] = useState(null);
  const [subdistrictOptions, setSubdistrictOptions] = useState(null);
  const [errorArray, setErrorArray] = useState([]);
  const [district, setDistrict] = useState(0);
  const [subdistrict, setSubdistrict] = useState(0);
  const [memberOptions, setMemberOptions] = useState(null);
  const [subLearnerOptions, setSubLearnerOptions] = useState(null);
  const { register, setValue, getValues } = useForm({
    defaultValues: {},
  });
  const [medical, setMedical] = useState(false);
  const [nurse, setNurse] = useState(false);
  const [subLearner, setSubLearner] = useState(false);
  const [subLearnerEtc, setSubLearnerEtc] = useState(false);
  const [showInternal, setShowInternal] = useState(false);
  const [showEmail, setShowEmail] = useState(false);
  const [checkPop, setCheckPop] = useState(true);
  const [internal, setInternal] = useState([
    { key: "", value: "", text: "ประเภทบุคลากร" },
    { key: "se3", value: 3, text: "บุคลากรคณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย" },
    { key: "se1", value: 1, text: "บุคลากรโรงพยาบาลจุฬาลงกรณ์" },
    { key: "se2", value: 2, text: "บุคลากรโรงพยาบาลอื่นๆ" },
  ]);
  const [userLoad, setUserLoad] = React.useState(true);

  useEffect(() => {
    if (reload) {
      setReload(false);
      setValue("login_type", "email");
      getProvince();
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/userType",
        null,
        (data) => {
          setMemberOptions(data.data);
          setSubLearnerOptions(data.sub_learner);
        }
      );
    }
  }, [reload]);

  useEffect(() => {
    if (user) {
      setUserLoad(false)
      setValue("name", user.name);
      if(user.lastname!='null'){
        setValue("lastname", user.lastname);
      }else{
        setValue("lastname", '');
      }
      if(user.email!='null'){
        setValue("email", user.email);
      }else{
        setValue("email", '');
      }
      setValue("password", user.password);
      if(user.mobile!='null'){
        setValue("mobile", user.mobile);
      }else{
        setValue("mobile", '');
      }
      if(user.address!='null'){
        setValue("address", user.address);
      }else{
        setValue("address", '');
      }
      if(user.subdistrict!='null'){
        setValue("subdistrict", user.subdistrict);
      }else{
        setValue("subdistrict", '');
      }
      if(user.district!='null'){
        setValue("district", user.district);
      }else{
        setValue("district", '');
      }
      if(user.province!='null'){
        setValue("province", user.province);
      }else{
        setValue("province", '');
      }
      if(user.postcode!='null'){
        setValue("postcode", user.postcode);
      }else{
        setValue("postcode", '');
      }
      if(user.medical_id!='null'){
        setValue("medical_id", user.medical_id);
      }else{
        setValue("medical_id", '');
      }
      if(user.n_id!='null'){
        setValue("n_id", user.n_id);
      }else{
        setValue("n_id", '');
      }
      if(user.internal!='null'){
        setValue("internal", user.internal);
      }else{
        setValue("internal", '');
      }
      if(user.internal_email!='null'){
        setValue("internal_email", user.internal_email);
      }else{
        setValue("internal_email", '');
      }
      if(user.user_type!='null'){
        setValue("user_type", user.user_type);
      }else{
        setValue("user_type", '');
      }
      if (user.user_type==1) {
        setMedical(true)
      }
      if (user.user_type==3 && user.sub_learner==2) {
        setNurse(true)
      }
      if (user.user_type==3) {
        setSubLearner(true)
      }
      if(user.sub_learner!='null'){
        setValue("sub_learner", user.sub_learner);
      }else{
        setValue("sub_learner", '');
      }
      if (user.sub_learner==1) {
        setSubLearnerEtc(true)
      }
      if(user.sub_learner_etc!='null'){
        setValue("sub_learner_etc", user.sub_learner_etc);
      }else{
        setValue("sub_learner_etc", '');
      }
      if (user.user_type==1||user.user_type==3||user.user_type==4||user.user_type==5) {
        setShowInternal(true)
        if (user.internal==1||user.internal==3||user.internal==4||user.internal==5) {
          if (user.email!=null&&user.email!=''&&user.email!='null'&&
          (user.email.indexOf('@student.chula.ac.th') > -1
          ||user.email.indexOf('@chulahospital.org') > -1
          ||user.email.indexOf('@chula.md') > -1
          ||user.email.indexOf('@chula.ac.th') > -1)) {
            setShowEmail(false)
          }else{
            setShowEmail(true)
          }
        }
      }
      if (user.user_type==4||user.user_type==5) {
        setInternal([
          { key: "", value: "", text: "ประเภทบุคลากร" },
          // { key: "se4", value: 4, text: "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย" },
          { key: "se5", value: 5, text: "จุฬาลงกรณ์มหาวิทยาลัย" },
          { key: "se6", value: 6, text: "มหาวิทยาลัยอื่นๆ" },
        ]);
      }else{
        setInternal([
          { key: "", value: "", text: "ประเภทบุคลากร" },
          { key: "se3", value: 3, text: "บุคลากรคณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย" },
          { key: "se1", value: 1, text: "บุคลากรโรงพยาบาลจุฬาลงกรณ์" },
          { key: "se2", value: 2, text: "บุคลากรโรงพยาบาลอื่นๆ" },
        ]);
      }
      if(user.subdistrict_name!='null'){
        setValue("subdistrict_name", user.subdistrict_name);
      }else{
        setValue("subdistrict_name", '');
      }
      if(user.district_name!='null'){
        setValue("district_name", user.district_name);
      }else{
        setValue("district_name", '');
      }
      if(user.province_name!='null'){
        setValue("province_name", user.province_name);
      }else{
        setValue("province_name", '');
      }
      if (user.avatar && user.avatar != "") {
        setValue("avatar", user.avatar);
      } else {
        setValue("avatar", "/assets/images/user-key.png");
      }
      setTimeout(() => {
        setUserLoad(true)
      }, "0");
      //   login_type
      // if (user.line_id != "") {
      //   setValue("login_type", "line");
      // } else if (user.facebook_id != "") {
      //   setValue("login_type", "facebook");
      // } else if (user.google_id != "") {
      //   setValue("login_type", "google");
      // }
    }
  }, [user]);

  useEffect(() => {
    if (reloadDistrict) {
      // console.log('register')
      // console.log(getValues('province'))
      setTimeout(() => {
        appContext.loadApi(
          process.env.NEXT_PUBLIC_API +
            "/api/area/district/" +
            getValues("province"),
          null,
          (data) => {
            setDistrictOptions(data.data);
            setSubdistrictOptions(null);
            setReloadDistrict(false);
          }
        );
      }, "0");
    }
  }, [reloadDistrict]);

  useEffect(() => {
    if (reloadSubDistrict) {
      // console.log('register')
      // console.log(getValues('province'))
      setTimeout(() => {
        appContext.loadApi(
          process.env.NEXT_PUBLIC_API +
            "/api/area/subdistrict/" +
            getValues("district"),
          null,
          (data) => {
            setSubdistrictOptions(data.data);
            setReloadSubDistrict(false);
          }
        );
      }, "0");
    }
  }, [reloadSubDistrict]);

  useEffect(() => {
    if (checkPop) {
      if (user) {
        setCheckPop(false);
        if((user.verify_internal==2&&user.internal_email!=null&&user.internal_email!=''&&user.internal_email!='null')
        &&(user.verify_email==2&&user.email!=null&&user.email!=''&&user.email!='null')){
          Swal.fire({
            text: "กรุณา Verify อีเมล์ และอีเมล์หน่วยงานค่ะ",
            icon: "info",
            confirmButtonText: "ปิด",
            confirmButtonColor: "#648d2f"
          });
        }else if(user.verify_internal==2&&user.internal_email!=null&&user.internal_email!=''&&user.internal_email!='null'){
          Swal.fire({
            text: "กรุณา Verify อีเมล์หน่วยงานค่ะ",
            icon: "info",
            confirmButtonText: "ปิด",
            confirmButtonColor: "#648d2f"
          });
        }else if(user.verify_email==2&&user.email!=null&&user.email!=''&&user.email!='null'){
          Swal.fire({
            text: "กรุณา Verify อีเมล์ค่ะ",
            icon: "info",
            confirmButtonText: "ปิด",
            confirmButtonColor: "#648d2f"
          });
        }
      }
    }
  }, [checkPop]);

  let maxW = 500;
  let maxH = 500;

  const previewFile = (event) => {
    event.preventDefault();
    var img = document.createElement('img');
    img.onload = () => {
      var canvas = document.createElement("canvas");
      var ctx = canvas.getContext("2d");
      var iw = img.width;
      var ih = img.height;
      var scale = Math.min(maxW / iw, maxH / ih);
      var iwScaled = iw * scale;
      var ihScaled = ih * scale;
      canvas.width = iwScaled;
      canvas.height = ihScaled;
      ctx.drawImage(img, 0, 0, iwScaled, ihScaled);
      setValue("avatar", canvas.toDataURL());
      forceUpdate();
    };
    try{
      img.src = URL.createObjectURL(event.target.files[0]);
    }catch(e){
      // console.log('kpayment not active')
    }
  };

  const onSubmit = () => {
    let register_data = getValues();
    // console.log(register_data);
    let bol = true;
    // setErrorArray
    var errarr = [];
    if (!appContext.isEmail(register_data["email"])) {
      bol = false;
      errarr.push("email");
    }
    if (appContext.isNull(register_data["name"])) {
      bol = false;
      errarr.push("name");
    }
    if (appContext.isNull(register_data["lastname"])) {
      bol = false;
      errarr.push("lastname");
    }
    if (appContext.isNull(register_data["user_type"])) {
      bol = false;
      errarr.push("user_type");
    }
    if (!appContext.isNull(register_data["user_type"])&&register_data["user_type"]==1&&!appContext.isMed(register_data["medical_id"])) {
      bol = false;
      errarr.push("medical_id");
    }
    if (
      !appContext.isNull(register_data["user_type"]) &&
      register_data["user_type"] == 1 &&
      (!appContext.isThaiOrSpace(register_data["name"])||!appContext.isThaiOrSpace(register_data["lastname"]))
    ) {
      if (
        !appContext.isNull(register_data["user_type"]) &&
        register_data["user_type"] == 1 &&
        !appContext.isThaiOrSpace(register_data["name"])
      ) {
        bol = false;
        errarr.push("name");
      }
      if (
        !appContext.isNull(register_data["user_type"]) &&
        register_data["user_type"] == 1 &&
        !appContext.isThaiOrSpace(register_data["lastname"])
      ) {
        bol = false;
        errarr.push("lastname");
      }
      errarr.push("thainame");
    }
    if (!appContext.isNull(register_data["user_type"])&&register_data["user_type"]==3&&
    !appContext.isNull(register_data["sub_learner"])&&register_data["sub_learner"]==2&&!appContext.isNurse(register_data["n_id"])) {
      bol = false;
      errarr.push("n_id");
    }
    if (!appContext.isNull(register_data["user_type"])&&register_data["user_type"]==3&&appContext.isNull(register_data["sub_learner"])) {
      bol = false;
      errarr.push("sub_learner");
    }
    if (!appContext.isNull(register_data["user_type"])&&register_data["user_type"]==3&&!appContext.isNull(register_data["sub_learner"])&&register_data["sub_learner"]==1&&appContext.isNull(register_data["sub_learner_etc"])) {
      bol = false;
      errarr.push("sub_learner_etc");
    }
    if (!appContext.isNull(register_data["user_type"])&&(register_data["user_type"]==1||register_data["user_type"]==3||register_data["user_type"]==4||register_data["user_type"]==5)&&appContext.isNull(register_data["internal"])) {
      bol = false;
      errarr.push("internal");
    }
    if (!appContext.isNull(register_data["internal"])&&(register_data["internal"]==1||register_data["internal"]==3||register_data["internal"]==4||register_data["internal"]==5)&&appContext.isNull(register_data["internal_email"])) {
      if (register_data["email"]!=null&&register_data["email"]!=''&&register_data["email"]!='null'&&
      (register_data["email"].indexOf('@student.chula.ac.th') > -1
      ||register_data["email"].indexOf('@chulahospital.org') > -1
      ||register_data["email"].indexOf('@chula.md') > -1
      ||register_data["email"].indexOf('@chula.ac.th') > -1)) {
      }else{
        bol = false;
        errarr.push("internal_email");
      }
    }
    if (appContext.isNull(register_data["address"])) {
      bol = false;
      errarr.push("address");
    }
    if (appContext.isNull(register_data["province"])) {
      bol = false;
      errarr.push("province");
    }
    if (appContext.isNull(register_data["district"])) {
      bol = false;
      errarr.push("district");
    }
    if (appContext.isNull(register_data["subdistrict"])) {
      bol = false;
      errarr.push("subdistrict");
    }
    if (!appContext.isPostal(register_data["postcode"])) {
      bol = false;
      errarr.push("postcode");
    }
    // if (!register_data["consent"]) {
    //   bol = false;
    //   errarr.push("consent");
    // }

    if (!user) {
      if (!appContext.isPassword(register_data["password"])) {
        bol = false;
        errarr.push("password");
      }
    }

    // console.log(errarr);

    setErrorArray(errarr);
    if (bol) {
      if (_.startsWith(register_data["avatar"], "data:")) {
        appContext.loadApi(
          process.env.NEXT_PUBLIC_API + "/api/image/save",
          { image: register_data["avatar"] },
          (data) => {
            // console.log(data);
            if (data["status"] == "true") {
              setValue("avatar", data["path"]);
              onSubmitRegister();
            }
          }
        );
      } else {
        onSubmitRegister();
      }
    } else {
      if (errarr.includes('thainame')) {
        Swal.fire({
          html: "เพื่อประโยชน์ในการได้รับคะแนน CME ของท่าน<br>กรุณาตรวจสอบว่า ชื่อ-นามสกุลของท่าน<br>ตรงกับใบประกอบวิชาชีพ และเป็นภาษาไทย",
          icon: "error",
          confirmButtonText: "ปิด",
          confirmButtonColor: "#648d2f",
        });
      }else{
        Swal.fire({
          text: "กรุณาตรวจสอบข้อมูล",
          icon: "error",
          confirmButtonText: "ปิด",
          confirmButtonColor: "#648d2f"
        });
      }
    }
  };

  const onSubmitRegister = () => {
    let register_data = getValues();
    appContext.loadApiToken(
      process.env.NEXT_PUBLIC_API + "/api/user/profile/edit",
      {...register_data,utoken:utoken},
      (data) => {
        // console.log(data);
        if (data["status"] == "success") {
          if (data["change_medical"]) {
            Swal.fire({
              html: "เลขใบประกอบวิชาชีพของท่านจะถูกตรวจสอบในภายหลัง",
              icon: "success",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
            });
          }else if (data["verify_fail"] == "true") {
            Swal.fire({
              html: "ขณะนี้ทาง ศนพ กำลังนำเข้าข้อมูลของท่าน<br>เลขใบประกอบวิชาชีพของท่านจะถูกตรวจสอบในภายหลัง",
              icon: "success",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
            });
          }else if (data["api_fail"] == "true") {
            Swal.fire({
              html: "ระบบจะตรวจสอบใบประกอบวิชาชีพในภายหลัง",
              icon: "success",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
            });
          }else{
            window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
          }
        } else {
          if (data["status"] == "duplicate_email") {
            Swal.fire({
              text: "อีเมลมีผู้ใช้งานแล้ว",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          } else if (data["status"] == "duplicate_email_internal") {
            Swal.fire({
              text: "อีเมลหน่วยงานมีผู้ใช้งานแล้ว",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          } else if (data["status"] == "duplicate_mobile") {
            Swal.fire({
              text: "เบอร์โทรศัพท์มีผู้ใช้งานแล้ว",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          } else if (data["status"] == "duplicate_medical_id") {
            Swal.fire({
              text: "เลขใบประกอบวิชาชีพมีผู้ใช้งานแล้ว",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          } else if (data["status"] == "verify_medical_fail") {
            Swal.fire({
              text: "ไม่พบเลขใบประกอบวิชาชีพ",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f",
            });
          } else if (data["status"] == "medical_data_not_match") {
            Swal.fire({
              text: "ข้อมูลของคุณกับใบประกอบวิชาชีพไม่ตรงกัน",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f",
            });
          } else {
            Swal.fire({
              text: "กรุณาตรวจสอบข้อมูล",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          }
        }
      }
    );
  };

  const getProvince = () => {
    appContext.loadApi(
      process.env.NEXT_PUBLIC_API + "/api/area/province",
      null,
      (data) => {
        setProvinceOptions(data.data);
      }
    );
  };

  const checkMyError = (_val) => {
    if (errorArray.indexOf(_val) > -1) {
      return true;
    }
    return false;
  };
  const checkMedical = (_val) => {
    setValue("user_type", _val);
    if (_val==3) {
      setSubLearner(true)
    }else{
      setSubLearner(false)
    }
    if (_val==1) {
      setMedical(true)
    }else{
      setMedical(false)
    }
    if (_val==1||_val==3||_val==4||_val==5) {
      setShowInternal(true)
    }else{
      setShowInternal(false)
    }
    if (_val==4||_val==5) {
      setInternal([
        { key: "", value: "", text: "ประเภทบุคลากร" },
        // { key: "se4", value: 4, text: "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย" },
        { key: "se5", value: 5, text: "จุฬาลงกรณ์มหาวิทยาลัย" },
        { key: "se6", value: 6, text: "มหาวิทยาลัยอื่นๆ" },
      ]);
    }else{
      setInternal([
        { key: "", value: "", text: "ประเภทบุคลากร" },
        { key: "se3", value: 3, text: "บุคลากรคณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย" },
        { key: "se1", value: 1, text: "บุคลากรโรงพยาบาลจุฬาลงกรณ์" },
        { key: "se2", value: 2, text: "บุคลากรโรงพยาบาลอื่นๆ" },
      ]);
    }
    // setValue("internal", "");
    setValue("sub_learner", "");
    setValue("sub_learner_etc", "");
    setNurse(false);
    setSubLearnerEtc(false);
  };
  const checkSubLearner = (_val) => {
    setValue("sub_learner", _val)
    if (_val == 2) {
      setNurse(true);
    } else {
      setNurse(false);
    }
    if (_val==1) {
      setSubLearnerEtc(true)
    }else{
      setSubLearnerEtc(false)
    }
    setValue("sub_learner_etc", "");
  };

  const checkInternal = (_val) => {
    setValue("internal", _val)
    if (_val==1||_val==3||_val==4||_val==5) {
      if (getValues("email")!=null&&getValues("email")!=''&&getValues("email")!='null'&&
      (getValues("email").indexOf('@student.chula.ac.th') > -1
      ||getValues("email").indexOf('@chulahospital.org') > -1
      ||getValues("email").indexOf('@chula.md') > -1
      ||getValues("email").indexOf('@chula.ac.th') > -1)) {
        setShowEmail(false)
      }else{
        setShowEmail(true)
      }
    }else{
      setShowEmail(false)
    }
  };

  const verifyEmail = () => {
    let register_data = getValues();
    // console.log(register_data);
    let bol = true;
    // setErrorArray
    var errarr = [];
    if (!appContext.isEmail(register_data["email"])) {
      bol = false;
      errarr.push("email");
    }

    // console.log(errarr);

    setErrorArray(errarr);
    if (bol) {
      appContext.loadApiToken(
        process.env.NEXT_PUBLIC_API + "/api/user/verify_email",
        {...register_data,utoken:utoken},
        (data) => {
          // console.log(data);
          if (data["status"] == "success") {
            Swal.fire({
              html: "กรุณายืนยันตัวตนที่<br>Email : "+register_data["email"]+'<br>(Email อาจจะอยู่ใน Junkmail)<br>พบปัญหาการยืนยันตัวตน ติดต่อ <a href="https://line.me/R/ti/p/@562tfgma" target="_blank">แจ้งปัญหาการใช้งาน</a>',
              icon: "success",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
            });
          } else {
            if (data["status"] == "duplicate_email") {
              Swal.fire({
                text: "อีเมลมีผู้ใช้งานแล้ว",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: "กรุณาตรวจสอบข้อมูล",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
            }
          }
        }
      );
    } else {
      Swal.fire({
        text: "กรุณากรอกอีเมล์",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f"
      });
    }
  };

  const verifyInternal = () => {
    let register_data = getValues();
    // console.log(register_data);
    let bol = true;
    // setErrorArray
    var errarr = [];
    if (!appContext.isEmail(register_data["internal_email"])) {
      bol = false;
      errarr.push("internal_email");
    }

    // console.log(errarr);

    setErrorArray(errarr);
    if (bol) {
      appContext.loadApiToken(
        process.env.NEXT_PUBLIC_API + "/api/user/verify_internal",
        {...register_data,utoken:utoken},
        (data) => {
          // console.log(data);
          if (data["status"] == "success") {
            Swal.fire({
              html: "กรุณายืนยันตัวตนที่<br>Email : "+register_data["internal_email"]+'<br>(Email อาจจะอยู่ใน Junkmail)<br>พบปัญหาการยืนยันตัวตน ติดต่อ <a href="https://line.me/R/ti/p/@562tfgma" target="_blank">แจ้งปัญหาการใช้งาน</a>',
              icon: "success",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
            });
          } else {
            if (data["status"] == "duplicate_email") {
              Swal.fire({
                text: "อีเมลมีผู้ใช้งานแล้ว",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: "กรุณาตรวจสอบข้อมูล",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
            }
          }
        }
      );
    } else {
      Swal.fire({
        text: "กรุณากรอกอีเมล์",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f"
      });
    }
  };
  return (
    <>
      <style>{`
        #onetrust-consent-sdk{
          display:none!important;
        }
      `}</style>
      <div className="main-all">
        <div className="main-body">
          <div className="register-page">
            <div className="register-block-form">
              <div className="block-form-inner">
                {/* ===== */}
                <div className="row">
                  <div className="col-12">
                    <div className="register-block-form-title">
                      <h3>PROFILE</h3>
                      <p>แก้ไขข้อมูลส่วนตัว</p>
                      {user.email==null||user.email==''||user.email=='null' ? (
                        <h2>กรุณากรอกข้อมูลส่วนตัวให้ครบถ้วน</h2>
                      ):null}
                    </div>
                  </div>
                </div>
                {/* ===== */}
                <div className="row-fm-register row">
                  <div className="col-fm-register col-12">
                    <div className="item-fm">
                      <p className="fm-title">ข้อมูล</p>
                    </div>
                  </div>
                  <div className="block-profile-register">
                    <div className="my-info">
                      <div className="info-avatar">
                        <div className="inner">
                          <button
                            className="invisible"
                            onClick={() => {
                              inputFiledata.current.click();
                            }}
                          >
                            <div className="profileAvatarImg">
                              <div  className="imgResponsiveCustom">
                                {getValues("avatar") != null && getValues("avatar") != '' && getValues("avatar") !='null' ? (
                                  <Image
                                    className=" cursor-pointer"
                                    src={getValues("avatar")}
                                    layout='fill'
                                    objectFit='contain'
                                    sizes="100%"
                                    alt=""
                                  />
                                ):null}
                              </div>
                            </div>
                          </button>
                          <button
                            className="btn-edit-img"
                            onClick={() => {
                              inputFiledata.current.click();
                            }}
                          >
                            <i className="icon-ic-image"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="col-fm-register col-12 col-md-6">
                    <div className="item-fm">
                      <Input
                        type="text"
                        className="fm-control"
                        placeholder={medical?"ชื่อ (ภาษาไทย)":"ชื่อ"}
                      >
                        <input
                          {...register("name")}
                          maxLength={100}
                          data-type="textaddress"
                          onInput={appContext.diFormPattern}
                          className={checkMyError("name") ? "error" : ""}
                        />
                      </Input>
                    </div>
                  </div>
                  <div className="col-fm-register col-12 col-md-6">
                    <div className="item-fm">
                      <Input
                        type="text"
                        className="fm-control"
                        placeholder={medical?"นามสกุล (ภาษาไทย)":"นามสกุล"}
                      >
                        <input
                          {...register("lastname")}
                          maxLength={100}
                          data-type="textaddress"
                          onInput={appContext.diFormPattern}
                          className={checkMyError("lastname") ? "error" : ""}
                        />
                      </Input>
                    </div>
                  </div>
                  <div className="col-fm-register col-12 col-md-6">
                    <div className="item-fm">
                      <Input
                        type="tel"
                        className="fm-control"
                        placeholder="เบอร์โทรศัพท์"
                      >
                        <input
                          {...register("mobile")}
                          maxLength={10}
                          data-type="number"
                          onInput={appContext.diFormPattern}
                          className={checkMyError("mobile") ? "error" : ""}
                        />
                      </Input>
                    </div>
                  </div>
                  {userLoad?(
                    <div className="col-fm-register col-12 col-md-6">
                      <div className="item-fm">
                        <Select
                          placeholder="ประเภทสมาชิก"
                          options={memberOptions}
                          defaultValue={user.user_type}
                          onChange={(event, data) => {
                            checkMedical(data.value)
                          }}
                          className={
                            "fm-control " +
                            (checkMyError("user_type") ? "error" : "")
                          }
                        />
                      </div>
                    </div>
                  ):null}
                  {subLearner?(
                    <div className="col-fm-register col-12 col-md-6">
                      <div className="item-fm">
                        <Select
                          placeholder="ประเภทย่อย"
                          options={subLearnerOptions}
                          defaultValue={user.sub_learner}
                          onChange={(event, data) => {
                            checkSubLearner(data.value)
                          }}
                          className={
                            "fm-control " +
                            (checkMyError("sub_learner") ? "error" : "")
                          }
                        />
                      </div>
                    </div>
                  ):null}
                  {subLearnerEtc?(
                    <div className="col-fm-register col-12 col-md-6">
                      <div className="item-fm">
                        <Input
                          type="text"
                          className="fm-control"
                          placeholder="ระบุประเภทย่อย"
                        >
                          <input
                            {...register("sub_learner_etc")}
                            maxLength={100}
                            data-type="textaddress"
                            onInput={appContext.diFormPattern}
                            className={checkMyError("sub_learner_etc") ? "error" : ""}
                          />
                        </Input>
                      </div>
                    </div>
                  ):null}

                  {medical ? (
                    <div className="col-fm-register col-12 col-md-6">
                      <div className="item-fm">
                        {user.medical_id !=null && user.medical_id !='' && user.medical_id !='null' ? (
                          <Input
                            type="text"
                            className="fm-control"
                            placeholder="เลขใบประกอบวิชาชีพ (กรอกเฉพาะตัวเลข 5 หลัก)"
                            disabled
                          >
                            <input
                              {...register("medical_id")}
                              maxLength={5}
                              data-type="number"
                              onInput={appContext.diFormPattern}
                              className={checkMyError("medical_id") ? "error" : ""}
                            />
                          </Input>
                        ):(
                          <Input
                            type="text"
                            className="fm-control"
                            placeholder="เลขใบประกอบวิชาชีพ (กรอกเฉพาะตัวเลข 5 หลัก)"
                          >
                            <input
                              {...register("medical_id")}
                              maxLength={5}
                              data-type="number"
                              onInput={appContext.diFormPattern}
                              className={checkMyError("medical_id") ? "error" : ""}
                            />
                          </Input>
                        ) }
                        
                      </div>
                    </div>
                  ):null}
                  {nurse ? (
                    <div className="col-fm-register col-12 col-md-6">
                      <div className="item-fm">
                        {user.n_id !=null && user.n_id !='' && user.n_id !='null' ? (
                          <Input
                            type="text"
                            className="fm-control"
                            placeholder="เลขใบประกอบวิชาชีพ (กรอกเฉพาะตัวเลข 10 หลัก)"
                            disabled
                          >
                            <input
                              {...register("n_id")}
                              maxLength={10}
                              data-type="number"
                              onInput={appContext.diFormPattern}
                              className={checkMyError("n_id") ? "error" : ""}
                            />
                          </Input>
                        ):(
                          <Input
                            type="text"
                            className="fm-control"
                            placeholder="เลขใบประกอบวิชาชีพ (กรอกเฉพาะตัวเลข 10 หลัก)"
                          >
                            <input
                              {...register("n_id")}
                              maxLength={10}
                              data-type="number"
                              onInput={appContext.diFormPattern}
                              className={checkMyError("n_id") ? "error" : ""}
                            />
                          </Input>
                        ) }
                        
                      </div>
                    </div>
                  ):null}
                  <></>
                  {showInternal ? (
                    <>
                      <div className="col-fm-register col-12 col-md-6">
                        <div className="item-fm">
                          <Select
                            placeholder="ประเภทบุคลากร"
                            options={internal}
                            defaultValue={user.internal}
                            onChange={(event, data) => {
                              checkInternal(data.value)
                            }}
                            className={
                              "fm-control " +
                              (checkMyError("internal") ? "error" : "")
                            }
                          />
                        </div>
                      </div>
                      {showEmail ? (
                        <div className="col-fm-register col-12 col-md-6">
                          {user.verify_internal == 1 ? (
                            <div className="item-fm fm-flex">
                              <Input
                                type="email"
                                className="fm-control"
                                placeholder="อีเมล์หน่วยงาน"
                                disabled
                              >
                                <input
                                  {...register("internal_email")}
                                  maxLength={100}
                                  data-type="email"
                                  onInput={appContext.diFormPattern}
                                  className={checkMyError("internal_email") ? "error" : ""}
                                />
                              </Input>
                              <Button className="accept-button active">
                                <span className="name"><i className="icon-ic-tick-thanks"></i></span>
                              </Button>
                            </div>
                          ):(
                            <div className="item-fm fm-flex">
                              <Input
                                type="email"
                                className="fm-control"
                                placeholder="อีเมล์หน่วยงาน"
                              >
                                <input
                                  {...register("internal_email")}
                                  maxLength={100}
                                  data-type="email"
                                  onInput={appContext.diFormPattern}
                                  className={checkMyError("internal_email") ? "error" : ""}
                                />
                              </Input>
                              <Button className="accept-button" onClick={() => verifyInternal()}>
                                <span className="name">Verify</span>
                              </Button>
                            </div>
                          )}
                          
                        </div>
                      ):null}
                    </>
                  ):null}

                  <div className="col-fm-register col-12 col-md-6">
                    {user.verify_email == 1 ? (
                      <div className="item-fm fm-flex">
                        <Input
                          type="email"
                          className="fm-control"
                          placeholder="อีเมล์"
                          disabled
                        >
                          <input
                            {...register("email")}
                            maxLength={100}
                            data-type="email"
                            onInput={appContext.diFormPattern}
                            className={checkMyError("email") ? "error" : ""}
                          />
                        </Input>
                        <Button className="accept-button active">
                          <span className="name"><i className="icon-ic-tick-thanks"></i></span>
                        </Button>
                      </div>
                    ):(
                      <div className="item-fm fm-flex">
                        <Input
                          type="email"
                          className="fm-control"
                          placeholder="อีเมล์"
                        >
                          <input
                            {...register("email")}
                            maxLength={100}
                            data-type="email"
                            onInput={appContext.diFormPattern}
                            className={checkMyError("email") ? "error" : ""}
                          />
                        </Input>
                        <Button className="accept-button" onClick={() => verifyEmail()}>
                          <span className="name">Verify</span>
                        </Button>
                      </div>
                    )}
                  </div>

                  <div className="col-fm-register col-12">
                    <div className="item-fm">
                      <p className="fm-title">ที่อยู่ในการออกใบเสร็จ</p>
                    </div>
                  </div>
                  <div className="col-fm-register col-12">
                    <div className="item-fm">
                      <TextArea
                        placeholder="ที่อยู่"
                        className={
                          "fm-control " + (checkMyError("address") ? "error" : "")
                        }
                        defaultValue={user.address}
                        data-type="textaddress"
                        onInput={appContext.diFormPattern}
                        onChange={(event) =>
                          setValue("address", event.target.value)
                        }
                      />
                    </div>
                  </div>
                  <div className="col-fm-register col-12 col-md-6">
                    <div className="item-fm">
                      <Select
                        className={
                          "fm-control " +
                          (checkMyError("province") ? "error" : "")
                        }
                        placeholder="กรุณาเลือกจังหวัด"
                        options={provinceOptions}
                        defaultValue={user.province}
                        onChange={(event, data) => {
                          //   console.log(event)
                          setValue("province", data.value);
                          setValue("province_name", event.target.innerText);
                          setReloadDistrict(true);
                        }}
                      />
                    </div>
                  </div>
                  <div className="col-fm-register col-12 col-md-6">
                    <div className="item-fm">
                      <Select
                        className={
                          "fm-control " +
                          (checkMyError("district") ? "error" : "")
                        }
                        placeholder="กรุณาเลือกเขต/อำเภอ"
                        options={districtOptions}
                        defaultValue={user.district}
                        onChange={(event, data) => {
                          setValue("district", data.value);
                          setValue("district_name", event.target.innerText);
                          setReloadSubDistrict(true);
                        }}
                      />
                    </div>
                  </div>
                  <div className="col-fm-register col-12 col-md-6">
                    <div className="item-fm">
                      <Select
                        className={
                          "fm-control " +
                          (checkMyError("subdistrict") ? "error" : "")
                        }
                        placeholder="กรุณาเลือกแขวง/ตำบล"
                        options={subdistrictOptions}
                        defaultValue={user.subdistrict}
                        onChange={(event, data) => {
                          setValue("subdistrict", data.value);
                          setValue("subdistrict_name", event.target.innerText);
                        }}
                      />
                    </div>
                  </div>
                  <div className="col-fm-register col-12 col-md-6">
                    <div className="item-fm">
                      <Input
                        type="tel"
                        className="fm-control"
                        placeholder="รหัสไปรษณีย์"
                      >
                        <input
                          {...register("postcode")}
                          maxLength={5}
                          data-type="number"
                          onInput={appContext.diFormPattern}
                          defaultValue={user.postcode}
                          className={checkMyError("postcode") ? "error" : ""}
                        />
                      </Input>
                    </div>
                  </div>
                  <div className="col-12 col-md-6 offset-md-3">
                    <button
                      className="btn-default btn-register"
                      onClick={onSubmit}
                    >
                      <span>แก้ไขข้อมูลส่วนตัว</span>
                    </button>
                  </div>
                </div>
                {/* ===== */}
              </div>
            </div>
          </div>
        </div>
        <form id="FormFileData" className="hide">
          <input
            type="file"
            accept="image/jpeg,image/jpg,image/png,image/gif,image/xbm,image/xpm,image/wbmp,image/webp,image/bmp"
            onChange={(e) => {
              previewFile(e);
            }}
            ref={inputFiledata}
            className="hide"
          />
        </form>
      </div>
    </>
  );
}

import React, { useState, useEffect, useRef, useContext } from "react";
import Router, { useRouter } from "next/router";
import FormData from "form-data";
import Script from "next/script";

import "semantic-ui-css/semantic.min.css";
import "/public/assets/css/grid/bootstrap-grid.css";
import "/public/assets/css/fonts.css";
import "/public/assets/css/styles.css";
import Swal from "sweetalert2";
import Image from 'next/image';
import {
  Button,
  Modal,
} from "semantic-ui-react";
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { SessionProvider } from "next-auth/react";
import AppContext from "/libs/contexts/AppContext";
function MyApp({ Component, pageProps: { session, ...pageProps } }) {
  const router = useRouter();
  const { locale, pathname, asPath, query } = router;
  const [token, setToken] = useState(null);

  async function getToken(payload,state,type) {
    try {
      const response = await fetch('/api/jwt/'+type, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
  
      const data = await response.json();
      submitForm(data.token,state);
    } catch (error) {
      console.error('Error fetching token:', error);
    }
  }
  const formLtiLaunch = React.useRef(null);
  async function submitForm(_token,_state) {
    if(!process.env.NEXT_PUBLIC_APPDOMAIN||(process.env.NEXT_PUBLIC_APPDOMAIN&&process.env.NEXT_PUBLIC_APPDOMAIN!='https://www.medumore.org')){
      var api_path = 'https://api.staging.lll.degree.plus/user-api/lti';
    }else{
      var api_path = 'https://api.lifelong.chula.ac.th/user-api/lti';
    }
    const formAppend = formLtiLaunch.current;
    formAppend.querySelector('input[name="id_token"]').value = _token;
    formAppend.querySelector('input[name="state"]').value = _state;
    formLtiLaunch.current.action = api_path;
    formLtiLaunch.current.submit();

    // const formData = new URLSearchParams();
    // formData.append('id_token', _token);
    // formData.append('state', _state);

    // var my_cookie = '';
    // const response = await fetch(api_path, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/x-www-form-urlencoded',
    //     'cookie': my_cookie,
    //   },
    //   body: formData.toString(),
    // });

    // if (response.ok) {
    //   const responseData = await response.json();
    //   console.log('Form submitted successfully:', responseData);
    // } else {
    //   console.error('Failed to submit form:', response.statusText);
    // }
  }
  const {
    response_type,
    response_mode,
    id_token_signed_response_alg,
    scope,
    prompt,
    redirect_uri,
    login_hint,
    client_id,
    nonce,
    state
  } = query;
  useEffect(() => {
    if (
      response_type &&
      response_mode &&
      id_token_signed_response_alg &&
      scope &&
      prompt &&
      redirect_uri &&
      login_hint &&
      client_id &&
      nonce &&
      state &&
      token
    ) {
      console.log('All parameters are available');
      if(token&&redirect_uri=='https://lifelong.chula.ac.th'){
        const formData = new URLSearchParams();
        sendApi(
          process.env.NEXT_PUBLIC_API + "/api/user/lti",
          formData,
          (obj) => {
            if(obj['status']=='success'){
              if(login_hint==obj['data']){
                if(!process.env.NEXT_PUBLIC_APPDOMAIN||(process.env.NEXT_PUBLIC_APPDOMAIN&&process.env.NEXT_PUBLIC_APPDOMAIN!='https://www.medumore.org')){
                  var web_url = 'https://uat.medumore.org';
                }else{
                  var web_url = 'https://www.medumore.org';
                }
                const payload = {
                  "nonce": nonce,
                  "iss": web_url,
                  "aud": client_id,
                  "https://purl.imsglobal.org/spec/lti/claim/deployment_id": "medumore",
                  "https://purl.imsglobal.org/spec/lti/claim/target_link_uri": 'https://lifelong.chula.ac.th',
                  "sub": login_hint,
                  "https://purl.imsglobal.org/spec/lti/claim/roles": [
                    "http://purl.imsglobal.org/vocab/lis/v2/membership#Learner"
                  ],
                  "https://purl.imsglobal.org/spec/lti/claim/message_type": "LtiResourceLinkRequest",
                  "https://purl.imsglobal.org/spec/lti/claim/resource_link": {
                    "id": login_hint
                  },
                  "https://purl.imsglobal.org/spec/lti/claim/tool_platform": {
                    "guid": "mdcu",
                    "name": "Med U More",
                    "description": "MDCU Med U More",
                    "product_family_code": "mdcu"
                  },
                  "given_name": obj['name'],
                  "family_name": obj['lastname'],
                  "name": obj['fullname'],
                  "email": obj['email'],
                  "https://purl.imsglobal.org/spec/lti/claim/version": "1.3.0",
                  "iat": Math.floor(Date.now() / 1000)
                };
                if(!process.env.NEXT_PUBLIC_APPDOMAIN||(process.env.NEXT_PUBLIC_APPDOMAIN&&process.env.NEXT_PUBLIC_APPDOMAIN!='https://www.medumore.org')){
                  getToken(payload,state,'uat');
                }else{
                  getToken(payload,state,'prod');
                }
              }
            }
          }
        );
      }
    }
  }, [
    response_type,
    response_mode,
    id_token_signed_response_alg,
    scope,
    prompt,
    redirect_uri,
    login_hint,
    client_id,
    nonce,
    state,
    token
  ]);
  const [lang, setLang] = useState(locale);
  const [loading, setLoading] = useState(false);
  const [reloadCart, setReloadCart] = useState(false);
  const [reloadCourse, setReloadCourse] = useState(false);
  const [user, setUser] = useState(null);
  const [page, setPage] = useState(null);
  const [open, setOpen] = useState(false);
  const [firstPop, setFirstPop] = useState(true);
  const [popCreditBank, setPopCreditBank] = useState(false);

  const [loadPercent, setLoadPercent] = useState(true);
  const [percent, setPercent] = useState(0);

  useEffect(() => {
    if (loadPercent) {
      let currentPercent = 0;
      const interval = setInterval(() => {
        currentPercent += 1;
        setPercent(currentPercent);
        if (currentPercent >= 95) {
          clearInterval(interval);
        }
      }, 50);
      return () => clearInterval(interval);
    }else{
      let currentPercent = 95;
      const interval = setInterval(() => {
        currentPercent += 1;
        setPercent(currentPercent);
        if (currentPercent >= 100) {
          clearInterval(interval);
        }
      }, 10);
      return () => clearInterval(interval);
    }
  }, [loadPercent]);

  const [popOffset, setPopOffset] = useState(0);
  const scrollPop = useRef(null);

  useEffect(() => {
    const onScrollPop = () => setPopOffset(window.pageYOffset);
    window.removeEventListener("scroll", onScrollPop);
    window.addEventListener("scroll", onScrollPop, { passive: true });
    if (firstPop) {
      if (window.pageYOffset > screen.height) {
        if (scrollPop.current) {
          scrollPop.current.classList.add("active");
          setFirstPop(false);
        }
      }
    }
  }, [popOffset]);

  useEffect(() => {
    const cookies = parseCookies();
    // console.log(cookies)
    // console.log("xxxxxxxxxxxx");
    if (cookies[process.env.NEXT_PUBLIC_APP + "_token"]) {
      const fetchData = async () => {
        const formData = new URLSearchParams();
        formData.append(
          "username",
          cookies[process.env.NEXT_PUBLIC_APP + "_token"]
        );
        formData.append(
          "utoken",
          cookies[process.env.NEXT_PUBLIC_APP + "_token"]
        );

        const response = await fetch(
          process.env.NEXT_PUBLIC_API + "/api/user/profile",
          {
            body: formData,
            method: "POST",
          }
        );

        const jsonData = await response.json();
        // console.log(jsonData);
        if (jsonData["status"] == "success") {
          setUser(jsonData["user"]);
          if(location.pathname!='/profile/edit'){
            if(isNull(jsonData.user.email)||isNull(jsonData.user.user_type)){
              location.href="/profile/edit";
            }
          }
        } else {
          setUser(null);
        }
        return jsonData;
      };
      const jsonData = fetchData().catch(console.error);
    }
  }, [token]);

  if (typeof window !== 'undefined' && typeof document !== 'undefined') {
    // Check if the page has changed
    const currentPage = window.location.href;
    const storedPage = localStorage.getItem('currentPage');

    if (currentPage !== storedPage) {
        // Page has changed, trigger the event
        document.dispatchEvent(new Event('pageChanged'));
        // Update the stored page
        localStorage.setItem('currentPage', currentPage);

        // Listen for the custom event on the new page
        document.addEventListener('pageChanged', function () {
          // Execute your function
          console.log(user);
          if(location.pathname!='/profile/edit'){
            if(!isNull(user)){
              if(isNull(user.email)||isNull(user.user_type)){
                location.href="/profile/edit";
              }
            }else{
              const cookies = parseCookies();
              // console.log(cookies)
              // console.log("xxxxxxxxxxxx");
              if (cookies[process.env.NEXT_PUBLIC_APP + "_token"]) {
                const fetchData = async () => {
                  const formData = new URLSearchParams();
                  formData.append(
                    "username",
                    cookies[process.env.NEXT_PUBLIC_APP + "_token"]
                  );
                  formData.append(
                    "utoken",
                    cookies[process.env.NEXT_PUBLIC_APP + "_token"]
                  );

                  const response = await fetch(
                    process.env.NEXT_PUBLIC_API + "/api/user/profile",
                    {
                      body: formData,
                      method: "POST",
                    }
                  );

                  const jsonData = await response.json();
                  // console.log(jsonData);
                  if (jsonData["status"] == "success") {
                    if(location.pathname!='/profile/edit'){
                      if(isNull(jsonData.user.email)||isNull(jsonData.user.user_type)){
                        location.href="/profile/edit";
                      }
                    }
                  }
                  return jsonData;
                };
                const jsonData = fetchData().catch(console.error);
              }
            }
          }
        });
    }
  }


  useEffect(() => {
    localStorage.setItem("page", page);
    // console.log(router.asPath);
    // console.log("user");
    // console.log(user);
    // console.log("token : " + token);
    if (
      router.asPath.indexOf("/auth/") == "-1" &&
      router.asPath.indexOf("/register") == "-1" &&
      router.asPath.indexOf("/privancy") == "-1" &&
      router.asPath.indexOf("/profile/edit") == "-1"
    ) {
      localStorage.setItem("backpath", router.asPath);
    }
    if (!isNull(jGet("utm_source"))) {
      setLocal("utm_source", jGet("utm_source"));
      setLocal("utm_time", new Date());
    } else {
      if (!isNull(getLocal("utm_source"))) {
        if (!isNull(getLocal("utm_time"))) {
          var diff_hours =
            Math.abs(new Date() - new Date(getLocal("utm_time"))) / 36e5;
          if (diff_hours >= 1) {
            removeLocal("utm_source");
          }
        } else {
          removeLocal("utm_source");
        }
      }
    }
    if (!isNull(getLocal("utm_source"))) {
      // console.log(getLocal("utm_source"));
      // console.log(Math.abs(new Date() - new Date(getLocal("utm_time"))) / 36e5);
    } else {
      // console.log("no utm");
    }
  }, [page, router]);

  // useEffect(() => {
  //   const handleTabClose = event => {
  //     event.preventDefault();

  //     // console.log('beforeunload event triggered');

  //     // return (event.returnValue = 'Are you sure you want to exit?');
  //     if (!isNull(getLocal("utm_source"))) {
  //       if (!isNull(getLocal("utm_time"))) {
  //         var diff_hours = Math.abs(new Date() - new Date(getLocal("utm_time"))) / 36e5;
  //         if(diff_hours>=1){
  //           removeLocal('utm_source');
  //         }
  //       }else{
  //         removeLocal('utm_source');
  //       }
  //     }
  //   };

  //   window.addEventListener('beforeunload', handleTabClose);

  //   return () => {
  //     window.removeEventListener('beforeunload', handleTabClose);
  //   };
  // }, []);

  const creditBankPopup = () => {
    setPopCreditBank(true);
  };
  const loadApi = (path, param = null, callback = null) => {
    const cookies = parseCookies();
    let formData = new FormData();
    if (cookies[process.env.NEXT_PUBLIC_APP + "_token"]) {
      setToken(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
      formData.append(
        "utoken",
        cookies[process.env.NEXT_PUBLIC_APP + "_token"]
      );
    }
    if (param) {
      // console.log(param)
      for (let n in param) {
        formData.append(n, param[n]);
      }
    }

    setLoading(true);
    fetch(path, {
      method: "POST",
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
    })
      .then((res) => res.json())
      .then((data) => {
        if (callback) {
          callback(data);
        }
        setLoading(false);
      });
  };
  const loadApiToken = (path, param = null, callback = null) => {
    const cookies = parseCookies();
    let formData = new FormData();
    if (param) {
      // console.log(param)
      for (let n in param) {
        formData.append(n, param[n]);
      }
    }

    setLoading(true);
    fetch(path, {
      method: "POST",
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
    })
      .then((res) => res.json())
      .then((data) => {
        if (callback) {
          callback(data);
        }
        setLoading(false);
      });
  };
  const loadApiNoLoader = (path, param = null, callback = null) => {
    const cookies = parseCookies();
    let formData = new FormData();
    if (cookies[process.env.NEXT_PUBLIC_APP + "_token"]) {
      setToken(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
      formData.append(
        "utoken",
        cookies[process.env.NEXT_PUBLIC_APP + "_token"]
      );
    }
    if (param) {
      // console.log(param)
      for (let n in param) {
        formData.append(n, param[n]);
      }
    }

    // setLoading(true);
    fetch(path, {
      method: "POST",
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
    })
      .then((res) => res.json())
      .then((data) => {
        if (callback) {
          callback(data);
        }
        // setLoading(false);
      });
  };
  const sendApi = (path, param, callback) => {
    const cookies = parseCookies();
    if (cookies[process.env.NEXT_PUBLIC_APP + "_token"]) {
      param.append("utoken", cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
    }
    fetch(path, {
      method: "POST",
      body: param,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
    })
      .then((res) => res.json())
      .then((data) => {
        if (callback) {
          callback(data);
        }
      });
  };
  const sendApiToken = (path, param, callback) => {
    const cookies = parseCookies();
    fetch(path, {
      method: "POST",
      body: param,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
    })
      .then((res) => res.json())
      .then((data) => {
        if (callback) {
          callback(data);
        }
      });
  };
  const shuffle = (array) => {
    var _arr = array.slice();
    let currentIndex = _arr.length,
      randomIndex;

    // While there remain elements to shuffle.
    while (currentIndex != 0) {
      // Pick a remaining element.
      randomIndex = Math.floor(Math.random() * currentIndex);
      currentIndex--;

      // And swap it with the current element.
      [_arr[currentIndex], _arr[randomIndex]] = [
        _arr[randomIndex],
        _arr[currentIndex],
      ];
    }

    return _arr;
  };
  const diFormPattern = (event) => {
    // event.target.value
    // console.log(event.target.value)
    // console.log(event)
    // console.log(event.target.getAttribute('data-type'))
    var keyCodeChar = event.target.value.slice(-1);
    var st_pattern = "";
    if (event.target.getAttribute("data-type") == "english")
      st_pattern = "[a-z.A-Z]";
    else if (event.target.getAttribute("data-type") == "thaionly")
      st_pattern =
        "[ภถุึคตจขชๆไำพะัีรนยบลฟหกดเ้่าสวงผปแอิืทมใฝฃูฎฑธํ๊ณฯญฐฅฤฆฏโฌ็๋ษศซฉฮฺ์ฒฬฦ ]";
    else if (event.target.getAttribute("data-type") == "thai")
      st_pattern =
        "[ภถุึคตจขชๆไำพะัีรนยบลฟหกดเ้่าสวงผปแอิืทมใฝฃูฎฑธํ๊ณฯญฐฅฤฆฏโฌ็๋ษศซฉฮฺ์ฒฬฦ A-Za-z]";
    else if (event.target.getAttribute("data-type") == "textaddress")
      st_pattern =
        "[ภถุึคตจขชๆไำพะัีรนยบลฟหกดเ้่าสวงผปแอิืทมใฝฃูฎฑธํ๊ณฯญฐฅฤฆฏโฌ็๋ษศซฉฮฺ์ฒฬฦ A-Za-z0-9+#.-/ ()]";
    else if (event.target.getAttribute("data-type") == "textaddressthai")
    st_pattern =
      "[ภถุึคตจขชๆไำพะัีรนยบลฟหกดเ้่าสวงผปแอิืทมใฝฃูฎฑธํ๊ณฯญฐฅฤฆฏโฌ็๋ษศซฉฮฺ์ฒฬฦ 0-9+#.-/ ()]";
    else if (event.target.getAttribute("data-type") == "number")
      st_pattern = "[0-9]";
    else if (event.target.getAttribute("data-type") == "numberslash")
      st_pattern = "[0-9/-]";
    else if (event.target.getAttribute("data-type") == "numberpoint")
      st_pattern = "[0-9.]";
    else if (event.target.getAttribute("data-type") == "email")
      st_pattern = "[a-z_.@0-9\\-\\^]";
    else if (event.target.getAttribute("data-type") == "password")
      st_pattern = "[A-Za-z0-9!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]+";
    else if (event.target.getAttribute("data-type") == "unchar")
      st_pattern = "[]";
    if (!keyCodeChar.match(new RegExp(st_pattern, "i"))) {
      //  event.preventDefault();
      var inputString = event.target.value;
      var shortenedString = inputString.substr(0, inputString.length - 1);
      event.target.value = shortenedString;
    }
  };

  const isMed = (_var) => {
    // var medExpression = /ว\d{10}/g;
    // return medExpression.test(_var);
    if (isNumeric(_var)) {
      return (
        _var.toString().length >= 4 && _var.toString().length <= 6 && isNumeric(_var)
      );
    }
    return false;
  };
  const isNurse = (_var) => {
    // var medExpression = /ว\d{10}/g;
    // return medExpression.test(_var);
    if (isNumeric(_var)) {
      return (
        _var.toString().length && isNumeric(_var) && _var.toString().length == 10
      );
    }
    return false;
  };

  const isNull = (_var) => {
    if (
      _var == null ||
      _var == "null" ||
      _var == "" ||
      _var == undefined ||
      _var == "undefined" ||
      _var == "NaN" ||
      _var == NaN
    ) {
      return true;
    } else {
      return false;
    }
  };
  const isThaiOrSpace = (inputString) => {
    const thaiCharRange = /^[\u0E00-\u0E7F ]+$/;
    return thaiCharRange.test(inputString);
  };
  const isEmail = (email) => {
    var emailExpression = /^[a-z0-9][\w.-]+@\w[\w.-]+\.[\w.-]*[a-z][a-z]$/i;
    return emailExpression.test(email);
    // if (
    //   email == null ||
    //   email == "null" ||
    //   email == "" ||
    //   email == undefined ||
    //   email == "undefined" ||
    //   email == "NaN" ||
    //   email == NaN
    // ) {
    //   return false;
    // } else {
    //   return true;
    // }
  };
  const isNumeric = (input) => {
    return !isNaN(parseFloat(input)) && isFinite(input);
  };
  const isMobile = (input) => {
    if (isNumeric(input)) {
      if (
        input.indexOf("09") > -1 ||
        input.indexOf("08") > -1 ||
        input.indexOf("07") > -1 ||
        input.indexOf("06") > -1 ||
        input.indexOf("05") > -1 ||
        input.indexOf("04") > -1 ||
        input.indexOf("03") > -1 ||
        input.indexOf("02") > -1
      ) {
        return (
          !isNaN(parseFloat(input)) && isFinite(input) && input.length >= 10
        );
      } else {
        return false;
      }
    }
    return false;
  };
  const isPassword = (input) => {
    return input.length >= 6;
  };
  const isUndefined = (st) => {
    return st == undefined || st == null || st == "" || st == "0" || st == 0;
  };
  const isPostal = (ps) => {
    if (isNumeric(ps)) {
      return ps.toString().length && isNumeric(ps);
    }
    return false;
  };
  const isIdenNo = (id) => {
    // if (!isNumeric(id)) return false;
    // if (id.substring(0, 1) == 0) return false;
    // if (id.length != 13) return false;
    // for (var i = 0, sum = 0; i < 12; i++)
    //   sum += parseFloat(id.charAt(i)) * (13 - i);
    // if ((11 - (sum % 11)) % 10 != parseFloat(id.charAt(12))) return false;
    // return true;
    if (isNumeric(id)) {
      return (
        id.toString().length && isNumeric(id) && id.toString().length == 13
      );
    }
  };
  const jGet = (name) => {
    return decodeURI(
      (RegExp(name + "=" + "(.+?)(&|$)").exec(location.search) || [, null])[1]
    );
  };

  const getLocal = (key) => {
    if (window.localStorage.getItem(key)) {
      return JSON.parse(window.localStorage.getItem(key));
    }
    return null;
  };

  const setLocal = (key, obj) => {
    window.localStorage.setItem(key, JSON.stringify(obj));
  };

  const removeLocal = (key) => {
    window.localStorage.removeItem(key);
  };

  const removeCart = (cart_id) => {
    let formData = new FormData();
    formData.append("utoken", token);
    formData.append("cart_id", cart_id);
    fetch(process.env.NEXT_PUBLIC_API + "/api/mdcu/removeCart", {
      method: "POST",
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
    })
      .then((res) => res.json())
      .then((data) => {
        if(window.location.pathname=='/checkout'){
          location.reload();
        }else{
          setReloadCart(true);
          setReloadCourse(true);
        }
        // console.log(data)
      });
  };

  const truncate = (input, limit) => {
    if (input && input.length > limit)
      return input.substring(0, limit) + " ...";
    else return input;
  };

  const convertToThaiDate = (sqlDateTime) => {
    const monthsInThai = [
      "มกราคม", "กุมภาพันธ์", "มีนาคม", "เมษายน", "พฤษภาคม", "มิถุนายน",
      "กรกฎาคม", "สิงหาคม", "กันยายน", "ตุลาคม", "พฤศจิกายน", "ธันวาคม"
    ];
  
    const date = new Date(sqlDateTime);
    const day = date.getDate();
    const month = monthsInThai[date.getMonth()];
    const year = date.getFullYear() + 543; // Convert to Thai year
  
    return `${day} ${month} ${year}`;
  };

  const formImmersive = React.useRef(null);
  const formLti = React.useRef(null);
  const submitImmersive = () => {
    if(token){
      const formData = new URLSearchParams();
      sendApi(
        process.env.NEXT_PUBLIC_API + "/api/user/immersive",
        formData,
        (obj) => {
          if(obj['status']=='success'){
            if(!process.env.NEXT_PUBLIC_APPDOMAIN||(process.env.NEXT_PUBLIC_APPDOMAIN&&process.env.NEXT_PUBLIC_APPDOMAIN!='https://www.medumore.org')){
              const apiPath = 'https://demo.mycourseville.com/?q=courseville/lti/launch/&distributor_id=7';
              const formAppend = formImmersive.current;
              formAppend.querySelector('input[name="data"]').value = obj['data'];
              formAppend.querySelector('input[name="signature"]').value = obj['signature'];
              formImmersive.current.action = apiPath;
              formImmersive.current.submit();
            }else{
              const apiPath = 'https://www.mycourseville.com/?q=courseville/lti/launch/&distributor_id=7';
              const formAppend = formImmersive.current;
              formAppend.querySelector('input[name="data"]').value = obj['data'];
              formAppend.querySelector('input[name="signature"]').value = obj['signature'];
              formImmersive.current.action = apiPath;
              formImmersive.current.submit();
            }
          }
        }
      );
    }else{
      Swal.fire({
        text: 'กรุณาเข้าสู่ระบบค่ะ',
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        setOpen(true);
      });
    }
  };
  const submitImmersiveApp = (_fix_token) => {
    if(_fix_token){
      const formData = new URLSearchParams();
      formData.append("utoken", _fix_token);
      formData.append("type", 'cvlearn');
      sendApiToken(
        process.env.NEXT_PUBLIC_API + "/api/user/immersive",
        formData,
        (obj) => {
          if(obj['status']=='success'){
            if(!process.env.NEXT_PUBLIC_APPDOMAIN||(process.env.NEXT_PUBLIC_APPDOMAIN&&process.env.NEXT_PUBLIC_APPDOMAIN!='https://www.medumore.org')){
              const apiPath = 'https://demo.mycourseville.com/?q=courseville/lti/launch/&distributor_id=7';
              const formAppend = formImmersive.current;
              formAppend.querySelector('input[name="data"]').value = obj['data'];
              formAppend.querySelector('input[name="signature"]').value = obj['signature'];
              formImmersive.current.action = apiPath;
              formImmersive.current.submit();
            }else{
              const apiPath = 'https://www.mycourseville.com/?q=courseville/lti/launch/&distributor_id=7';
              const formAppend = formImmersive.current;
              formAppend.querySelector('input[name="data"]').value = obj['data'];
              formAppend.querySelector('input[name="signature"]').value = obj['signature'];
              formImmersive.current.action = apiPath;
              formImmersive.current.submit();
            }
          }
        }
      );
    }
  };
  const submitImmersiveCourseApp = (_fix_token,_ref_id,_type) => {
    if(_fix_token&&_ref_id&&_type){
      const formData = new URLSearchParams();
      formData.append("utoken", _fix_token);
      formData.append("course_id", _ref_id);
      formData.append("type", _type);
      sendApiToken(
        process.env.NEXT_PUBLIC_API + "/api/user/immersive",
        formData,
        (obj) => {
          if(obj['status']=='success'){
            if(!process.env.NEXT_PUBLIC_APPDOMAIN||(process.env.NEXT_PUBLIC_APPDOMAIN&&process.env.NEXT_PUBLIC_APPDOMAIN!='https://www.medumore.org')){
              const apiPath = 'https://demo.mycourseville.com/?q=courseville/lti/launch/&distributor_id=7';
              const formAppend = formImmersive.current;
              formAppend.querySelector('input[name="data"]').value = obj['data'];
              formAppend.querySelector('input[name="signature"]').value = obj['signature'];
              formImmersive.current.action = apiPath;
              formImmersive.current.submit();
            }else{
              const apiPath = 'https://www.mycourseville.com/?q=courseville/lti/launch/&distributor_id=7';
              const formAppend = formImmersive.current;
              formAppend.querySelector('input[name="data"]').value = obj['data'];
              formAppend.querySelector('input[name="signature"]').value = obj['signature'];
              formImmersive.current.action = apiPath;
              formImmersive.current.submit();
            }
          }
        }
      );
    }
  };
  const submitImmersiveCourse = (_id,_type) => {
    if(token){
      const formData = new URLSearchParams();
      formData.append("course_id", _id);
      formData.append("type", _type);
      sendApi(
        process.env.NEXT_PUBLIC_API + "/api/user/immersive",
        formData,
        (obj) => {
          if(obj['status']=='success'){
            if(!process.env.NEXT_PUBLIC_APPDOMAIN||(process.env.NEXT_PUBLIC_APPDOMAIN&&process.env.NEXT_PUBLIC_APPDOMAIN!='https://www.medumore.org')){
              const apiPath = 'https://demo.mycourseville.com/?q=courseville/lti/launch/&distributor_id=7';
              const formAppend = formImmersive.current;
              formAppend.querySelector('input[name="data"]').value = obj['data'];
              formAppend.querySelector('input[name="signature"]').value = obj['signature'];
              formImmersive.current.action = apiPath;
              formImmersive.current.submit();
            }else{
              const apiPath = 'https://www.mycourseville.com/?q=courseville/lti/launch/&distributor_id=7';
              const formAppend = formImmersive.current;
              formAppend.querySelector('input[name="data"]').value = obj['data'];
              formAppend.querySelector('input[name="signature"]').value = obj['signature'];
              formImmersive.current.action = apiPath;
              formImmersive.current.submit();
            }
          }
        }
      );
    }else{
      Swal.fire({
        text: 'กรุณาเข้าสู่ระบบค่ะ',
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        setOpen(true);
      });
    }
  };
  const submitLti = () => {
    if(token){
      const formData = new URLSearchParams();
      formData.append("utoken", token);
      sendApi(
        process.env.NEXT_PUBLIC_API + "/api/user/lti",
        formData,
        (obj) => {
          if(obj['status']=='success'){
            if(obj['data']!=''){
              if(!process.env.NEXT_PUBLIC_APPDOMAIN||(process.env.NEXT_PUBLIC_APPDOMAIN&&process.env.NEXT_PUBLIC_APPDOMAIN!='https://www.medumore.org')){
                const apiPath = 'https://api.staging.lll.degree.plus/user-api/lti/login';
                const formAppend = formLti.current;
                formAppend.querySelector('input[name="iss"]').value = 'https://uat.medumore.org';
                formAppend.querySelector('input[name="login_hint"]').value = obj['data'];
                formAppend.querySelector('input[name="target_link_uri"]').value = 'https://lifelong.chula.ac.th';
                formAppend.querySelector('input[name="lti_deployment_id"]').value = 'medumore';
                formAppend.querySelector('input[name="client_id"]').value = '6PzJx2vV7Q74';
                formLti.current.action = apiPath;
                formLti.current.submit();
              }else{
                const apiPath = 'https://api.lifelong.chula.ac.th/user-api/lti/login';
                const formAppend = formLti.current;
                formAppend.querySelector('input[name="iss"]').value = 'https://www.medumore.org';
                formAppend.querySelector('input[name="login_hint"]').value = obj['data'];
                formAppend.querySelector('input[name="target_link_uri"]').value = 'https://lifelong.chula.ac.th';
                formAppend.querySelector('input[name="lti_deployment_id"]').value = 'medumore';
                formAppend.querySelector('input[name="client_id"]').value = '6KcotjUJ63qT';
                formLti.current.action = apiPath;
                formLti.current.submit();
              }
            }else{
              Swal.fire({
                text: 'กรุณากรอก E-Mail ค่ะ',
                icon: "info",
                confirmButtonText: "ตกลง",
                confirmButtonColor: "#648d2f"
              }).then((result) => {
                location.href="/profile/edit";
              });
            }
          }
        }
      );
    }else{
      Swal.fire({
        text: 'กรุณาเข้าสู่ระบบค่ะ',
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        setOpen(true);
      });
    }
  };

  const shareVar = {
    setLoadPercent,
    token,
    setToken,
    user,
    page,
    setLoading,
    setPage,
    diFormPattern,
    loadApi,
    loadApiToken,
    loadApiNoLoader,
    sendApi,
    sendApiToken,
    shuffle,
    reloadCart,
    setReloadCart,
    removeCart,
    reloadCourse,
    setReloadCourse,
    isNull,
    isThaiOrSpace,
    isMed,
    isNurse,
    isEmail,
    isNumeric,
    isMobile,
    isPassword,
    isUndefined,
    isPostal,
    isIdenNo,
    jGet,
    truncate,
    open,
    setOpen,
    getLocal,
    setLocal,
    removeLocal,
    lang,
    pathname,
    convertToThaiDate,
    submitImmersive,
    submitImmersiveApp,
    submitImmersiveCourseApp,
    submitImmersiveCourse,
    submitLti,
    creditBankPopup
  };

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=G-MQK7R31GW`}
        id={"ga_header"}
      />
      <Script
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
        
          gtag('config', 'G-MQK7R31GWK');
          `,
        }}
        id={"ga_config"}
      />

      <Script
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-PRZZ6FL');`,
        }}
        id={"gtm_config"}
      />

      <Script
        strategy="afterInteractive"
        src={`https://cdn-apac.onetrust.com/scripttemplates/otSDKStub.js`}
        id={"otSDKStub"}
        data-domain-script={"171dd066-59ca-4dbb-8391-4e0b80c566a5"}
        charset="UTF-8"
        dangerouslySetInnerHTML={{
          __html: `
          function OptanonWrapper() { }
          `,
        }}
      />
      <Script
        strategy="afterInteractive"
        src={`//s7.addthis.com/js/300/addthis_widget.js#pubid=ra-62b9549a501268f1`}
        id={"addThis"}
      />

      <Script
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `!function (w, d, t) {
            w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};var o=document.createElement("script");o.type="text/javascript",o.async=!0,o.src=i+"?sdkid="+e+"&lib="+t;var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(o,a)};
          
            ttq.load('CJK9J0JC77U5E795UAE0');
            ttq.page();
          }(window, document, 'ttq');
          `,
        }}
        id={"tiktok_pixel"}
      />

      <AppContext.Provider value={shareVar}>
        <SessionProvider session={session}>
          <Component {...pageProps} />
          {loading ? (
            <div className="loader test_update">
              <div className="lds-ripple">
                <div></div>
                <div></div>
              </div>
            </div>
          ) : null}
          <form ref={formImmersive} style={{ display: 'none' }} method="post">
            <input type="hidden" name="data" value="" />
            <input type="hidden" name="signature" value="" />
          </form>
          <form ref={formLti} style={{ display: 'none' }} method="post">
            <input type="hidden" name="iss" value="" />
            <input type="hidden" name="login_hint" value="" />
            <input type="hidden" name="target_link_uri" value="" />
            <input type="hidden" name="lti_deployment_id" value="" />
            <input type="hidden" name="client_id" value="" />
          </form>
          <form ref={formLtiLaunch} style={{ display: 'none' }} method="post">
            <input type="hidden" name="id_token" value="" />
            <input type="hidden" name="state" value="" />
          </form>
          {/* {!user && !(router.asPath.includes("/checkoutView")||router.asPath.includes("/courseView")) ? (
            <div className="scroll-popup" ref={scrollPop}>
              <Button
                className="close-scroll-popup"
                onClick={() => scrollPop.current.classList.remove("active")}
              >
                <i className="icon-ic-close"></i>
              </Button>
              <h3>
                &#8221;ลงทะเบียนเพื่อปลดล็อกบทเรียนที่เหมาะสมกับคุณ&#8221;
              </h3>
              <Button className="green-button" onClick={() => setOpen(true)}>
                <span className="name">ลงทะเบียน</span>
              </Button>
            </div>
          ) : null} */}
          {router.pathname === '/' && percent<100 ? (
            <div className="loading-progress">
              <Image
                src="/assets/images/header-logo-m.png"
                alt=""
                width="100px"
                height="100px"
                objectFit="contain"
              />
              <div className="box-progress">
                <h3>
                  มีเนื้อหามากมายให้ท่านรับชม กรุณารอสักครู่...
                </h3>
                <div className="progress-outside">
                  <div className="progress-inside" style={{width:percent+'%'}}></div>
                </div>
              </div>
            </div>
          ):null}
          <Modal
            className={`modal-credit-bank`}
            onClose={() => setPopCreditBank(false)}
            open={popCreditBank}
          >
            <Modal.Content className={`detail-section`}>
              <div className="swal-credit-bank">
                <div className="close-button" onClick={() => setPopCreditBank(false)}>
                  <Image
                      src="/assets/images/close-icon.png?v=1"
                      alt=""
                      width="100px"
                      height="100px"
                      objectFit="contain"
                    />
                  </div>
                <div className="header-section">
                  <div className="logo-lll">
                    <Image
                      src="/assets/images/lll-icon.png"
                      alt=""
                      width="100px"
                      height="100px"
                      objectFit="contain"
                    />
                  </div>
                  <h3>Chula Lifelong Learning<br></br>Ecosystem คืออะไร?</h3>
                </div>
                <p>
                  จุฬาลงกรณ์มหาวิทยาลัยมุ่งหวังที่จะขยายโอกาสทางการเรียนรู้ในทุกช่วงวัย<br></br>
                  และลดความเหลื่อมล้ำทางการศึกษาทุกระดับ ด้วย Chula Lifelong Learning<br></br>
                  Ecosystem หรือระบบนิเวศแห่งการเรียนรู้ตลอดชีวิต<br></br>
                  เพื่อให้สอดรับกับบริบทการศึกษาโลกที่เปลี่ยนแปลงไปอย่างรวดเร็ว<br></br>
                  ตอบรับวิสัยทัศน์ และเจตนารมณ์นโยบายการศึกษาแห่งชาติเรื่องการเรียนรู้ตลอดชีวิต<br></br>
                  และวางรากฐานระบบ {'"Chula Lifelong Learning"'} (NCBS) Ecosystem<br></br>
                  ที่จะขยายโอกาสให้กับนิสิตนักศึกษาทั้งจากการศึกษาในระบบ(Formal)<br></br>
                  การศึกษานอกระบบ (In-Formal) และการศึกษาตามอัธยาศัย (Non-Formal)<br></br>
                  เพื่อที่จะได้รับความรู้ใหม่ๆ โดยการไม่ผูกขาดองค์ความรู้
                </p>
                <p className="text-footer">
                  เชื่อมต่อผลการเรียนจาก MedUMORE<br></br>
                  ไปยัง Chula Lifelong Learning Ecosystem
                </p>
                <div className="modal-footer">
                  <button onClick={() => submitLti()} className="go-button">
                    <span>ตกลง</span>
                  </button>
                </div>
              </div>
            </Modal.Content>
          </Modal>
        </SessionProvider>
      </AppContext.Provider>
    </>
  );
}

export default MyApp;

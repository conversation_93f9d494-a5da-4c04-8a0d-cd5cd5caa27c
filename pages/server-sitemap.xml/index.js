import { getServerSideSitemap } from "next-sitemap";
import _ from "lodash";

export const getServerSideProps = async (ctx) => {
    const siteUrl = "https://www.medumore.org";

  let posts = await fetch("https://core.medumore.org/api/sitemap/course");
  posts = await posts.json();
  const newsSitemaps = posts.map((item) => ({
    loc: `${siteUrl}/course/${item.slug.toString()}`,
    lastmod: new Date().toISOString(),
  }));

  let postsInfo = await fetch("https://core.medumore.org/api/sitemap/infographic");
  postsInfo = await postsInfo.json();
  const infoSitemaps = postsInfo.map((item) => ({
    loc: `${siteUrl}/infographic/${item.slug.toString()}`,
    lastmod: new Date().toISOString(),
  }));
   

  const fields = _.merge(infoSitemaps,newsSitemaps);

  return getServerSideSitemap(ctx, fields);
};

export default function Site() {}
import React from "react";
import Script from "next/script";
import Image from "next/image";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import Head from "next/head";
import Header from "../../themes/header/header";
import Footer from "../../themes/footer/footer";
import AddressContact from "../../themes/components/addressContact";
import GroupRadio from "../../themes/components/groupRadio";
import CourseDetailCode from "/themes/components/courseDetailCode";
import Switch from "react-switch";
import {
  But<PERSON>,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";
import PaymentOption from "../../themes/components/paymentOption";
import Swal from "sweetalert2";
import NumberFormat from "react-number-format";
export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const params = context.query;
  const utoken = params.utoken;
  const user = await getUser(utoken);
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/checkout",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  const payment = {
    payment_callback: process.env.PAYMENT_CALLBACK_URL,
    payment_check_callback: process.env.PAYMENT_CHECK_CALLBACK_URL,
    payment_sdk: process.env.PAYMENT_SDK,
    payment_pkey: process.env.PAYMENT_PKEY,
    payment_mid: process.env.PAYMENT_MID,
    payment_name: process.env.PAYMENT_NAME,
  };

  return {
    props: { seo_data, utoken, user, params, payment},
  };
}
export default function Checkout({
  seo_data,
  utoken,
  user,
  params,
  payment,
}) {
  const appContext = useContext(AppContext);
  const router = useRouter();
  const inputFiledata = useRef(null);
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [reloadCart, setReloadCart] = React.useState(true);
  const [cartData, setCartData] = React.useState(null);
  const [taxType, setTaxType] = React.useState(2);
  const [companyType, setCompanyType] = React.useState(1);
  const [giftType, setGiftType] = React.useState(2);
  const [reload, setReload] = React.useState(true);
  const [dataSendComplete, setDataSendComplete] = React.useState(false);
  
  const [reloadDistrict, setReloadDistrict] = React.useState(true);
  const [reloadSubDistrict, setReloadSubDistrict] = React.useState(true);
  const [provinceOptions, setProvinceOptions] = useState(null);
  const [districtOptions, setDistrictOptions] = useState(null);
  const [subdistrictOptions, setSubdistrictOptions] = useState(null);
  const [district, setDistrict] = useState(0);
  const [subdistrict, setSubdistrict] = useState(0);
  const [formType, setFormType] = useState(1);
  const [getReceipt, setGetReceipt] = useState(2);
  const [addressType, setAddressType] = useState(2);
  const [leftShow, setLeftShow] = useState(true);
  const [payShow, setPayShow] = useState(false);
  const [qrId, setQrId] = useState(null);
  const [payType, setPayType] = useState('card');
  const { register, setValue, getValues } = useForm({
    defaultValues: {},
  });

  const [switchStatus, setSwitchStatus] = React.useState(false);

  useEffect(() => {
    if (reload) {
      setReload(false);
      getProvince();
      if (params.receipt) {
        setReceipt(1);
        setTimeout(() => {
          document.getElementById("receipt").checked = true;
          setSwitchStatus(false);
        }, "500");
      }
    }
  }, [reload]);

  useEffect(() => {
    if (user) {
      // if (user.name == "null") {
        setValue("name", null);
      // } else {
      //   setValue("name", user.name);
      // }
      // if (user.lastname == "null") {
        setValue("lastname", null);
      // } else {
      //   setValue("lastname", user.lastname);
      // }
      if (user.address == "null") {
        setValue("address", null);
      } else {
        setValue("address", user.address);
      }
      if (user.subdistrict == "null") {
        setValue("subdistrict", null);
      } else {
        setValue("subdistrict", user.subdistrict);
      }
      if (user.district == "null") {
        setValue("district", null);
      } else {
        setValue("district", user.district);
      }
      if (user.province == "null") {
        setValue("province", null);
      } else {
        setValue("province", user.province);
      }
      if (user.postcode == "null") {
        setValue("postcode", null);
      } else {
        setValue("postcode", user.postcode);
      }
      setValue("receipt", 2);
      setValue("receipt_type", 1);
      setValue("address_type", 1);
      setValue("tax", 2);
      setValue("gift", 2);
      setValue("gift_email", null);
      setValue("gift_message", null);
      setValue("iden_no", null);
      setValue("note", null);
      setValue("payment", null);
      setValue("cart_data", null);
      setValue("utm_source", null);
      setSwitchStatus(false);
    }
  }, [user]);

  useEffect(() => {
    if (reloadCart) {
      setReloadCart(false);
      const formData = new URLSearchParams();
      formData.append("tax_type", taxType);
      formData.append("utoken", utoken);
      appContext.sendApiToken(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/getCheckout",
        formData,
        (data) => {
          if (data["status"] != "success") {
            window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
          }
          if (Number(data["total_price"]) == 0) {
            setPayment(3);
            setLeftShow(false);
          }
          setCartData(data);
        }
      );
    }
  }, [reloadCart]);

  useEffect(() => {
    if (reloadDistrict) {
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/area/district/" +
          getValues("province"),
        null,
        (data) => {
          setDistrictOptions(data.data);
          setSubdistrictOptions(null);
          setReloadDistrict(false);
        }
      );
    }
  }, [reloadDistrict]);

  useEffect(() => {
    if (reloadSubDistrict) {
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/area/subdistrict/" +
          getValues("district"),
        null,
        (data) => {
          setSubdistrictOptions(data.data);
          setReloadSubDistrict(false);
        }
      );
    }
  }, [reloadSubDistrict]);

  const getProvince = () => {
    appContext.loadApi(
      process.env.NEXT_PUBLIC_API + "/api/area/province",
      null,
      (data) => {
        setProvinceOptions(data.data);
      }
    );
  };

  function addDiscountCode(_code, _type) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("discount_code", _code);
      formData.append("utoken", utoken);
      appContext.sendApiToken(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addDiscountWeb",
        formData,
        (obj) => {
          if (obj["is_discount"]) {
            Swal.fire({
              text: "ยินดีด้วย ใช้คูปองสำเร็จ",
              icon: "success",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          } else {
            Swal.fire({
              text: "ขออภัย ไม่พบคูปอง",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          }
          setReloadCart(true);
          appContext.setReloadCart(true);
        }
      );
    } else {
      Swal.fire({
        text: "กรุณาเข้าสู่ระบบค่ะ",
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }

  function setReceipt(_value) {
    // console.log(_value)
    setGetReceipt(parseInt(_value));
    setValue("receipt", parseInt(_value));
    setFormType(1);
    setValue("receipt_type", 1);
    setAddressType(2);
    setValue("address_type", 2);
    setTaxType(2);
    setValue("tax", 2);
    setCompanyType(1);
    setValue("company_type", 1);
    setValue("company_branch", '');
    setTimeout(() => {
      if (_value == 1) {
        document.getElementById("receipt_type").checked = true;
        // document.getElementById("address_type").checked = true;
      }
    }, "0");
    setReloadCart(true);
  }
  function setReceiptType(_value) {
    setFormType(parseInt(_value));
    setValue("receipt_type", parseInt(_value));
    setAddressType(2);
    setValue("address_type", 2);
    setTaxType(2);
    setValue("tax", 2);
    setCompanyType(1);
    setValue("company_type", 1);
    setValue("company_branch", '');
    setTimeout(() => {
      if (_value == 2) {
        document.getElementById("company_type").checked = true;
        document.getElementById("tax2").checked = true;
      }
      // document.getElementById("address_type").checked = true;
    }, "0");
    setReloadCart(true);
  }
  function setAddress(_value) {
    // console.log(_value)
    setAddressType(parseInt(2));
    setValue("address_type", parseInt(2));
    setTaxType(2);
    setValue("tax", 2);
    setCompanyType(1);
    setValue("company_type", 1);
    setValue("company_branch", '');
    setTimeout(() => {
      if (formType == 2) {
        document.getElementById("tax2").checked = true;
      }
    }, "0");
    setReloadCart(true);
  }
  function setTax(_value) {
    // console.log(_value)
    setTaxType(parseInt(_value));
    setValue("tax", parseInt(_value));
    setReloadCart(true);
    setCompanyType(1);
    setValue("company_type", 1);
    setValue("company_branch", '');
    if (_value == 1) {
      setAddressType(parseInt(2));
      setValue("address_type", parseInt(2));
      setTimeout(() => {
        // document.getElementById("address_type2").checked = true;
        document.getElementById("tax").checked = true;
      }, "0");
    }
  }
  function setCompany(_value) {
    // console.log(_value)
    setCompanyType(parseInt(_value));
    setValue("company_type", parseInt(_value));
    setValue("company_branch", '');
    setReloadCart(true);
  }
  function setPayment(_value) {
    // console.log(_value)
    setValue("payment", parseInt(_value));
  }
  function onSubmit() {
    let register_data = getValues();
    if (!appContext.isNull(register_data["address_type"])&&!appContext.isNull(register_data["receipt_type"]) && !appContext.isNull(register_data["tax"])) {
      if (!appContext.isNull(register_data["receipt"]) && register_data["receipt"]==2) {
        if (!appContext.isNull(register_data["payment"])) {
          if(register_data["gift"]==1&&!appContext.isEmail(register_data["gift_email"])){
            Swal.fire({
              text: "กรุณากรอกอีเมลผู้รับของขวัญ",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          }else{
            if (cartData.data.length > 0) {
              checkPaymentMethod();
            } else {
              Swal.fire({
                text: "ไม่พบสินค้าในตะกร้า",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
            }
          }
        } else {
          Swal.fire({
            text: "กรุณาเลือกช่องทางชำระเงิน",
            icon: "error",
            confirmButtonText: "ปิด",
            confirmButtonColor: "#648d2f"
          });
        }
      } else if (!appContext.isNull(register_data["receipt"]) && register_data["receipt"]==1) {
        if(register_data["receipt_type"]==1){
          if (!appContext.isNull(register_data["name"]) && !appContext.isNull(register_data["lastname"]) && !appContext.isNull(register_data["address"]) && !appContext.isNull(register_data["district"]) && appContext.isPostal(register_data["postcode"]) && !appContext.isNull(register_data["province"]) && !appContext.isNull(register_data["subdistrict"])) {
            if (appContext.isIdenNo(register_data["iden_no"])) {
              if (!appContext.isNull(register_data["payment"])) {
                if(register_data["gift"]==1&&!appContext.isEmail(register_data["gift_email"])){
                  Swal.fire({
                    text: "กรุณากรอกอีเมลผู้รับของขวัญ",
                    icon: "error",
                    confirmButtonText: "ปิด",
                    confirmButtonColor: "#648d2f"
                  });
                }else{
                  if (cartData.data.length > 0) {
                    checkPaymentMethod();
                  } else {
                    Swal.fire({
                      text: "ไม่พบสินค้าในตะกร้า",
                      icon: "error",
                      confirmButtonText: "ปิด",
                      confirmButtonColor: "#648d2f"
                    });
                  }
                }
              } else {
                Swal.fire({
                  text: "กรุณาเลือกช่องทางชำระเงิน",
                  icon: "error",
                  confirmButtonText: "ปิด",
                  confirmButtonColor: "#648d2f"
                });
              }
            } else {
              Swal.fire({
                text: "กรุณากรอกเลขประจำตัวประชาชน",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
            }
          }else{
            Swal.fire({
              text: "กรุณากรอกชื่อ-นามสกุล ที่อยู่ให้ครบถ้วน",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          }
        }else{
          if (!appContext.isNull(register_data["name"]) && !appContext.isNull(register_data["address"]) && !appContext.isNull(register_data["district"]) && appContext.isPostal(register_data["postcode"]) && !appContext.isNull(register_data["province"]) && !appContext.isNull(register_data["subdistrict"])) {
            if (appContext.isIdenNo(register_data["iden_no"])) {
              if(register_data["company_type"]==2&&appContext.isNull(register_data["company_branch"])){
                Swal.fire({
                  text: "กรุณาระบุชื่อสาขา",
                  icon: "error",
                  confirmButtonText: "ปิด",
                  confirmButtonColor: "#648d2f"
                });
              } else {
                if (!appContext.isNull(register_data["payment"])) {
                  if(register_data["gift"]==1&&!appContext.isEmail(register_data["gift_email"])){
                    Swal.fire({
                      text: "กรุณากรอกอีเมลผู้รับของขวัญ",
                      icon: "error",
                      confirmButtonText: "ปิด",
                      confirmButtonColor: "#648d2f"
                    });
                  }else{
                    if (cartData.data.length > 0) {
                      checkPaymentMethod();
                    } else {
                      Swal.fire({
                        text: "ไม่พบสินค้าในตะกร้า",
                        icon: "error",
                        confirmButtonText: "ปิด",
                        confirmButtonColor: "#648d2f"
                      });
                    }
                  }
                }else{
                  Swal.fire({
                    text: "กรุณาเลือกช่องทางชำระเงิน",
                    icon: "error",
                    confirmButtonText: "ปิด",
                    confirmButtonColor: "#648d2f"
                  });
                }
              }
            } else {
              Swal.fire({
                text: "กรุณากรอกเลขประจำตัวผู้เสียภาษีอากร",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
            }
          }else{
            Swal.fire({
              text: "กรุณากรอกชื่อบริษัท และที่อยู่ให้ครบถ้วน",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f"
            });
          }
        }
      } else {
        Swal.fire({
          text: "กรุณากรอกข้อมูลให้ครบถ้วน",
          icon: "error",
          confirmButtonText: "ปิด",
          confirmButtonColor: "#648d2f"
        });
      }
    } else {
      Swal.fire({
        text: "กรุณากรอกข้อมูลให้ครบถ้วน",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f"
      });
    }
  }

  function checkPaymentMethod(){
    let register_data = getValues();
    appContext.setLoading(true);
    if(cartData.cart_live){
      const formCheck = new URLSearchParams();
      formCheck.append("course_id", cartData.data[0]['course_id']);
      formCheck.append("utoken", utoken);
      appContext.sendApiToken(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/checkLive",
        formCheck,
        (res_live) => {
          if(res_live['limit']==0||(res_live['limit']!=0 && res_live['remain']!=0)){
            if(res_live['limit']==0){
              const boxes = document.querySelectorAll('.remove_submit_btn');
              boxes.forEach(box => {
                box.remove();
              });
              if(register_data["payment"]==1||register_data["payment"]==2){
                kPaymentSubmit(register_data["payment"]);
              }else{
                sentDataToAddOrder();
              }
            }else{
              // html: "กรุณากดยืนยันเพื่อลงทะเบียน<br>Live Streaming<br>จำนวนผู้ลงทะเบียน "+(res_live['limit']-res_live['remain'])+'/'+res_live['limit'],
              appContext.setLoading(false);
              Swal.fire({
                html: "กรุณากดยืนยันเพื่อลงทะเบียน<br>Live Streaming",
                icon: "info",
                showCancelButton: true,
                confirmButtonColor: '#648d2f',
                cancelButtonColor: '#d33',
                confirmButtonText: 'ยืนยัน',
                cancelButtonText: 'ยกเลิก'
              }).then((result) => {
                if (result.value) {
                  appContext.setLoading(true);
                  const boxes = document.querySelectorAll('.remove_submit_btn');
                  boxes.forEach(box => {
                    box.remove();
                  });
                  if(register_data["payment"]==1||register_data["payment"]==2){
                    kPaymentSubmit(register_data["payment"]);
                  }else{
                    sentDataToAddOrder();
                  }
                }
              });
            }
          }else{
            appContext.setLoading(false);
            Swal.fire({
              html: "Live Streaming<br>มีผู้ลงทะเบียนครบตามจำนวนแล้ว",
              icon: "error",
              confirmButtonText: 'ปิด',
              confirmButtonColor: "#648d2f"
            });
          }
        }
      );
    }else{
      if(cartData.yearly_member && cartData.yearly_member != '' && cartData.cart_type != 'subscription'
      && cartData.yearly_setting && cartData.yearly_setting != '' && cartData.total_price_excl_ebook >= cartData.yearly_setting.value
      && cartData.learner_type && (cartData.learner_type == 1 || cartData.learner_type == 3 || cartData.learner_type == 4)){
        if(cartData.yearly_setting.qty&&cartData.yearly_setting.qty!=0&&cartData.yearly_setting.qty!='0'&&cartData.yearly_setting.qty!=''&&cartData.yearly_setting.qty!=null
        &&cartData.yearly_setting.qty!='null'&&
        cartData.count_excl_ebook&&cartData.count_excl_ebook!=0&&cartData.count_excl_ebook!='0'&&cartData.count_excl_ebook!=''&&cartData.count_excl_ebook!=null
        &&cartData.count_excl_ebook!='null' && cartData.yearly_setting.qty > cartData.count_excl_ebook){
          const boxes = document.querySelectorAll('.remove_submit_btn');
          boxes.forEach(box => {
            box.remove();
          });
          if(register_data["payment"]==1||register_data["payment"]==2){
            kPaymentSubmit(register_data["payment"]);
          }else{
            sentDataToAddOrder();
          }
        }else{
          appContext.setLoading(false);
          Swal.fire({
            html: "คุณต้องการซื้อ Yearly Member<br>เพื่อความคุ้มค่ามากกว่าหรือไม่?",
            icon: "info",
            showCancelButton: true,
            confirmButtonColor: '#648d2f',
            cancelButtonColor: '#d33',
            confirmButtonText: 'ซื้อ Yearly Member',
            cancelButtonText: 'ซื้อสินค้านี้'
          }).then((result) => {
            if (result.value) {
              appContext.setLoading(true);
              const formData = new URLSearchParams();
              formData.append("course_id", cartData.yearly_member.id);
              formData.append("content_type", "subscription");
              formData.append("utoken", utoken);
              appContext.sendApiToken(
                process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
                formData,
                (res_check) => {
                  if(res_check['status']=='success'){
                    if(res_check['count']==0){
                      appContext.sendApiToken(
                        process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                        formData,
                        (data) => {
                          if(data['status']=='success'){
                            location.reload();
                          }else if(data['status']=='limit'){
                            appContext.setLoading(false);
                            Swal.fire({
                              text: "ขออภัยค่ะ คุณไม่สามารถต่ออายุเกิน 2 ปีได้",
                              icon: "info",
                              confirmButtonText: "ปิด",
                              confirmButtonColor: "#648d2f"
                            }).then((result) => {
                              location.reload();
                            });
                          }else if(data['status']=='limit_buy'){
                            appContext.setLoading(false);
                            Swal.fire({
                              text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                              icon: "info",
                              confirmButtonText: "ปิด",
                              confirmButtonColor: "#648d2f"
                            }).then((result) => {
                              location.reload();
                            });
                          }
                        }
                      );
                    }else{
                      appContext.sendApiToken(
                        process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                        formData,
                        (res_clear) => {
                          if(res_clear['status']=='success'){
                            appContext.sendApiToken(
                              process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                              formData,
                              (data) => {
                                if(data['status']=='success'){
                                  location.reload();
                                }else if(data['status']=='limit'){
                                  appContext.setLoading(false);
                                  Swal.fire({
                                    text: "ขออภัยค่ะ คุณไม่สามารถต่ออายุเกิน 2 ปีได้",
                                    icon: "info",
                                    confirmButtonText: "ปิด",
                                    confirmButtonColor: "#648d2f"
                                  }).then((result) => {
                                    location.reload();
                                  });
                                }else if(data['status']=='limit_buy'){
                                  appContext.setLoading(false);
                                  Swal.fire({
                                    text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                                    icon: "info",
                                    confirmButtonText: "ปิด",
                                    confirmButtonColor: "#648d2f"
                                  }).then((result) => {
                                    location.reload();
                                  });
                                }
                              }
                            );
                          }
                        }
                      );
                    }
                  }
                }
              );
            }else{
              const boxes = document.querySelectorAll('.remove_submit_btn');
              boxes.forEach(box => {
                box.remove();
              });
              if(register_data["payment"]==1||register_data["payment"]==2){
                kPaymentSubmit(register_data["payment"]);
              }else{
                sentDataToAddOrder()
              }
            }
          });
        }
      }else{
        const boxes = document.querySelectorAll('.remove_submit_btn');
        boxes.forEach(box => {
          box.remove();
        });
        if(register_data["payment"]==1||register_data["payment"]==2){
          kPaymentSubmit(register_data["payment"]);
        }else{
          sentDataToAddOrder();
        }
      }
    }
  }

  function sentDataToAddOrder(){
    const formData = new URLSearchParams();
    formData.append("tax_type", taxType);
    formData.append("utoken", utoken);
    appContext.sendApiToken(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/getCheckout",
      formData,
      (data) => {
        if (data["status"] != "success") {
          window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
        }
        if (Number(data["total_price"]) == 0) {
            setPayment(3);
            setLeftShow(false);
        }
        setCartData(data);
        setValue("cart_data", JSON.stringify(data));
        if (!appContext.isNull(appContext.getLocal("utm_source"))) {
          setValue("utm_source", appContext.getLocal("utm_source"));
        }
        let form_data = getValues();
        appContext.loadApiToken(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrder",
          {...form_data,utoken:utoken},
          (obj) => {
            if (obj["status"] == "success") {
              appContext.removeLocal("utm_source");
              appContext.removeLocal("utm_time");
              window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
            }
          }
        );
      }
    );
  }

  function kPaymentSubmit(_type){
    const formData = new URLSearchParams();
    formData.append("tax_type", taxType);
    formData.append("utoken", utoken);
    appContext.sendApiToken(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/getCheckout",
      formData,
      (data) => {
        if (data["status"] != "success") {
          window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
        }
        if (Number(data["total_price"]) == 0) {
            setPayment(3);
            setLeftShow(false);
        }
        setCartData(data);
        setValue("cart_data", JSON.stringify(data));
        if (!appContext.isNull(appContext.getLocal("utm_source"))) {
          setValue("utm_source", appContext.getLocal("utm_source"));
        }
        let form_data = getValues();
        appContext.loadApiToken(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrder",
          {...form_data,utoken:utoken},
          (obj) => {
            setDataSendComplete(true)
            if (obj["status"] == "success") {
              appContext.removeLocal("utm_source");
              appContext.removeLocal("utm_time");
              if(_type==1){
                const formData = new URLSearchParams();
                formData.append("order_id", obj["order_id_encrypt"]);
                formData.append("utoken", utoken);
                appContext.sendApiToken(
                  process.env.NEXT_PUBLIC_API + "/api/mdcu/kpayOrder",
                  formData,
                  (res) => {
                    if (res["status"] == "success") {
                      appContext.setLoading(false);
                      setPayType('qr');
                      setQrId(res['qr_id']);
                      setTimeout(() => {
                        appContext.setLoading(false);
                        setPayShow(true);
                      }, "100");
                      setTimeout(() => {
                        var kpayemnt_form = document.getElementById('kpayemnt_form');
                        var _path_action = kpayemnt_form.getAttribute('action')
                        kpayemnt_form.setAttribute('action',_path_action+'/'+ obj["order_id_encrypt"]);
                        
                        var element = document.getElementsByClassName('pay-button');
                        
                        try{
                          element[0].click();
                        }catch(e){
                          // console.log('kpayment not active')
                        }
                      }, "1000");
                    }
                  }
                );
              }else{
                setPayType('card');
                setTimeout(() => {
                  appContext.setLoading(false);
                  setPayShow(true);
                }, "100");
                setTimeout(() => {
                  var kpayemnt_form = document.getElementById('kpayemnt_form');
                  var _path_action = kpayemnt_form.getAttribute('action')
                  kpayemnt_form.setAttribute('action',_path_action+'/'+ obj["order_id_encrypt"]);
                  
                  var element = document.getElementsByClassName('pay-button');
                  
                  try{
                    element[0].click();
                  }catch(e){
                    // console.log('kpayment not active')
                  }
                }, "1000");
              }
            }
          }
        );
      }
    );
  }

  return ( 
    <>
      <style>{`
        #onetrust-consent-sdk{
          display:none!important;
        }
      `}</style>
      <div className="main-all">
        <div className="main-body">
          <div className={`container custom-container space-between-content`}>
            <div className="checkout-page">
              <div className="left">
                {leftShow ? (
                <>
                  <h2 className="switch-group-title">ข้อมูลใบเสร็จ</h2>
                  <div className="switch-box">
                    <Switch onChange={(checked)=>{
                      setSwitchStatus(checked);
                      if(checked){
                        setReceipt(1);
                      }else{
                        setReceipt(2);
                      }
                    }} checked={switchStatus} uncheckedIcon={false} checkedIcon={false} />
                    <h2 className="switch-title">ต้องการใบเสร็จ</h2>
                  </div>
                  {getReceipt == 1 ? (
                    <div>
                      <GroupRadio
                        callback={setReceiptType}
                        title="ออกในนาม"
                        name="receipt_type"
                        value_1="บุคคลธรรมดา"
                        value_2="นิติบุคคล"
                      ></GroupRadio>
                    </div>
                  ) : null}
                  {getReceipt == 1 ? (
                    formType == 1 ? (
                      addressType == 1 ? (
                        <AddressContact data={user}></AddressContact>
                      ) : (
                        <div className="row-fm-register-checkout row">
                          <div className="col-fm-register col-12">
                            <div className="item-fm">
                              <p className="fm-title checkout">บุคคลธรรมดา</p>
                            </div>
                          </div>
                          <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                            <div className="item-fm">
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="ชื่อ"
                              >
                                <input
                                  {...register("name")}
                                  maxLength={100}
                                  data-type="thaionly"
                                  onInput={appContext.diFormPattern}
                                />
                              </Input>
                            </div>
                          </div>
                          <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                            <div className="item-fm">
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="นามสกุล"
                              >
                                <input
                                  {...register("lastname")}
                                  maxLength={100}
                                  data-type="thaionly"
                                  onInput={appContext.diFormPattern}
                                />
                              </Input>
                            </div>
                          </div>
                          <div className="col-fm-register col-12 col-md-12 checkout-register-mgb">
                            <div className="item-fm">
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="เลขประจำตัวประชาชน"
                              >
                                <input
                                  {...register("iden_no")}
                                  maxLength={13}
                                  data-type="number"
                                  onInput={appContext.diFormPattern}
                                />
                              </Input>
                            </div>
                          </div>
                          <div className="col-fm-register col-12 checkout-register-mgb">
                            <div className="item-fm">
                              <TextArea
                                placeholder="ที่อยู่"
                                className="fm-control"
                                defaultValue={getValues("address")}
                                data-type="textaddressthai"
                                onInput={appContext.diFormPattern}
                                onChange={(event) =>
                                  setValue("address", event.target.value)
                                }
                              />
                            </div>
                          </div>
                          <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                            <div className="item-fm">
                              <Select
                                className="fm-control"
                                placeholder="กรุณาเลือกจังหวัด"
                                options={provinceOptions}
                                defaultValue={user.province}
                                onChange={(event, data) => {
                                  //   console.log(event)
                                  setReloadDistrict(true);
                                  setValue("province", data.value);
                                  setValue(
                                    "province_name",
                                    event.target.innerText
                                  );
                                }}
                              />
                            </div>
                          </div>
                          <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                            <div className="item-fm">
                              <Select
                                className="fm-control"
                                placeholder="กรุณาเลือกเขต/อำเภอ"
                                options={districtOptions}
                                defaultValue={user.district}
                                onChange={(event, data) => {
                                  setReloadSubDistrict(true);
                                  setValue("district", data.value);
                                  setValue(
                                    "district_name",
                                    event.target.innerText
                                  );
                                }}
                              />
                            </div>
                          </div>
                          <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                            <div className="item-fm">
                              <Select
                                className="fm-control"
                                placeholder="กรุณาเลือกแขวง/ตำบล"
                                options={subdistrictOptions}
                                defaultValue={user.subdistrict}
                                onChange={(event, data) => {
                                  setValue("subdistrict", data.value);
                                  setValue(
                                    "subdistrict_name",
                                    event.target.innerText
                                  );
                                }}
                              />
                            </div>
                          </div>
                          <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                            <div className="item-fm">
                              <Input
                                type="tel"
                                className="fm-control"
                                placeholder="รหัสไปรษณีย์"
                              >
                                <input
                                  {...register("postcode")}
                                  maxLength={5}
                                  data-type="number"
                                  onInput={appContext.diFormPattern}
                                  defaultValue={register["postcode"]}
                                />
                              </Input>
                            </div>
                          </div>
                          <div className="col-fm-register col-12 checkout-register-mgb">
                            <div className="item-fm">
                              <TextArea
                                placeholder="โน้ตเพิ่มเติม"
                                className="fm-control"
                                defaultValue={getValues("note")}
                                data-type="textaddress"
                                onInput={appContext.diFormPattern}
                                onChange={(event) =>
                                  setValue("note", event.target.value)
                                }
                              />
                            </div>
                          </div>
                        </div>
                      )
                    ) : addressType == 1 ? (
                      <div>
                        <AddressContact data={user}></AddressContact>
                        <GroupRadio
                          callback={setTax}
                          title=""
                          name="tax"
                          value_1="ต้องการหัก ณ ที่จ่าย"
                          value_2="ไม่ต้องการ หัก ณ ที่จ่าย"
                        ></GroupRadio>
                      </div>
                    ) : (
                      <div className="row-fm-register-checkout row">
                        <div className="col-fm-register col-12">
                          <div className="item-fm">
                            <p className="fm-title checkout">นิติบุคคล</p>
                          </div>
                        </div>
                        <div className="col-fm-register col-12 col-md-12 checkout-register-mgb">
                          <div className="item-fm">
                            <Input
                              type="text"
                              className="fm-control"
                              placeholder="ชื่อบริษัท"
                            >
                              <input
                                {...register("name")}
                                maxLength={100}
                                data-type="thaionly"
                                onInput={appContext.diFormPattern}
                              />
                            </Input>
                          </div>
                        </div>
                        <div className="col-fm-register col-12 col-md-12 checkout-register-mgb">
                          <GroupRadio
                            callback={setCompany}
                            title=""
                            name="company_type"
                            value_1="สำนักงานใหญ่"
                            value_2="สาขา ระบุ..."
                          ></GroupRadio>
                        </div>
                        {companyType==2?(
                          <div className="col-fm-register col-12 col-md-12 checkout-register-mgb">
                            <div className="item-fm">
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="ชื่อสาขา"
                              >
                                <input
                                  {...register("company_branch")}
                                  maxLength={100}
                                  data-type="thaionly"
                                  onInput={appContext.diFormPattern}
                                />
                              </Input>
                            </div>
                          </div>
                        ):null}
                        <div className="col-fm-register col-12 col-md-12 checkout-register-mgb">
                          <div className="item-fm">
                            <Input
                              type="text"
                              className="fm-control"
                              placeholder="เลขประจำตัวผู้เสียภาษีอากร"
                            >
                              <input
                                {...register("iden_no")}
                                maxLength={13}
                                data-type="number"
                                onInput={appContext.diFormPattern}
                              />
                            </Input>
                          </div>
                        </div>
                        <div className="col-fm-register col-12 checkout-register-mgb">
                          <div className="item-fm">
                            <TextArea
                              placeholder="ที่อยู่"
                              className="fm-control"
                              defaultValue={getValues("address")}
                              data-type="textaddressthai"
                              onInput={appContext.diFormPattern}
                              onChange={(event) =>
                                setValue("address", event.target.value)
                              }
                            />
                          </div>
                        </div>
                        <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                          <div className="item-fm">
                            <Select
                              className="fm-control"
                              placeholder="กรุณาเลือกจังหวัด"
                              options={provinceOptions}
                              defaultValue={user.province}
                              onChange={(event, data) => {
                                //   console.log(event)
                                setReloadDistrict(true);
                                setValue("province", data.value);
                                setValue("province_name", event.target.innerText);
                              }}
                            />
                          </div>
                        </div>
                        <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                          <div className="item-fm">
                            <Select
                              className="fm-control"
                              placeholder="กรุณาเลือกเขต/อำเภอ"
                              options={districtOptions}
                              defaultValue={user.district}
                              onChange={(event, data) => {
                                setReloadSubDistrict(true);
                                setValue("district", data.value);
                                setValue("district_name", event.target.innerText);
                              }}
                            />
                          </div>
                        </div>
                        <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                          <div className="item-fm">
                            <Select
                              className="fm-control"
                              placeholder="กรุณาเลือกแขวง/ตำบล"
                              options={subdistrictOptions}
                              defaultValue={user.subdistrict}
                              onChange={(event, data) => {
                                setValue("subdistrict", data.value);
                                setValue(
                                  "subdistrict_name",
                                  event.target.innerText
                                );
                              }}
                            />
                          </div>
                        </div>
                        <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                          <div className="item-fm">
                            <Input
                              type="tel"
                              className="fm-control"
                              placeholder="รหัสไปรษณีย์"
                            >
                              <input
                                {...register("postcode")}
                                maxLength={5}
                                data-type="number"
                                onInput={appContext.diFormPattern}
                                defaultValue={register["postcode"]}
                              />
                            </Input>
                          </div>
                        </div>
                        <div className="col-fm-register col-12 checkout-register-mgb">
                          <div className="item-fm">
                            <TextArea
                              placeholder="โน้ตเพิ่มเติม"
                              className="fm-control"
                              defaultValue={getValues("note")}
                              data-type="textaddress"
                              onInput={appContext.diFormPattern}
                              onChange={(event) =>
                                setValue("note", event.target.value)
                              }
                            />
                          </div>
                        </div>
                        <GroupRadio
                          callback={setTax}
                          title=""
                          name="tax"
                          value_1="ต้องการหัก ณ ที่จ่าย"
                          value_2="ไม่ต้องการ หัก ณ ที่จ่าย"
                        ></GroupRadio>
                      </div>
                    )
                  ) : null}
                  <h2 className="form-title m-top">การชำระเงิน</h2>
                  <h3 className="form-description m-bot">
                    ธุรกรรมทั้งหมดมีความปลอดภัยและได้รับการเข้ารหัส
                  </h3>
                  <div className="block-payment-list">
                    {/* ======== */}
                    <div className="item-payment">
                      <div
                        className="item-choose-payment"
                        onClick={() => setPayment(1)}
                      >
                        <div className="fm-check">
                          <input type="radio" name="payment" />
                          <div className="text">
                            <div className="i_remark">
                              <i className="icon-ic-circle" />
                            </div>
                            <div className="content">
                              <p>QR Code</p>
                              <div  className="imgResponsiveQr">
                                <Image
                                    src={'/assets/images/qr.png'}
                                    layout='fill'
                                    objectFit='contain'
                                    sizes="100%"
                                    alt=""
                                  />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    {/* ======== */}
                    <div className="item-payment">
                      <div
                        className="item-choose-payment"
                        onClick={() => setPayment(2)}
                      >
                        <div className="fm-check">
                          <input type="radio" name="payment" />
                          <div className="text">
                            <div className="i_remark">
                              <i className="icon-ic-circle" />
                            </div>
                            <div className="content">
                              <p>Credit/Debit</p>
                              <div  className="imgResponsiveVisa">
                                <Image
                                    src={'/assets/images/visa.jpg'}
                                    layout='fill'
                                    objectFit='contain'
                                    sizes="100%"
                                    alt=""
                                  />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    {/* ======== */}
                    {/* <div className="item-payment">
                      <div
                        className="item-choose-payment"
                        onClick={() => setPayment(3)}
                      >
                        <div className="fm-check">
                          <input type="radio" name="payment" />
                          <div className="text">
                            <div className="i_remark">
                              <i className="icon-ic-circle" />
                            </div>
                            <div className="content">
                              <p>โอนเงิน</p>
                              <div className="account-bank">
                                <Image
                                  className="bank"
                                  src="/assets/images/KBANK_square.png"
                                  layout="fill"
                                  objectFit="contain"
                                  alt=""
                                />
                                <div className="bank-description">
                                  <h3>ธนาคารกสิกรไทย</h3>
                                  <h4>
                                    บัญชี บริษัทเอ็มดีซียู เมดโนเวชั่น จำกัด
                                  </h4>
                                  <h4>ธนาคารกสิกรไทย สาขา โรงพยาบาลจุฬาลงกรณ์</h4>
                                  <h4>127-8-44476-3</h4>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div> */}
                    {/* ======== */}
                  </div>
                  <h2 className="form-title m-top">ซื้อเป็นของขวัญ</h2>
                </>
                ):<h2 className="form-title">ซื้อเป็นของขวัญ</h2>}
                <div className="switch-box gift-box">
                  <Switch onChange={(checked)=>{
                    if(checked){
                      setGiftType(1);
                      setValue("gift", 1);
                    }else{
                      setGiftType(2);
                      setValue("gift", 2);
                    }
                  }} checked={giftType==1} uncheckedIcon={false} checkedIcon={false} />
                  <h2 className="switch-title">ส่งของขวัญ</h2>
                </div>
                {giftType==1 ? (
                  <>
                    <div className="col-fm-register col-12 col-md-6 mg-top-5">
                      <div className="item-fm">
                        <Input
                          type="email"
                          className="fm-control"
                          placeholder="อีเมล์ผู้รับ"
                        >
                          <input
                            {...register("gift_email")}
                            maxLength={100}
                            data-type="email"
                            onInput={appContext.diFormPattern}
                          />
                        </Input>
                      </div>
                    </div>
                    <div className="col-fm-register col-12 col-md-12 mg-top-5">
                      <div className="item-fm">
                        <TextArea
                          placeholder="ข้อความถึงผู้รับ"
                          defaultValue={getValues("gift_message")}
                          data-type="textaddress"
                          onInput={appContext.diFormPattern}
                          onChange={(event) =>
                            setValue("gift_message", event.target.value)
                          }
                          className={
                            "fm-control"
                          }
                        />
                      </div>
                    </div>
                  </>
                ):null}
              </div>
              <div className="right">
                <h2 className="form-title m-bot">สรุปรายการสั่งซื้อ</h2>
                {cartData ? (
                  <div>
                    {cartData["data"].map((val, key) => (
                      <div key={key}>
                        <div className="item-purchase-report">
                          <div className="purchase-report-img">
                            <div className="purchase-thumb">
                              <div  className="imgResponsiveCustom">
                                {val && val.image && val.image != null && val.image != '' && val.image != 'null' ? (
                                  <Image
                                   src={val.image}
                                    layout='fill'
                                    objectFit='contain'
                                    sizes="100%"
                                    alt=""
                                  />
                                ):null}
                                </div>
                            </div>
                          </div>
                          <div className="purchase-report-content">
                            <h3>{val.title}</h3>
                            <p
                              dangerouslySetInnerHTML={{
                                __html: appContext.truncate(
                                  val.description,
                                  80
                                ),
                              }}
                            ></p>
                          </div>
                          <NumberFormat
                            decimalScale={2}
                            value={val.price}
                            displayType={"text"}
                            thousandSeparator={true}
                            renderText={(value, props) => (
                              <div className="purchase-report-price" {...props}>
                                {value} บาท
                              </div>
                            )}
                          />
                        </div>
                        <div className="item-purchase-report">
                          {val.discount_code != "" &&
                          val.discount_code != null ? (
                            <div className="item-report-price">
                              <div className="purchase-report-name">
                                <h4>
                                  สวนลด{" "}
                                  <span>
                                    <i className="icon-ic-tikket"></i>{" "}
                                    {val.discount_code}
                                  </span>
                                </h4>
                              </div>
                              <NumberFormat
                                decimalScale={2}
                                value={val.discount_price}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <div
                                    className="purchase-report-price"
                                    {...props}
                                  >
                                    -{value} บาท
                                  </div>
                                )}
                              />
                            </div>
                          ) : null}
                          <div className="width-100">
                            <div className="item-report-price">
                              <div className="purchase-report-name">
                                <h4>ราคาก่อน VAT</h4>
                              </div>
                              <NumberFormat
                                decimalScale={2}
                                value={val.pre_vat}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <div
                                    className="purchase-report-price"
                                    {...props}
                                  >
                                    {value} บาท
                                  </div>
                                )}
                              />
                            </div>
                            <div className="item-report-price">
                              <div className="purchase-report-name">
                                <h4>ยอด VAT</h4>
                              </div>
                              <NumberFormat
                                decimalScale={2}
                                value={val.vat}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <div
                                    className="purchase-report-price"
                                    {...props}
                                  >
                                    {value} บาท
                                  </div>
                                )}
                              />
                            </div>
                          </div>
                          {cartData["tax_type"] == 1 ? (
                            <div className="item-report-price">
                              <div className="purchase-report-name">
                                <h4>หักภาษี ณ ที่จ่าย 3%</h4>
                              </div>
                              <NumberFormat
                                decimalScale={2}
                                value={val.tax}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <div
                                    className="purchase-report-price"
                                    {...props}
                                  >
                                    {value} บาท
                                  </div>
                                )}
                              />
                            </div>
                          ) : null}
                          <div className="item-report-price">
                            <div className="purchase-report-name">
                              <h3>ยอดรวมย่อย</h3>
                            </div>
                            <NumberFormat
                              decimalScale={2}
                              value={val.total}
                              displayType={"text"}
                              thousandSeparator={true}
                              renderText={(value, props) => (
                                <div
                                  className="purchase-report-price"
                                  {...props}
                                >
                                  {value} บาท
                                </div>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                    <div className="item-purchase-report">
                      {cartData["discount_web"] > 0 ? (
                        <div className="item-report-price">
                            <div className="purchase-report-name">
                              {/* <h3>ส่วนลดคูปองเงินสด</h3> */}
                              <h4>
                                ส่วนลดคูปองเงินสด{" "}
                                <span>
                                  <i className="icon-ic-tikket"></i>{" "}
                                  {cartData["code_web"]}
                                </span>
                              </h4>
                            </div>
                            <NumberFormat
                              decimalScale={2}
                              value={cartData["discount_web"]}
                              displayType={"text"}
                              thousandSeparator={true}
                              renderText={(value, props) => (
                                <div
                                  className="purchase-report-price"
                                  {...props}
                                >
                                  - {value} บาท
                                </div>
                              )}
                            />
                        </div>
                      ):null}
                      <div className="item-report-price">
                        <div className="purchase-report-name">
                          <h3>ยอดรวม</h3>
                          {cartData["tax_type"] == 1 ? (
                            <NumberFormat
                              decimalScale={2}
                              value={cartData.total_tax}
                              displayType={"text"}
                              thousandSeparator={true}
                              renderText={(value, props) => (
                                <p {...props}>รวมภาษี ฿{value}</p>
                              )}
                            />
                          ) : null}
                        </div>
                        <NumberFormat
                          decimalScale={2}
                          value={cartData.total_price}
                          displayType={"text"}
                          thousandSeparator={true}
                          renderText={(value, props) => (
                            <div className="purchase-report-price" {...props}>
                              <b>{value} บาท</b>
                            </div>
                          )}
                        />
                      </div>
                    </div>
                    <div className="coupon-checkout">
                      <p className="space-between-content-top title-button-group">
                        คูปองเงินสด
                      </p>
                      <CourseDetailCode
                        callback={addDiscountCode}
                      ></CourseDetailCode>
                    </div>
                    {
                      !dataSendComplete?
                      (
                        <div className="item-purchase-report border0">
                          <div className="purchase-report-action">
                            <button
                              onClick={() => onSubmit()}
                              className="btn-default remove_submit_btn"
                            >
                              {leftShow ? (
                                <span>ดำเนินการชำระเงิน</span>
                              ):(
                                <span>ยืนยัน</span>
                              )}
                            </button>
                          </div>
                        </div>
                      ):null
                    }
                    
                  </div>
                ) : null}
              </div>
            </div>
          </div>

          {cartData && payShow ? (
              payType=='qr' ? (
                <>
                  <form method="POST" action={payment.payment_check_callback} id="kpayemnt_form">
                    <script type="text/javascript"
                      async
                      src={payment.payment_sdk}
                      data-apikey={payment.payment_pkey}
                      data-amount={cartData.total_price}
                      data-currency="THB"
                      data-order-id={qrId}
                      data-payment-methods={payType}
                      data-name={payment.payment_name}
                      data-mid={payment.payment_mid}
                    >
                    </script>
                  </form>
                  <Script
                    strategy="afterInteractive"
                    src={payment.payment_sdk}
                    data-apikey={payment.payment_pkey}
                    data-amount={cartData.total_price}
                    data-currency="THB"
                    data-order-id={qrId}
                    data-payment-methods={payType}
                    data-name={payment.payment_name}
                    data-mid={payment.payment_mid}
                    id={"kpayment"}
                    onLoad={() => {
                      // console.log('KPayment loaded')
                      window.KPayment.create();
                      var payment_show = 0;
                      window.setInterval( function() {  
                        var elem = document.getElementsByClassName('payment-container');
                        var lastClassName = elem[0].className;
                        if ( (" " + lastClassName + " ").replace(/[\n\t]/g, " ").indexOf(" show ") > -1 ){
                          if(payment_show==0){
                            payment_show = 1;
                          }
                        }else{
                          if(payment_show==1){
                            payment_show = 2;
                            setTimeout(() => {
                              window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
                            }, "1000");
                          }
                        }
                      },1000);
                    }}
                  /> 
                </>
              ):(
                <>
                  <form method="POST" action={payment.payment_callback} id="kpayemnt_form">
                    <script type="text/javascript"
                      async
                      src={payment.payment_sdk}
                      data-apikey={payment.payment_pkey}
                      data-amount={cartData.total_price}
                      data-currency="THB"
                      data-payment-methods={payType}
                      data-name={payment.payment_name}
                      data-mid={payment.payment_mid}
                    >
                    </script>
                  </form>
                  <Script
                    strategy="afterInteractive"
                    src={payment.payment_sdk}
                    data-apikey={payment.payment_pkey}
                    data-amount={cartData.total_price}
                    data-currency="THB"
                    data-payment-methods={payType}
                    data-name={payment.payment_name}
                    data-mid={payment.payment_mid}
                    id={"kpayment"}
                    onLoad={() => {
                      // console.log('KPayment loaded')
                      window.KPayment.create();
                      var payment_show = 0;
                      window.setInterval( function() {  
                        var elem = document.getElementsByClassName('payment-container');
                        var lastClassName = elem[0].className;
                        if ( (" " + lastClassName + " ").replace(/[\n\t]/g, " ").indexOf(" show ") > -1 ){
                          if(payment_show==0){
                            payment_show = 1;
                          }
                        }else{
                          if(payment_show==1){
                            payment_show = 2;
                            setTimeout(() => {
                              window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
                            }, "1000");
                          }
                        }
                      },1000);
                    }}
                  /> 
                </>
              )
          ):null}
        </div>
      </div> 
    </>
  );
}

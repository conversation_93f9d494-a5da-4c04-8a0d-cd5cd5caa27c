import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "/pages/api/user";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";

import { useState, useEffect, useContext, useRef, useReducer } from "react";

import { useRouter } from "next/router";
import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import Link from "next/link";
import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
  Checkbox
} from "semantic-ui-react";
import { useForm } from "react-hook-form";
// Serverside & Api fetching
import Swal from "sweetalert2";

export async function getServerSideProps(context) {
  return {
    redirect: { destination: "/" },
  };
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const params = context.query;
  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/donation",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      params,
      utoken,
      user,
    },
  };
}

export default function Payment({
  seo_data,
  params,
  utoken,
  user,
}) {
  const router = useRouter();
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [errorArray, setErrorArray] = useState([]);
  const { register, setValue, getValues } = useForm({
    defaultValues: {},
  });

  const onSubmit = () => {
    let register_data = getValues();
    let bol = true;
    var errarr = [];
    if (appContext.isNull(register_data["name"])) {
      bol = false;
      errarr.push("name");
    }
    setErrorArray(errarr);
    if (bol) {
      let register_data = getValues();
      const formData = new URLSearchParams();
      formData.append("data", JSON.stringify(register_data));
      // appContext.sendApi(
      //   process.env.NEXT_PUBLIC_API + "/api/mdcu/updateOrder",
      //   formData,
      //   (obj) => {
      //     if (obj["status"] == "success") {
      //       setTimeout(() => {
      //         Swal.fire({
      //           text: "แจ้งชำระเงินสำเร็จ",
      //           icon: "success",
      //           confirmButtonText: "ปิด",
      //           confirmButtonColor: "#648d2f"
      //         }).then((result) => {
      //           location.href = "/profile/history";
      //         });
      //       }, "0");
      //     }
      //   }
      // );
      console.log('send api');
      console.log(formData);
    } else {
      Swal.fire({
        text: "กรุณาตรวจสอบข้อมูล",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f"
      });
    }
  };

  const checkMyError = (_val) => {
    if (errorArray.indexOf(_val) > -1) {
      return true;
    }
    return false;
  };

  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className="register-page">
          <div className="register-block-form">
            <div className="block-form-inner">
              <div className="row">
                <div className="col-12">
                  <div className="register-block-form-title">
                    <h3>ข้อมูลผู้บริจาค</h3>
                  </div>
                </div>
              </div>
              <div className="row-fm-register row">
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input type="text" className="fm-control">
                      <input
                        {...register("name")}
                        maxLength={100}
                        data-type="textaddress"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("name") ? "error" : ""}
                        placeholder="ชื่อ"
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input type="text" className="fm-control">
                      <input
                        {...register("lastname")}
                        maxLength={100}
                        data-type="textaddress"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("lastname") ? "error" : ""}
                        placeholder="นามสกุล"
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input type="email" className="fm-control">
                      <input
                        {...register("email")}
                        maxLength={100}
                        data-type="email"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("email") ? "error" : ""}
                        placeholder="อีเมล์"
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input type="tel" className="fm-control">
                      <input
                        {...register("mobile")}
                        maxLength={10}
                        data-type="number"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("mobile") ? "error" : ""}
                        placeholder="เบอร์โทร"
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input type="tel" className="fm-control">
                      <input
                        {...register("iden_no")}
                        maxLength={13}
                        data-type="number"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("iden_no") ? "error" : ""}
                        placeholder="เลขบัตรประชาชน"
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input type="tel" className="fm-control">
                      <input
                        {...register("amount")}
                        maxLength={10}
                        data-type="number"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("amount") ? "error" : ""}
                        placeholder="จำนวนเงิน"
                      />
                    </Input>
                  </div>
                  
                </div>
                <div className="col-fm-register col-12 col-md-12">
                  <div className="item-fm item-donation">
                    <div className="ui fitted toggle checkbox slider-checkbox">
                      <input type="checkbox" checked className="hidden"/><label></label>
                      <div className="slider-title">
                        <p>ต้องการลดหย่อนภาษี</p>
                      </div>
                    </div>
                    <div className="donation-time">
                      <p>กรอกข้อมูล ณ 2023-01-09 17:18:15</p>
                    </div>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-12">
                  <div className="item-fm">
                    <div className="donation-remark">
                      <p>การบริจาคนี้เข้าในโครงการ MedUMORE ของมูลนิธิคณะแพทยศาสตร์ จุฬาลงกรณ์</p>
                    </div>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-12">
                  <div className="item-fm donation-checkbox">
                    <div className="ui radio checkbox">
                      <input type="checkbox" checked className="hidden"/>
                      <label><span>ฉันยินยอมให้ผู้ที่เกี่ยวข้องเปิดเผยข้อมูลรายการนี้<br></br>ให้แก่กรมสรรพากร หรือผู้ที่เกี่ยวข้อง</span></label>
                    </div>
                  </div>
                </div>
                <div className="col-12 col-md-6 offset-md-3">
                  <button
                    id="send_payment_btn"
                    onClick={onSubmit}
                    className="btn-default btn-register"
                  >
                    <span>ยืนยัน</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer></Footer>
    </div>
  );
}

import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import { useState, useEffect, useContext } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";

import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import GroupCategory from "/themes/components/groupCategory";
import Image from "next/image";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/article/get/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["status"] == "false") {
    return {
      redirect: { destination: "/" },
    };
  }

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/article/" + params.id,
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      utoken,
      user,
    },
  };
}

export default function Article({
  seo_data,
  errorCode,
  data,
  params,
  utoken,
  user,
}) {
  // console.log(data);
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  const [articleData, setArticleData] = useState(null);
  const [reload, setReload] = useState(true);
  useEffect(() => {
    if (reload) {
      setReload(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/mdcu/article/relate/" +
          data.data.id,
        null,
        (data) => {
          setArticleData(data);
        }
      );
    }
  }, [reload]);

  appContext.setToken(utoken);
  if (errorCode) {
    return <Error statusCode={errorCode} />;
  }

  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        {/* <div className="news-banner-div">
          <Image className={'news-banner'} src={data["data"]["banner"]} layout="fill" />
        </div> */}
        <div className="background-section white news-detail">
          {/* body_news_detail */}
          
          {/* *********** */}
          {data && data["data"] && !appContext.isNull(data["data"]["image"]) ? (
            <div className="thumb_news_detail_div">
              <Image alt="" className={'thumb_news_detail'} src={data["data"]["image"]} layout="fill" />  
            </div>
          ) : null}
          {/* *********** */}
          <div
            className={`container custom-container space-between-content bg-white`}
          >
            <div className="news-detail-page">
              <h3>{data["data"]["title_th"]}</h3>
            </div>
          </div>
          <div
            className={`container custom-container space-between-content bg-white`}
          >
            <div className="news-detail-page">
              <div
                dangerouslySetInnerHTML={{ __html: data["data"]["details_th"] }}
              ></div>
            </div>
          </div>
          {/* body_news_detail */}
        </div>
        <div className="background-section news-detail">
          {articleData &&
          articleData["data"] &&
          articleData["data"].length > 0 ? (
            <GroupCategory
              type="news"
              name="ข่าวสารที่เกี่ยวข้อง"
              color="#6E953D"
              size="3.5"
              index="20"
              data={articleData["data"]}
            ></GroupCategory>
          ) : null}
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
}

import React from "react";
import Image from "next/image";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import Router, { useRouter } from "next/router";
import { getUser } from "../../api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import ReactPlayer from "react-player";
import Swal from "sweetalert2";
import stylesModal from "/public/assets/css/component/listQuizModal.module.css";
import styles from "/public/assets/css/pages/course.module.css";

import {
  Menu,
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
  Accordion,
  Tab,
  Popup,
} from "semantic-ui-react";
import "semantic-ui-css/semantic.min.css";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const params = context.query;
  const utoken = params.utoken;
  const user = await getUser(utoken);
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);
  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/course/get/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();
  if (data["status"] == "false" || data["data"]["type"] == "infographic" || data["data"]["type"] == "Ebook" || data["data"]["type"] == "live") {
    return {
      redirect: { destination: "/" },
    };
  }

  return {
    props: {
      session: await getSession(context),
      data,
      utoken,
    },
  };
}

export default function Course({
  data,
  utoken,
}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);
  const [errorArray, setErrorArray] = useState([]);
  const [epIndex, setEpIndex] = useState(parseInt(router.query.ep)||0);
  const [reloadVdo, setReloadVdo] = useState(true);
  const [showVideo, setShowVideo] = useState(false);
  const [vdoList, setVdoList] = useState(null);
  const [modalCert, setModalCert] = useState(false);
  const [modalCertShow, setModalCertShow] = useState(false);
  const [currentSec, setCurrentSec] = useState(0);
  const player = useRef();
  const [stampProgress, setStampProgress] = useState(null);
  const [answerPreTest, setAnswerPreTest] = useState([]);
  const [modalPreTest, setModalPreTest] = useState(false);
  const [lockAddPreTest, setLockAddPreTest] = useState(false);
  const [answer, setAnswer] = useState([]);
  const [modalQuizOpen, setModalQuizOpen] = useState(false);
  const [lockAddQuiz, setLockAddQuiz] = useState(false);
  const [modalResult, setModalResult] = useState(false);
  const [resultData, setResultData] = useState([]);
  const [assessmentStep, setAssessmentStep] = useState(1);
  const [assessmentData, setAssessmentData] = useState([]);
  const [modalAssessment, setModalAssessment] = useState(false);
  const [choice, setChoice] = useState([]);
  useEffect(() => {
    if (reloadVdo) {
      setReloadVdo(false);
      appContext.loadApiToken(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/course/vdo/" + data.data.id,
        {utoken:utoken},
        (videoData) => {
          setVdoList(videoData["data"]);
          setCurrentSec(videoData["data"][epIndex]["watching"]);
          if(videoData["data"][epIndex]["ep_oculus"]==1&&videoData["data"][epIndex]["pre_test"]["question"]&&
          videoData["data"][epIndex]["pre_test"]["question"].length>0&&videoData["data"][epIndex]["pre_test"]["allowed"]
          &&videoData["data"][epIndex]["pre_test"]["first_time"]&&!videoData["data"][epIndex]["pre_test"]["check"]&&
          videoData["data"][epIndex]["pre_test"]["is_show"]){
            selectPreTest(videoData["data"]);
            setShowVideo(false);
          }else{
            setShowVideo(true);
          }
        }
      );
    }
  }, [reloadVdo]);
  const checkMyError = (_val) => {
    if (errorArray.indexOf(_val) > -1) {
      return true;
    }
    return false;
  };
  function courseProgress(_data, _id, _last) {
    const formData = new URLSearchParams();
    formData.append("course_id", data["data"]["id"]);
    formData.append("lesson_id", _id);
    formData.append("watching_time", _data.playedSeconds);
    formData.append("utoken", utoken);
    appContext.sendApiToken(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseStamp",
      formData,
      (obj) => {
        if (obj["status"] == "success") {
          if(obj["unlocked_cert"]){
            setModalCertShow(true);
          }else{
            if(obj['open_assessment']){
              if(stampProgress==null || Math.floor(Date.now() / 1000) - stampProgress>=60){
                setStampProgress(Math.floor(Date.now() / 1000));
                selectAssessment();
              }
            }
          }
        }
      }
    );
  }
  function progressEnd(_id, _last) {
    appContext.loadApiToken(
      process.env.NEXT_PUBLIC_API +
        "/api/mdcu/course/vdo/" +
        data.data.id,
        {utoken:utoken},
      (res) => {
        if (
          res["data"][epIndex]["quiz"].question &&
          res["data"][epIndex]["quiz"].question.length > 0 &&
          res["data"][epIndex]["quiz"].allowed &&
          res["data"][epIndex]["quiz"].first_time &&
          !res["data"][epIndex]["quiz"].check
        ) {
          selectQuiz();
        }else{
          if (_last == "last") {
            if(modalCertShow){
              setModalCertShow(false);
              setModalCert(true);
            }
          }
        }
      }
    );
  }
  function selectAssessment() {
    const formQuiz = new URLSearchParams();
    formQuiz.append("course_id", data["data"]["id"]);
    formQuiz.append("utoken", utoken);
    appContext.sendApiToken(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/checkAssessment",
      formQuiz,
      (res_quiz) => {
        if (res_quiz["status"] == "success"&&res_quiz["allow"]&&res_quiz["data"]&&res_quiz["data"].length>0) {
          setAssessmentStep(1);
          setAssessmentData(res_quiz['data']);
          setShowVideo(false);
          setModalAssessment(true);
          var ch = [];
          for (var i = 0; i < res_quiz['data'].length; i++) {
            var ans = {
              course_id: data["data"]["id"],
              question_id: res_quiz['data'][i]["id"],
              question_type: res_quiz['data'][i]["type"],
              answer: "",
            };
            ch.push(ans);
          }
          var collection = document.getElementsByClassName("choice_input");
          for (var i = 0; i < collection.length; i++) {
            collection[i].checked = false;
          }
          var collection = document.getElementsByClassName("choice_textarea");
          for (var i = 0; i < collection.length; i++) {
            collection[i].value = "";
          }
          setChoice(ch);
        }
      }
    );
  }
  function onChoice(_key, _ans) {
    const updatedChoice = [...choice];
    updatedChoice[_key] = { ...updatedChoice[_key], answer: _ans };
    setChoice(updatedChoice);
  }
  function selectQuiz() {
    setShowVideo(false);
    selectChapter();
  }
  function selectChapter() {
    var obj = [];
    for (var i = 0; i < vdoList[epIndex]["quiz"]["question"].length; i++) {
      var ans = {
        course_id: data["data"]["id"],
        lesson_id: vdoList[epIndex]["id"],
        question_id: vdoList[epIndex]["quiz"]["question"][i]["id"],
        question_type: vdoList[epIndex]["quiz"]["question"][i]["type"],
        answer: "",
      };
      obj.push(ans);
    }
    var collection = document.getElementsByClassName("answer_input");
    for (var i = 0; i < collection.length; i++) {
      collection[i].checked = false;
    }
    var collection = document.getElementsByClassName("answer_textarea");
    for (var i = 0; i < collection.length; i++) {
      collection[i].value = "";
    }
    setAnswer(obj);
    setModalQuizOpen(true);
  }
  function onAnswer(_key, _ans) {
    const updatedAnswer = [...answer];
    updatedAnswer[_key] = { ...updatedAnswer[_key], answer: _ans };
    setAnswer(updatedAnswer);
  }
  function onAssessmentNext() {
    if(choice.length>0 && !appContext.isNull(choice[0]["answer"])){
      setAssessmentStep(2);
    }else{
      Swal.fire({
        text: translateEng('กรุณาตอบคำถามให้ครบถ้วน'),
        icon: "error",
        confirmButtonText: translateEng('ปิด'),
        confirmButtonColor: "#648d2f"
      });
    }
  }
  function onAssessment() {
    let bol = true;
    var errarr = [];
    setErrorArray(errarr);
    if (bol) {
      const formData = new URLSearchParams();
      formData.append("answer_data", JSON.stringify(choice));
      formData.append("utoken", utoken);
      appContext.sendApiToken(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addAssessment",
        formData,
        (obj) => {
          if (obj["status"] == "success") {
            setModalAssessment(false);
            Swal.fire({
              text: translateEng('ส่งคำตอบสำเร็จ'),
              icon: "success",
              confirmButtonText: translateEng('ปิด'),
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              setShowVideo(true);
            });
          }
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาตอบคำถามให้ครบถ้วน'),
        icon: "error",
        confirmButtonText: translateEng('ปิด'),
        confirmButtonColor: "#648d2f"
      });
    }
  }
  const previewFile = (_key, event) => {
    event.preventDefault();
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      function () {
        if (_.startsWith(reader.result, "data:")) {
          appContext.loadApiToken(
            process.env.NEXT_PUBLIC_API + "/api/image/save",
            { image: reader.result, utoken: utoken },
            (resData) => {
              if (resData["status"] == "true") {
                onAnswer(_key,resData["path"]);
              }
            }
          );
        }
      },
      false
    );
    if (file) {
      reader.readAsDataURL(file);
    }
  };
  function onSubmit(_key) {
    let bol = true;
    var errarr = [];
    for (var i = 0; i < answer.length; i++) {
      if (appContext.isNull(answer[i]["answer"])) {
        bol = false;
        errarr.push(answer[i]["question_id"]);
      }
    }
    setErrorArray(errarr);
    if (bol && !lockAddQuiz) {
      setLockAddQuiz(true);
      const formData = new URLSearchParams();
      formData.append("answer_data", JSON.stringify(answer));
      formData.append("utoken", utoken);
      appContext.sendApiToken(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addQuiz",
        formData,
        (obj) => {
          if (obj["status"] == "success") {
            setLockAddQuiz(false);
            setAnswer([]);
            setModalQuizOpen(false);
            Swal.fire({
              text: translateEng('ส่งคำตอบสำเร็จ'),
              icon: "success",
              confirmButtonText: translateEng('ปิด'),
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              setShowVideo(true);
              if(obj['result_log']){
                showResult(obj['result_log']['result']);
              }
              if(obj["unlocked_cert"]){
                setModalCertShow(false);
                setModalCert(true);
              }
            });
          }
        }
      );
    } else {
      if(!lockAddQuiz){
        Swal.fire({
          text: translateEng('กรุณาตอบคำถามให้ครบถ้วน'),
          icon: "error",
          confirmButtonText: translateEng('ปิด'),
          confirmButtonColor: "#648d2f"
        });
      }
    }
  }
  function selectPreTest(vdoData){
    selectChapterPre(vdoData);
    const formData = new URLSearchParams();
    formData.append("lesson_id", vdoData[epIndex]["id"]);
    formData.append("utoken", utoken);
    appContext.sendApiToken(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/preTestSkip",
      formData,
      (pin_res) => {
      }
    );
  }
  function selectChapterPre(vdoData) {
    var obj = [];
    for (var i = 0; i < vdoData[epIndex]["pre_test"]["question"].length; i++) {
      var ans = {
        course_id: data["data"]["id"],
        lesson_id: vdoData[epIndex]["id"],
        question_id: vdoData[epIndex]["pre_test"]["question"][i]["id"],
        question_type: vdoData[epIndex]["pre_test"]["question"][i]["type"],
        answer: "",
      };
      obj.push(ans);
    }
    var collection = document.getElementsByClassName("answer_input");
    for (var i = 0; i < collection.length; i++) {
      collection[i].checked = false;
    }
    var collection = document.getElementsByClassName("answer_textarea");
    for (var i = 0; i < collection.length; i++) {
      collection[i].value = "";
    }
    setAnswerPreTest(obj);
    setModalPreTest(true);  
  }
  function onAnswerPreTest(_key, _ans) {
    const updatedAnswerPreTest = [...answerPreTest];
    updatedAnswerPreTest[_key] = { ...updatedAnswerPreTest[_key], answer: _ans };
    setAnswerPreTest(updatedAnswerPreTest);
  }
  const previewFilePreTest = (_key, event) => {
    event.preventDefault();
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      function () {
        if (_.startsWith(reader.result, "data:")) {
          appContext.loadApiToken(
            process.env.NEXT_PUBLIC_API + "/api/image/save",
            { image: reader.result,utoken:utoken },
            (resData) => {
              if (resData["status"] == "true") {
                onAnswerPreTest(_key,resData["path"]);
              }
            }
          );
        }
      },
      false
    );
    if (file) {
      reader.readAsDataURL(file);
    }
  };
  function onSubmitPreTest(_key) {
    let bol = true;
    var errarr = [];
    for (var i = 0; i < answerPreTest.length; i++) {
      if (appContext.isNull(answerPreTest[i]["answer"])) {
        bol = false;
        errarr.push(answerPreTest[i]["question_id"]);
      }
    }
    setErrorArray(errarr);
    if (bol && !lockAddPreTest) {
      setLockAddPreTest(true);
      const formData = new URLSearchParams();
      formData.append("answer_data", JSON.stringify(answerPreTest));
      formData.append("utoken", utoken);
      appContext.sendApiToken(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addPreTest",
        formData,
        (obj) => {
          if (obj["status"] == "success") {
            setLockAddPreTest(false);
            setAnswerPreTest([]);
            setModalPreTest(false);
            Swal.fire({
              text: translateEng('ส่งคำตอบสำเร็จ'),
              icon: "success",
              confirmButtonText: translateEng('ปิด'),
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              setShowVideo(true);
            });
          }
        }
      );
    } else {
      if(!lockAddPreTest){
        Swal.fire({
          text: translateEng('กรุณาตอบคำถามให้ครบถ้วน'),
          icon: "error",
          confirmButtonText: translateEng('ปิด'),
          confirmButtonColor: "#648d2f"
        });
      }
    }
  }
  function showResult(_data){
    setResultData(_data);
    setModalResult(true);
  }
  function translateEng(_value) {
    if(lang=='en'){
      if(_value=='ฟรี'){
        return "Free";
      }else if(_value=='รับฟรี'){
        return "Get Free";
      }else if(_value=='ซื้อ Ticket'){
        return "Buy Ticket";
      }else if(_value=='บาท'){
        return "Baht";
      }else if(_value=='คะแนน'){
        return "Points";
      }else if(_value=='ปกติ'){
        return "Normal";
      }else if(_value=='ราคา'){
        return "Price";
      }else if(_value=='สถิติ'){
        return "Statistics";
      }else if(_value=='ภาพรวมการทำแบบทดสอบ'){
        return "Examination overview";
      }else if(_value=='แบบทดสอบทั้งหมด'){
        return "All Examinations";
      }else if(_value=='ชุด'){
        return "Sets";
      }else if(_value=='ทำแล้ว'){
        return "Done";
      }else if(_value=='ผ่าน'){
        return "Pass";
      }else if(_value=='ไม่ผ่าน'){
        return "Fail";
      }else if(_value=='ยังไม่ได้ทำ'){
        return "Not yet";
      }else if(_value=='เฉลี่ยคะแนนที่ทำได้'){
        return "Average score";
      }else if(_value=='คะแนนสูงสุด'){
        return "Highest score";
      }else if(_value=='คะแนนที่ทำได้'){
        return "Score";
      }else if(_value=='คะแนนต่ำสุด'){
        return "Lowest score";
      }else if(_value=='ผลคะแนน'){
        return "Result";
      }else if(_value=='อันดับของคุณ จากผู้ทำแบบทดสอบทั้งหมด'){
        return "Your ranking";
      }else if(_value=='อันดับ'){
        return "Ranking";
      }else if(_value=='ชื่อ'){
        return "Name";
      }else if(_value=='วันที่ทำ'){
        return "Date";
      }else if(_value=='วันที่'){
        return "Date";
      }else if(_value=='รอการตรวจสอบ'){
        return "Pending";
      }else if(_value=='ประวัติการทำแบบทดสอบหลังเรียน'){
        return "Post-Examination history";
      }else if(_value=='ประวัติการทำแบบทดสอบก่อนเรียน'){
        return "Pre-Examination history";
      }else if(_value=='ทดสอบครั้งที่'){
        return "Round";
      }else if(_value=='ชื่อแบบทดสอบ'){
        return "Examination Title";
      }else if(_value=='สถานะ'){
        return "Status";
      }else if(_value=='ผลการทดสอบ'){
        return "Result";
      }else if(_value=='ตรวจแล้ว'){
        return "Checked";
      }else if(_value=='ส่งคำตอบสำเร็จ'){
        return "Submitted";
      }else if(_value=='ปิด'){
        return "Close";
      }else if(_value=='กรุณาตอบคำถามให้ครบถ้วน'){
        return "Please answer questions completely";
      }else if(_value=='คุณต้องการเริ่มเรียนใช่หรือไม่?'){
        return "Do you want to start studying?";
      }else if(_value=='ใช่'){
        return "Yes";
      }else if(_value=='ไม่'){
        return "No";
      }else if(_value=='กรุณาซื้อคอร์สนี้ก่อนค่ะ'){
        return "Please buy this course";
      }else if(_value=='คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'){
        return "You have another item in your cart<br>Want to buy this product?";
      }else if(_value=='ยืนยัน'){
        return "Submit";
      }else if(_value=='ยกเลิก'){
        return "Cancel";
      }else if(_value=='พบข้อผิดพลาด กรุณาลองอีกครั้ง'){
        return "Found an error, please try again";
      }else if(_value=='กรุณาเข้าสู่ระบบค่ะ'){
        return "Please login";
      }else if(_value=='กรุณาเลือกเพลย์ลิสต์'){
        return "Choose playlist";
      }else if(_value=='ยินดีด้วย ใช้โค้ดส่วนลดสำเร็จ'){
        return "Congratulations, the discount code has been used successfully";
      }else if(_value=='ยินดีด้วย คุณได้รับการสนับสนุน'){
        return "Congratulations, you are supported";
      }else if(_value=='ขออภัย ไม่พบโค้ดส่วนลด'){
        return "Sorry, the discount code could not be found";
      }else if(_value=='ขออภัย ไม่พบผู้สนับสนุน'){
        return "Sorry, the supporter could not be found";
      }else if(_value=='บทเรียนทั้งหมด'){
        return "All lessons";
      }else if(_value=='เอกสารประกอบการเรียน'){
        return "Documents";
      }else if(_value=='ดาวน์โหลดไฟล์ในบทเรียน'){
        return "Download";
      }else if(_value=='รายละเอียด'){
        return "Details";
      }else if(_value=='ถาม-ตอบ'){
        return "Q & A";
      }else if(_value=='แบบทดสอบ'){
        return "Examination";
      }else if(_value=='คะแนนล่าสุดของคุณ'){
        return "Recently score";
      }else if(_value=='เริ่มใหม่'){
        return "Restart";
      }else if(_value=='แบบทดสอบของคุณอยู่ระหว่างตรวจสอบ'){
        return "Your test is under review";
      }else if(_value=='โค้ดส่วนลด'){
        return "Discount code";
      }else if(_value=='เลือกรับ Scholarship จากผู้สนับสนุน'){
        return "Select scholarship";
      }else if(_value=='เพิ่มไปยังเพลย์ลิสต์ของคุณ'){
        return "Add to playlist";
      }else if(_value=='สร้างเพลย์ลิสต์'){
        return "Create playlist";
      }else if(_value=='เพลย์ลิสต์ของคุณ'){
        return "Your playlist";
      }else if(_value=='ป้อนชื่อเพลย์ลิสต์'){
        return "Enter plalist title";
      }else if(_value=='เริ่มเรียน'){
        return "Start learning";
      }else if(_value=='ซื้อคอร์สนี้'){
        return "Buy this course";
      }else if(_value=='คอร์สเกี่ยวข้อง'){
        return "Related course";
      }else if(_value=='ข้อที่'){
        return "No.";
      }else if(_value=='ส่งคำตอบ'){
        return "Submit answer";
      }else if(_value=='ข้าม'){
        return "Skip";
      }else if(_value=='ต่อไป'){
        return "Next";
      }else if(_value=='แบบประเมินความพึงพอใจ (ใช้เวลาไม่ถึง 1 นาที)'){
        return "Assessment (It takes less than 1 minute.)";
      }else if(_value=='คำตอบ'){
        return "Answer";
      }else if(_value=='คำตอบของท่าน'){
        return "Your answer";
      }else if(_value=='คำตอบที่ถูก'){
        return "Correct";
      }else if(_value=='คำอธิบาย'){
        return "Explain";
      }else if(_value=='ดูคำตอบ'){
        return "View";
      }else if(_value=='ยินดีด้วย!'){
        return "Congratulations!";
      }else if(_value=='คุณปลดล็อกใบประกาศ'){
        return "You unlocked the certificate";
      }else if(_value=='สามารถดาวน์โหลดใบประกาศได้ที่โปรไฟล์ของท่าน'){
        return "Certificate can be download at your profile";
      }else if(_value=='หรือคลิกที่นี่'){
        return "Or click here";
      }else if(_value=='คอร์ส'){
        return "Course";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  return (
    <>
      <style>{`
        #onetrust-consent-sdk{
          display:none!important;
        }
        body{
          background:black!important;
        }
        .icon-ic-lock{
          color:white!important;
        }
      `}</style>
      {showVideo && data && vdoList && vdoList.length > 0 && vdoList.length >= (epIndex+1) ? (
        <div className="modal-video-block full-screen">
          {vdoList[epIndex]["lock"] ? (
            <div className="video-lock">
              <ReactPlayer
                playsinline
                className="video-player"
                width={680}
                height={Number(680 / (16 / 9))}
              />
              <i className={`icon-ic-lock`}></i>
            </div>
          ):(
            <ReactPlayer
              playsinline
              ref={player}
              playing
              muted
              className="video-player"
              url={vdoList[epIndex]["vdo"]}
              controls={true}
              width={680}
              height={Number(680 / (16 / 9))}
              onDuration={() => {
                player.current.seekTo(currentSec,'seconds');
              }}
              progressInterval={3000}
              onProgress={(progress) => {
                if(epIndex == vdoList.length - 1){
                  courseProgress(progress,vdoList[epIndex]["id"],'last');
                }else{
                  courseProgress(progress,vdoList[epIndex]["id"],'normal');
                }
                setCurrentSec(progress.playedSeconds);
              }}
              onEnded={() => {
                if(epIndex == vdoList.length - 1){
                  progressEnd(vdoList[epIndex]["id"],'last');
                }else{
                  progressEnd(vdoList[epIndex]["id"],'normal');
                }
              }}
            />
          )}
          {vdoList[epIndex]["skip_intro"] == 1 && vdoList[epIndex]["skip_sec"] != 0 
          && currentSec < vdoList[epIndex]["skip_sec"] ?(
            <div className="video-skip-intro">
              <Button onClick={() => {
                player.current.seekTo(vdoList[epIndex]["skip_sec"],'seconds');
                setCurrentSec(vdoList[epIndex]["skip_sec"]);
              }}>
                ข้าม intro
              </Button>
            </div>
          ):null}
        </div>
      ) : null}
      {modalPreTest && data && vdoList && vdoList.length > 0 && vdoList.length >= (epIndex+1) ? (
        <Modal
          className={`${stylesModal.modalVdo}`}
          onClose={() => {setModalPreTest(false);setShowVideo(true);}}
          open={true}
        >
          <Modal.Content className={`${stylesModal.modaVdoContent}`}>
            {vdoList[epIndex]["pre_test"].question.map((val_q, key_q) => (
              <div className="block-quiz" key={key_q}>
                <div className="i-title-quiz">
                  <h3>
                    <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question}
                  </h3>
                  {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                    <div className={styles.questionImageDiv}>
                      <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                    </div>
                  ):null}
                </div>
                {val_q.type == "choice" ? (
                  <div className="i-choose-quiz-row row">
                    {val_q.answer.map((val_ans, key_ans) => (
                      <div
                        className="col-12 col-md-6 col-choose-quiz"
                        key={key_ans}
                      >
                        <div
                          className="item-choose"
                          onClick={() => onAnswerPreTest(key_q, val_ans.id)}
                        >
                          <div className="fm-check">
                            <input
                              className="answer_input"
                              type="radio"
                              name={`answer_${key_q}`}
                            />
                            <div className="text">
                              <div className="i_remark">
                                <i className="icon-ic-circle" />
                              </div>
                              <p>{val_ans.answer}</p>
                            </div>
                            {val_ans && val_ans.image && val_ans.image!=null&&val_ans.image!=''&&val_ans.image!='null' ?(
                              <div className={styles.answerImageDiv}>
                                <Image alt="" layout="fill" className="answer_image" src={val_ans.image} />
                              </div>
                            ):null}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : val_q.type == "text" ? (
                  <div className="i-choose-quiz-row row">
                    <div className="col-12 col-md-12 col-choose-quiz">
                      <div className="item-fm">
                        <TextArea
                          placeholder=""
                          className={
                            "fm-control answer_textarea " +
                            (checkMyError(val_q.id) ? "error" : "")
                          }
                          data-type="textaddress"
                          onInput={appContext.diFormPattern}
                          onChange={(event) =>
                            onAnswerPreTest(key_q, event.target.value)
                          }
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="i-choose-quiz-row row">
                    <div className="col-12 col-md-12 col-choose-quiz">
                      <div className="item-fm">
                        <input
                          className="answer_textarea"
                          type="file"
                          accept="image/jpeg,image/jpg,image/png,image/gif,image/xbm,image/xpm,image/wbmp,image/webp,image/bmp"
                          onChange={(e) => {
                            previewFilePreTest(key_q, e);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
            <div className="block-to-buy-course">
              <button
                className="btn-to-buy-course left"
                onClick={() => onSubmitPreTest(epIndex)}
              >
                <span>{translateEng('ส่งคำตอบ')}</span>
              </button>
            </div>
          </Modal.Content>
        </Modal>
      ) : null}
      {modalQuizOpen && data && vdoList && vdoList.length > 0 && vdoList.length >= (epIndex+1) ? (
        <Modal
          className={`${stylesModal.modalVdo}`}
          onClose={() => {setModalQuizOpen(false);setShowVideo(true);}}
          open={true}
        >
          <Modal.Content className={`${stylesModal.modaVdoContent}`}>
            {vdoList[epIndex]["quiz"].question.map((val_q, key_q) => (
              <div className="block-quiz" key={key_q}>
                <div className="i-title-quiz">
                  <h3>
                    <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question}
                  </h3>
                  {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                    <div className={styles.questionImageDiv}>
                      <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                    </div>
                  ):null}
                </div>
                {val_q.type == "choice" ? (
                  <div className="i-choose-quiz-row row">
                    {val_q.answer.map((val_ans, key_ans) => (
                      <div
                        className="col-12 col-md-6 col-choose-quiz"
                        key={key_ans}
                      >
                        <div
                          className="item-choose"
                          onClick={() => onAnswer(key_q, val_ans.id)}
                        >
                          <div className="fm-check">
                            <input
                              className="answer_input"
                              type="radio"
                              name={`answer_${key_q}`}
                            />
                            <div className="text">
                              <div className="i_remark">
                                <i className="icon-ic-circle" />
                              </div>
                              <p>{val_ans.answer}</p>
                            </div>
                            {val_ans && val_ans.image && val_ans.image!=null&&val_ans.image!=''&&val_ans.image!='null' ?(
                              <div className={styles.answerImageDiv}>
                                <Image alt="" layout="fill" className="answer_image" src={val_ans.image} />
                              </div>
                            ):null}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : val_q.type == "text" ? (
                  <div className="i-choose-quiz-row row">
                    <div className="col-12 col-md-12 col-choose-quiz">
                      <div className="item-fm">
                        <TextArea
                          placeholder=""
                          className={
                            "fm-control answer_textarea " +
                            (checkMyError(val_q.id) ? "error" : "")
                          }
                          data-type="textaddress"
                          onInput={appContext.diFormPattern}
                          onChange={(event) =>
                            onAnswer(key_q, event.target.value)
                          }
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="i-choose-quiz-row row">
                    <div className="col-12 col-md-12 col-choose-quiz">
                      <div className="item-fm">
                        <input
                          className="answer_textarea"
                          type="file"
                          accept="image/jpeg,image/jpg,image/png,image/gif,image/xbm,image/xpm,image/wbmp,image/webp,image/bmp"
                          onChange={(e) => {
                            previewFile(key_q, e);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
            <div className="block-to-buy-course">
              <button
                className="btn-to-buy-course left"
                onClick={() => onSubmit(epIndex)}
              >
                <span>{translateEng('ส่งคำตอบ')}</span>
              </button>
            </div>
          </Modal.Content>
        </Modal>
      ) : null}
      {modalResult && resultData &&  resultData.length > 0 ? (
        <Modal
          className={`${stylesModal.modalVdo}`}
          onClose={() => setModalResult(false)}
          open={true}
        >
          <Modal.Content className={`${stylesModal.modaVdoContent}`}>
            {resultData.map((val_q, key_q) => (
              <div className="block-quiz-border" key={key_q}>
                <div className="block-quiz">
                  <div className="i-title-quiz">
                    <h3>
                      <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                    </h3>
                    {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                      <div className={styles.questionImageDiv}>
                        <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                      </div>
                    ):null}
                  </div>
                </div>
                <div className="block-quiz">
                  <div className="i-title-quiz">
                    {val_q.exam_type==1 ? (
                      val_q.is_answer==1 ? (
                        <>
                          <h3 className="result">
                            <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title}
                          </h3>
                          {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                            <div className={styles.answerImageResultDiv}>
                              <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                            </div>
                          ):null}
                        </>
                      ):(
                        val_q.is_right==1 ? (
                          <>
                            <h3 className="result true">
                              <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title} <i className="icon-ic-tick"></i>
                            </h3>
                            {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                              <div className={styles.answerImageResultDiv}>
                                <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                              </div>
                            ):null}
                            {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                              <h3
                                className="description true"
                                dangerouslySetInnerHTML={{
                                  __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                }}
                              ></h3>
                            ):null}
                          </>
                        ):(
                          <>
                            <h3 className="result false">
                              <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title} <i className="icon-ic-close"></i>
                            </h3>
                            {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                              <div className={styles.answerImageResultDiv}>
                                <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                              </div>
                            ):null}
                            <h3 className="description-true true">
                              <span>{translateEng('คำตอบที่ถูก')} :</span> {val_q.true_answer}
                            </h3>
                            {val_q && val_q.true_answer_image && val_q.true_answer_image!=null&&val_q.true_answer_image!=''&&val_q.true_answer_image!='null' ?(
                              <div className={styles.answerImageResultDiv}>
                                <Image alt="" layout="fill" className="answer_image_result" src={val_q.true_answer_image} />
                              </div>
                            ):null}
                            {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                              <h3
                                className="description true"
                                dangerouslySetInnerHTML={{
                                  __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                }}
                              ></h3>
                            ):null}
                          </>
                        )
                      )
                    ):(
                      val_q.exam_type==2 ? (
                        val_q.is_answer==1 ? (
                          <h3 className="result">
                            <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_text}
                          </h3>
                        ):(
                          <>
                            <h3 className="result">
                              <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_text}
                            </h3>
                            {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                              <h3
                                className="description true"
                                dangerouslySetInnerHTML={{
                                  __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                }}
                              ></h3>
                            ):null}
                          </>
                        )
                      ):(
                        val_q.is_answer==1 ? (
                          <h3 className="result">
                            <span>{translateEng('คำตอบของท่าน')} :</span> <a href={val_q.file} target="_blank" rel="noopener noreferrer">{translateEng('ดูคำตอบ')}</a>
                          </h3>
                        ):(
                          <>
                            <h3 className="result">
                              <span>{translateEng('คำตอบของท่าน')} :</span> <a href={val_q.file} target="_blank" rel="noopener noreferrer">{translateEng('ดูคำตอบ')}</a>
                            </h3>
                            {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                              <h3
                                className="description true"
                                dangerouslySetInnerHTML={{
                                  __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                }}
                              ></h3>
                            ):null}
                          </>
                        )
                      )
                    )}
                  </div>
                </div>
              </div>
            ))}
          </Modal.Content>
        </Modal>
      ) : null}
      {modalCert ? (
        <Modal
          className={`modal-unlock-cert`}
          onClose={() => setModalCert(false)}
          open={true}
        >
          <Modal.Content className={`inner`}>
            <div className="block-unlock-cert">
              <div className="left">
                <div className={styles.unlockCertIconDiv}>
                  <Image alt="" layout="intrinsic" src="/assets/images/unlock_cert.png" width={500} height={522} />
                </div>
              </div>
              <div className="right">
                <h3>{translateEng('ยินดีด้วย!')}</h3>
                <h2>{translateEng('คุณปลดล็อกใบประกาศ')}</h2>
                <p>{translateEng('คอร์ส')} {data["data"]["title"]}</p>
                <div className="curve">
                  <span>{translateEng('สามารถดาวน์โหลดใบประกาศได้ที่โปรไฟล์ของท่าน')}</span>
                </div>
              </div>
            </div>
          </Modal.Content>
        </Modal>
      ) : null}
      {modalAssessment && data && assessmentData && assessmentData.length > 0 ? (
        <Modal
          className={`${stylesModal.modalVdo}`}
          onClose={() => {setModalAssessment(false);setShowVideo(true);}}
          open={true}
        >
          <Modal.Content className={`${stylesModal.modaVdoContent}`}>
            <div className="block-quiz">
              <div className="i-title-quiz">
                <h2>
                  {translateEng('แบบประเมินความพึงพอใจ (ใช้เวลาไม่ถึง 1 นาที)')}
                </h2>
              </div>
            </div>
            {assessmentData.map((val_q, key_q) => (
              key_q==0 ? (
                assessmentStep == 1 ?(
                  <div className={`block-quiz assessment`} key={key_q}>
                    <div className="i-title-quiz">
                      <h3>
                        <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                      </h3>
                    </div>
                    {val_q.type == 1 ? (
                      <div className="i-choose-quiz-row row">
                        {val_q.choice.map((val_ans, key_ans) => (
                          <div
                            className="col-12 col-md-6 col-choose-quiz"
                            key={key_ans}
                          >
                            <div
                              className="item-choose"
                              onClick={() => onChoice(key_q, val_ans.id)}
                            >
                              <div className="fm-check">
                                <input
                                  className="choice_input"
                                  type="radio"
                                  name={`choice_${key_q}`}
                                />
                                <div className="text">
                                  <div className="i_remark">
                                    <i className="icon-ic-circle" />
                                  </div>
                                  <p>{val_ans.choice_th}</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      val_q.type == 3 ? (
                        <div className="i-choose-quiz-row row">
                          {val_q.choice.map((val_ans, key_ans) => (
                            <div
                              className="col-4 col-md-2 col-choose-quiz"
                              key={key_ans}
                            >
                              <div
                                className="item-choose"
                                onClick={() => onChoice(key_q, val_ans.id)}
                              >
                                <div className="fm-check">
                                  <input
                                    className="choice_input"
                                    type="radio"
                                    name={`choice_${key_q}`}
                                  />
                                  <div className="text with-icon">
                                    {val_ans && val_ans.icon && val_ans.icon!=null && val_ans.icon!='' && val_ans.icon!='null' ? (
                                      <div className={styles.assessmentIconDiv}>
                                        <Image alt="" layout="intrinsic" className={`${styles.iconThumb}`} src={val_ans.icon} width={500} height={500} />
                                      </div>
                                    ):null}
                                    <div className="i_remark">
                                      <i className="icon-ic-circle" />
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <TextArea
                                placeholder=""
                                className={
                                  "fm-control choice_textarea " +
                                  (checkMyError(val_q.id) ? "error" : "")
                                }
                                data-type="textaddress"
                                onInput={appContext.diFormPattern}
                                onChange={(event) =>
                                  onChoice(key_q, event.target.value)
                                }
                              />
                            </div>
                          </div>
                        </div>
                      )
                    )}
                  </div>
                ):null
              ):(
                assessmentStep == 2 ?(
                  <div className={`block-quiz assessment`} key={key_q}>
                    <div className="i-title-quiz">
                      <h3>
                        <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                      </h3>
                    </div>
                    {val_q.type == 1 ? (
                      <div className="i-choose-quiz-row row">
                        {val_q.choice.map((val_ans, key_ans) => (
                          <div
                            className="col-12 col-md-6 col-choose-quiz"
                            key={key_ans}
                          >
                            <div
                              className="item-choose"
                              onClick={() => onChoice(key_q, val_ans.id)}
                            >
                              <div className="fm-check">
                                <input
                                  className="choice_input"
                                  type="radio"
                                  name={`choice_${key_q}`}
                                />
                                <div className="text">
                                  <div className="i_remark">
                                    <i className="icon-ic-circle" />
                                  </div>
                                  <p>{val_ans.choice_th}</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      val_q.type == 3 ? (
                        <div className="i-choose-quiz-row row">
                          {val_q.choice.map((val_ans, key_ans) => (
                            <div
                              className="col-4 col-md-2 col-choose-quiz"
                              key={key_ans}
                            >
                              <div
                                className="item-choose"
                                onClick={() => onChoice(key_q, val_ans.id)}
                              >
                                <div className="fm-check">
                                  <input
                                    className="choice_input"
                                    type="radio"
                                    name={`choice_${key_q}`}
                                  />
                                  <div className="text with-icon">
                                    {val_ans && val_ans.icon && val_ans.icon!=null && val_ans.icon!='' && val_ans.icon!='null' ? (
                                      <div className={styles.assessmentIconDiv}>
                                        <Image alt="" layout="intrinsic" className={`${styles.iconThumb}`} src={val_ans.icon} width={500} height={500} />
                                      </div>
                                    ):null}
                                    <div className="i_remark">
                                      <i className="icon-ic-circle" />
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <TextArea
                                placeholder=""
                                className={
                                  "fm-control choice_textarea " +
                                  (checkMyError(val_q.id) ? "error" : "")
                                }
                                data-type="textaddress"
                                onInput={appContext.diFormPattern}
                                onChange={(event) =>
                                  onChoice(key_q, event.target.value)
                                }
                              />
                            </div>
                          </div>
                        </div>
                      )
                    )}
                  </div>
                ):null
              )
            ))}
            {assessmentStep == 1 ?(
              <div className="block-to-buy-course assessment-group-btn">
                <button
                  className="btn-to-buy-course left"
                  onClick={() => {setModalAssessment(false);setShowVideo(true);}}
                >
                  <span>{translateEng('ข้าม')}</span>
                </button>
                <button
                  className="btn-to-buy-course right"
                  onClick={() => onAssessmentNext()}
                >
                  <span>{translateEng('ต่อไป')}</span>
                </button>
              </div>
            ):(
              <div className="block-to-buy-course">
                <button
                  className="btn-to-buy-course left"
                  onClick={() => onAssessment()}
                >
                  <span>{translateEng('ส่งคำตอบ')}</span>
                </button>
              </div>
            )}
          </Modal.Content>
        </Modal>
      ) : null}
    </>
  );
}

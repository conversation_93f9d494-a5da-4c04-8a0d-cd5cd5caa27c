import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import { useForm } from "react-hook-form";
import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import CourseDetailIntro from "/themes/components/courseDetailIntro";
import CourseDetailDescription from "/themes/components/courseDetailDescription";
import CourseDetailPrice from "/themes/components/courseDetailPrice";
import CourseDetailCode from "/themes/components/courseDetailCode";
import CourseDetailScholarship from "/themes/components/courseDetailScholarship";
import GroupCategory from "/themes/components/groupCategory";
import CommentZone from "/themes/components/commentZone";
import ListVdoModal from "/themes/components/listVdoModal";
import GroupAudioList from "/themes/components/groupAudioList";
import ListVdoEp from "/themes/components/listVdoEp";
import CardVideo from "/themes/components/cardVideo";
import RankingViewHistory from "/themes/components/rankingViewHistory";
import ReactPlayer from "react-player";
import NumberFormat from "react-number-format";
import Image from "next/image";
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import { Pie } from "react-chartjs-2";
ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);
import {
  Menu,
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
  Accordion,
  Tab,
} from "semantic-ui-react";
function show_add_list() {
  document.getElementById("fmCreateListAdd").classList.toggle("active");
}
import "semantic-ui-css/semantic.min.css";
import stylesModal from "/public/assets/css/component/listQuizModal.module.css";
import styles from "/public/assets/css/pages/course.module.css";
import Swal from "sweetalert2";

import moment from "moment";
import Link from "next/link";
// import { gsap } from "gsap";
// import { ScrollTrigger } from "gsap/dist/ScrollTrigger";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/course/get/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();
  if (data["status"] == "false" || data["data"]["type"] != "infographic") {
    return {
      redirect: { destination: "/" },
    };
  }

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/course/" + params.id,
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  //View Log
  const formDataView = new URLSearchParams();
  formDataView.append("utoken", utoken);
  formDataView.append("course_id", data.data.id);

  const res_view = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseView",
    {
      body: formDataView,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCodeView = res_view.ok ? false : res_view.statusCode;
  const data_view = await res_view.json();
  //View Log

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      utoken,
      user,
    },
  };
}

export default function Course({
  seo_data,
  errorCode,
  data,
  params,
  utoken,
  user,
}) {
  // console.log(data);
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  const [lockAddQuiz, setLockAddQuiz] = useState(false);
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [modalVdoOpen, setModalVdoOpen] = useState(false);
  const [modalQuizOpen, setModalQuizOpen] = useState(false);
  const [modalVdoAudio, setModalVdoAudio] = useState(false);
  const [keySelect, setKeySelect] = useState(0);
  const [quizSelect, setQuizSelect] = useState(0);
  const [courseData, setCourseData] = useState(null);
  const [playAudio, setPlayAudio] = useState(false);
  const [discountCode, setDiscountCode] = useState("");
  const [discountChannel, setDiscountChannel] = useState("");
  const [courseProPrice, setCourseProPrice] = useState(
    data["data"]["pro_price"]
  );
  const [coursePrice, setCoursePrice] = useState(data["data"]["price"]);
  const [coursePriceReload, setCoursePriceReload] = useState(true);
  const [reloadComment, setReloadComment] = useState(true);
  const [commentData, setCommentData] = useState(null);
  const [reloadStar, setReloadStar] = useState(true);
  const [reloadQuizBox, setReloadQuizBox] = useState(true);

  const [reloadQuiz, setReloadQuiz] = useState(true);
  const [quizData, setQuizData] = useState([]);
  const [reloadRank, setReloadRank] = useState(false);
  const [answer, setAnswer] = useState([]);
  const [stateIndex, setStateIndex] = useState({ activeIndexs: [] });
  const [errorArray, setErrorArray] = useState([]);
  const [scroll, setScroll] = useState(false);
  const spaceMove = useRef(null);
  const fixBuy = useRef(null);
  const audioRef = React.useRef();
  const [playerEnd, setPlayerEnd] = useState(false);
  const [offset, setOffset] = useState(0);
  const [reloadVdo, setReloadVdo] = useState(true);
  const [vdoList, setVdoList] = useState(null);

  const [addPlaylist, setAddPlaylist] = useState(false);
  const [courseId, setCourseId] = useState(null);
  const [playlistTitle, setPlaylistTitle] = useState(null);
  const [playlistSelect, setPlaylistSelect] = useState(null);
  const [playListOptions, setPlayListOptions] = useState([]);

  const [quickResult, setQuickResult] = useState([]);

  useEffect(() => {
    if (
      data["data"]["type"] != "infographic" &&
      data["data"]["type"] != "podcast"
    ) {
      // update
      const el = spaceMove.current;
      const elHight = spaceMove.current.offsetHeight;
      const topBuy = el.getBoundingClientRect().bottom;
      const topTotal = topBuy + elHight;
      // update
      const onScroll = () => setOffset(window.pageYOffset);
      // clean up code
      window.removeEventListener("scroll", onScroll);
      window.addEventListener("scroll", onScroll, { passive: true });
      // if (offset > topTotal) {
      if (topBuy < 60) {
        if (fixBuy.current) {
          fixBuy.current.classList.add("active");
        }
      } else {
        if (fixBuy.current) {
          fixBuy.current.classList.remove("active");
        }
      }
    }
  }, [offset]);

  useEffect(() => {
    if (appContext.reloadCourse) {
      appContext.setReloadCourse(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/mdcu/course/relate/" +
          data.data.id +
          "/" +
          data.data.type,
        null,
        (data) => {
          setCourseData(data);
          setPlayListOptions(data.userPlaylist);
        }
      );
      // console.log("reload_course");
    }
    if (reloadComment) {
      setReloadComment(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/mdcu/course/comment/" +
          data.data.id,
        null,
        (data) => {
          setCommentData(data);
        }
      );
      // console.log("reload_comment");
    }
    if (reloadQuiz) {
      setReloadQuiz(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/course/quiz/" + data.data.id,
        null,
        (data) => {
          setQuizData(data["data"]);
          setReloadRank(true);
        }
      );
      // console.log("reload_quiz");
    }
    if (reloadVdo) {
      setReloadVdo(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/course/vdo/" + data.data.id,
        null,
        (data) => {
          setVdoList(data["data"]);
        }
      );
      // console.log("reload_quiz");
    }
    if (reloadRank) {
      setReloadRank(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/course/rank/" + data.data.id,
        null,
        (data) => {
          var quick_array = [];
          if (data.status == "success" && data.data.count_all > 0) {
            var quick_item = {
              menuItem: "สถิติ",
              render: () => (
                <Tab.Pane attached={false}>
                  <div className="content-box quickResult-box">
                    <div className="quickResult-inner-row row-statistic">
                      <div className="col-12 col-quickResult">
                        <div className="StatisticViewTitle">
                          <h1>ภาพรวมการทำแบบทดสอบ</h1>
                        </div>
                      </div>
                      <div className="col-12 col-lg-6 col-quickResult">
                        <div className="statisticViewAmount">
                          <div className="statisticViewAmount-description">
                            <div className="item-description">
                              แบบทดสอบทั้งหมด {data.data.count_all} ชุด
                            </div>
                            <div className="item-description">
                              {" "}
                              ทำแล้ว {data.data.count_do} ชุด
                            </div>
                          </div>
                          <div className="statisticViewAmount-chart">
                            <div className="chart-inner">
                              <Pie
                                data={{
                                  labels: ["ผ่าน", "ไม่ผ่าน", "ยังไม่ได้ทำ"],
                                  datasets: [
                                    {
                                      data: [
                                        data.data.count_pass,
                                        data.data.count_fail,
                                        data.data.count_not,
                                      ],
                                      backgroundColor: [
                                        "rgba(75, 192, 192, 0.5)",
                                        "rgba(255, 159, 64, 0.5)",
                                        "rgba(255, 99, 132, 0.5)",
                                      ],
                                      borderColor: [
                                        "rgba(75, 192, 192, 0.5)",
                                        "rgba(255, 159, 64, 0.5)",
                                        "rgba(255, 99, 132, 0.5)",
                                      ],
                                      borderWidth: 1,
                                      options: {
                                        plugins: {},
                                      },
                                    },
                                  ],
                                }}
                                options={{
                                  responsive: true,
                                  layout: {},
                                  plugins: {
                                    legend: {
                                      position: "bottom",
                                      labels: {
                                        usePointStyle: true,
                                        boxWidth: 10,
                                        padding: 15,
                                        font: {
                                          size: 14,
                                          family: "Kanit",
                                        },
                                      },
                                    },
                                    title: {},
                                  },
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="col-12 col-lg-6 col-quickResult">
                        <div className="statisticViewAverage">
                          <div className="statisticViewAverage-description">
                            <div className="item-description">
                              เฉลี่ยคะแนนที่ทำได้
                            </div>
                          </div>
                          <div className="statisticViewAverage-chart">
                            <div className="chart-inner">
                              <Bar
                                options={{
                                  responsive: true,
                                  scales: {
                                    yAxis: {
                                      min: 0,
                                      max: 10,
                                    },
                                    xAxis: {
                                      pointLabelFontSize: 18,
                                      ticks: {
                                        size: 40,
                                      },
                                    },
                                  },
                                  plugins: {
                                    legend: {
                                      display: false,
                                      labels: {},
                                    },
                                    title: {
                                      display: false,
                                    },
                                  },
                                }}
                                data={{
                                  labels: [
                                    "คะแนนสูงสุด",
                                    "คะแนนที่ทำได้",
                                    "คะแนนต่ำสุด",
                                  ],
                                  datasets: [
                                    {
                                      data: [
                                        data.data.max_point,
                                        data.data.min_point,
                                        data.data.my_point,
                                      ],
                                      backgroundColor: [
                                        "rgba(75, 192, 192, 0.5)",
                                        "rgba(54, 162, 235, 0.5)",
                                        "rgba(255, 99, 132, 0.5)",
                                      ],
                                    },
                                  ],
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Tab.Pane>
              ),
            };
            quick_array.push(quick_item);
          }
          if (
            data.status == "success" &&
            (data.data.top_ten.length > 0 || data.data.my_log.length > 0)
          ) {
            var quick_item = {
              menuItem: "ผลคะแนน",
              render: () => (
                <Tab.Pane attached={false}>
                  <div className="content-box quickResult-box">
                    <div className="quickResult-inner-row row-ranking">
                      {data.data.top_ten.length > 0 ? (
                        <div className="col-12 col-lg-12 col-quickResult">
                          <div className="RankingViewTop10 MyRankingView">
                            <div className="MyRankingViewTitle">
                              <h1>10 อันดับที่ดีที่สุด</h1>
                            </div>
                            <div className="MyRankingViewTable">
                              <div className="InnerViewTableScroll">
                                <div className="InnerViewTable">
                                  <table className="table">
                                    <thead>
                                      <tr>
                                        <th scope="col">อันดับ</th>
                                        <th scope="col">ชื่อ</th>
                                        <th scope="col">วันที่ทำ</th>
                                        <th scope="col">คะแนน</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {data.data.top_ten.map((val, key) =>
                                        val.is_me ? (
                                          <tr key={key}>
                                            <td className="my">
                                              {val.position}
                                            </td>
                                            <td className="my">
                                              {val.name} {val.lastname}
                                            </td>
                                            <td className="my">
                                              {moment(val.created_at).format(
                                                "DD/MM/YYYY"
                                              )}
                                            </td>
                                            {val.point > 0 ? (
                                              <td className="my">
                                                <span>{val.point}</span> คะแนน
                                              </td>
                                            ) : (
                                              <td className="my">
                                                <span>-</span>
                                              </td>
                                            )}
                                          </tr>
                                        ) : (
                                          <tr key={key}>
                                            <td>{val.position}</td>
                                            <td>
                                              {val.name} {val.lastname}
                                            </td>
                                            <td>{val.date}</td>
                                            {val.point > 0 ? (
                                              <td>
                                                <span>{val.point}</span> คะแนน
                                              </td>
                                            ) : (
                                              <td>
                                                <span>-</span>
                                              </td>
                                            )}
                                          </tr>
                                        )
                                      )}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : null}
                      {data.data.my_log.length > 0 ? (
                        <div className="col-12 col-lg-12 col-quickResult">
                          <div className="RankingViewHistory MyRankingView">
                            <div className="MyRankingViewTitle">
                              <h1>ประวัติการทำแบบทดสอบ</h1>
                            </div>
                            <div className="MyRankingViewTable">
                              <div className="InnerViewTableScroll">
                                <div className="InnerViewTable">
                                  <table className="table">
                                    <thead>
                                      <tr>
                                        <th scope="col">ทดสอบครั้งที่</th>
                                        <th scope="col">ชื่อแบบทดสอบ</th>
                                        <th scope="col">วันที่</th>
                                        <th scope="col">คะแนน</th>
                                        <th scope="col">สถานะ</th>
                                        <th scope="col">ผลการทดสอบ</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {data.data.my_log.map((val, key) => (
                                        <tr key={key}>
                                          <td>{key + 1}</td>
                                          <td>{val.title_th}</td>
                                          <td>
                                            {moment(val.created_at).format(
                                              "DD/MM/YYYY"
                                            )}
                                          </td>
                                          {val.point > 0 ? (
                                            <td>
                                              <span>{val.point}</span> คะแนน
                                            </td>
                                          ) : (
                                            <td>
                                              <span>-</span>
                                            </td>
                                          )}
                                          {val.check_status == 1 ? (
                                            <td>
                                              <span className="status complete">
                                                ตรวจแล้ว
                                              </span>
                                            </td>
                                          ) : (
                                            <td>
                                              <span className="status wait">
                                                รอการตรวจสอบ
                                              </span>
                                            </td>
                                          )}
                                          {val.status == 1 ? (
                                            val.check_status == 1 ? (
                                              <td>
                                                <span className="result succes">
                                                  ผ่าน
                                                </span>
                                              </td>
                                            ) : (
                                              <td>
                                                <span className="result succes">
                                                  -
                                                </span>
                                              </td>
                                            )
                                          ) : val.check_status == 1 ? (
                                            <td>
                                              <span className="result fail">
                                                ไม่ผ่าน
                                              </span>
                                            </td>
                                          ) : (
                                            <td>
                                              <span className="result succes">
                                                -
                                              </span>
                                            </td>
                                          )}
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : null}
                    </div>
                  </div>
                </Tab.Pane>
              ),
            };
            quick_array.push(quick_item);
          }
          setQuickResult(quick_array);
        }
      );
      // console.log("reload_rank");
    }
  }, [
    appContext.reloadCourse,
    reloadComment,
    reloadQuiz,
    reloadVdo,
    reloadRank,
  ]);

  function selectChapter(_key) {
    var obj = [];
    for (var i = 0; i < vdoList[_key]["quiz"]["question"].length; i++) {
      var ans = {
        course_id: data["data"]["id"],
        lesson_id: vdoList[_key]["id"],
        question_id: vdoList[_key]["quiz"]["question"][i]["id"],
        question_type: vdoList[_key]["quiz"]["question"][i]["type"],
        answer: "",
      };
      obj.push(ans);
    }
    // answer_textarea
    // answer_input
    var collection = document.getElementsByClassName("answer_input");
    for (var i = 0; i < collection.length; i++) {
      collection[i].checked = false;
    }
    var collection = document.getElementsByClassName("answer_textarea");
    for (var i = 0; i < collection.length; i++) {
      collection[i].value = "";
    }
    setAnswer(obj);
  }

  function onAnswer(_key, _ans) {
    answer[_key]["answer"] = _ans;
  }
  function onSubmit(_key) {
    let bol = true;
    var errarr = [];
    for (var i = 0; i < answer.length; i++) {
      if (appContext.isNull(answer[i]["answer"])) {
        bol = false;
        errarr.push(answer[i]["question_id"]);
      }
    }
    setErrorArray(errarr);
    if (bol && !lockAddQuiz) {
      setLockAddQuiz(true);
      const formData = new URLSearchParams();
      formData.append("answer_data", JSON.stringify(answer));
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addQuiz",
        formData,
        (obj) => {
          if (obj["status"] == "success") {
            setLockAddQuiz(false);
            setStateIndex({ activeIndexs: [] });
            setAnswer([]);
            // var collection = document.getElementsByClassName("answer_input");
            // for(var i = 0; i<collection.length; i++){
            //   collection[i].checked = false;
            // }
            // var collection = document.getElementsByClassName("answer_textarea");
            // for(var i = 0; i<collection.length; i++){
            //   collection[i].value = '';
            // }
            setReloadQuiz(true);
            setReloadQuizBox(false);
            setModalQuizOpen(false);
            setReloadVdo(true);
            setTimeout(() => {
              Swal.fire({
                text: "ส่งคำตอบสำเร็จ",
                icon: "success",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              }).then((result) => {
                setReloadQuizBox(true);
              });
            }, "0");
          }
        }
      );
    } else {
      if(!lockAddQuiz){
        Swal.fire({
          text: "กรุณาตอบคำถามให้ครบถ้วน",
          icon: "error",
          confirmButtonText: "ปิด",
          confirmButtonColor: "#648d2f"
        });
      }
    }
  }
  const previewFile = (_key, event) => {
    event.preventDefault();
    // answer[_key]['answer'] = URL.createObjectURL(event.target.files[0]);
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      function () {
        if (_.startsWith(reader.result, "data:")) {
          appContext.loadApi(
            process.env.NEXT_PUBLIC_API + "/api/image/save",
            { image: reader.result },
            (data) => {
              if (data["status"] == "true") {
                answer[_key]["answer"] = data["path"];
              }
            }
          );
        }
      },
      false
    );
    if (file) {
      reader.readAsDataURL(file);
    }
  };
  const checkMyError = (_val) => {
    if (errorArray.indexOf(_val) > -1) {
      return true;
    }
    return false;
  };

  // update
  // update

  function courseDescription(_value) {
    const formData = new URLSearchParams();
    formData.append("course_id", data["data"]["id"]);
    formData.append("rate", _value);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseRate",
      formData,
      (obj) => {
        if (obj["status"] == "success") {
          setReloadStar(false);
          data["data"]["rate"] = obj["rate"];
          data["data"]["rating"] = obj["rating"];
          setTimeout(() => {
            setReloadStar(true);
          }, "0");
          forceUpdate();
        }
      }
    );
  }
  function audioReady(_sec) {
    if (audioRef) {
      setTimeout(() => {
        audioRef.current.seekTo(_sec, "seconds");
      }, "0");
    }
  }
  function courseProgress(_data, _id, _last) {
    const formData = new URLSearchParams();
    formData.append("course_id", data["data"]["id"]);
    formData.append("lesson_id", _id);
    formData.append("watching_time", _data.playedSeconds);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseStamp",
      formData,
      (obj) => {
        if (obj["status"] == "success") {
          
        }
      }
    );
  }
  function progressEnd(_id, _last) {
    setReloadVdo(true);
    setReloadQuiz(true);
    setModalVdoOpen(false);
    setReloadQuizBox(false);
    setPlayAudio(false);
    setTimeout(() => {
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/mdcu/course/vdo/" +
          data.data.id,
        null,
        (res) => {
          if (
            res["data"][keySelect]["quiz"].question &&
            res["data"][keySelect]["quiz"].question.length > 0 &&
            res["data"][keySelect]["quiz"].allowed &&
            res["data"][keySelect]["quiz"].first_time &&
            !res["data"][keySelect]["quiz"].check
          ) {
            selectQuiz(keySelect);
            if (_last == "last") {
              setExamAssessment(true);
            }
          }else{
            if (_last == "last") {
              selectAssessment();
            }else{
              selectEp(keySelect + 1);
            }
          }
          setReloadQuizBox(true);
        }
      );
    }, "0");
    if (_last == "last") {
      if (data["data"]["type"] == "podcast") {
        setKeySelect(null);
        setReloadAudio(false);
        setTimeout(() => {
          setReloadAudio(true);
        }, "0");
      }
    }
  }
  function skipVdo(_id) {
    const formData = new URLSearchParams();
    formData.append("course_id", data["data"]["id"]);
    formData.append("lesson_id", _id);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/skipEp",
      formData,
      (obj) => {
        if (obj["status"] == "success") {
          // console.log(obj);
        }
      }
    );
    // console.log("skipVdo : " + _id);
  }
  function podcastProgress(_data, _id, _last) {
    // console.log(_data);
  }

  appContext.setToken(utoken);

  if (errorCode) {
    return <Error statusCode={errorCode} />;
  }

  function playerCallback(bol) {
    setModalVdoOpen(bol);
    setModalVdoAudio(bol);
    setReloadQuizBox(false);
    setReloadVdo(true);
    setReloadQuiz(true);
    setTimeout(() => {
      setReloadQuizBox(true);
    }, "500");
  }

  function quizCallback(bol) {
    setModalQuizOpen(bol);
  }

  function selectEp(_key) {
    setKeySelect(_key);
    setModalVdoOpen(true);
    setPlayAudio(true);
    if (data["data"]["type"] == "podcast") {
      audioReady(data["audioList"][_key]["watching"]);
    }
  }
  function selectQuiz(_key) {
    setQuizSelect(_key);
    setModalVdoOpen(false);
    selectChapter(_key);
    setModalQuizOpen(true);
  }
  function playVdo(_key) {
    setKeySelect(_key);
    setModalVdoAudio(true);
  }
  function groupCategoryCallback(_type, _id, _title) {
    if (user) {
      if (_type == "cart") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "course");
        formData.append("discount_code", discountCode);
        formData.append("discount_channel", discountChannel);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
          formData,
          (res_check) => {
            if(res_check['status']=='success'){
              if(res_check['count']==0){
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                  formData,
                  (data) => {
                    // console.log(data);
                    appContext.setReloadCart(true);
                    appContext.setReloadCourse(true);
        
                    document
                      .getElementsByClassName("main-header")[0]
                      .classList.add("active_cart");
                    document
                      .getElementsByClassName("group-menu-cart")[0]
                      .classList.add("on_show");
                    document.body.classList.add("open_cart");
                    document
                      .getElementsByClassName("group-menu-f-mobile")[0]
                      .classList.remove("on_show");
                  }
                );
              }else{
                Swal.fire({
                  html: "คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?",
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: 'ยืนยัน',
                  cancelButtonText: 'ยกเลิก'
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                      formData,
                      (res_clear) => {
                        if(res_clear['status']=='success'){
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                            formData,
                            (data) => {
                              // console.log(data);
                              appContext.setReloadCart(true);
                              appContext.setReloadCourse(true);
                  
                              document
                                .getElementsByClassName("main-header")[0]
                                .classList.add("active_cart");
                              document
                                .getElementsByClassName("group-menu-cart")[0]
                                .classList.add("on_show");
                              document.body.classList.add("open_cart");
                              document
                                .getElementsByClassName("group-menu-f-mobile")[0]
                                .classList.remove("on_show");
                            }
                          );
                        }
                      }
                    );
                  }
                });
              }
            }
          }
        );
      } else if (_type == "group") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "group");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
          formData,
          (res_check) => {
            if(res_check['status']=='success'){
              if(res_check['count']==0){
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                  formData,
                  (data) => {
                    // console.log(data);
                    appContext.setReloadCart(true);
                    appContext.setReloadCourse(true);
        
                    document
                      .getElementsByClassName("main-header")[0]
                      .classList.add("active_cart");
                    document
                      .getElementsByClassName("group-menu-cart")[0]
                      .classList.add("on_show");
                    document.body.classList.add("open_cart");
                    document
                      .getElementsByClassName("group-menu-f-mobile")[0]
                      .classList.remove("on_show");
                  }
                );
              }else{
                Swal.fire({
                  html: "คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?",
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: 'ยืนยัน',
                  cancelButtonText: 'ยกเลิก'
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                      formData,
                      (res_clear) => {
                        if(res_clear['status']=='success'){
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                            formData,
                            (data) => {
                              // console.log(data);
                              appContext.setReloadCart(true);
                              appContext.setReloadCourse(true);
                  
                              document
                                .getElementsByClassName("main-header")[0]
                                .classList.add("active_cart");
                              document
                                .getElementsByClassName("group-menu-cart")[0]
                                .classList.add("on_show");
                              document.body.classList.add("open_cart");
                              document
                                .getElementsByClassName("group-menu-f-mobile")[0]
                                .classList.remove("on_show");
                            }
                          );
                        }
                      }
                    );
                  }
                });
              }
            }
          }
        );
      } else if (_type == "favourite") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "course");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addFavourite",
          formData,
          (res_fav) => {
            // console.log(data);
            // appContext.setReloadCourse(true);
            if(res_fav['status']=='success'){
              if(res_fav['action']=='add'){
                document.querySelector(".favourite_class_"+_id).classList.add("active");
              }else{
                document.querySelector(".favourite_class_"+_id).classList.remove("active");
              }
            }
          }
        );
      } else if (_type == "free") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderFree",
          formData,
          (data) => {
            if (data["status"] == "success") {
              Swal.fire({
                text: "ลงทะเบียนสำเร็จ เริ่มเรียนได้เลย",
                icon: "success",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              }).then((result) => {
                location.reload();
              });
            } else {
              Swal.fire({
                text: "พบข้อผิดพลาด กรุณาลองอีกครั้ง",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      } else if (_type == "playlist") {
        setCourseId(_id);
        setPlaylistTitle("");
        setPlaylistSelect("");
        setAddPlaylist(true);
      }
    } else {
      Swal.fire({
        text: "กรุณาเข้าสู่ระบบค่ะ",
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function submitPlaylist() {
    if (
      (playlistTitle != null && playlistTitle != "") ||
      (playlistSelect != null && playlistSelect != "")
    ) {
      const formData = new URLSearchParams();
      formData.append("course_id", courseId);
      formData.append("title", playlistTitle);
      formData.append("select", playlistSelect);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addPlaylist",
        formData,
        (res_play) => {
          // appContext.setReloadCourse(true);
          setAddPlaylist(false);
          if(res_play['status']=='success'){
            if(res_play['action']=='add'){
              document.querySelector(".playlist_class_"+courseId).classList.add("active");
              document.querySelector(".playlist_icon_"+courseId).classList.remove("icon-ic-circle-plus");
              document.querySelector(".playlist_icon_"+courseId).classList.add("icon-ic-tick-thanks");
            }else{
              document.querySelector(".playlist_class_"+courseId).classList.remove("active");
              document.querySelector(".playlist_icon_"+courseId).classList.remove("icon-ic-tick-thanks");
              document.querySelector(".playlist_icon_"+courseId).classList.add("icon-ic-circle-plus");
            }
          }
        }
      );
    } else {
      Swal.fire({
        text: "กรุณาเลือกเพลย์ลิสต์",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f"
      });
    }
  }
  function addDiscountCode(_code, _type) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("course_id", data["data"]["id"]);
      formData.append("content_type", "course");
      formData.append("discount_code", _code);
      formData.append("discount_channel", _type);
      setCoursePriceReload(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addDiscountCode",
        formData,
        (obj) => {
          // console.log(obj);
          setCoursePriceReload(false);
          if (obj["is_discount"]) {
            if (_type == "code") {
              Swal.fire({
                text: "ยินดีด้วย ใช้โค้ดส่วนลดสำเร็จ",
                icon: "success",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: "ยินดีด้วย คุณได้รับการสนับสนุน",
                icon: "success",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
              document.getElementById("input_code").value = _code;
            }
            if (data["data"]["is_promotion"] == 1) {
              if (obj["discount_type"] == "regular") {
                data["data"]["pro_price"] =
                  courseProPrice - obj["discount_value"];
              } else {
                data["data"]["pro_price"] =
                  courseProPrice - (courseProPrice * obj["discount_value"]) / 100;
              }
            } else {
              if (obj["discount_type"] == "regular") {
                data["data"]["price"] = coursePrice - obj["discount_value"];
              } else {
                data["data"]["price"] =
                  coursePrice - (coursePrice * obj["discount_value"]) / 100;
              }
            }
          } else {
            if (_type == "code") {
              Swal.fire({
                text: "ขออภัย ไม่พบโค้ดส่วนลด",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: "ขออภัย ไม่พบผู้สนับสนุน",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              });
            }
            if (data["data"]["is_promotion"] == 1) {
              data["data"]["pro_price"] = courseProPrice;
            } else {
              data["data"]["price"] = coursePrice;
            }
          }
          setDiscountChannel(obj["discount_channel"]);
          setDiscountCode(obj["discount_code"]);
          setTimeout(() => {
            setCoursePriceReload(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: "กรุณาเข้าสู่ระบบค่ะ",
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function addLike(_comment_id) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("comment_id", _comment_id);
      setReloadComment(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addLike",
        formData,
        (obj) => {
          // console.log(obj);
          setReloadComment(false);
          setTimeout(() => {
            setReloadComment(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: "กรุณาเข้าสู่ระบบค่ะ",
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function addComment(_comment) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("comment", _comment);
      formData.append("course_id", data["data"]["id"]);
      formData.append("content_type", "course");
      setReloadComment(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addComment",
        formData,
        (obj) => {
          // console.log(obj);
          setReloadComment(false);
          setTimeout(() => {
            setReloadComment(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: "กรุณาเข้าสู่ระบบค่ะ",
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function handleClick(e, titleProps) {
    const { index } = titleProps;
    const { activeIndexs } = stateIndex;
    const newIndex = [];
    // const newIndex = activeIndexs;
    selectChapter(titleProps.index);
    const currentIndexPosition = activeIndexs.indexOf(index);
    if (currentIndexPosition > -1) {
      newIndex.splice(currentIndexPosition, 1);
    } else {
      newIndex.push(index);
    }

    setStateIndex({ activeIndexs: newIndex });
  }
  function onStartExam(_id) {
    for (var i = 0; i < vdoList.length; i++) {
      if (vdoList[i]["id"] == _id) {
        selectQuiz(i);
        break;
      }
    }
  }

  const { activeIndexs } = stateIndex;

  var panels = [];

  if (vdoList && vdoList.length > 0) {
    var quizLength = 0;
    for (var i = 0; i < vdoList.length; i++) {
      for (var j = 0; j < vdoList[i]["quiz"]["question"].length; j++) {
        quizLength++;
      }
    }
    var panes = [];
  } else {
    var panes = [
      {
        menuItem: "รายละเอียด",
        pane: {
          key: "tab1",
          content: (
            <div className="content-box">
              <Accordion
                defaultActiveIndex={[0]}
                exclusive={false}
                panels={panels}
                fluid
              />
            </div>
          ),
        },
      },
      {
        menuItem: "ถาม-ตอบ",
        pane: {
          key: "tab2",
          content: (
            <div className="content-box">
              {commentData ? (
                <CommentZone
                  user={user}
                  addLike={addLike}
                  addComment={addComment}
                  data={commentData["commentData"]}
                ></CommentZone>
              ) : null}
            </div>
          ),
        },
      },
    ];
  }

  return (
    <div className="main-all page-course">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="background-section white infographic-detail">
          <div
            className={`container custom-container space-between-content bg-white`}
          >
            <div className="infographic-detail-page">
              <div className="row">
                <div className="col-12 col-infographic-detail">
                  <div className="infographic-detail-title">
                    <h1>
                      <span>{data.data.title}</span>
                    </h1>
                  </div>
                </div>

                <div className="col-12 col-md-6 col-xl-5 col-infographic-detail">
                  <div className="infographic-detail-img">
                    <div  className="imgResponsiveCustom">
                      {data && data.data && data.data.image && data.data.image!=null && data.data.image!='' && data.data.image!='null' ? (
                        <Image
                          src={data.data.image}
                          layout='fill'
                          objectFit='contain'
                          sizes="100%"
                          alt=""
                        />
                      ):null}
                      </div>
                  </div>
                  
                </div>

                <div className="col-12 col-md-6 col-xl-7 col-infographic-detail">
                  <div
                    className="infographic-detail-contnet"
                    dangerouslySetInnerHTML={{ __html: data.data.description }}
                  >
                    {/* <h1>header h1</h1>
                          <h2>header h2</h2>
                          <h3>header h3</h3>
                          <h4>header h4</h4>
                          <h5>header h5</h5>
                          <p>ผู้ที่สูบบุหรี่เป็นประจำ สามารถประเมินตนเองได้ว่าติดบุหรี่ (หรือติดนิโคติน) ในระดับใดเพื่อช่วยให้ทราบว่าจำเป็นต้องได้รับความช่วยเหลือจากโปรแกรมการเลิกบุหรี่หรือไม่หรือสามารถเลิกบุหรี่ได้เอง</p>
                          <p><strong>คำถามเพื่อประเมินตนเอง</strong></p>
                          <ol>
                            <li>คุณเริ่มสูบบุหรี่มวนแรกภายในครึ่งชั่วโมงหลังตื่นนอน</li>
                            <li>คุณสูบบุหรี่มากกว่า 10 มวนต่อวัน</li>
                            <li>เป็นเรื่องยากที่คุณจะไม่สูบบุหรี่ในสถานที่ห้ามสูบบุหรี่</li>
                            <li>คุณสูบบุหรี่ช่วงเช้ามากกว่าช่วงเวลาอื่น</li>
                            <li>แม้ว่าคุณจะป่วย แต่คุณยังคงสูบบุหรี่</li>
                          </ol>
                          <p><strong>หากคุณตอบ “ใช่” มากกว่า 3 ข้อขึ้นไป</strong></p>
                          <p>โดยเฉพาะถ้ามีข้อ 1 หรือ 2 หรือทั้ง 1 และ 2 แสดงว่ามีระดับการติดบุหรี่สูงมาก ควรเข้ารับคำปรึกษาและจำเป็นต้องได้รับยาเพื่อช่วยเลิกบุหรี่ แม้ว่าการเลิกบุหรี่จะเป็นเรื่องยากแต่ก็เป็นเรื่องที่ทุกคนสามารถทำได้</p>
                          <p>ข้อมูล 31 พฤษภาคม 2565 <br></br>ที่มา : ผศ. นพ.ธีรยุทธ รุ่งนิรันดร  <br></br>ฝ่ายจิตเวชศาสตร์</p> */}
                  </div>
                </div>

                <div className="info-arrow">
                {data.data.previous ? (
                 
                   <Link href={data.data.previous}>
                    <button className="btn-info-button prev"> <i className="icon-ic-left"></i> <span>ก่อนหน้า</span></button>
                    </Link>
                   
                ) : null}

                {data.data.next ? ( 
                    <Link href={data.data.next}>
                      <button className="btn-info-button next"><span>ต่อไป</span> <span className="icon-ic-right"></span></button>
                    </Link> 
                ) : null}
                </div>


              </div>
            </div>
          </div>
        </div>
        {courseData && courseData["data"] && courseData["data"].length > 0 ? (
          <GroupCategory
            type="seminar"
            name="คอร์สเกี่ยวข้อง"
            color="#6E953D"
            size="3.5"
            index="20"
            callback={groupCategoryCallback}
            data={courseData["data"]}
          ></GroupCategory>
        ) : null}
        <Footer></Footer>
      </div>
    </div>
  );
}

import React from "react";
import Image from "next/image";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import _ from "lodash";

import { useRouter } from "next/router";
import { getUser } from "../../api/user";
import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import Link from "next/link";

import { useSession, getSession } from "next-auth/react";
import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";
import { useForm } from "react-hook-form";
// Serverside & Api fetching
import Error from "next";
import NumberFormat from "react-number-format";
// Serverside & Api fetching
// Auth
import nookies from "nookies";
// Auth
import AppContext from "/libs/contexts/AppContext";
import Swal from "sweetalert2";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const params = context.query;
  const utoken = params.utoken;
  const user = await getUser(utoken);
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/rewardDetail/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["status"] == "false") {
    return {
      redirect: { destination: "/" },
    };
  }
  

  return {
    props: {
      session: await getSession(context),
      data,
      utoken,
    },
  };
}

export default function PointDetail ({ data,utoken }) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  const [lock, setLock] = React.useState(false);

  function userRedeem(){
    if(!lock){
      Swal.fire({
        html: 'กรุณาแลกรับสิทธิ์ เมื่ออยู่หน้าร้าน<br>ต้องใช้ภายในเวลาที่กำหนด<br>เมื่อคลิกรับสิทธิ์ Points จะถูกตัดทันที',
        icon: 'info',
        showCancelButton: true,
        confirmButtonColor: '#648d2f',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก'
      }).then((result) => {
        if (result.isConfirmed) {
          setLock(true);
          const formData = new URLSearchParams();
          formData.append("reward_id", data.data.id);
          formData.append("utoken", utoken);
          appContext.sendApiToken(
            process.env.NEXT_PUBLIC_API + "/api/user/redeemReward",
            formData,
            (res_data) => {
              console.log(res_data);
              if(res_data['status']=='success'){
                location.href="/redeemView/"+utoken+"/"+res_data['redeem_token'];
              }else if(res_data['status']=='not_enough'){
                Swal.fire({
                  text: 'ขออภัย คะแนนของคุณไม่เพียงพอ',
                  icon: "error",
                  confirmButtonText: 'ปิด',
                  confirmButtonColor: "#648d2f"
                });
              }else if(res_data['status']=='redeemed'){
                Swal.fire({
                  text: 'ขออภัย คุณเคยแลกของรางวัลแล้ว แลกได้อีกครั้งใน '+res_data['period']+' วัน',
                  icon: "error",
                  confirmButtonText: 'ปิด',
                  confirmButtonColor: "#648d2f"
                });
              }else if(res_data['status']=='out'){
                Swal.fire({
                  text: 'ขออภัยของรางวัลหมด',
                  icon: "error",
                  confirmButtonText: 'ปิด',
                  confirmButtonColor: "#648d2f"
                });
              }else{
                Swal.fire({
                  text: 'พบข้อผิดพลาด กรุณาลองอีกครั้ง',
                  icon: "error",
                  confirmButtonText: 'ปิด',
                  confirmButtonColor: "#648d2f"
                });
              }
            }
          );
        }
      })
    }
  }
  return (
    <>
      <style>{`
        #onetrust-consent-sdk{
          display:none!important;
        }
      `}</style>
      <div className="main-all">
        <div className="main-body">
          <div className="point-page">
            <div className="logo-coupon">
              <Image alt="" height={56} width={163} src={'/assets/images/logo-coupon.png'} />
            </div>
            <div className="block-point-detail">
              <div className="img-detail">
                {!appContext.isNull(data.data.image)?(
                  <Image alt="" height={276} width={256} src={data.data.image} />
                ):null}
              </div>
              <div className="content-detail">
                <div className="text-point">
                  <div className="img-coin">
                    <Image alt="" height={30} width={30} src={'/assets/images/coin.png'} />
                  </div>
                  <NumberFormat
                    value={data.data.redeem_point}
                    displayType={"text"}
                    thousandSeparator={true}
                    renderText={(value, props) => (
                      <p {...props}>
                        {value}
                        <span>
                          คะแนน
                        </span>
                      </p>
                    )}
                  />
                </div>
                <div className="text-detail">
                  {!appContext.isNull(data.data.remark)?(
                    <p className="indent">
                      {data.data.remark}
                    </p>
                  ):null}
                  {!appContext.isNull(data.data.started) || !appContext.isNull(data.data.end) ?(
                    <div className="date">
                      <div className="img-date">
                        <Image alt="" height={20} width={20} src={'/assets/images/icon-date.png'} />
                      </div>
                      <div className="text-date">
                        {!appContext.isNull(data.data.started) || !appContext.isNull(data.data.end) ?(
                          <p>
                            {!appContext.isNull(data.data.started) ? appContext.convertToThaiDate(data.data.started):'วันนี้'}{!appContext.isNull(data.data.end) ? ' - '+appContext.convertToThaiDate(data.data.end):''}
                          </p>
                        ):null}
                      </div>
                    </div>
                  ):null}
                  {!appContext.isNull(data.data.branch)?(
                    <div className="date">
                      <div className="img-date">
                        <Image alt="" height={20} width={20} src={'/assets/images/store-icon.png'} />
                      </div>
                      <div className="text-date">
                        <p className="small">
                          {data.data.branch}
                        </p>
                      </div>
                    </div>
                  ):null}
                </div>
                <div className="text-list" dangerouslySetInnerHTML={{
                  __html: data.data.condition,
                }}></div>
              </div>
            </div>
            <div className="action">
              <Button className="btn-detail" onClick={() => userRedeem()}>
                <span>แลกรับสิทธิ์</span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

import React ,{ useLayoutEffect, useRef } from "react";
// Serverside & Api fetching
import nookies from 'nookies'
import { parseCookies, setCookie, destroyCookie } from 'nookies'
import { getUser } from "./api/user";
import { useState, useEffect,useContext } from "react";
import AppContext from '/libs/contexts/AppContext';
import Router, { useRouter } from "next/router";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react"
import Error from "next";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation,Lazy,FreeMode,Mousewheel } from "swiper";
import Head from "next/head";
import Header from "../themes/header/header";
import Footer from "../themes/footer/footer";
import GroupCardNews from "../themes/components/groupCardNews";
import Image from "next/image";
import "swiper/css/navigation";
import "swiper/css";
import "swiper/css/lazy";
export async function getServerSideProps(context) {
  const cookies = nookies.get(context)
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP+'_token']);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP+'_token'] || '';
  //SEO
  const seo_res = await fetch(process.env.NEXT_PUBLIC_API + "/api/seo/infographic", {
    method: "POST",
  });
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO
  return {
    props: {seo_data,utoken,user},
  };
}

export default function Infographic({seo_data,utoken,user}) {
  const { data: session, status: session_status  } = useSession()
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);

  const swipperBig = useRef();
  const [reloadInfo, setReloadInfo] = useState(true);
  const [infographicData, setInfographicData] = useState(null);

  const [reloadSlide, setReloadSlide] = useState(false);
  const [swiperSlide, setSwiperSlide] = useState(0);
  const [swiperData, setSwiperData] = useState(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(24);
  const [pageList, setPageList] = useState([]);
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);
  function nextPage() {
    setPage(page+1);
    setReloadInfo(true);
  }
  function prevPage() {
    setPage(page-1);
    setReloadInfo(true);
  }
  function selectPage(_page) {
    setPage(_page);
    setReloadInfo(true);
  }
  useEffect(() => {
    if(reloadInfo){
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
      setReloadInfo(false)
      const formData = new URLSearchParams();
      formData.append("limit", limit);
      formData.append("page", page);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/getInfographicAll",
        formData,
        (data) => {
          setInfographicData(data['data'])
          var pages = [];
          for(var i=0;i<data['all_page'];i++){
            pages.push((i+1));
          }
          setPageList(pages);
          if(data && data['data']){
            setSwiperData(data['data'])
            goToSwiper(0)
          }
        }
      );
    }
    if(reloadSlide){
      setReloadSlide(false)
      try {
        swipperBig.current.slideTo(swiperSlide)
        window.scrollTo({
          top: 0,
          behavior: 'smooth',
        })
      }
      catch(e) {
      }
      
    }
  }, [reloadInfo,reloadSlide]);
  function goToSwiper(_index){
    setSwiperSlide(_index)
    setReloadSlide(true)
  }
  return (
    <div className="main-all">
     <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className="infographic-page">
          <div className={`container custom-container space-between-content`}>
            <div className="row">
              {swiperData && swiperData.length>0 ? (
                <div className="col-12">
                  <h2>
                    Infographic
                  </h2>                  
                  <div className="block-infographic size-big">
                    <Swiper freeMode={false} lazy={true} mousewheel={{
                forceToAxis: true,
                enabled: true,
              }} modules={[FreeMode,Navigation,Mousewheel]} onSwiper={(swiper) => {swipperBig.current = swiper;}} navigation={true} className="mySwiper">
                      {swiperData.map((val, key) => (
                        <SwiperSlide key={key}>
                          <div className="card-img">
                            {lang=='en'?(
                              <a href={`/en/infographic/${val.slug}`}>
                                <div className="infographicThumb">
                                  <div  className="imgResponsiveCustom">
                                  <span className="loaderInfographic"></span>
                                  {val && val.image && val.image!=null &&val.image!=''&&val.image!='null' ? (
                                    <Image
                                      className=" cursor-pointer"
                                      src={val.image} 
                                      alt={val.title_th}
                                      layout='fill'
                                      objectFit='contain'
                                      sizes="100%"
                                    />
                                  ):null}
                                  </div>
                                </div>
                              </a>
                            ):(
                              <a href={`/infographic/${val.slug}`}>
                                <div className="infographicThumb">
                                  <div  className="imgResponsiveCustom">
                                  <span className="loaderInfographic"></span>
                                  {val && val.image && val.image!=null &&val.image!=''&&val.image!='null' ? (
                                    <Image
                                      className=" cursor-pointer"
                                      src={val.image} 
                                      alt={val.title_th}
                                      layout='fill'
                                      objectFit='contain'
                                      sizes="100%"
                                    />
                                  ):null}
                                  </div>
                                </div>
                              </a>
                            )}
                            
                          </div>
                        </SwiperSlide>
                      ))}
                    </Swiper>
                  </div>
                </div>
              ):(null)}
              {infographicData && infographicData.length>0 ? (
                infographicData.map((val, key) => (
                  <div className="col-6 col-md-4 col-lg-2 col-xl-2 infographic-list-page" key={key}>
                    <div className="card-img">
                      <div className="infographicThumbClick">

                        <div  className="imgResponsiveCustom" onClick={() => goToSwiper(key)}>
                          <span className="loaderInfographic"></span>
                          {val && val.image && val.image!=null &&val.image!=''&&val.image!='null' ? (
                            <Image
                              className=" cursor-pointer"
                              src={val.image} 
                              alt={val.title_th}
                              layout='fill'
                              objectFit='contain'
                              sizes="100%"
                            />
                          ):null}
                        </div>

                        {/* <img onClick={() => goToSwiper(key)} className="img-thumb cursor-pointer" src={val.image} alt={val.title_th} /> */}

                      </div>
                    </div>
                  </div>
                ))
              ):(null)}
              <div className="col-12">
                {pageList.length>1?(
                  <div className="block-pagination-pages">
                    <div className="ui pagination menu">
                      <a className={`item page ${page == 1 ? 'disable' : ""}`} onClick={() => prevPage()}>
                        <i className="icon-ic-left"></i>
                      </a>
                      {pageList.map((val, key) =>
                        page<pageList.length-2 && val==pageList.length ? (
                          (val>=page-1 && val<=page+1)||(page==1 && val==3) || (page==pageList.length && val==pageList.length-2)
                          ||val==1||val==pageList.length ? (
                            <div className="page-flex">
                              <a className="item link dot">...</a>
                              <a className={`item link ${page==val ? 'active' : ""}`} onClick={() => selectPage(val)}>{val}</a>
                            </div>
                          ):(
                            <a className="item link dot">...</a>
                          )
                        ):page>3 && val==1 ? (
                          (val>=page-1 && val<=page+1)||(page==1 && val==3) || (page==pageList.length && val==pageList.length-2)
                          ||val==1||val==pageList.length ? (
                            <div className="page-flex">
                              <a className={`item link ${page==val ? 'active' : ""}`} onClick={() => selectPage(val)}>{val}</a>
                              <a className="item link dot">...</a>
                            </div>
                          ):(
                            <a className="item link dot">...</a>
                          )
                        ):(
                          (val>=page-1 && val<=page+1)||(page==1 && val==3) || (page==pageList.length && val==pageList.length-2)
                          ||val==1||val==pageList.length ? (
                            <a className={`item link ${page==val ? 'active' : ""}`} onClick={() => selectPage(val)}>{val}</a>
                          ):null
                        )
                      )}
                      <a className={`item page ${page == pageList.length ? 'disable' : ""}`} onClick={() => nextPage()}>
                        <i className="icon-ic-right"></i>
                      </a>
                    </div>
                  </div>
                ):null}
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
}

import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";

import { useState, useEffect, useContext, useRef, useReducer } from "react";

import { useRouter } from "next/router";
import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import Link from "next/link";
import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";
import { useForm } from "react-hook-form";
// Serverside & Api fetching
import Swal from "sweetalert2";

import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/foundation",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      utoken,
      user,
    },
  };
}

export default function Foundation({
  seo_data,
  utoken,
  user,
}) {
  // console.log(data)
  const router = useRouter();
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [errorArray, setErrorArray] = useState([]);
  const [firstLoad, setFirstLoad] = useState(true);
  const [lockForm, setLockForm] = useState(false);
  const [nowDate, setNowDate] = useState('');
  const { register, setValue, getValues } = useForm({
    defaultValues: {},
  });

  useEffect(() => {
    if(firstLoad){
      setFirstLoad(false);
      var currentdate = new Date(); 
      var datetime = '';
      datetime += currentdate.getFullYear();
      datetime += '-';
      if(currentdate.getMonth()<10){
        datetime += '0';
      }
      datetime += currentdate.getMonth();
      datetime += '-';
      if(currentdate.getDate()<10){
        datetime += '0';
      }
      datetime += currentdate.getDate();
      datetime += ' ';
      if(currentdate.getHours()<10){
        datetime += '0';
      }
      datetime += currentdate.getHours();
      datetime += ':';
      if(currentdate.getMinutes()<10){
        datetime += '0';
      }
      datetime += currentdate.getMinutes();
      datetime += ':';
      if(currentdate.getSeconds()<10){
        datetime += '0';
      }
      datetime += currentdate.getSeconds();
      setNowDate(datetime);
    }
  }, [firstLoad]);
  useEffect(() => {
    if (user) {
      if (user.name == "null") {
        setValue("name", null);
      } else {
        setValue("name", user.name);
      }
      if (user.lastname == "null") {
        setValue("lastname", null);
      } else {
        setValue("lastname", user.lastname);
      }
      if (user.email == "null") {
        setValue("email", null);
      } else {
        setValue("email", user.email);
      }
      if (user.mobile == "null") {
        setValue("mobile", null);
      } else {
        setValue("mobile", user.mobile);
      }
      setValue("iden_no", '');
      setValue("amount", '');
      setValue("consent", '');
      setValue("tax", '');
      document.getElementById("consent").checked = false;
      document.getElementById("tax").checked = false;
    }
  }, [user]);
  function toggleConsent() {
    let register_data = getValues();
    if (appContext.isNull(register_data["consent"])) {
      setValue("consent", 1);
    }else{
      setValue("consent", '');
    }
  }
  function toggleTax() {
    let register_data = getValues();
    if (appContext.isNull(register_data["tax"])) {
      setValue("tax", 1);
    }else{
      setValue("tax", '');
    }
  }
  const onSubmit = () => {
    let register_data = getValues();
    let bol = true;
    var errarr = [];
    if (appContext.isNull(register_data["name"])) {
      bol = false;
      errarr.push("name");
    }
    if (appContext.isNull(register_data["lastname"])) {
      bol = false;
      errarr.push("lastname");
    }
    if (!appContext.isEmail(register_data["email"])) {
      bol = false;
      errarr.push("email");
    }
    if (!appContext.isMobile(register_data["mobile"])) {
      bol = false;
      errarr.push("mobile");
    }
    if (!appContext.isIdenNo(register_data["iden_no"])) {
      bol = false;
      errarr.push("iden_no");
    }
    if (!appContext.isNumeric(register_data["amount"]) || register_data["amount"]==0) {
      bol = false;
      errarr.push("amount");
    }
    if (appContext.isNull(register_data["consent"])) {
      bol = false;
      errarr.push("consent");
    }
    if (appContext.isNull(register_data["tax"])) {
      bol = false;
      errarr.push("tax");
    }
    setErrorArray(errarr);
    if (bol && !lockForm) {
      setLockForm(true);
      let register_data = getValues();
      const formData = new URLSearchParams();
      formData.append("data", JSON.stringify(register_data));
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/support/foundation",
        formData,
        (obj) => {
          if (obj["status"] == "success") {
            setTimeout(() => {
              Swal.fire({
                text: "สำเร็จ",
                icon: "success",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              }).then((result) => {
                location.href="https://www.scb.co.th/th/personal-banking/other-services/e-donation/e-donation-foundation/f5.html";
              });
            }, "0");
          }
        }
      );
    } else {
      Swal.fire({
        text: "กรุณาตรวจสอบข้อมูล",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f"
      });
    }
  };
  const checkMyError = (_val) => {
    if (errorArray.indexOf(_val) > -1) {
      return true;
    }
    return false;
  };

  
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className="register-page">
          <div className="register-block-form support-form">
            <div className="block-form-inner">
              {/* ===== */}
              <div className="row">
                <div className="col-12">
                  <div className="register-block-form-title">
                    <h3>ข้อมูลผู้บริจาค</h3>
                  </div>
                </div>
              </div>
              {/* ===== */}
              <div className="row-fm-register row">
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input
                      type="text"
                      className="fm-control"
                      placeholder="ชื่อ"
                    >
                      <input
                        {...register("name")}
                        maxLength={100}
                        data-type="textaddress"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("name") ? "error" : ""}
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input
                      type="text"
                      className="fm-control"
                      placeholder="นามสกุล"
                    >
                      <input
                        {...register("lastname")}
                        maxLength={100}
                        data-type="textaddress"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("lastname") ? "error" : ""}
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input
                      type="email"
                      className="fm-control"
                      placeholder="อีเมล์"
                    >
                      <input
                        {...register("email")}
                        maxLength={100}
                        data-type="email"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("email") ? "error" : ""}
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input
                      type="tel"
                      className="fm-control"
                      placeholder="เบอร์โทรศัพท์"
                    >
                      <input
                        {...register("mobile")}
                        maxLength={10}
                        data-type="number"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("mobile") ? "error" : ""}
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input type="text" className="fm-control" placeholder="เลขบัตรประชาชน">
                      <input
                        {...register("iden_no")}
                        maxLength={13}
                        data-type="number"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("iden_no") ? "error" : ""}
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input type="text" className="fm-control" placeholder="จำนวนเงิน">
                      <input
                        {...register("amount")}
                        maxLength={10}
                        data-type="number"
                        onInput={appContext.diFormPattern}
                        className={checkMyError("amount") ? "error" : ""}
                      />
                    </Input>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-12">
                  <p className="remark">กรอกข้อมูล ณ {nowDate}</p>
                  <h3 className="description">การบริจาคให้ &quot;กองทุนเพื่อพัฒนางานบริการวิชาการ&quot; มูลนิธิคณะแพทยศาสตร์ จุฬาลงกรณ์<br></br>
                  เพื่อดำเนินโครงการ Online learning platform &quot;MDCU MedUMORE&quot;</h3>
                </div>
                <div className="col-fm-register col-12 col-md-12 check-box">
                  <div className="item-choose row">
                    <div className={checkMyError("tax") ? "fm-check error" : "fm-check"} onClick={() => toggleTax()}>
                      <input id="tax" type="checkbox" name="tax"/>
                      <div className="text">
                        <div className="i_remark">
                          <i className="icon-ic-circle" />
                        </div>
                        <p>ประสงค์ลดหย่อนภาษี (1 เท่า)<br></br>(ทั้งนี้ หากท่านไม่ได้เลือกตัวเลือกข้างต้นในขั้นตอนนี้ จะไม่สามารถทำการลดหย่อนภาษีในภายหลังได้)</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-12 check-box">
                  <div className="item-choose row">
                    <div className={checkMyError("consent") ? "fm-check error" : "fm-check"} onClick={() => toggleConsent()}>
                      <input id="consent" type="checkbox" name="consent"/>
                      <div className="text">
                        <div className="i_remark">
                          <i className="icon-ic-circle" />
                        </div>
                        <p>ข้าพเจ้ายินยอมให้หน่วยงานที่เกี่ยวข้องเปิดเผยข้อมูลรายการนี้ ให้แก่กรมสรรพากร และ/หรือหน่วยงานที่เกี่ยวข้อง</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-12 col-md-12 action-btn">
                  <button
                    onClick={onSubmit}
                    className="btn-default btn-register"
                  >
                    <span>ยืนยัน</span>
                  </button>
                </div>
              </div>
              {/* ===== */}
            </div>
          </div>
        </div>
      </div>

      <Footer></Footer>
    </div>
  );
}

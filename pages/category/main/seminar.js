import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "/pages/api/user";
import { useState, useEffect, useContext } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import Image from "next/image";

import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";
export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/staticSeminar",
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["data"].length == 0) {
    return {
      redirect: { destination: "/" },
    };
  }

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/seminar",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      utoken,
      user,
    },
  };
}

export default function Article({
  seo_data,
  errorCode,
  data,
  params,
  utoken,
  user,
}) {
  // console.log(data);
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);

  appContext.setToken(utoken);
  if (errorCode) {
    return <Error statusCode={errorCode} />;
  }

  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space">
        </div>
        <div className="container custom-container">
          {data && data.data.length>0 ? (
            data.data.map((val, key) =>
              <div key={key} className="about-section-item">
                <div className="about-banner">
                  <div className="img-div">
                    {val && val.banner_d && val.banner_d!=null && val.banner_d!='' && val.banner_d!='null' ?(
                      <Image alt="" className={'d-none d-xl-block'} src={val.banner_d} layout="fill" />  
                    ):null}
                    {val && val.banner_m && val.banner_m!=null && val.banner_m!='' && val.banner_m!='null' ?(
                      <Image alt="" className={'d-block d-xl-none'} src={val.banner_m} layout="fill" />  
                    ):null}
                  </div>
                </div>
                <div className="about-content">
                  <h5>{val.title}</h5>
                  <div
                    dangerouslySetInnerHTML={{ __html: val.details }}
                  ></div>
                  {val.cta!=null&&val.cta!=''&&val.cta!='null'&&val.link!=null&&val.link!=''&&val.link!='null' ? (
                    <div className="about-action">
                      <a href={val.link}>
                        <Button className="green-button-text">
                          <span className="name">{val.cta}</span>
                        </Button>
                      </a>
                    </div>
                  ):null}
                </div>
              </div>
            )
          ):null}
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
}

import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import { useState, useEffect, useContext } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import Link from "next/link";
import Image from "next/image";
export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/order/get/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();
  if (data.status == "false") {
    return {
      redirect: { destination: "/" },
    };
  }
  //SEO
  const seo_res = await fetch(process.env.NEXT_PUBLIC_API + "/api/seo/order", {
    method: "POST",
  });
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      utoken,
      user,
    },
  };
}
export default function Thanks({
  seo_data,
  errorCode,
  data,
  params,
  utoken,
  user,
}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div
          className={`container custom-container space-between-content thanks-page-container`}
        >
          <div className="thanks-page white">
            {data.order.total_price==0 ? (
              <i className="icon-ic-tick-thanks"></i>
            ):null}
            <h3>
              ยืนยันคำสั่งซื้อ
              <br className="is-mobile" />
              ของคุณแล้ว
            </h3>
            {data.payment == 1 ? (
              <p>
                ชำระค่าสินค้าได้ที่ <br className="is-mobile" />
                ชื่อบัญชี : ...
                <br className="is-desktop" />
                <br className="is-mobile" />
                ธนาคาร : กรุงเทพ สาขา :.... <br className="is-mobile" />
                เลขที่บัญชี : 666-8-55555-7 <br className="is-mobile" />
                ภายหลังการชำระเงินแล้ว กรุณาส่งหลักฐาน
                <br className="is-mobile" />
                การโอนเงินมาที่ จุฬา หรือ Line: @jjjj
                <br className="is-desktop" />
                <br className="is-mobile" />
                (ท่านสามารถชำระเงินได้ภายใน 1 วัน นับจากวันที่ท่านสั่งซื้อ{" "}
                <br className="is-mobile" />
                โดยหากพ้นกำหนดการชำระเงินแล้ว ทางบริษัทขอสงวนสิทธิ์
                <br className="is-mobile" />
                ในการยกเลิกรายการสั่งซื้อของท่านโดยอัตโนมัติ)
              </p>
            ) : data.payment == 2 ? (
              <p>
                ชำระค่าสินค้าได้ที่ <br className="is-mobile" />
                ชื่อบัญชี : ...
                <br className="is-desktop" />
                <br className="is-mobile" />
                ธนาคาร : กรุงเทพ สาขา :.... <br className="is-mobile" />
                เลขที่บัญชี : 666-8-55555-7 <br className="is-mobile" />
                ภายหลังการชำระเงินแล้ว กรุณาส่งหลักฐาน
                <br className="is-mobile" />
                การโอนเงินมาที่ จุฬา หรือ Line: @jjjj
                <br className="is-desktop" />
                <br className="is-mobile" />
                (ท่านสามารถชำระเงินได้ภายใน 2 วัน นับจากวันที่ท่านสั่งซื้อ{" "}
                <br className="is-mobile" />
                โดยหากพ้นกำหนดการชำระเงินแล้ว ทางบริษัทขอสงวนสิทธิ์
                <br className="is-mobile" />
                ในการยกเลิกรายการสั่งซื้อของท่านโดยอัตโนมัติ)
              </p>
            ) : (
              data.order.total_price==0 ? (
                null
              ):(
                <>
                  <h4>ช่องทางการชำระเงิน</h4>
                    <div className="top-card">
                      <p>ธนาคารกสิกรไทย</p>
                    </div>
                  <div className="payment-card">
                    <div className="col-md-6 col-12 in-card-left">
                      <p>
                        เลขที่บัญชี 127-8-44476-3
                        <br />
                        บริษัทเอ็มดีซียู เมดโนเวชั่น จำกัด
                        <br />
                        สาขา โรงพยาบาลจุฬาลงกรณ์
                      </p>
                    </div>
                    <div className="col-md-6 col-12 in-card-right">
                      <div className="bankLogoDiv">
                        <Image alt="" layout="intrinsic" src="/assets/images/KBANK.png" width={266} height={176} />
                      </div>
                    </div>
                  </div>
                  <br></br>
                  <p>เมื่อท่านยืนยันการชำระเงินเรียบร้อยแล้วระบบจะทำการตรวจสอบและอนุมัติรายการภายใน 24 ชม.</p>
                </>
              )
              
            )}

            <div className="item-purchase-report border0">
              <div className="purchase-report-action">
                {data.order.status == 2 ? (
                  data.order.buy_type==1 ? (
                    <Link href="/profile">
                      <button className="btn-default">
                        <span>เริ่มเรียนได้เลย</span>
                      </button>
                    </Link>
                  ):(
                    <Link href="/profile/history">
                      <button className="btn-default">
                        <span>ตรวจสอบคำสั่งซื้อ</span>
                      </button>
                    </Link>
                  )
                ) : (
                  <Link href={`/payment/${data.order_no}`}>
                    <button className="btn-default">
                      <span>ยืนยันการชำระเงิน</span>
                    </button>
                  </Link>
                )}
              </div>
              {/* <Link href="">
            <a >Download ใบเสร็จ</a>
            </Link> */}
            </div>
          </div>
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
}

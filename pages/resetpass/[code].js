import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../api/user";
import { useState, useEffect, useContext } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import Link from "next/link";


import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
  TextArea,
} from "semantic-ui-react";

import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  if (user) {
    return {
      redirect: { destination: "/profile" },
    };
  }

  const params = context.query;

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/resetpass",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO
 
  return {
    props: {
      session: await getSession(context),
      seo_data,
      params,
    },
  };
}
export default function Verify({ seo_data, params }) {
  const router = useRouter();
  const appContext = useContext(AppContext);
  const { register, setValue, getValues, handleSubmit } = useForm({
    defaultValues: {},
  });
  const [errorArray, setErrorArray] = useState([]);
  const showPassword = () => {
    var attr_pass = document.getElementById("input_password").getAttribute('type');
    if(attr_pass=='password'){
      document.getElementById("input_password").setAttribute("type", "text");
    }else{
      document.getElementById("input_password").setAttribute("type", "password");
    }
    document.getElementById("input_eye").classList.toggle('slash');
  };
  const showPassword2 = () => {
    var attr_pass2 = document.getElementById("input_password2").getAttribute('type');
    if(attr_pass2=='password'){
      document.getElementById("input_password2").setAttribute("type", "text");
    }else{
      document.getElementById("input_password2").setAttribute("type", "password");
    }
    document.getElementById("input_eye2").classList.toggle('slash');
  };
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>

      <div className="main-body">
        <div className="fix-space"></div>
        <div className="register-page">
          <div className="register-block-form">
            <div className="block-form-inner">
              {/* ===== */}
              <div className="row">
                <div className="col-12">
                  <div className="register-block-form-title">
                    <h3>FORGOT PASSWORD</h3>
                    <p>กรอกรหัสผ่านใหม่</p>
                  </div>
                </div>
              </div>
              {/* ===== */}
              <div className="row-fm-register row">
                <div className="col-fm-register col-12 col-md-12">
                  <div className="item-fm">
                    <Input
                      id="input_password"
                      type="password"
                      className="fm-control"
                      placeholder="รหัสผ่าน (อย่างน้อย 6 ตัวอักษร)"
                    >
                      <input
                        {...register("password")}
                        maxLength={25}
                        data-type="password"
                        onInput={appContext.diFormPattern}
                      />
                    </Input>
                    <div className="eye-button"><i id="input_eye" className="eye icon" onClick={showPassword}></i></div>
                  </div>
                </div><div className="col-fm-register col-12 col-md-12">
                  <div className="item-fm">
                    <Input
                      id="input_password2"
                      type="password"
                      className="fm-control"
                      placeholder="ยืนยันรหัสผ่าน"
                    >
                      <input
                        {...register("confirm_password")}
                        maxLength={25}
                        data-type="password"
                        onInput={appContext.diFormPattern}
                      />
                    </Input>
                    <div className="eye-button"><i id="input_eye2" className="eye icon" onClick={showPassword2}></i></div>
                  </div>
                </div>
                <div className="col-12 col-md-6 offset-md-3">
                  <button
                    className="btn-default btn-register"
                    onClick={()=>
                      {
                        setValue("token", params.code);
                        let obj = getValues();
                        let bol = true;
                        var errarr = [];
                        if (!appContext.isPassword(obj["password"])) {
                          bol = false;
                        }
                        if (obj["password"] != obj['confirm_password']) {
                          bol = false;
                        }

                        if (bol) {
                          appContext.loadApi(
                            process.env.NEXT_PUBLIC_API + "/api/user/resetpass",
                            obj,
                            (data) => {
                              if (data["status"] == "success") {
                                Swal.fire({
                                  text: "ระบบได้รีเซ็ทรหัสผ่านของคุณแล้ว",
                                  icon: "info",
                                  confirmButtonText: "ปิด",
                                  confirmButtonColor: "#648d2f"
                                });
                                router.replace("/");
                              } else {
                                if (data["status"] == "expire") {
                                  Swal.fire({
                                    html: "ลิงค์อ้างอิงหมดอายุ <br>กรุณาแจ้งลืมรหัสผ่านใหม่อีกครั้ง",
                                    icon: "error",
                                    confirmButtonText: "ปิด",
                                    confirmButtonColor: "#648d2f"
                                  });
                                } else {
                                  Swal.fire({
                                    html: "ลิงค์อ้างอิงไม่สามารถใช้งานได้  <br>กรุณาแจ้งลืมรหัสผ่านใหม่อีกครั้ง",
                                    icon: "error",
                                    confirmButtonText: "ปิด",
                                    confirmButtonColor: "#648d2f"
                                  });
                                }
                              }
                            }
                          );
                        } else {
                          if (obj["password"] != obj['confirm_password']) {
                            Swal.fire({
                              html: "กรุณาตรวจสอบข้อมูล <br>รหัสผ่านไม่ตรงกัน",
                              icon: "error", 
                              confirmButtonText: "ปิด",
                              confirmButtonColor: "#648d2f"
                            });
                          }else{
                            Swal.fire({
                              text: "กรุณาตรวจสอบข้อมูล",
                              icon: "error",
                              confirmButtonText: "ปิด",
                              confirmButtonColor: "#648d2f"
                            });
                          }
                        }
                      }
                    }
                  >
                    <span>ยืนยันการเปลี่ยนรหัส</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer></Footer>
    </div>
  );
}

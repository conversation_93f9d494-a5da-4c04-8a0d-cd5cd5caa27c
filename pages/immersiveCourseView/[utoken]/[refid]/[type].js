import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../../../api/user";
import { useState, useEffect, useContext } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";

import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import GroupCategory from "/themes/components/groupCategory";
import Image from "next/image";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const params = context.query;
  const utoken = params.utoken;
  const refid = params.refid;
  const type = params.type;
  const user = await getUser(utoken);
  

  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }

  return {
    props: {
      session: await getSession(context),
      utoken,
      refid,
      type
    },
  };
}

export default function Article({utoken,refid,type}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  const [firstLoad, setFirstLoad] = useState(true);
  useEffect(() => {
    if (firstLoad && utoken) {
      setFirstLoad(false);
      appContext.submitImmersiveCourseApp(utoken,refid,type);
    }
  }, [firstLoad]);
  return (
    <>
      <style>{`
        #onetrust-consent-sdk{
          display:none!important;
        }
        body{
          background:black!important;
        }
        .icon-ic-lock{
          color:white!important;
        }
      `}</style>
      <div className="main-all">
        <div className="main-body">
          
        </div>
      </div>
    </>
  );
}

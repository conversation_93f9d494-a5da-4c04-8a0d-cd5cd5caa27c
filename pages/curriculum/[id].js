import React from 'react'
import { useState, useEffect, useContext, useReducer } from 'react'
import nookies from 'nookies'
import { getUser } from "../api/user";
import { getSession } from 'next-auth/react'
import AppContext from '/libs/contexts/AppContext'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Header from '/themes/header/header'
import Footer from '/themes/footer/footer'
import { Button, Modal, Input, Select } from 'semantic-ui-react'
import Image from 'next/image'
import Swal from 'sweetalert2'
import NumberFormat from 'react-number-format'
import CurriculumDetailDescription from '@/themes/components/curriculum/CurriculumDetailDescription'
import CurriculumStats from '@/themes/components/curriculum/curriculumStats'
import CurriculumFlow from '@/themes/components/curriculum/curriculumFlow'

function show_add_list() {
  document.getElementById('fmCreateListAdd').classList.toggle('active')
}

export async function getServerSideProps(context) {
  const cookies = nookies.get(context)
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + '_token'])
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + '_token'] || ''
  const params = context.query

  // if (!user) {
  //   return {
  //     redirect: { destination: '/' },
  //   }
  // }

  const curriculumFormData = new URLSearchParams()
  curriculumFormData.append('key', params.id)
  curriculumFormData.append('utoken', utoken)

  const curriculumRes = await fetch(
    process.env.NEXT_PUBLIC_API + '/api/mdcu/curriculum/details',
    {
      body: curriculumFormData,
      method: 'POST',
    }
  )
  const curriculumData = await curriculumRes.json()

  // Mock progress สำหรับทดสอบ
  // if (curriculumData.courses && curriculumData.courses.length > 0) {
  //   curriculumData.courses = curriculumData.courses.map((course, index) => {
  //     // กำหนด progress ตามต้องการ
  //     const mockProgress = [100, 100, 100, 100, 100, 100, 100, 100, 100, 75, 100, 100, 100, 100, 75];
  //     return {
  //       ...course,
  //       progress_percent: mockProgress[index] || 0
  //     };
  //   });
  // }

  // if (curriculumData['status'] == 'false') {
  //   return {
  //     redirect: { destination: '/' },
  //   }
  // }

  // SEO data
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + '/api/seo/curriculum/' + params.id,
    {
      method: 'POST',
    }
  )
  const seo_data = await seo_res.json()

  const formDataView = new URLSearchParams()
  formDataView.append('utoken', utoken)
  formDataView.append('curriculum_id', params.id)

  await fetch(process.env.NEXT_PUBLIC_API + '/api/mdcu/addCurriculumView', {
    body: formDataView,
    method: 'POST',
  })

  return {
    props: {
      session: await getSession(context),
      seo_data,
      curriculumData,
      utoken,
    },
  }
}

export default function Curriculum({ seo_data, curriculumData, utoken }) {
  const appContext = useContext(AppContext)
  const router = useRouter()
  const { locale } = router
  const [lang] = useState(locale)
  const [, forceUpdate] = useReducer((x) => x + 1, 0)

  const [relateData, setRelateData] = useState(null)
  const [reloadRelate, setReloadRelate] = useState(true)
  const [reloadStar, setReloadStar] = useState(true)

  const [discountCode, setDiscountCode] = useState('')
  const [discountChannel, setDiscountChannel] = useState('')
  const [curriculumProPrice, setCurriculumProPrice] = useState(
    curriculumData?.data?.pro_price || 0
  )
  const [curriculumPrice, setCurriculumPrice] = useState(
    curriculumData?.data?.price || 0
  )
  const [curriculumPriceReload, setCurriculumPriceReload] = useState(true)

  const [addPlaylist, setAddPlaylist] = useState(false)
  const [curriculumId, setCurriculumId] = useState(null)
  const [playlistTitle, setPlaylistTitle] = useState(null)
  const [playlistSelect, setPlaylistSelect] = useState(null)
  const [playListOptions, setPlayListOptions] = useState([])

  useEffect(() => {
    if (reloadRelate) {
      setReloadRelate(false)
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          '/api/mdcu/course/relate/' +
          curriculumData?.data?.id +
          '/curriculum',
        null,
        (res_relate) => {
          if (res_relate['status'] == 'success') {
            setRelateData(res_relate.data)
            setPlayListOptions(res_relate.userPlaylist || [])
          }
        }
      )
    }
  }, [reloadRelate, appContext, curriculumData?.data?.id])

  function checkFree() {
    if (
      (curriculumData?.data?.price == 0 ||
        (curriculumData?.data?.is_promotion == 1 &&
          curriculumData?.data?.pro_price == 0) ||
        curriculumData?.data?.is_internal ||
        curriculumData?.data?.is_subscription ||
        curriculumData?.data?.is_volume ||
        curriculumData?.data?.expire_date) &&
      discountCode == ''
    ) {
      return true
    } else {
      return false
    }
  }

  function handleCourseRating(ratingValue) {
    const formData = new URLSearchParams()
    formData.append('course_id', curriculumData?.data?.id)
    formData.append('rate', ratingValue)

    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + '/api/mdcu/addCourseRate',
      formData,
      (obj) => {
        if (obj['status'] == 'success') {
          setReloadStar(false)
          curriculumData['data']['rate'] = obj['rate']
          curriculumData['data']['rating'] = obj['rating']
          setTimeout(() => {
            setReloadStar(true)
          }, 0)
          forceUpdate()
        }
      }
    )
  }

  function groupCategoryCallback(_type, _id, _title) {
    if (appContext.user) {
      if (_type == 'cart') {
        //   const str="ขออภัย MDCU MedUMORE\n" +
        //   "ปิดปรับปรุงระบบชำระเงินชั่วคราว\n"+
        //   "ท่านจะได้รับการแจ้งเตือน\n"+
        //  "เมื่อการดำเนินการเสร็จสิ้น";
        //   Swal.fire({
        //     html: '<pre style="font-family: \'Kanit\'">' + str + '</pre>',
        //     icon: "warning",
        //     confirmButtonText: "ตกลง",
        //     confirmButtonColor: "#648d2f"
        //   });
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        formData.append('content_type', 'course')
        formData.append('discount_code', discountCode)
        formData.append('discount_channel', discountChannel)
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/checkCart',
          formData,
          (res_check) => {
            if (res_check['status'] == 'success') {
              if (res_check['count'] == 0) {
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + '/api/mdcu/addCart',
                  formData,
                  (data) => {
                    if (data['status'] == 'limit_buy') {
                      Swal.fire({
                        text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                        icon: 'info',
                        confirmButtonText: 'ปิด',
                        confirmButtonColor: '#648d2f',
                      }).then((result) => {})
                    } else {
                      appContext.setReloadCart(true)
                      setReloadRelate(true)

                      document
                        .getElementsByClassName('main-header')[0]
                        .classList.add('active_cart')
                      document
                        .getElementsByClassName('group-menu-cart')[0]
                        .classList.add('on_show')
                      document.body.classList.add('open_cart')
                      document
                        .getElementsByClassName('group-menu-f-mobile')[0]
                        .classList.remove('on_show')
                    }
                  }
                )
              } else {
                Swal.fire({
                  html: translateText(
                    'คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'
                  ),
                  icon: 'info',
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: translateText('ยืนยัน'),
                  cancelButtonText: translateText('ยกเลิก'),
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + '/api/mdcu/clearCart',
                      formData,
                      (res_clear) => {
                        if (res_clear['status'] == 'success') {
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + '/api/mdcu/addCart',
                            formData,
                            (data) => {
                              if (data['status'] == 'limit_buy') {
                                Swal.fire({
                                  text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                                  icon: 'info',
                                  confirmButtonText: 'ปิด',
                                  confirmButtonColor: '#648d2f',
                                }).then((result) => {})
                              } else {
                                appContext.setReloadCart(true)
                                setReloadRelate(true)

                                document
                                  .getElementsByClassName('main-header')[0]
                                  .classList.add('active_cart')
                                document
                                  .getElementsByClassName('group-menu-cart')[0]
                                  .classList.add('on_show')
                                document.body.classList.add('open_cart')
                                document
                                  .getElementsByClassName(
                                    'group-menu-f-mobile'
                                  )[0]
                                  .classList.remove('on_show')
                              }
                            }
                          )
                        }
                      }
                    )
                  }
                })
              }
            }
          }
        )
      } else if (_type == 'group') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        formData.append('content_type', 'group')
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/checkCart',
          formData,
          (res_check) => {
            if (res_check['status'] == 'success') {
              if (res_check['count'] == 0) {
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + '/api/mdcu/addCart',
                  formData,
                  (data) => {
                    if (data['status'] == 'limit_buy') {
                      Swal.fire({
                        text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                        icon: 'info',
                        confirmButtonText: 'ปิด',
                        confirmButtonColor: '#648d2f',
                      }).then((result) => {})
                    } else {
                      appContext.setReloadCart(true)
                      setReloadRelate(true)

                      document
                        .getElementsByClassName('main-header')[0]
                        .classList.add('active_cart')
                      document
                        .getElementsByClassName('group-menu-cart')[0]
                        .classList.add('on_show')
                      document.body.classList.add('open_cart')
                      document
                        .getElementsByClassName('group-menu-f-mobile')[0]
                        .classList.remove('on_show')
                    }
                  }
                )
              } else {
                Swal.fire({
                  html: translateText(
                    'คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'
                  ),
                  icon: 'info',
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: translateText('ยืนยัน'),
                  cancelButtonText: translateText('ยกเลิก'),
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + '/api/mdcu/clearCart',
                      formData,
                      (res_clear) => {
                        if (res_clear['status'] == 'success') {
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + '/api/mdcu/addCart',
                            formData,
                            (data) => {
                              if (data['status'] == 'limit_buy') {
                                Swal.fire({
                                  text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                                  icon: 'info',
                                  confirmButtonText: 'ปิด',
                                  confirmButtonColor: '#648d2f',
                                }).then((result) => {})
                              } else {
                                appContext.setReloadCart(true)
                                setReloadRelate(true)

                                document
                                  .getElementsByClassName('main-header')[0]
                                  .classList.add('active_cart')
                                document
                                  .getElementsByClassName('group-menu-cart')[0]
                                  .classList.add('on_show')
                                document.body.classList.add('open_cart')
                                document
                                  .getElementsByClassName(
                                    'group-menu-f-mobile'
                                  )[0]
                                  .classList.remove('on_show')
                              }
                            }
                          )
                        }
                      }
                    )
                  }
                })
              }
            }
          }
        )
      } else if (_type == 'favourite') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        formData.append('content_type', 'course')
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/addFavourite',
          formData,
          (res_fav) => {
            if (res_fav['status'] == 'success') {
              if (res_fav['action'] == 'add') {
                document
                  .querySelector('.favourite_class_' + _id)
                  .classList.add('active')
              } else {
                document
                  .querySelector('.favourite_class_' + _id)
                  .classList.remove('active')
              }
            }
          }
        )
      } else if (_type == 'free') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/addOrderFree',
          formData,
          (obj) => {
            if (obj['status'] == 'success') {
              location.reload()
            } else {
              Swal.fire({
                text: obj['msg'] || 'เกิดข้อผิดพลาด',
                icon: 'error',
                confirmButtonText: 'ปิด',
                confirmButtonColor: '#648d2f',
              })
            }
          }
        )
      } else if (_type == 'playlist') {
        setCourseId(_id)
        setPlaylistTitle('')
        setPlaylistSelect('')
        setAddPlaylist(true)
      } else if (_type == 'vip') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/addOrderVip',
          formData,
          (obj) => {
            if (obj['status'] == 'success') {
              if (data['data']['trailer_media'] == 8) {
                Swal.fire({
                  text: 'สำเร็จ',
                  icon: 'success',
                  confirmButtonText: translateText('ปิด'),
                  confirmButtonColor: '#648d2f',
                }).then((result) => {
                  location.reload()
                })
              } else {
                // Swal.fire({
                //   text: "ลงทะเบียนสำเร็จ เริ่มเรียนได้เลย",
                //   icon: "success",
                //   confirmButtonText: translateText('ปิด'),
                //   confirmButtonColor: "#648d2f"
                // }).then((result) => {
                //   location.reload();
                // });
                data['data']['allowed'] = true
                if (data['data']['type'] == 'zoom') {
                  data['data']['zoom_join_url'] = obj['zoom_join_url']
                  window.open(obj['zoom_join_url'], '_blank')
                } else {
                  setReloadVdo(true)
                  setReloadQuiz(true)
                  setModalVdoOpen(false)
                  setReloadQuizBox(false)
                  setPlayAudio(false)
                  setTimeout(() => {
                    appContext.loadApi(
                      process.env.NEXT_PUBLIC_API +
                        '/api/mdcu/course/vdo/' +
                        data.data.id,
                      null,
                      (data_res) => {
                        if (
                          data_res &&
                          data_res['data'] &&
                          data_res['data'][keySelect]
                        ) {
                          if (vdoList[keySelect]['ep_oculus'] == 1) {
                            if (
                              vdoList[keySelect]['pre_test']['question'] &&
                              vdoList[keySelect]['pre_test']['question']
                                .length > 0 &&
                              vdoList[keySelect]['pre_test']['allowed'] &&
                              vdoList[keySelect]['pre_test']['first_time'] &&
                              !vdoList[keySelect]['pre_test']['check'] &&
                              vdoList[keySelect]['pre_test']['is_show']
                            ) {
                              // alert('pre_test_first');
                              selectPreTest(keySelect)
                            } else {
                              if (data_res['data']['type'] == 'podcast') {
                                setPlayAudio(true)
                                audioReady(vdoList[keySelect]['watching'])
                              } else {
                                location.href =
                                  '/course/' + params.id + '?ep=' + keySelect
                                // setModalVdoOpen(true);
                              }
                            }
                          } else {
                            setModalVdoOpen(false)
                            setPlayAudio(false)
                            const formData = new URLSearchParams()
                            formData.append('course_id', data['data']['id'])
                            formData.append(
                              'lesson_id',
                              vdoList[keySelect]['id']
                            )
                            appContext.sendApi(
                              process.env.NEXT_PUBLIC_API +
                                '/api/user/genOculus',
                              formData,
                              (pin_res) => {
                                if (pin_res.status == 'success') {
                                  setOculusPin(pin_res.pin)
                                  setModalOculus(true)
                                }
                              }
                            )
                          }
                        }
                        setReloadQuizBox(true)
                      }
                    )
                  }, '100')
                }
              }
            } else if (obj['status'] == 'limit_buy') {
              Swal.fire({
                text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                icon: 'info',
                confirmButtonText: 'ปิด',
                confirmButtonColor: '#648d2f',
              }).then((result) => {})
            } else {
              Swal.fire({
                text: translateText('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: 'error',
                confirmButtonText: translateText('ปิด'),
                confirmButtonColor: '#648d2f',
              })
            }
          }
        )
      } else if (_type == 'volume') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/addOrderVolume',
          formData,
          (obj) => {
            if (obj['status'] == 'success') {
              if (data['data']['trailer_media'] == 8) {
                Swal.fire({
                  text: 'สำเร็จ',
                  icon: 'success',
                  confirmButtonText: translateText('ปิด'),
                  confirmButtonColor: '#648d2f',
                }).then((result) => {
                  location.reload()
                })
              } else {
                // Swal.fire({
                //   text: "ลงทะเบียนสำเร็จ เริ่มเรียนได้เลย",
                //   icon: "success",
                //   confirmButtonText: translateText('ปิด'),
                //   confirmButtonColor: "#648d2f"
                // }).then((result) => {
                //   location.reload();
                // });
                data['data']['allowed'] = true
                if (data['data']['type'] == 'zoom') {
                  data['data']['zoom_join_url'] = obj['zoom_join_url']
                  window.open(obj['zoom_join_url'], '_blank')
                } else {
                  setReloadVdo(true)
                  setReloadQuiz(true)
                  setModalVdoOpen(false)
                  setReloadQuizBox(false)
                  setPlayAudio(false)
                  setTimeout(() => {
                    appContext.loadApi(
                      process.env.NEXT_PUBLIC_API +
                        '/api/mdcu/course/vdo/' +
                        data.data.id,
                      null,
                      (data_res) => {
                        if (
                          data_res &&
                          data_res['data'] &&
                          data_res['data'][keySelect]
                        ) {
                          if (vdoList[keySelect]['ep_oculus'] == 1) {
                            if (
                              vdoList[keySelect]['pre_test']['question'] &&
                              vdoList[keySelect]['pre_test']['question']
                                .length > 0 &&
                              vdoList[keySelect]['pre_test']['allowed'] &&
                              vdoList[keySelect]['pre_test']['first_time'] &&
                              !vdoList[keySelect]['pre_test']['check'] &&
                              vdoList[keySelect]['pre_test']['is_show']
                            ) {
                              // alert('pre_test_first');
                              selectPreTest(keySelect)
                            } else {
                              if (data_res['data']['type'] == 'podcast') {
                                setPlayAudio(true)
                                audioReady(vdoList[keySelect]['watching'])
                              } else {
                                location.href =
                                  '/course/' + params.id + '?ep=' + keySelect
                                // setModalVdoOpen(true);
                              }
                            }
                          } else {
                            setModalVdoOpen(false)
                            setPlayAudio(false)
                            const formData = new URLSearchParams()
                            formData.append('course_id', data['data']['id'])
                            formData.append(
                              'lesson_id',
                              vdoList[keySelect]['id']
                            )
                            appContext.sendApi(
                              process.env.NEXT_PUBLIC_API +
                                '/api/user/genOculus',
                              formData,
                              (pin_res) => {
                                if (pin_res.status == 'success') {
                                  setOculusPin(pin_res.pin)
                                  setModalOculus(true)
                                }
                              }
                            )
                          }
                        }
                        setReloadQuizBox(true)
                      }
                    )
                  }, '100')
                }
              }
            } else if (obj['status'] == 'limit_buy') {
              Swal.fire({
                text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                icon: 'info',
                confirmButtonText: 'ปิด',
                confirmButtonColor: '#648d2f',
              }).then((result) => {})
            } else {
              Swal.fire({
                text: translateText('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: 'error',
                confirmButtonText: translateText('ปิด'),
                confirmButtonColor: '#648d2f',
              })
            }
          }
        )
      } else if (_type == 'internal') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/addOrderInternal',
          formData,
          (obj) => {
            if (obj['status'] == 'success') {
              if (data['data']['trailer_media'] == 8) {
                Swal.fire({
                  text: 'สำเร็จ',
                  icon: 'success',
                  confirmButtonText: translateText('ปิด'),
                  confirmButtonColor: '#648d2f',
                }).then((result) => {
                  location.reload()
                })
              } else {
                // Swal.fire({
                //   text: "ลงทะเบียนสำเร็จ เริ่มเรียนได้เลย",
                //   icon: "success",
                //   confirmButtonText: translateText('ปิด'),
                //   confirmButtonColor: "#648d2f"
                // }).then((result) => {
                //   location.reload();
                // });
                data['data']['allowed'] = true
                if (data['data']['type'] == 'zoom') {
                  data['data']['zoom_join_url'] = obj['zoom_join_url']
                  window.open(obj['zoom_join_url'], '_blank')
                } else {
                  setReloadVdo(true)
                  setReloadQuiz(true)
                  setModalVdoOpen(false)
                  setReloadQuizBox(false)
                  setPlayAudio(false)
                  setTimeout(() => {
                    appContext.loadApi(
                      process.env.NEXT_PUBLIC_API +
                        '/api/mdcu/course/vdo/' +
                        data.data.id,
                      null,
                      (data_res) => {
                        if (
                          data_res &&
                          data_res['data'] &&
                          data_res['data'][keySelect]
                        ) {
                          if (vdoList[keySelect]['ep_oculus'] == 1) {
                            if (
                              vdoList[keySelect]['pre_test']['question'] &&
                              vdoList[keySelect]['pre_test']['question']
                                .length > 0 &&
                              vdoList[keySelect]['pre_test']['allowed'] &&
                              vdoList[keySelect]['pre_test']['first_time'] &&
                              !vdoList[keySelect]['pre_test']['check'] &&
                              vdoList[keySelect]['pre_test']['is_show']
                            ) {
                              // alert('pre_test_first');
                              selectPreTest(keySelect)
                            } else {
                              if (data_res['data']['type'] == 'podcast') {
                                setPlayAudio(true)
                                audioReady(vdoList[keySelect]['watching'])
                              } else {
                                location.href =
                                  '/course/' + params.id + '?ep=' + keySelect
                                // setModalVdoOpen(true);
                              }
                            }
                          } else {
                            setModalVdoOpen(false)
                            setPlayAudio(false)
                            const formData = new URLSearchParams()
                            formData.append('course_id', data['data']['id'])
                            formData.append(
                              'lesson_id',
                              vdoList[keySelect]['id']
                            )
                            appContext.sendApi(
                              process.env.NEXT_PUBLIC_API +
                                '/api/user/genOculus',
                              formData,
                              (pin_res) => {
                                if (pin_res.status == 'success') {
                                  setOculusPin(pin_res.pin)
                                  setModalOculus(true)
                                }
                              }
                            )
                          }
                        }
                        setReloadQuizBox(true)
                      }
                    )
                  }, '100')
                }
              }
            } else if (obj['status'] == 'limit_buy') {
              Swal.fire({
                text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                icon: 'info',
                confirmButtonText: 'ปิด',
                confirmButtonColor: '#648d2f',
              }).then((result) => {})
            } else {
              Swal.fire({
                text: translateText('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: 'error',
                confirmButtonText: translateText('ปิด'),
                confirmButtonColor: '#648d2f',
              })
            }
          }
        )
      }
    } else {
      Swal.fire({
        text: translateText('กรุณาเข้าสู่ระบบค่ะ'),
        icon: 'info',
        confirmButtonText: 'ตกลง',
        confirmButtonColor: '#648d2f',
      }).then((result) => {
        appContext.setOpen(true)
      })
    }
  }

  function addDiscountCode(code, type) {
    if (appContext.user) {
      const formData = new URLSearchParams()
      formData.append('course_id', curriculumData?.data?.id)
      formData.append('content_type', 'course') // เปลี่ยนจาก 'curriculum' เป็น 'course'
      formData.append('discount_code', code)
      formData.append('discount_channel', type)
      setCurriculumPriceReload(true)

      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + '/api/mdcu/addDiscountCode',
        formData,
        (obj) => {
          setCurriculumPriceReload(false)
          if (obj['is_discount']) {
            setCurriculumProPrice(obj['pro_price'])
            setCurriculumPrice(obj['price'])
            Swal.fire({
              text: translateText('ใช้โค้ดส่วนลดสำเร็จ'),
              icon: 'success',
              confirmButtonText: translateText('ปิด'),
              confirmButtonColor: '#648d2f',
            })
          } else {
            Swal.fire({
              text: obj['msg'] || translateText('โค้ดส่วนลดไม่ถูกต้อง'),
              icon: 'error',
              confirmButtonText: translateText('ปิด'),
              confirmButtonColor: '#648d2f',
            })
          }
          setDiscountChannel(obj['discount_channel'] || '')
          setDiscountCode(obj['discount_code'] || '')
          setTimeout(() => {
            setCurriculumPriceReload(true)
          }, 0)
        }
      )
    } else {
      Swal.fire({
        text: translateText('กรุณาเข้าสู่ระบบค่ะ'),
        icon: 'info',
        confirmButtonText: 'ตกลง',
        confirmButtonColor: '#648d2f',
      }).then((result) => {
        appContext.setOpen(true)
      })
    }
  }

  function submitPlaylist() {
    if (
      (playlistTitle != null && playlistTitle != '') ||
      (playlistSelect != null && playlistSelect != '')
    ) {
      const formData = new URLSearchParams()
      formData.append('course_id', curriculumId)
      formData.append('title', playlistTitle)
      formData.append('select', playlistSelect)
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + '/api/mdcu/addPlaylist',
        formData,
        (res_play) => {
          setAddPlaylist(false)
          if (res_play['status'] == 'success') {
            Swal.fire({
              text: translateText('เพิ่มไปยังเพลย์ลิสต์สำเร็จ'),
              icon: 'success',
              confirmButtonText: translateText('ปิด'),
              confirmButtonColor: '#648d2f',
            })
          } else {
            Swal.fire({
              text: res_play['msg'] || translateText('เกิดข้อผิดพลาด'),
              icon: 'error',
              confirmButtonText: translateText('ปิด'),
              confirmButtonColor: '#648d2f',
            })
          }
        }
      )
    } else {
      Swal.fire({
        text: translateText('กรุณาเลือกเพลย์ลิสต์'),
        icon: 'error',
        confirmButtonText: translateText('ปิด'),
        confirmButtonColor: '#648d2f',
      })
    }
  }

  function translateText(value) {
    if (lang === 'en') {
      const translations = {
        ฟรี: 'Free',
        รับฟรี: 'Get Free',
        บาท: 'Baht',
        ปกติ: 'Regular',
        ราคา: 'Price',
        คอร์สเกี่ยวข้อง: 'Related Courses',
        เริ่มเรียน: 'Start Learning',
        ซื้อหลักสูตรนี้: 'Buy This Curriculum',
        ซื้อคอร์สนี้: 'Buy This Course',
        โค้ดส่วนลด: 'Discount Code',
        'เลือกรับ Scholarship จากผู้สนับสนุน':
          'Select Scholarship from Sponsors',
        เพิ่มไปยังเพลย์ลิสต์ของคุณ: 'Add to Your Playlist',
        เพลย์ลิสต์ของคุณ: 'Your Playlist',
        สร้างเพลย์ลิสต์: 'Create Playlist',
        ป้อนชื่อเพลย์ลิสต์: 'Enter Playlist Name',
        กรุณาเข้าสู่ระบบค่ะ: 'Please login',
        ปิด: 'Close',
        ใช้โค้ดส่วนลดสำเร็จ: 'Discount code applied successfully',
        โค้ดส่วนลดไม่ถูกต้อง: 'Invalid discount code',
        เพิ่มไปยังเพลย์ลิสต์สำเร็จ: 'Added to playlist successfully',
        เกิดข้อผิดพลาด: 'An error occurred',
        กรุณาเลือกเพลย์ลิสต์: 'Please select a playlist',
      }
      return translations[value] || value
    }
    return value
  }

  appContext.setToken(utoken)

  const schemaData = {
    '@context': 'https://schema.org/',
    '@type': 'Course',
    name: curriculumData?.data?.title,
    image: curriculumData?.data?.image,
    description: curriculumData?.data?.description,
    brand: 'MedUMore',
    provider: {
      '@type': 'Person',
      name: curriculumData?.data?.speaker_name,
    },
    publisher: {
      '@type': 'Organization',
      name: 'คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย',
      logo: 'https://www.md.chula.ac.th/wp-content/uploads/2016/02/MDCU-Logo-300x300.jpg',
      url: 'https://www.md.chula.ac.th/',
    },
    author: {
      '@type': 'Organization',
      logo: 'https://www.md.chula.ac.th/wp-content/uploads/2016/02/MDCU-Logo-300x300.jpg',
      name: 'คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย',
      url: 'https://www.md.chula.ac.th/',
    },
  }

  const coverImage = curriculumData?.data?.image
  const bannerImage = curriculumData?.data?.cover
  const coverTitle = curriculumData?.data?.title || 'Curriculum Cover'

  function addLike(_comment_id) {
    if (appContext.user) {
      const formData = new URLSearchParams()
      formData.append('comment_id', _comment_id)
      setReloadRelate(true)
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + '/api/mdcu/addLike',
        formData,
        (obj) => {
          if (obj['status'] == 'success') {
          }
        }
      )
    } else {
      Swal.fire({
        text: translateText('กรุณาเข้าสู่ระบบค่ะ'),
        icon: 'info',
        confirmButtonText: 'ตกลง',
        confirmButtonColor: '#648d2f',
      }).then((result) => {
        appContext.setOpen(true)
      })
    }
  }

  function handleCurriculumCourseActions(_type, _id, _title) {
    if (appContext.user) {
      if (_type == 'cart') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        formData.append('content_type', 'course')
        formData.append('discount_code', discountCode)
        formData.append('discount_channel', discountChannel)
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/checkCart',
          formData,
          (res_check) => {
            if (res_check['status'] == 'success') {
              if (res_check['count'] == 0) {
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + '/api/mdcu/addCart',
                  formData,
                  (data) => {
                    if (data['status'] == 'limit_buy') {
                      Swal.fire({
                        text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                        icon: 'info',
                        confirmButtonText: 'ปิด',
                        confirmButtonColor: '#648d2f',
                      }).then((result) => {})
                    } else {
                      appContext.setReloadCart(true)
                      setReloadRelate(true)

                      document
                        .getElementsByClassName('main-header')[0]
                        .classList.add('active_cart')
                      document
                        .getElementsByClassName('group-menu-cart')[0]
                        .classList.add('on_show')
                      document.body.classList.add('open_cart')
                      document
                        .getElementsByClassName('group-menu-f-mobile')[0]
                        .classList.remove('on_show')
                    }
                  }
                )
              } else {
                Swal.fire({
                  html: translateText(
                    'คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'
                  ),
                  icon: 'info',
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: translateText('ยืนยัน'),
                  cancelButtonText: translateText('ยกเลิก'),
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + '/api/mdcu/clearCart',
                      formData,
                      (res_clear) => {
                        if (res_clear['status'] == 'success') {
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + '/api/mdcu/addCart',
                            formData,
                            (data) => {
                              if (data['status'] == 'limit_buy') {
                                Swal.fire({
                                  text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                                  icon: 'info',
                                  confirmButtonText: 'ปิด',
                                  confirmButtonColor: '#648d2f',
                                }).then((result) => {})
                              } else {
                                appContext.setReloadCart(true)
                                setReloadRelate(true)

                                document
                                  .getElementsByClassName('main-header')[0]
                                  .classList.add('active_cart')
                                document
                                  .getElementsByClassName('group-menu-cart')[0]
                                  .classList.add('on_show')
                                document.body.classList.add('open_cart')
                                document
                                  .getElementsByClassName(
                                    'group-menu-f-mobile'
                                  )[0]
                                  .classList.remove('on_show')
                              }
                            }
                          )
                        }
                      }
                    )
                  }
                })
              }
            }
          }
        )
      } else if (_type == 'favourite') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        formData.append('content_type', 'course')
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/addFavourite',
          formData,
          (res_fav) => {
            if (res_fav['status'] == 'success') {
              setReloadRelate(true)

              if (res_fav['action'] == 'add') {
                document
                  .querySelector('.favourite_class_' + _id)
                  ?.classList.add('active')
              } else {
                document
                  .querySelector('.favourite_class_' + _id)
                  ?.classList.remove('active')
              }
            }
          }
        )
      } else if (_type == 'free') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/addOrderFree',
          formData,
          (obj) => {
            if (obj['status'] == 'success') {
              location.reload()
            } else {
              Swal.fire({
                text: obj['msg'] || 'เกิดข้อผิดพลาด',
                icon: 'error',
                confirmButtonText: 'ปิด',
                confirmButtonColor: '#648d2f',
              })
            }
          }
        )
      } else if (_type == 'playlist') {
        setCurriculumId(_id)
        setPlaylistTitle('')
        setPlaylistSelect('')
        setAddPlaylist(true)
      } else if (_type == 'vip') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/addOrderVip',
          formData,
          (obj) => {
            if (obj['status'] == 'success') {
              if (obj['data'] && obj['data']['trailer_media'] == 8) {
                Swal.fire({
                  text: 'สำเร็จ',
                  icon: 'success',
                  confirmButtonText: translateText('ปิด'),
                  confirmButtonColor: '#648d2f',
                }).then((result) => {
                  location.reload()
                })
              } else {
                // Navigate to course page
                if (obj['data'] && obj['data']['slug']) {
                  window.location.href = `/course/${obj['data']['slug']}`
                } else {
                  location.reload()
                }
              }
            } else if (obj['status'] == 'limit_buy') {
              Swal.fire({
                text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                icon: 'info',
                confirmButtonText: 'ปิด',
                confirmButtonColor: '#648d2f',
              }).then((result) => {})
            } else {
              Swal.fire({
                text: translateText('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: 'error',
                confirmButtonText: translateText('ปิด'),
                confirmButtonColor: '#648d2f',
              })
            }
          }
        )
      } else if (_type == 'volume') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/addOrderVolume',
          formData,
          (obj) => {
            if (obj['status'] == 'success') {
              if (obj['data'] && obj['data']['trailer_media'] == 8) {
                Swal.fire({
                  text: 'สำเร็จ',
                  icon: 'success',
                  confirmButtonText: translateText('ปิด'),
                  confirmButtonColor: '#648d2f',
                }).then((result) => {
                  location.reload()
                })
              } else {
                if (obj['data'] && obj['data']['slug']) {
                  window.location.href = `/course/${obj['data']['slug']}`
                } else {
                  location.reload()
                }
              }
            } else if (obj['status'] == 'limit_buy') {
              Swal.fire({
                text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                icon: 'info',
                confirmButtonText: 'ปิด',
                confirmButtonColor: '#648d2f',
              }).then((result) => {})
            } else {
              Swal.fire({
                text: translateText('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: 'error',
                confirmButtonText: translateText('ปิด'),
                confirmButtonColor: '#648d2f',
              })
            }
          }
        )
      } else if (_type == 'internal') {
        const formData = new URLSearchParams()
        formData.append('course_id', _id)
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + '/api/mdcu/addOrderInternal',
          formData,
          (obj) => {
            if (obj['status'] == 'success') {
              if (obj['data'] && obj['data']['trailer_media'] == 8) {
                Swal.fire({
                  text: 'สำเร็จ',
                  icon: 'success',
                  confirmButtonText: translateText('ปิด'),
                  confirmButtonColor: '#648d2f',
                }).then((result) => {
                  location.reload()
                })
              } else {
                if (obj['data'] && obj['data']['slug']) {
                  window.location.href = `/course/${obj['data']['slug']}`
                } else {
                  location.reload()
                }
              }
            } else if (obj['status'] == 'limit_buy') {
              Swal.fire({
                text: 'ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว',
                icon: 'info',
                confirmButtonText: 'ปิด',
                confirmButtonColor: '#648d2f',
              }).then((result) => {})
            } else {
              Swal.fire({
                text: translateText('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: 'error',
                confirmButtonText: translateText('ปิด'),
                confirmButtonColor: '#648d2f',
              })
            }
          }
        )
      }
    } else {
      Swal.fire({
        text: translateText('กรุณาเข้าสู่ระบบค่ะ'),
        icon: 'info',
        confirmButtonText: 'ตกลง',
        confirmButtonColor: '#648d2f',
      }).then((result) => {
        appContext.setOpen(true)
      })
    }
  }

  return (
    <div className="main-all page-course">
      <Head>
        {seo_data?.seo ? (
          seo_data.seo.map((val, key) =>
            val.name === 'title' ? (
              <React.Fragment key={key}>
                <title>{val.content}</title>
                <meta name={val.name} content={val.content} />
                <meta name={'og:' + val.name} content={val.content} />
                <meta name={'twitter:' + val.name} content={val.content} />
              </React.Fragment>
            ) : (
              <React.Fragment key={key}>
                <meta name={val.name} content={val.content} />
                <meta name={'og:' + val.name} content={val.content} />
                <meta name={'twitter:' + val.name} content={val.content} />
              </React.Fragment>
            )
          )
        ) : (
          <title>MDCU : MedU MORE - หลักสูตร</title>
        )}

        <meta name="twitter:card" content="summary_large_image" />
        <meta name="og:type" content="website" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />
      </Head>

      <Header />

      <div className="main-body bg-white">
        <div className="fix-space"></div>

        <div style={{ width: '100%', overflow: 'hidden' }}>
          {bannerImage ? (
            <Image
              src={bannerImage}
              alt={coverTitle}
              width={1920}
              height={400}
              style={{ 
                width: '100%', 
                height: '400px', 
                objectFit: 'cover' 
              }}
              priority
            />
          ) : (
            <div
              style={{
                width: '100%',
                height: '400px',
                backgroundColor: '#f0f0f0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <span>No Cover Image</span>
            </div>
          )}
        </div>

        <div className="container custom-container space-between-content">
          <div className="curriculum">
            <div className="podcast-page">
              <div className="row podcast-row">
                <div className="col-lg-5 first">
                  <div>
                    {coverImage ? (
                      <Image
                        src={coverImage}
                        alt={coverTitle}
                        width={600}
                        height={400}
                         style={{
                          width: '100%',
                          height: 'auto',
                          borderRadius: '10px',
                        }}
                      />
                    ) : (
                      <div
                        style={{
                          width: '100%',
                          height: '300px',
                          backgroundColor: '#f0f0f0',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          borderRadius: '10px',
                        }}
                      >
                        <span>No Image</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="col-lg-5">
                  <CurriculumDetailDescription
                    data={curriculumData?.data || {}}
                    callback={handleCourseRating}
                    lang={lang}
                  />

                  {/* Discount Code and Scholarship Section (copied from course page) */}
                  {/* {curriculumData?.data?.allowed ||
                  curriculumData?.data?.order_status == 4 ||
                  curriculumData?.data?.order_status ==
                    1 ? null : curriculumData?.data?.is_promotion == 1 ? (
                    curriculumData.data.pro_price == 0 &&
                    discountCode == '' ? null : (
                      <div className="row podcastjs">
                        <div className="col-12 col-md-6">
                          <p className="space-between-content-top title-button-group">
                            {translateText('โค้ดส่วนลด')}
                          </p>
                          <CourseDetailCode
                            callback={addDiscountCode}
                            lang={lang}
                          />
                        </div>
                        {curriculumData &&
                        curriculumData?.scholarshipList &&
                        curriculumData?.scholarshipList?.length > 0 ? (
                          <div className="col-12 col-md-6">
                            <p className="space-between-content-top title-button-group">
                              {translateText(
                                'เลือกรับ Scholarship จากผู้สนับสนุน'
                              )}
                            </p>
                            <CourseDetailScholarship
                              callback={addDiscountCode}
                              name="curriculumScholarship"
                              data={curriculumData?.scholarshipList}
                            />
                          </div>
                        ) : null}
                      </div>
                    )
                  ) : curriculumData?.data?.price == 0 &&
                    discountCode == '' ? null : (
                    <div className="row podcastjs">
                      <div className="col-12 col-md-6">
                        <p className="space-between-content-top title-button-group">
                          {translateText('โค้ดส่วนลด')}
                        </p>
                        <CourseDetailCode
                          callback={addDiscountCode}
                          lang={lang}
                        />
                      </div>
                      {curriculumData &&
                      curriculumData?.scholarshipList &&
                      curriculumData?.scholarshipList?.length > 0 ? (
                        <div className="col-12 col-md-6">
                          <p className="space-between-content-top title-button-group">
                            {translateText(
                              'เลือกรับ Scholarship จากผู้สนับสนุน'
                            )}
                          </p>
                          <CourseDetailScholarship
                            callback={addDiscountCode}
                            name="curriculumScholarship"
                            data={curriculumData?.scholarshipList}
                          />
                        </div>
                      ) : null}
                    </div>
                  )} */}
                </div>

                <div className="col-lg-2">
                  {curriculumPriceReload &&
                  curriculumData &&
                  curriculumData?.data &&
                  curriculumData?.data?.is_soon == false ? (
                    curriculumData.data.allowed ||
                    curriculumData.data.order_status == 4 ||
                    curriculumData.data.order_status ==
                      1 ? null : curriculumData.data.is_subscription ? (
                      <div className="block-course-detail-price text-center podcast-price">
                        <div className="description-big-price">Member</div>
                      </div>
                    ) : curriculumData.data.is_internal ||
                      curriculumData.data.is_volume ||
                      curriculumData.data.expire_date ? (
                      <div className="block-course-detail-price text-center podcast-price">
                        <div className="description-big-price">
                          {translateText('ฟรี')}
                        </div>
                      </div>
                    ) : curriculumData.data.is_promotion == 1 ? (
                      <div className="block-course-detail-price text-center podcast-price">
                        {/* <div className="description-sale">
                          <NumberFormat
                            value={curriculumData.data.price}
                            displayType={'text'}
                            thousandSeparator={true}
                            renderText={(value, props) => (
                              <span {...props}>{translateText('ปกติ')} {value}</span>
                            )}
                          />
                        </div> */}
                        {curriculumData.data.pro_price == 0 ||
                        curriculumData.data.expire_date ? (
                          <div className="description-big-price">
                            {translateText('ฟรี')}
                          </div>
                        ) : (
                          <div className="description-big-price">
                            {translateText('ราคา')}
                            <NumberFormat
                              value={curriculumData.data.pro_price}
                              displayType={'text'}
                              thousandSeparator={true}
                              renderText={(value, props) => (
                                <span {...props}>{value}</span>
                              )}
                            />
                            {translateText('บาท')}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="block-course-detail-price text-center podcast-price">
                        {curriculumData.data.price == 0 ? (
                          <div className="description-big-price">
                            {translateText('ฟรี')}
                          </div>
                        ) : (
                          <div className="description-big-price">
                            {translateText('ราคา')}
                            <NumberFormat
                              value={curriculumData.data.price}
                              displayType={'text'}
                              thousandSeparator={true}
                              renderText={(value, props) => (
                                <span {...props}>{value}</span>
                              )}
                            />
                            {translateText('บาท')}
                          </div>
                        )}
                      </div>
                    )
                  ) : null}

                  {curriculumData?.data?.allowed ||
                  curriculumData?.data?.order_status == 4 ||
                  curriculumData?.data?.order_status == 1 ||
                  curriculumData?.data?.is_soon == true ? null : (
                    <div className="block-to-buy-course">
                      {checkFree() ? (
                        curriculumData.data.is_subscription ? (
                          <button
                            className="btn-to-buy-course"
                            onClick={() =>
                              groupCategoryCallback(
                                'vip',
                                curriculumData?.data?.id,
                                ''
                              )
                            }
                          >
                            <span>{translateText('เริ่มเรียน')}</span>
                          </button>
                        ) : curriculumData.data.is_volume ? (
                          <button
                            className="btn-to-buy-course"
                            onClick={() =>
                              groupCategoryCallback(
                                'volume',
                                curriculumData?.data?.id,
                                ''
                              )
                            }
                          >
                            <span>{translateText('เริ่มเรียน')}</span>
                          </button>
                        ) : curriculumData.data.is_internal &&
                          curriculumData.data.user_internal ? (
                          <button
                            className="btn-to-buy-course"
                            onClick={() =>
                              groupCategoryCallback(
                                'internal',
                                curriculumData?.data?.id,
                                ''
                              )
                            }
                          >
                            <span>{translateText('เริ่มเรียน')}</span>
                          </button>
                        ) : (
                          <button
                            className="btn-to-buy-course"
                            onClick={() =>
                              groupCategoryCallback(
                                'free',
                                curriculumData?.data?.id,
                                ''
                              )
                            }
                          >
                            <span>{translateText('เริ่มเรียน')}</span>
                          </button>
                        )
                      ) : (
                        <button
                          className="btn-to-buy-course"
                          onClick={() =>
                            groupCategoryCallback(
                              'cart',
                              curriculumData?.data?.id,
                              ''
                            )
                          }
                        >
                          <span>{translateText('ซื้อหลักสูตรนี้')}</span>
                        </button>
                      )}
                    </div>
                  )}
                </div>

                <div className="col-lg-12">
                  <CurriculumStats curriculumData={curriculumData} />
                  <CurriculumFlow
                    curriculumData={curriculumData}
                    onCourseAction={handleCurriculumCourseActions}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <Modal
          className="modalCreateList"
          onClose={() => setAddPlaylist(false)}
          onOpen={() => setAddPlaylist(true)}
          open={addPlaylist}
        >
          <Modal.Content className="modalCreateListContent">
            <div className="block-modal-CreateList">
              <div className="inner">
                <h3>{translateText('เพิ่มไปยังเพลย์ลิสต์ของคุณ')}</h3>
              </div>
              <div className="fm-CreateList-select">
                {playListOptions.length > 0 ? (
                  <div className="item-fm">
                    <Select
                      className="fm-control"
                      placeholder={translateText('เพลย์ลิสต์ของคุณ')}
                      options={playListOptions}
                      onChange={(event, data) => {
                        setPlaylistSelect(data.value)
                      }}
                    />
                  </div>
                ) : null}
                <button
                  className="btn-add-list"
                  onClick={() => show_add_list()}
                >
                  <span>{translateText('สร้างเพลย์ลิสต์')}</span>
                </button>
              </div>
              <div id="fmCreateListAdd" className="fm-CreateList-add">
                <div className="item-fm">
                  <Input
                    id="create_list"
                    type="text"
                    className="fm-control"
                    placeholder={translateText('ป้อนชื่อเพลย์ลิสต์')}
                    onChange={(event) => setPlaylistTitle(event.target.value)}
                  />
                </div>
              </div>
              <div className="fm-CreateList-action">
                <button
                  className="btn-add-list"
                  onClick={() => submitPlaylist()}
                >
                  <span>ตกลง</span>
                </button>
              </div>
            </div>
          </Modal.Content>
        </Modal>

        <Footer />
      </div>
    </div>
  )
}

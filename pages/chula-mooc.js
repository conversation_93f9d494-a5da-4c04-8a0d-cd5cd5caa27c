import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import Router, { useRouter } from "next/router";
import { getUser } from "./api/user";
import { useState, useEffect, useContext } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import { Swiper, SwiperSlide } from "swiper/react";
import { Zoom, FreeMode, Pagination, Controller, Navigation } from "swiper";
import Image from "next/image";

import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import CourseFilter from "/themes/components/courseFilter";
import CourseSearch from "/themes/components/courseSearch";
import CardDeafultGroup from "/themes/components/cardDeafultGroup";
import PaginationPages from "/themes/components/paginationPages";
import Swal from "sweetalert2";
import {
  Menu,
  Button,
  Modal,
  Select,
  Input,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";

import "swiper/css";
import "swiper/css/pagination";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";
  //SEO
  const seo_res = await fetch(process.env.NEXT_PUBLIC_API + "/api/seo/course", {
    method: "POST",
  });
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  //Filter
  const filter_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/getMoocFilterData",
    {
      method: "POST",
    }
  );
  const filter_errorCode = filter_res.ok ? false : filter_res.statusCode;
  const filter_data = await filter_res.json();
  //Filter

  return {
    props: {
      session: await getSession(context),
      seo_data,
      utoken,
      user,
      filter_data,
    },
  };
}

export default function ChulaMooc({ seo_data, utoken, user, filter_data }) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);

  const [reloadUrl, setReloadUrl] = useState(true);
  const [reloadCourse, setReloadCourse] = useState(false);
  const [courseData, setCourseData] = useState(null);
  const [filterData, setFilterData] = useState(null);

  const [keyword, setKeyword] = useState(null);
  const [sort, setSort] = useState(null);
  const [filter, setFilter] = useState([]);
  const [tag, setTag] = useState([]);
  const [pageList, setPageList] = useState([]);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(24);
  const [isTag, setIsTag] = useState(2);

  const [addPlaylist, setAddPlaylist] = useState(false);
  const [courseId, setCourseId] = useState(null);
  const [playlistTitle, setPlaylistTitle] = useState(null);
  const [playlistSelect, setPlaylistSelect] = useState(null);
  const [playListOptions, setPlayListOptions] = useState([]);
  const [top, setTop] = useState(true);
  const [loadingApi, setLoadingApi] = useState(true);

  useEffect(() => {
    if (reloadUrl) {
      setReloadUrl(false);
      if (!appContext.isNull(appContext.jGet("keyword"))) {
        setKeyword(appContext.jGet("keyword"));
      }
      if (!appContext.isNull(appContext.jGet("disease"))) {
        selectCateFirst("disease", appContext.jGet("disease"));
      }
      if (!appContext.isNull(appContext.jGet("categories"))) {
        selectCateFirst("categories", appContext.jGet("categories"));
      }
      if (!appContext.isNull(appContext.jGet("speaker"))) {
        selectCateFirst("speaker", appContext.jGet("speaker"));
      }
      if (!appContext.isNull(appContext.jGet("learner"))) {
        selectCateFirst("learner", appContext.jGet("learner"));
      }
      if (!appContext.isNull(appContext.jGet("level"))) {
        selectCateFirst("level", appContext.jGet("level"));
      }
      if (!appContext.isNull(appContext.jGet("sub_categories"))) {
        selectCateFirst("sub_categories", appContext.jGet("sub_categories"));
      }
      if (!appContext.isNull(appContext.jGet("organ_system"))) {
        selectCateFirst("organ_system", appContext.jGet("organ_system"));
      }
      if (!appContext.isNull(appContext.jGet("gender"))) {
        selectCateFirst("gender", appContext.jGet("gender"));
      }
      if (!appContext.isNull(appContext.jGet("department"))) {
        selectCateFirst("department", appContext.jGet("department"));
      }
      if (!appContext.isNull(appContext.jGet("cme_score"))) {
        selectCateFirst("cme_score", appContext.jGet("cme_score"));
      }
      if (!appContext.isNull(appContext.jGet("is_certificate"))) {
        selectCateFirst("is_certificate", appContext.jGet("is_certificate"));
      }
      if (!appContext.isNull(appContext.jGet("price"))) {
        selectCateFirst("price", appContext.jGet("price"));
      }
      if (!appContext.isNull(appContext.jGet("promotion"))) {
        selectCateFirst("promotion", appContext.jGet("promotion"));
      }
      if (!appContext.isNull(appContext.jGet("year"))) {
        selectCateFirst("year", appContext.jGet("year"));
      }
      if (!appContext.isNull(appContext.jGet("duration"))) {
        selectCateFirst("duration", appContext.jGet("duration"));
      }

      if (
        !appContext.isNull(appContext.jGet("tag")) &&
        !appContext.isNull(appContext.jGet("tag_id"))
      ) {
        var obj = {
          id: parseInt(appContext.jGet("tag_id")),
          title: appContext.jGet("tag"),
        };
        tag.push(obj);
        setIsTag(1);
      }
      setReloadCourse(true);
    }
  }, [reloadUrl]);

  useEffect(() => {
    if (reloadCourse) {
      setLoadingApi(true);
      setCourseData(null);
      if (top) {
        window.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      }
      setTop(true);
      const formData = new URLSearchParams();
      formData.append("filter", JSON.stringify(filter));
      formData.append("tag", JSON.stringify(tag));
      formData.append("sort", sort);
      formData.append("keyword", keyword);
      formData.append("is_tag", isTag);
      formData.append("limit", limit);
      formData.append("page", page);
      setReloadCourse(false);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/getMoocFilter",
        formData,
        (obj) => {
          setTag(obj["tag_data"]);
          setIsTag(2);
          setCourseData(obj);
          setLoadingApi(false);

          var pages = [];
          for (var i = 0; i < obj["all_page"]; i++) {
            pages.push(i + 1);
          }
          setPageList(pages);
          setPlayListOptions(obj.userPlaylist);
        }
      );
      // console.log("reloadCourse");
      setFilterData(filter_data);
    }
  }, [reloadCourse]);

  function selectSort(_value) {
    setSort(_value);
    setPage(1);
    setReloadCourse(true);
  }
  function keywordSort(_value) {
    setKeyword(_value);
    setPage(1);
    setReloadCourse(true);
  }
  function removeTag(_key) {
    tag.splice(_key, 1);
    if(tag.length==0){
      setIsTag(2);
    }else{
      setIsTag(1);
    }
    setPage(1);
    setReloadCourse(true);
  }
  function selectTag(_key) {
    var search_box = document.getElementById("search_box");
    search_box.value = _key;
    setKeyword(_key);
    setPage(1);
    setReloadCourse(true);
  }
  function clearFilter() {
    setFilter([]);
    setKeyword("");
    var search_box = document.getElementById("search_box");
    search_box.value = "";

    // search_box
    setPage(1);
    setReloadCourse(true);
  }

  function countNumFilter(_key) {
    for (var i = 0; i < filter.length; i++) {
      if (filter[i]["category_key"] == _key) {
        if (filter[i]["cate"].length > 0) {
          return "(" + filter[i]["cate"].length + ")";
        } else {
          return "";
        }
      }
    }
    return "";
  }
  function selectCate(_key, _id) {
    if (!appContext.isNull(appContext.jGet(_key))) {
      for (var i = 0; i < filter.length; i++) {
        if (filter[i].category_key == _key) {
          const index = filter[i].cate.indexOf(_id);
          if (index <= -1) {
            window.history.pushState(
              "",
              "",
              window.location.href.replace(_key + "=", _key + "=" + _id + ",")
            );
          } else {
            const old_arr = appContext.jGet(_key);
            const split_arr = appContext.jGet(_key).split(",");
            const new_arr = [];
            for (var z = 0; z < split_arr.length; z++) {
              if (split_arr[z] != _id) {
                new_arr.push(split_arr[z]);
              }
            }
            if (new_arr.length > 0) {
              var new_text = "";
              for (var z = 0; z < new_arr.length; z++) {
                if (new_text != "") {
                  new_text += ",";
                }
                new_text += new_arr[z].toString();
              }
              window.history.pushState(
                "",
                "",
                window.location.href.replace(
                  _key + "=" + old_arr,
                  _key + "=" + new_text
                )
              );
            } else {
              if (window.location.href.indexOf("?" + _key) > -1) {
                window.history.pushState(
                  "",
                  "",
                  window.location.href.replace("?" + _key + "=" + old_arr, "")
                );
              } else {
                window.history.pushState(
                  "",
                  "",
                  window.location.href.replace("&" + _key + "=" + old_arr, "")
                );
              }
              if (window.location.href.indexOf("course&") > -1) {
                window.history.pushState(
                  "",
                  "",
                  window.location.href.replace("course&", "course?")
                );
              }
            }
          }
        }
      }
    } else {
      if (window.location.href.indexOf("course?") > -1) {
        window.history.pushState(
          "",
          "",
          window.location.href + "&" + _key + "=" + _id
        );
      } else {
        window.history.pushState(
          "",
          "",
          window.location.href + "?" + _key + "=" + _id
        );
      }
    }
    if (filter.length == 0) {
      const obj = {
        category_key: _key,
        cate: [_id],
      };
      filter.push(obj);
    } else {
      var check_key = 0;
      for (var i = 0; i < filter.length; i++) {
        if (filter[i].category_key != _key) {
          check_key++;
        }
      }
      if (check_key == filter.length) {
        const obj = {
          category_key: _key,
          cate: [_id],
        };
        filter.push(obj);
      } else {
        for (var i = 0; i < filter.length; i++) {
          if (filter[i].category_key == _key) {
            const index = filter[i].cate.indexOf(_id);
            if (index <= -1) {
              filter[i].cate.push(_id);
            } else {
              filter[i].cate.splice(index, 1);
            }
          }
        }
      }
    }
    // console.log(filter)
    setPage(1);
    setReloadCourse(true);
  }
  function selectCateFirst(_key, _id) {
    const id_replace = _id.replaceAll(" ", "");
    const id_arr = id_replace.split(",");
    for (var k = 0; k < id_arr.length; k++) {
      id_arr[k] = parseInt(id_arr[k]);
    }
    if (filter.length == 0) {
      const obj = {
        category_key: _key,
        cate: id_arr,
      };
      filter.push(obj);
    } else {
      var check_key = 0;
      for (var i = 0; i < filter.length; i++) {
        if (filter[i].category_key != _key) {
          check_key++;
        }
      }
      if (check_key == filter.length) {
        const obj = {
          category_key: _key,
          cate: id_arr,
        };
        filter.push(obj);
      } else {
        for (var i = 0; i < filter.length; i++) {
          if (filter[i].category_key == _key) {
            for (var m = 0; m < id_arr.length; m++) {
              const index = filter[i].cate.indexOf(id_arr[m]);
              if (index <= -1) {
                filter[i].cate.push(id_arr[m]);
              }
            }
          }
        }
      }
    }
    setPage(1);
    setReloadCourse(true);
  }

  function nextPage() {
    setPage(page + 1);
    setReloadCourse(true);
  }
  function prevPage() {
    setPage(page - 1);
    setReloadCourse(true);
  }
  function selectPage(_page) {
    setPage(_page);
    setReloadCourse(true);
  }
  function show_add_list() {
    document.getElementById("fmCreateListAdd").classList.toggle("active");
  }
  function groupCategoryCallback(_type, _id, _title) {
    if (user) {
      if (_type == "favourite") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "course");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addFavourite",
          formData,
          (res_fav) => {
            // console.log(data);
            setIsTag(2);
            // setReloadCourse(true);
            setTop(false);
            if(res_fav['status']=='success'){
              if(res_fav['action']=='add'){
                document.querySelector(".favourite_class_"+_id).classList.add("active");
              }else{
                document.querySelector(".favourite_class_"+_id).classList.remove("active");
              }
            }
          }
        );
      } else if (_type == "group") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "group");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
          formData,
          (res_check) => {
            if (res_check["status"] == "success") {
              if (res_check["count"] == 0) {
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                  formData,
                  (data) => {
                    // console.log(data);
                    appContext.setReloadCart(true);
                    appContext.setReloadCourse(true);

                    document
                      .getElementsByClassName("main-header")[0]
                      .classList.add("active_cart");
                    document
                      .getElementsByClassName("group-menu-cart")[0]
                      .classList.add("on_show");
                    document.body.classList.add("open_cart");
                    document
                      .getElementsByClassName("group-menu-f-mobile")[0]
                      .classList.remove("on_show");
                  }
                );
              } else {
                Swal.fire({
                  html: "คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?",
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: "#648d2f",
                  cancelButtonColor: "#d33",
                  confirmButtonText: "ยืนยัน",
                  cancelButtonText: "ยกเลิก",
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                      formData,
                      (res_clear) => {
                        if (res_clear["status"] == "success") {
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                            formData,
                            (data) => {
                              // console.log(data);
                              appContext.setReloadCart(true);
                              appContext.setReloadCourse(true);

                              document
                                .getElementsByClassName("main-header")[0]
                                .classList.add("active_cart");
                              document
                                .getElementsByClassName("group-menu-cart")[0]
                                .classList.add("on_show");
                              document.body.classList.add("open_cart");
                              document
                                .getElementsByClassName(
                                  "group-menu-f-mobile"
                                )[0]
                                .classList.remove("on_show");
                            }
                          );
                        }
                      }
                    );
                  }
                });
              }
            }
          }
        );
      } else if (_type == "playlist") {
        setCourseId(_id);
        setPlaylistTitle("");
        setPlaylistSelect("");
        setAddPlaylist(true);
      } else if (_type == "cart") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "course");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
          formData,
          (res_check) => {
            if (res_check["status"] == "success") {
              if (res_check["count"] == 0) {
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                  formData,
                  (data) => {
                    // console.log(data);
                    appContext.setReloadCart(true);
                    appContext.setReloadCourse(true);

                    document
                      .getElementsByClassName("main-header")[0]
                      .classList.add("active_cart");
                    document
                      .getElementsByClassName("group-menu-cart")[0]
                      .classList.add("on_show");
                    document.body.classList.add("open_cart");
                    document
                      .getElementsByClassName("group-menu-f-mobile")[0]
                      .classList.remove("on_show");
                  }
                );
              } else {
                Swal.fire({
                  html: "คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?",
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: "#648d2f",
                  cancelButtonColor: "#d33",
                  confirmButtonText: "ยืนยัน",
                  cancelButtonText: "ยกเลิก",
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                      formData,
                      (res_clear) => {
                        if (res_clear["status"] == "success") {
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                            formData,
                            (data) => {
                              // console.log(data);
                              appContext.setReloadCart(true);
                              appContext.setReloadCourse(true);

                              document
                                .getElementsByClassName("main-header")[0]
                                .classList.add("active_cart");
                              document
                                .getElementsByClassName("group-menu-cart")[0]
                                .classList.add("on_show");
                              document.body.classList.add("open_cart");
                              document
                                .getElementsByClassName(
                                  "group-menu-f-mobile"
                                )[0]
                                .classList.remove("on_show");
                            }
                          );
                        }
                      }
                    );
                  }
                });
              }
            }
          }
        );
      }
    } else {
      Swal.fire({
        text: "กรุณาเข้าสู่ระบบค่ะ",
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f",
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function submitPlaylist() {
    if (
      (playlistTitle != null && playlistTitle != "") ||
      (playlistSelect != null && playlistSelect != "")
    ) {
      const formData = new URLSearchParams();
      formData.append("course_id", courseId);
      formData.append("title", playlistTitle);
      formData.append("select", playlistSelect);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addPlaylist",
        formData,
        (res_play) => {
          setIsTag(2);
          setTop(false);
          // setReloadCourse(true);
          setAddPlaylist(false);
          if(res_play['status']=='success'){
            if(res_play['action']=='add'){
              document.querySelector(".playlist_class_"+courseId).classList.add("active");
              document.querySelector(".playlist_icon_"+courseId).classList.remove("icon-ic-circle-plus");
              document.querySelector(".playlist_icon_"+courseId).classList.add("icon-ic-tick-thanks");
            }else{
              document.querySelector(".playlist_class_"+courseId).classList.remove("active");
              document.querySelector(".playlist_icon_"+courseId).classList.remove("icon-ic-tick-thanks");
              document.querySelector(".playlist_icon_"+courseId).classList.add("icon-ic-circle-plus");
            }
          }
        }
      );
    } else {
      Swal.fire({
        text: "กรุณาเลือกเพลย์ลิสต์",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f",
      });
    }
  }
  function translateEng(_value) {
    if(lang=='en'){
      if(_value=='เหมาะกับคุณ'){
        return "Suitable for you";
      }else if(_value=='ยอดนิยม'){
        return "Popular";
      }else if(_value=='มาใหม่'){
        return "New arrival";
      }else if(_value=='คอร์สทั้งหมด'){
        return "All courses";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className={`container custom-container space-between-content`}>
          <div className="category-page">
            <div className="row">
              {/* <div
                className="col-12 col-md-3 col-lg-3 col-xl-3 col-filter"
                id="block_filter"
              >
                {filterData ? (
                  <CourseFilter
                    title="คอร์สออนไลน์"
                    tagData={tag}
                    filter={filter}
                    data={filterData["filter_data"]}
                    removeTag={removeTag}
                    selectTag={selectTag}
                    selectCate={selectCate}
                    countNumFilter={countNumFilter}
                    clearFilter={clearFilter}
                  ></CourseFilter>
                ) : null}
              </div> */}
              <div className="col-12 col-md-12 col-lg-12 col-lg-12 col-category">
                <CourseSearch
                  type="course"
                  selectSort={selectSort}
                  keywordSort={keywordSort}
                  default={keyword}
                  lang={lang}
                ></CourseSearch>
                <div className="category-list">
                  {courseData &&
                  courseData["course_data"] &&
                  courseData["course_data"].length > 0 ? (
                    courseData["course_data"].map((val, key) =>
                      val["data"].length > 0 ? (
                        <div key={key}>
                          <div className="category-list-title">
                            <h3>
                              <span>{translateEng(val.group_title)}</span>
                            </h3>
                          </div>
                          <div className="category-list-content">
                            <div className="category-item-desktop">
                              <CardDeafultGroup
                                type="desktop"
                                data={val.data}
                                callback={groupCategoryCallback}
                                index={15 - key}
                                lang={lang}
                              ></CardDeafultGroup>
                            </div>
                            <div className="category-item-mobile">
                              <CardDeafultGroup
                                type="mobile"
                                data={val.data}
                                callback={groupCategoryCallback}
                                index={15 - key}
                                lang={lang}
                              ></CardDeafultGroup>
                            </div>
                          </div>
                        </div>
                      ) : null
                    )
                  ) : 
                    loadingApi ?(
                      <div className="block-not-found white-bg">
                        <div className="inner-not-found">
                          <Image alt="" width={200} height={150} src={'/assets/images/loader-gif.gif'} objectFit='contain' />
                        </div>
                      </div>
                    ):(
                      <div className="block-not-found white-bg">
                        <div className="inner-not-found">
                          <i className="icon-ic-search-x"></i>
                          <p>
                            ขออภัยค่ะ<br></br>ไม่พบข้อมูลที่คุณต้องการ
                          </p>
                          <p></p>
                        </div>
                      </div>
                    )
                  }
                </div>
                {pageList.length > 1 ? (
                  <div className="block-pagination-pages">
                    <div className="ui pagination menu">
                      <a
                        className={`item page ${page == 1 ? "disable" : ""}`}
                        onClick={() => prevPage()}
                      >
                        <i className="icon-ic-left"></i>
                      </a>
                      {pageList.map((val, key) =>
                        page < pageList.length - 2 && val == pageList.length ? (
                          (val >= page - 1 && val <= page + 1) ||
                          (page == 1 && val == 3) ||
                          (page == pageList.length &&
                            val == pageList.length - 2) ||
                          val == 1 ||
                          val == pageList.length ? (
                            <div className="page-flex">
                              <a className="item link dot">...</a>
                              <a
                                className={`item link ${
                                  page == val ? "active" : ""
                                }`}
                                onClick={() => selectPage(val)}
                              >
                                {val}
                              </a>
                            </div>
                          ) : (
                            <a className="item link dot">...</a>
                          )
                        ) : page > 3 && val == 1 ? (
                          (val >= page - 1 && val <= page + 1) ||
                          (page == 1 && val == 3) ||
                          (page == pageList.length &&
                            val == pageList.length - 2) ||
                          val == 1 ||
                          val == pageList.length ? (
                            <div className="page-flex">
                              <a
                                className={`item link ${
                                  page == val ? "active" : ""
                                }`}
                                onClick={() => selectPage(val)}
                              >
                                {val}
                              </a>
                              <a className="item link dot">...</a>
                            </div>
                          ) : (
                            <a className="item link dot">...</a>
                          )
                        ) : (val >= page - 1 && val <= page + 1) ||
                          (page == 1 && val == 3) ||
                          (page == pageList.length &&
                            val == pageList.length - 2) ||
                          val == 1 ||
                          val == pageList.length ? (
                          <a
                            className={`item link ${
                              page == val ? "active" : ""
                            }`}
                            onClick={() => selectPage(val)}
                          >
                            {val}
                          </a>
                        ) : null
                      )}
                      <a
                        className={`item page ${
                          page == pageList.length ? "disable" : ""
                        }`}
                        onClick={() => nextPage()}
                      >
                        <i className="icon-ic-right"></i>
                      </a>
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        className="modalCreateList"
        onClose={() => setAddPlaylist(false)}
        onOpen={() => setAddPlaylist(true)}
        open={addPlaylist}
      >
        <Modal.Content className="modalCreateListContent">
          <div className="block-modal-CreateList">
            <div className="inner">
              <h3>เพิ่มไปยังเพลย์ลิสต์ของคุณ</h3>
            </div>
            <div className="fm-CreateList-select">
              {playListOptions.length > 0 ? (
                <div className="item-fm">
                  <Select
                    className="fm-control"
                    placeholder="เพลย์ลิสต์ของคุณ"
                    options={playListOptions}
                    onChange={(event, data) => {
                      setPlaylistSelect(data.value);
                    }}
                  />
                </div>
              ) : null}
              <button className="btn-add-list" onClick={() => show_add_list()}>
                <span>สร้างเพลย์ลิสต์</span>
              </button>
            </div>
            <div id="fmCreateListAdd" className="fm-CreateList-add">
              <div className="item-fm">
                <Input
                  id="create_list"
                  type="text"
                  className="fm-control"
                  placeholder="ป้อนชื่อเพลย์ลิสต์"
                  onChange={(event) => setPlaylistTitle(event.target.value)}
                ></Input>
              </div>
            </div>
            <div className="fm-CreateList-action">
              <button className="btn-add-list" onClick={() => submitPlaylist()}>
                <span>ตกลง</span>
              </button>
            </div>
          </div>
        </Modal.Content>
      </Modal>
      <Footer></Footer>
    </div>
  );
}

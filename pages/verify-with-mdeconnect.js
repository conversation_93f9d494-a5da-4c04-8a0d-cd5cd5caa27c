import React from 'react'
import Image from 'next/image'
import { useState, useEffect } from 'react'
import _ from 'lodash'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Header from '../themes/header/header'
import Footer from '../themes/footer/footer'
import { useForm } from 'react-hook-form'
// Serverside & Api fetching
import Error from 'next'
import { URLSearchParams } from 'url'
// Serverside & Api fetching
// Auth
import nookies from 'nookies'
// Auth
import Swal from 'sweetalert2'
import { getUser } from './api/user'
import { getSession } from 'next-auth/react'

export async function getServerSideProps(context) {
  const cookies = nookies.get(context)
  // Auth
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + '_token'])
  if (!user) {
    console.log('!user')
  }
  // Auth
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + '_token'] || ''
  const params = context.query
  const formData = new URLSearchParams()
  formData.append('utoken', utoken)
  const res = await fetch(
    process.env.NEXT_PUBLIC_API + '/api/user/profile/main',
    {
      body: formData,
      method: 'POST',
    }
  )
  const errorCode = res.ok ? false : res.statusCode
  const data = await res.json()

  if (data['status'] != 'success') {
    console.log('fetch data ! success')
  }
  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + '/api/seo/profile',
    {
      method: 'POST',
    }
  )
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode
  const seo_data = await seo_res.json()
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      user,
      utoken,
    },
  }
}

export default function VerifyWithMDEConnect({
  seo_data,
  errorCode,
  data,
  params,
  user,
  utoken,
}) {
  const router = useRouter()
  const { locale, pathname, asPath } = router
  const [lang, setLang] = useState(locale)

  const { register, setValue, getValues } = useForm({
    defaultValues: {},
  })
  const [time, setTime] = useState(0)
  const [countTime, setCountTime] = useState(0) // ตัวนี้จะคอยนับว่าเกินกำหนดหรือยัง ถ้าเกินจะให้แสดง Failure
  const [verifiedStatus, setVerifiedStatus] = useState(false)
  const [textResult, setTextResult] = useState('Failure')
  const [isShowForm, setIsShowForm] = useState(true)
  const [pin, setPin] = useState('')
  const [isPolling, setIsPolling] = useState(false)
  const [timeVerify, setTimeVerify] = useState(1800) // เวลาสำหรับ verify 1800 = 30 นาที
  const [firstRedirect, setFirstRedirect] = useState(true)
  // const MDEconnect_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
  // MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8RAczzQghSBi1SqfXzv0
  // TQ5V4obSlghFNsEYonDcuaktcz3RdJld+YMzTlMUJ011I1qNMzrN5Oq8a+JrBoCj
  // no+AA+GHMIlUBmZcAyR2iBZILjklaJA4gGlJa873t++hnvqu3oZHtQEWGPOVig1h
  // s41ENZvCfdBmBrCq99CB6sMXDh/dIkigYSrg6oO0TJTKrcI+ng/0Jn8T7sNENEtu
  // bFBwhpzfW85ZoA86wlSlGAYuhxSmCPJQHjcPxZg1CtKsSFI8mAlC+XhlRPduNAWC
  // fw4UxO8meKC70wcK05clnGbd071DXsOX872HhLgOpaAagWc3JnxdPIg4L2xVCHU+
  // GwIDAQAB
  // -----END PUBLIC KEY-----`;

  const MDEconnect_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
  MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAn2XDVgavpvILEgm5iHpa
  /tXFs54TsaKsZHwYbD6EfydcH7z5/hDUqVEbsiSOT2Y2vs7HSSQEtcalGrc6dRnx
  1qE8UmgN9bleUPXG8A6HPuHADnUgBKqQfOD9JZAZpNL5oEMGgvEjA7KKjE5PptV+
  r2E/Ke+by/8/+M1d2w9RX3sQRcuVn3WXYNteSE2A7rVSY1lr+8R9YHjnAzTp4nnZ
  gZRLEy45eb6ANHWx1d4Gix2gDzEBVV7/+r00YAJFr0F8usJ6I7eFSOVbxGJAEcFp
  yBEHCgAM7qnvjAAQtDhOoszkIKYbbnEm/XUxKQgTiRWA0+pALFwnKtYvzbB5Nmzn
  YQIDAQAB
  -----END PUBLIC KEY-----`

  const handleVerifyFailure = (reason) => {
    let message = 'หมดเวลา Verify กรุณาลองใหม่อีกครั้ง'
    let showConfirmButton = true

    switch (reason) {
      case 'EXPIRED':
        message = 'หมดเวลายืนยันกรุณาลองใหม่อีกครั้ง'
        break
      case 'REJECTED':
        message = 'การยืนยันตัวตนถูกปฏิเสธ กรุณาลองใหม่อีกครั้ง'
        showConfirmButton = false
        break
      case 'CANCEL':
        message = 'การยืนยันตัวตนถูกยกเลิก กรุณาลองใหม่อีกครั้ง'
        break
    }

    Swal.fire({
      text: message,
      icon: 'error',
      showCancelButton: true,
      showConfirmButton: showConfirmButton,
      confirmButtonText: 'ลองอีกครั้ง',
      cancelButtonText: 'กลับหน้าแรก',
      confirmButtonColor: '#648d2f',
      cancelButtonColor: '#d33',
    }).then((result) => {
      if (result.isConfirmed) {
        router.reload()
      } else {
        router.push('/')
      }
    })
  }

  useEffect(() => {
    // useEffect page ready
    const pinAuthen = localStorage.getItem('pinAuthen') //ดึง pin ที่ authen แล้ว จาก localstorage
    const redirectPage = localStorage.getItem(
      'verify_mdeconnect_page',
      'REGISTER'
    )
    setTime(0)
    setCountTime(0)
    if (!pinAuthen && !user) {
      // ถ้าไม่มี pinAuthen ใน localStorage (ถูกบันทึกจากหน้า register) && ไม่ได้ login -> ส่งกลับ homepage
      Swal.fire({
        text: 'กรุณา Login หรือสมัครสมาชิก',
        icon: 'info',
        confirmButtonText: 'Okay',
        confirmButtonColor: '#648d2f',
      }).then((result) => {
        router.push('/')
      })
    }

    if (user && user.user_type != 1) {
      // ถ้ามี user แต่ไม่ใช่แพทย์
      router.push('/')
    }

    if (user && user.verify_doctor_status == 'SUCCESS') {
      Swal.fire({
        text: 'คุณเคยยืนยันตัวตนแล้ว',
        icon: 'info',
        confirmButtonText: 'Okay',
        confirmButtonColor: '#648d2f',
      }).then((result) => {
        router.push('/')
      })
    }

    if (!pinAuthen) {
      // ถ้าไม่มี pinAuthen ใน localStorage (ถูกบันทึกจากหน้า register) แต่มี user login -> แสดงหน้าต่างกรอก pin
      setIsShowForm(true)
    }

    if ((pinAuthen && redirectPage == 'REGISTER') || redirectPage == 'EDIT') {
      setIsShowForm(false)
      // polling
      setIsPolling(true)
      // set countdown 30 minute
      setTime(timeVerify)
      localStorage.setItem('verify_mdeconnect_page', 'VERIFY')
    } else {
      // ถ้ามี pinAuthen ใน localStorage (ถูกบันทึกจากหน้า register) -> (send api verify) && (show countdown)
      // send api verify-mdeconnect
      sendVerifyDoctor(pinAuthen, localStorage.getItem('doctorId'))
      // show countdown page
      setIsShowForm(false)
      // polling
      setIsPolling(true)
      // set countdown 30 minute
      setTime(timeVerify)
      // console.log("verify-mdeconnect");
    }
  }, [])

  useEffect(() => {
    // useEffect manage time countdown
    const timer = setInterval(() => {
      setTime((prevTime) => (prevTime > 0 ? prevTime - 1 : 0))
      setCountTime(countTime + 1)
    }, 1000)
    // ถ้า countdown หมดเวลา -> แสดง fail -> show alert redirect to home page
    if (countTime > timeVerify - 1) {
      Swal.fire({
        text: 'หมดเวลา Verify กรุณาลองใหม่อีกครั้ง',
        icon: 'error',
        showCancelButton: true,
        confirmButtonText: 'ลองอีกครั้ง',
        cancelButtonText: 'กลับหน้าแรก',
        confirmButtonColor: '#648d2f',
        cancelButtonColor: '#d33',
      }).then((result) => {
        if (result.isConfirmed) {
          router.reload()
        } else if (result.isDismissed) {
          router.push('/')
        }
      })
    }

    return () => clearInterval(timer) // ล้างตัวจับเวลาเมื่อองค์ประกอบถูกถอดออก
  }, [time])

  useEffect(() => {
    // Polling Function (เช็คเซิร์ฟเวอร์ทุก 5 วินาที)
    if (isPolling) {
      const pollingInterval = setInterval(async () => {
        try {
          const doctorId = localStorage.getItem('doctorId')
          if (doctorId) {
            const payload = { doctorId: doctorId } // ตัวอย่าง payload
            const response = await fetch(
              `${process.env.NEXT_PUBLIC_API}/verify-polling`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
              }
            )
            const data = await response.json()
            console.log(data)
            // ถ้า success == true ให้หยุดการ polling
            if (data.success === true) {
              if (data.status == 'SUCCESS') {
                localStorage.removeItem('pinAuthen')
                localStorage.removeItem('doctorId')
                setVerifiedStatus(true)
                setTextResult('Success')
                clearInterval(pollingInterval) // หยุด polling
                router.push('/') // ทำการ redirect ไปที่หน้าโฮม
              }

              if (data.status == 'EXPIRED' || data.status == 'CANCEL') {
                setVerifiedStatus(true)
                setTextResult('FAIL')
                clearInterval(pollingInterval) // หยุด polling
                handleVerifyFailure(data.status)
              }

              if (data.status == 'REJECTED') {
                setVerifiedStatus(false)
                setTextResult('FAIL')
                clearInterval(pollingInterval)
                handleVerifyFailure(data.status)
              }
            }
          }
        } catch (error) {
          console.error('Error polling server:', error)
        }
      }, 5000) // Polling ทุก ๆ 5 วินาที

      // Cleanup เมื่อคอมโพเนนต์ถูกถอดออก
      return () => clearInterval(pollingInterval)
    }
  }, [isPolling])

  const formatTime = (seconds) => {
    if (!verifiedStatus && time != 0) {
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`
    } else {
      return textResult
    }
  }

  const sendVerifyDoctor = async (pinAuthen = '', doctorId = '') => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API}/verify-mdeconnect`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            pinAuthen: pinAuthen,
            doctorId: doctorId,
          }),
        }
      )

      if (!response.ok) {
        throw new Error('Failed to verify doctor')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Error verifying doctor:', error)
      throw error
    }
  }

  const submitPin = async () => {
    const pinRegex = /^\d{6}$/ // ตรวจสอบว่ามีตัวเลข 6 หลัก

    if (pinRegex.test(pin)) {
      // validate submit success
      // set pin to localstorage
      const publicKey = await importPublicKey(MDEconnect_PUBLIC_KEY)
      const pinAuthen = await encryptPin(publicKey, pin)
      localStorage.setItem('pinAuthen', pinAuthen)

      // set doctor to localStorage
      const doctorId = user?.medical_id
        ? user?.medical_id
        : localStorage.getItem('doctorId')
        ? localStorage.getItem('doctorId')
        : null
      localStorage.setItem('doctorId', doctorId)

      // close form  -> show ui for countdown
      setIsShowForm(false)

      // send api verify-mdeconnect
      // sendVerifyDoctor(pinAuthen, doctorId);

      // polling
      setIsPolling(true)

      // set countdown 30 min
      setTime(timeVerify)
    } else {
      Swal.fire({
        text: 'Invalid PIN: ต้องเป็นตัวเลข 6 หลัก',
        icon: 'info',
        confirmButtonText: 'ปิด',
        confirmButtonColor: '#648d2f',
      })
      console.log('Invalid PIN: ต้องเป็นตัวเลข 6 หลัก')
    }
  }

  async function importPublicKey(pem) {
    // Remove header, footer and whitespace
    const pemContents = pem
      .replace('-----BEGIN PUBLIC KEY-----', '')
      .replace('-----END PUBLIC KEY-----', '')
      .replace(/\s/g, '')

    // Convert base64 to binary
    const binaryDer = window.atob(pemContents)
    const arrayBuffer = new Uint8Array(binaryDer.length)
    for (let i = 0; i < binaryDer.length; i++) {
      arrayBuffer[i] = binaryDer.charCodeAt(i)
    }

    // Import the key
    return window.crypto.subtle.importKey(
      'spki',
      arrayBuffer,
      {
        name: 'RSA-OAEP',
        hash: { name: 'SHA-256' },
      },
      false,
      ['encrypt']
    )
  }

  // Function to encrypt data
  async function encryptPin(publicKey, data) {
    const encoder = new TextEncoder()
    const encodedData = encoder.encode(data)

    const encryptedData = await window.crypto.subtle.encrypt(
      {
        name: 'RSA-OAEP',
      },
      publicKey,
      encodedData
    )

    return btoa(String.fromCharCode(...new Uint8Array(encryptedData)))
  }

  return (
    <div className="main-all">
      <Head>
        <meta
          key={'twitter:card'}
          name={'twitter:card'}
          content="summary_large_image"
        />
        <meta key={'og:type'} name={'og:type'} content="website" />
      </Head>
      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className="register-page">
          <div className="register-block-form">
            <div className="block-form-inner">
              {/* ===== */}
              <div className="row">
                <div className="col-12">
                  <div className="register-block-form-title">
                    {lang == 'en' ? (
                      <h3>Doctor Authentication Provider</h3>
                    ) : (
                      <h3>Doctor Authentication Provider</h3>
                      // <h3>ยืนยันตัวตนแพทย์กับ MD E-connect</h3>
                    )}
                    {lang == 'en' ? (
                      <p>
                        Enter information to verify your identity with MD
                        E-connect.
                      </p>
                    ) : (
                      <p>กรอกข้อมูลเพื่อยืนยันตัวตนกับ MD E-connect</p>
                    )}
                  </div>
                </div>
              </div>
              {/* ===== */}
              <div className="row-fm-register row">
                <div className="col-fm-register col-12">
                  <div className="item-fm">
                    {lang == 'en' ? (
                      <p className="fm-title">Information</p>
                    ) : (
                      <p className="fm-title">ข้อมูล</p>
                    )}
                  </div>
                </div>
                <div className="block-profile-register">
                  <div className="my-info">
                    <div className="info-avatar">
                      <div className="inner">
                        <button className="invisible" onClick={() => {}}>
                          <div className="profileAvatarImg">
                            {getValues('avatar') == null ||
                            getValues('avatar') == '' ||
                            getValues('avatar') == 'null' ? (
                              <div className="imgResponsiveCustom">
                                <Image
                                  className=" cursor-pointer"
                                  src={'/assets/images/user-key.png'}
                                  layout="fill"
                                  objectFit="contain"
                                  sizes="100%"
                                  alt=""
                                />
                              </div>
                            ) : (
                              <div className="imgResponsiveCustom">
                                <Image
                                  className=" cursor-pointer"
                                  src={getValues('avatar')}
                                  layout="fill"
                                  objectFit="contain"
                                  sizes="100%"
                                  alt=""
                                />
                              </div>
                            )}
                          </div>
                        </button>
                        <button className="btn-edit-img" onClick={() => {}}>
                          <i className="icon-ic-image"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {isShowForm && (
                <div className="form-container-pin">
                  <h1>Input PIN</h1>
                  <div>
                    <input
                      type="text"
                      maxLength={6}
                      className="form-input-pin w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="กรอก PIN 6 หลัก..."
                      value={pin}
                      onChange={(e) => setPin(e.target.value)}
                    />
                  </div>
                  <div>
                    <button
                      className="btn-submit-pin px-4 py-2 bg-blue-500 text-white rounded-lg"
                      onClick={submitPin}
                      disabled={time !== 0}
                    >
                      Submit Form
                    </button>
                  </div>
                </div>
              )}
              {!isShowForm && (
                <div>
                  <div className="row">
                    <div className="col-12">
                      <div className="register-block-form-title-mde">
                        {lang == 'en' ? (
                          <h3>
                            The system is having MD eConnect verify the identity
                            and send back the information for further processing
                          </h3>
                        ) : (
                          <h3>
                            ระบบกําลังให้ MD eConnect ยืนยันตัวตน
                            และส่งข้อมูลกลับเพื่อดําเนินการต่อไป
                          </h3>
                        )}
                        {lang == 'en' ? (
                          <p>
                            Please verify your identity in the MD eConnect
                            application within 30 minutes and return here to
                            continue the process.
                          </p>
                        ) : (
                          <p>
                            กรุณาไปยืนยันตัวตนที่แอปพลิเคชัน MD eConnect ภายใน
                            30 นาทีและกลับมาทํารายการต่อที่นี่
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-12 my-3">
                      <div className="register-block-form-title-mde">
                        <h2 className="time-countdown">{formatTime(time)}</h2>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <Footer></Footer>
    </div>
  )
}

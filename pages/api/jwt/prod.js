import jwt from 'jsonwebtoken';
import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  const privateKeyPath = path.join(process.cwd(), 'private.key');
  const privateKey = fs.readFileSync(privateKeyPath, 'utf8');

  const payload = req.body;

  jwt.sign(payload, privateKey, { algorithm: 'RS256' }, function(err, token) {
    if (err) {
      res.status(500).json({ error: 'Internal Server Error' });
      return;
    }
    res.status(200).json({ token });
  });
}

import { Readable } from "stream";
import { openai } from "../../../../../libs/openai"

export const runtime = "nodejs";

export default async function handler(req, res) {
  if (req.method === "POST") {
    try {

      const { content } = req.body;
      if (!content) {
        return res.status(400).json({ error: "Missing required parameter: 'content'." });
      }

      const { threadId } = req.query;
      if (!threadId) {
        return res.status(400).json({ error: "Missing required parameter: 'threadId'." });
      }

      // Check if OpenAI API key and Assistant ID are configured
      if (!process.env.OPENAI_API_KEY) {
        return res.status(500).json({ error: "OpenAI API key not configured" });
      }

      if (!process.env.OPENAI_ASSISTANT_ID) {
        return res.status(500).json({ error: "OpenAI Assistant ID not configured" });
      }

      await openai.beta.threads.messages.create(threadId, {
        role: "user",
        content: content,
      });

      const stream = openai.beta.threads.runs.stream(threadId, {
        assistant_id: process.env.OPENAI_ASSISTANT_ID,
      });

      res.setHeader("Content-Type", "application/json");
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Connection", "keep-alive");

      const nodeStream = Readable.from(stream.toReadableStream());

      // Handle stream errors
      nodeStream.on('error', (error) => {
        console.error('Stream error:', error);
        if (!res.headersSent) {
          res.status(500).json({ error: "Stream error occurred" });
        }
      });

      nodeStream.pipe(res);
    } catch (error) {
      console.error('API Error:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: error.message || "Failed to send message" });
      }
    }
  } else {
    res.status(405).json({ error: "Method Not Allowed" });
  }
}

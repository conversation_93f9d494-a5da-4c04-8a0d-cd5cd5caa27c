import { openai } from "../../../libs/openai"

export default async function handler(req, res) {
  if (req.method === "POST") {
    try {
      const thread = await openai.beta.threads.create();
      res.status(200).json({ threadId: thread.id });
    } catch (error) {
      res.status(500).json({ error: error.message || "Failed to create thread" });
    }
  } else {
    res.status(405).json({ error: "Method Not Allowed" });
  }
}

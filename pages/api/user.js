import useSWR from "swr";
import { URLSearchParams } from "url";

export async function getUser(_token) {
  if(_token){
    const formData = new URLSearchParams();
    formData.append("username", _token);
    formData.append("utoken", _token);
  
    const response = await fetch(
      process.env.NEXT_PUBLIC_API + "/api/user/profile",
      {
        body: formData,
        // headers: {
        //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
        //   "Content-Type": "multipart/form-data",
        // },
        method: "POST",
      }
    );
  
    const jsonData = await response.json();
    if(jsonData['status']=='success'){
      return jsonData['user'];
    }else{
      return null;
    }
  }else{
    return null;
  }
}

export default async function handler(req, res) {
  // const cookies = parseCookies({req})
  // if(cookies[process.env.NEXT_PUBLIC_APP+'_token']){
  //   const jsonData = await getUser(cookies[process.env.NEXT_PUBLIC_APP+'_token']);
  //   res.status(200).json(jsonData);
  // }
  // res.status(200).json({});
}

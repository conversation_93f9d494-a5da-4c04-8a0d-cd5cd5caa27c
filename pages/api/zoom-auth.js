import { NextApiRequest, NextApiResponse } from 'next';
import { KJUR } from 'jsrsasign';

const zoomClientKey = process.env.ZOOM_CLIENT_KEY;
const zoomClientSecret = process.env.ZOOM_CLIENT_SECRET;

export default async function handler(req, res) {
  if (req.method === 'POST') {
    const { meetingNumber, role } = req.body;

    const iat = Math.round(new Date().getTime() / 1000) - 30;
    const exp = iat + 60 * 60 * 2;

    const oHeader = { alg: 'HS256', typ: 'JWT' };
    const oPayload = {
      sdkKey: zoomClientKey,
      mn: meetingN<PERSON>ber,
      role: role,
      iat: iat,
      exp: exp,
      appKey: zoomClientKey,
      tokenExp: iat + 60 * 60 * 2
    };

    const sHeader = JSON.stringify(oHeader);
    const sPayload = JSON.stringify(oPayload);
    const signature = KJUR.jws.JWS.sign('HS256', sHeader, sPayload, zoomClientSecret);

    res.status(200).json({
      signature: signature
    });
  } else {
    res.setHeader('Allow', 'POST');
    res.status(405).end('Method Not Allowed');
  }
}

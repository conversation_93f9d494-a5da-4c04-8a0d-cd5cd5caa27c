import React from "react";
import Image from "next/image";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import Router, { useRouter } from "next/router";
import { getUser } from "../api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import { useForm } from "react-hook-form";
import Head from "next/head";
import Link from "next/link";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import CourseDetailIntro from "/themes/components/courseDetailIntro";
import CourseDetailDescription from "/themes/components/courseDetailDescription";
import CourseDetailPrice from "/themes/components/courseDetailPrice";
import CourseDetailCode from "/themes/components/courseDetailCode";
import CourseDetailScholarship from "/themes/components/courseDetailScholarship";
import GroupCategory from "/themes/components/groupCategory";
import CommentZone from "/themes/components/commentZone";
import ListVdoModal from "/themes/components/listVdoModal";
import GroupAudioList from "/themes/components/groupAudioList";
import ListVdoEp from "/themes/components/listVdoEp";
import VdoModal from "/themes/components/vdoModal";
import CardVideo from "/themes/components/cardVideo";
import RankingViewHistory from "/themes/components/rankingViewHistory";
import ReactPlayer from "react-player";
import NumberFormat from "react-number-format";
import ListTags from "/themes/components/listTagsCourse";
import CurriculumBackButton from '@/themes/components/curriculumBackButton'
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import { Pie } from "react-chartjs-2";
ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

import {
  Menu,
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
  Accordion,
  Tab,
  Popup,
} from "semantic-ui-react";
function show_add_list() {
  document.getElementById("fmCreateListAdd").classList.toggle("active");
}
import "semantic-ui-css/semantic.min.css";
import stylesModal from "/public/assets/css/component/listQuizModal.module.css";
import styles from "/public/assets/css/pages/course.module.css";
import Swal from "sweetalert2";

import moment from "moment";
// import { gsap } from "gsap";
// import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
let resumeVdo = false;
let vdoStatus = false;
export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/course/get/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();
  if (data["status"] == "false" || data["data"]["type"] == "infographic" || data["data"]["type"] == "Ebook") {
    return {
      redirect: { destination: "/" },
    };
  }

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/course/" + params.id,
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  //View Log
  const formDataView = new URLSearchParams();
  formDataView.append("utoken", utoken);
  formDataView.append("course_id", data.data.id);

  const res_view = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseView",
    {
      body: formDataView,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCodeView = res_view.ok ? false : res_view.statusCode;
  const data_view = await res_view.json();
  //View Log

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      utoken,
      user,
    },
  };
}

export default function Course({
  seo_data,
  errorCode,
  data,
  params,
  utoken,
  user,
}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [lockAddQuiz, setLockAddQuiz] = useState(false);
  const [lockAddPreTest, setLockAddPreTest] = useState(false);
  const [modalVdoOpen, setModalVdoOpen] = useState(false);
  const [modalQuizOpen, setModalQuizOpen] = useState(false);
  const [modalVdoAudio, setModalVdoAudio] = useState(false);
  const [keySelect, setKeySelect] = useState(data["data"]["keySel"]);
  const [epIndex, setEpIndex] = useState(router.query.ep||null);
  const [loadEpIndex, setLoadEpIndex] = useState(false);
  const [quizSelect, setQuizSelect] = useState(0);
  const [relateData, setRelateData] = useState(null);
  const [reloadRelate, setReloadRelate] = useState(true);
  const [playAudio, setPlayAudio] = useState(false);
  const [discountCode, setDiscountCode] = useState("");
  const [discountChannel, setDiscountChannel] = useState("");
  const [courseProPrice, setCourseProPrice] = useState(
    data["data"]["pro_price"]
  );
  const [coursePrice, setCoursePrice] = useState(data["data"]["price"]);
  const [coursePriceReload, setCoursePriceReload] = useState(true);
  const [reloadComment, setReloadComment] = useState(true);
  const [commentData, setCommentData] = useState(null);
  const [reloadStar, setReloadStar] = useState(true);
  const [reloadQuizBox, setReloadQuizBox] = useState(true);

  const [reloadQuiz, setReloadQuiz] = useState(true);
  const [quizData, setQuizData] = useState([]);
  const [reloadRank, setReloadRank] = useState(false);
  const [answer, setAnswer] = useState([]);
  const [answerPreTest, setAnswerPreTest] = useState([]);
  const [stateIndex, setStateIndex] = useState({ activeIndexs: [] });
  const [errorArray, setErrorArray] = useState([]);
  const [scroll, setScroll] = useState(false);
  const spaceMove = useRef(null);
  const fixBuy = useRef(null);
  const audioRef = React.useRef();
  const [playerEnd, setPlayerEnd] = useState(false);
  const [offset, setOffset] = useState(0);
  const [reloadVdo, setReloadVdo] = useState(true);
  const [vdoList, setVdoList] = useState(null);
  const [vdoListAll, setVdoListAll] = useState(null);
  const [vdoSection, setVdoSection] = useState([]);

  const [addPlaylist, setAddPlaylist] = useState(false);
  const [reloadAudio, setReloadAudio] = useState(true);
  const [courseId, setCourseId] = useState(null);
  const [playlistTitle, setPlaylistTitle] = useState(null);
  const [playlistSelect, setPlaylistSelect] = useState(null);
  const [playListOptions, setPlayListOptions] = useState([]);

  const [assessmentData, setAssessmentData] = useState([]);
  const [modalAssessment, setModalAssessment] = useState(false);
  // const [examAssessment, setExamAssessment] = useState(false);
  const [choice, setChoice] = useState([]);
  const [isAssessment, setIsAssessment] = useState(false);
  const [reloadAsses, setReloadAsses] = useState(true);
  const [modalPreTest, setModalPreTest] = useState(false);
  
  const [quickResult, setQuickResult] = useState([]);
  const [assessmentStep, setAssessmentStep] = useState(1);
  const [reopenVdo, setReopenVdo] = useState(false);
  
  const [oculusPin, setOculusPin] = useState(null);
  const [modalOculus, setModalOculus] = React.useState(false);
  const [modalResult, setModalResult] = React.useState(false);
  const [resultData, setResultData] = React.useState([]);
  const [autoShowResult, setAutoShowResult] = React.useState(false);
  const [modalCert, setModalCert] = React.useState(false);
  const [modalCertShow, setModalCertShow] = React.useState(false);

  useEffect(() => {
    if (epIndex!=null&&!loadEpIndex) {
      setLoadEpIndex(true);
      setKeySelect(epIndex);
      setModalVdoOpen(true);
    }
  }, [epIndex,loadEpIndex]);

  function showResult(_data){
    setResultData(_data);
    setModalResult(true);
  }
  function resultCallback(bol) {
    setModalResult(bol);
  }
  const schemaData = {
    "@context": "https://schema.org/",
    "@type": "Course",
    "name": data["data"]["title"],
    "image": data["data"]["image"],
    "description": data["data"]["subtitle_th"],
    "brand": "MedUMore",
    "provider": {
      "@type": "Person",
      "name": data["data"]["speaker_name"]
    },
    "publisher": {
      "@type": "Organization",
      "name": "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย",
      "logo": "https://www.md.chula.ac.th/wp-content/uploads/2016/02/MDCU-Logo-300x300.jpg",
      "url": "https://www.md.chula.ac.th/"
    },
    "author": {
      "@type": "Organization",
      "logo": "https://www.md.chula.ac.th/wp-content/uploads/2016/02/MDCU-Logo-300x300.jpg",
      "name": "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย",
      "url": "https://www.md.chula.ac.th/"
    }
  };

  useEffect(() => {
    if (
      data["data"]["type"] != "infographic" &&
      data["data"]["type"] != "podcast"
    ) {
      // update
      const el = spaceMove.current;
      const elHight = spaceMove.current.offsetHeight;
      const topBuy = el.getBoundingClientRect().bottom;
      const topTotal = topBuy + elHight;
      // update
      const onScroll = () => setOffset(window.pageYOffset);
      // clean up code
      window.removeEventListener("scroll", onScroll);
      window.addEventListener("scroll", onScroll, { passive: true });
      // if (offset > topTotal) {
      if (topBuy < 60) {
        if (fixBuy.current) {
          fixBuy.current.classList.add("active");
        }
      } else {
        if (fixBuy.current) {
          fixBuy.current.classList.remove("active");
        }
      }
    }
  }, [offset]);
  useEffect(() => {
    if (reloadRelate) {
      setReloadRelate(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/mdcu/course/relate/" +
          data.data.id +
          "/" +
          data.data.type,
        null,
        (res_relate) => {
          if(res_relate['status']=='success'){
            setRelateData(res_relate.data);
            setPlayListOptions(res_relate.userPlaylist);
          }
        }
      );
    }
  }, [reloadRelate]);
  useEffect(() => {
    if (reloadComment) {
      setReloadComment(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/mdcu/course/comment/" +
          data.data.id,
        null,
        (data) => {
          setCommentData(data);
        }
      );
    }
  }, [reloadComment]);
  useEffect(() => {
    if (reloadQuiz) {
      setReloadQuiz(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/course/quiz/" + data.data.id,
        null,
        (data) => {
          setQuizData(data["data"]);
          setReloadRank(true);
        }
      );
    }
  }, [reloadQuiz]);
  useEffect(() => {
    if (reloadVdo) {
      setReloadVdo(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/course/vdo/" + data.data.id,
        null,
        (data) => {          
          setVdoList(data["data"]);
          setVdoListAll(data);
          setVdoSection(data["section"]);
          setTimeout(() => {
            setReloadQuizBox(true);
          }, "0");
        }
      );
    }
  }, [reloadVdo]);
  useEffect(() => {
    if (reloadRank) {
      setReloadRank(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/course/rank/" + data.data.id,
        null,
        (data) => {
          var quick_array = [];
          if (data.status == "success" && data.data.count_all > 0) {
            var quick_item = {
              menuItem: translateEng('สถิติ'),
              render: () => (
                <Tab.Pane attached={false}>
                  <div className="content-box quickResult-box">
                    <div className="quickResult-inner-row row-statistic">
                      <div className="col-12 col-quickResult">
                        <div className="StatisticViewTitle">
                          <h1>{translateEng('ภาพรวมการทำแบบทดสอบ')}</h1>
                        </div>
                      </div>
                      <div className="col-12 col-lg-6 col-quickResult">
                        <div className="statisticViewAmount">
                          <div className="statisticViewAmount-description">
                            <div className="item-description">
                            {translateEng('แบบทดสอบทั้งหมด')} {data.data.count_all} {translateEng('ชุด')}
                            </div>
                            <div className="item-description">
                              {" "}
                              {translateEng('ทำแล้ว')} {data.data.count_do} {translateEng('ชุด')}
                            </div>
                          </div>
                          <div className="statisticViewAmount-chart">
                            <div className="chart-inner">
                              <Pie
                                data={{
                                  labels: [translateEng('ผ่าน'), translateEng('ไม่ผ่าน'), translateEng('ยังไม่ได้ทำ')],
                                  datasets: [
                                    {
                                      data: [
                                        data.data.count_pass,
                                        data.data.count_fail,
                                        data.data.count_not,
                                      ],
                                      backgroundColor: [
                                        "rgba(75, 192, 192, 0.5)",
                                        "rgba(255, 159, 64, 0.5)",
                                        "rgba(255, 99, 132, 0.5)",
                                      ],
                                      borderColor: [
                                        "rgba(75, 192, 192, 0.5)",
                                        "rgba(255, 159, 64, 0.5)",
                                        "rgba(255, 99, 132, 0.5)",
                                      ],
                                      borderWidth: 1,
                                      options: {
                                        plugins: {},
                                      },
                                    },
                                  ],
                                }}
                                options={{
                                  responsive: true,
                                  layout: {},
                                  plugins: {
                                    legend: {
                                      position: "bottom",
                                      labels: {
                                        usePointStyle: true,
                                        boxWidth: 10,
                                        padding: 15,
                                        font: {
                                          size: 14,
                                          family: "Kanit",
                                        },
                                      },
                                    },
                                    title: {},
                                  },
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="col-12 col-lg-6 col-quickResult">
                        <div className="statisticViewAverage">
                          <div className="statisticViewAverage-description">
                            <div className="item-description">
                              {translateEng('เฉลี่ยคะแนนที่ทำได้')}
                            </div>
                          </div>
                          <div className="statisticViewAverage-chart">
                            <div className="chart-inner">
                              <Bar
                                options={{
                                  responsive: true,
                                  scales: {
                                    yAxis: {
                                      min: 0,
                                      max: 100,
                                    },
                                    xAxis: {
                                      pointLabelFontSize: 18,
                                      ticks: {
                                        size: 40,
                                      },
                                    },
                                  },
                                  scales: {
                                    yAxes: {
                                      ticks: {
                                        font: {
                                          size: 14,
                                          family: "Kanit",
                                        },
                                      },
                                    },
                                    xAxes: {
                                      ticks: {
                                        font: {
                                          size: 14,
                                          family: "Kanit",
                                        },
                                      },
                                    },
                                  },

                                  plugins: {
                                    legend: {
                                      display: false,
                                      labels: {},
                                    },
                                    title: {
                                      display: false,
                                    },
                                  },
                                }}
                                data={{
                                  labels: [
                                    translateEng('คะแนนสูงสุด'),
                                    translateEng('คะแนนที่ทำได้'),
                                    translateEng('คะแนนต่ำสุด'),
                                  ],
                                  datasets: [
                                    {
                                      data: [
                                        data.data.max_point,
                                        data.data.my_point,
                                        data.data.min_point,
                                      ],
                                      backgroundColor: [
                                        "rgba(75, 192, 192, 0.5)",
                                        "rgba(54, 162, 235, 0.5)",
                                        "rgba(255, 99, 132, 0.5)",
                                      ],
                                    },
                                  ],
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Tab.Pane>
              ),
            };
            // quick_array.push(quick_item);
          }
          if (
            data.status == "success" &&
            (data.data.top_ten.length > 0 || data.data.my_log.length > 0 || data.data.pre_log.length > 0)
          ) {
            var quick_item = {
              menuItem: translateEng('ผลคะแนน'),
              render: () => (
                <Tab.Pane attached={false}>
                  <div className="content-box quickResult-box">
                    <div className="quickResult-inner-row row-ranking">
                      {data.data.top_ten.length > 0 ? (
                        <div className="col-12 col-lg-12 col-quickResult">
                          <div className="RankingViewTop10 MyRankingView">
                            <div className="MyRankingViewTitle">
                              <h1>{translateEng('อันดับของคุณ จากผู้ทำแบบทดสอบทั้งหมด')}</h1>
                            </div>
                            <div className="MyRankingViewTable">
                              <div className="InnerViewTableScroll">
                                <div className="InnerViewTable">
                                  <table className="table">
                                    <thead>
                                      <tr>
                                        <th scope="col">{translateEng('อันดับ')}</th>
                                        <th scope="col">{translateEng('ชื่อ')}</th>
                                        <th scope="col">{translateEng('วันที่ทำ')}</th>
                                        <th scope="col">{translateEng('คะแนน')}</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {data.data.top_ten.map((val, key) =>
                                        val.is_me ? (
                                          <tr key={key}>
                                            <td className="my">
                                              {val.position}
                                            </td>
                                            <td className="my">
                                              {val.name} {val.lastname}
                                            </td>
                                            <td className="my">
                                              {moment(val.created_at).format(
                                                "DD/MM/YYYY"
                                              )}
                                            </td>
                                            {val.point > 0 ? (
                                              <td className="my">
                                                <span>{val.point}</span> {translateEng('คะแนน')}
                                              </td>
                                            ) : val.check_status == 1 ? (
                                              <td className="my">
                                                <span>0</span> {translateEng('คะแนน')}
                                              </td>
                                            ) : (
                                              <td className="my">
                                                <span>{translateEng('รอการตรวจสอบ')}</span>
                                              </td>
                                            )}
                                          </tr>
                                        ) : (
                                          <tr key={key}>
                                            <td>{val.position}</td>
                                            <td>
                                              {val.name} {val.lastname}
                                            </td>
                                            <td>{val.date}</td>
                                            {val.point > 0 ? (
                                              <td>
                                                <span>{val.point}</span> {translateEng('คะแนน')}
                                              </td>
                                            ) : val.check_status == 1 ? (
                                              <td>
                                                <span>0</span> {translateEng('คะแนน')}
                                              </td>
                                            ) : (
                                              <td>
                                                <span>{translateEng('รอการตรวจสอบ')}</span>
                                              </td>
                                            )}
                                          </tr>
                                        )
                                      )}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : null}
                      {data.data.pre_log.length > 0 ? (
                        <div className="col-12 col-lg-12 col-quickResult">
                          <div className="RankingViewHistory MyRankingView">
                            <div className="MyRankingViewTitle">
                              <h1>{translateEng('ประวัติการทำแบบทดสอบก่อนเรียน')}</h1>
                            </div>
                            <div className="MyRankingViewTable">
                              <div className="InnerViewTableScroll">
                                <div className="InnerViewTable">
                                  <table className="table">
                                    <thead>
                                      <tr>
                                        <th scope="col">{translateEng('ทดสอบครั้งที่')}</th>
                                        <th scope="col">{translateEng('ชื่อแบบทดสอบ')}</th>
                                        <th scope="col">{translateEng('วันที่')}</th>
                                        {/* <th scope="col">{translateEng('คะแนน')}</th>
                                        <th scope="col">{translateEng('สถานะ')}</th>
                                        <th scope="col">{translateEng('ผลการทดสอบ')}</th> */}
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {data.data.pre_log.map((val, key) => (
                                        <tr key={key}>
                                          <td>{key + 1}</td>
                                          {val.result.length>0?(
                                            <td className="blue-pointer" onClick={() => showResult(val.result)}>{val.title_th}</td>
                                          ):(
                                            <td>{val.title_th}</td>
                                          )}
                                          <td>
                                            {moment(val.created_at).format(
                                              "DD/MM/YYYY"
                                            )}
                                          </td>
                                          {/* {val.point > 0 ? (
                                            <td>
                                              <span>{val.point}</span> {translateEng('คะแนน')}
                                            </td>
                                          ) : val.check_status == 1 ? (
                                            <td>
                                              <span>0</span> {translateEng('คะแนน')}
                                            </td>
                                          ) : (
                                            <td>
                                              <span>-</span>
                                            </td>
                                          )}
                                          {val.check_status == 1 ? (
                                            <td>
                                              <span className="status complete">
                                                {translateEng('ตรวจแล้ว')}
                                              </span>
                                            </td>
                                          ) : (
                                            <td>
                                              <span className="status wait">
                                              {translateEng('รอการตรวจสอบ')}
                                              </span>
                                            </td>
                                          )}
                                          {val.status == 1 ? (
                                            val.check_status == 1 ? (
                                              <td>
                                                <span className="result succes">
                                                  {translateEng('ผ่าน')}
                                                </span>
                                              </td>
                                            ) : (
                                              <td>
                                                <span className="result succes">
                                                  -
                                                </span>
                                              </td>
                                            )
                                          ) : val.check_status == 1 ? (
                                            <td>
                                              <span className="result fail">
                                                {translateEng('ไม่ผ่าน')}
                                              </span>
                                            </td>
                                          ) : (
                                            <td>
                                              <span className="result succes">
                                                -
                                              </span>
                                            </td>
                                          )} */}
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : null}
                      {data.data.my_log.length > 0 ? (
                        <div className="col-12 col-lg-12 col-quickResult">
                          <div className="RankingViewHistory MyRankingView">
                            <div className="MyRankingViewTitle">
                              <h1>{translateEng('ประวัติการทำแบบทดสอบหลังเรียน')}</h1>
                            </div>
                            <div className="MyRankingViewTable">
                              <div className="InnerViewTableScroll">
                                <div className="InnerViewTable">
                                  <table className="table">
                                    <thead>
                                      <tr>
                                        <th scope="col">{translateEng('ทดสอบครั้งที่')}</th>
                                        <th scope="col">{translateEng('ชื่อแบบทดสอบ')}</th>
                                        <th scope="col">{translateEng('วันที่')}</th>
                                        <th scope="col">{translateEng('คะแนน')}</th>
                                        <th scope="col">{translateEng('สถานะ')}</th>
                                        <th scope="col">{translateEng('ผลการทดสอบ')}</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {data.data.my_log.map((val, key) => (
                                        <tr key={key}>
                                          <td>{key + 1}</td>
                                          {val.result.length>0?(
                                            <td className="blue-pointer" onClick={() => showResult(val.result)}>{val.title_th}</td>
                                          ):(
                                            <td>{val.title_th}</td>
                                          )}
                                          <td>
                                            {moment(val.created_at).format(
                                              "DD/MM/YYYY"
                                            )}
                                          </td>
                                          {val.point > 0 ? (
                                            <td>
                                              <span>{val.point}</span> {translateEng('คะแนน')}
                                            </td>
                                          ) : val.check_status == 1 ? (
                                            <td>
                                              <span>0</span> {translateEng('คะแนน')}
                                            </td>
                                          ) : (
                                            <td>
                                              <span>-</span>
                                            </td>
                                          )}
                                          {val.check_status == 1 ? (
                                            <td>
                                              <span className="status complete">
                                                {translateEng('ตรวจแล้ว')}
                                              </span>
                                            </td>
                                          ) : (
                                            <td>
                                              <span className="status wait">
                                              {translateEng('รอการตรวจสอบ')}
                                              </span>
                                            </td>
                                          )}
                                          {val.status == 1 ? (
                                            val.check_status == 1 ? (
                                              <td>
                                                <span className="result succes">
                                                  {translateEng('ผ่าน')}
                                                </span>
                                              </td>
                                            ) : (
                                              <td>
                                                <span className="result succes">
                                                  -
                                                </span>
                                              </td>
                                            )
                                          ) : val.check_status == 1 ? (
                                            <td>
                                              <span className="result fail">
                                                {translateEng('ไม่ผ่าน')}
                                              </span>
                                            </td>
                                          ) : (
                                            <td>
                                              <span className="result succes">
                                                -
                                              </span>
                                            </td>
                                          )}
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : null}
                    </div>
                  </div>
                </Tab.Pane>
              ),
            };
            quick_array.push(quick_item);
          }
          setQuickResult(quick_array);
          if (
            autoShowResult && data.status == "success" && data.data && data.data.my_log && data.data.my_log.length > 0
          ){
            showResult(data.data.my_log[data.data.my_log.length-1]['result']);
            setAutoShowResult(false);
          }
        }
      );
    }
  }, [reloadRank]);

  useEffect(() => {
    if (reloadAsses) {
      setReloadAsses(false);
      setIsAssessment(false);
      const formQuiz = new URLSearchParams();
      formQuiz.append("course_id", data["data"]["id"]);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/checkAssessment",
        formQuiz,
        (res_quiz) => {
          if (res_quiz["status"] == "success"&&res_quiz["allow"]&&res_quiz["data"]&&res_quiz["data"].length>0) {
            setIsAssessment(true);
          }
        }
      );
    }
  }, [reloadAsses]);

  function checkFree(){
    if((data.data.price == 0 || (data.data.is_promotion == 1 && data.data.pro_price == 0) || data.data.is_internal || data.data.is_subscription || data.data.is_volume)&&discountCode==''){
      return true;
    }else{
      return false;
    }
  }

  function selectChapter(_key) {
    appContext.loadApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/course/vdo/" + data.data.id,
      null,
      (res_data) => {
        setVdoList(res_data["data"]);
        setVdoListAll(res_data);
        setVdoSection(data["section"]);
        setTimeout(() => {
          setReloadQuizBox(true);
        }, "0");

        var obj = [];
        for (var i = 0; i < res_data["data"][_key]["quiz"]["question"].length; i++) {
          var ans = {
            course_id: data["data"]["id"],
            lesson_id: res_data["data"][_key]["id"],
            question_id: res_data["data"][_key]["quiz"]["question"][i]["id"],
            question_type: res_data["data"][_key]["quiz"]["question"][i]["type"],
            answer: "",
          };
          obj.push(ans);
        }
        // answer_textarea
        // answer_input
        var collection = document.getElementsByClassName("answer_input");
        for (var i = 0; i < collection.length; i++) {
          collection[i].checked = false;
        }
        var collection = document.getElementsByClassName("answer_textarea");
        for (var i = 0; i < collection.length; i++) {
          collection[i].value = "";
        }
        setAnswer(obj);
        setModalQuizOpen(true);
      }
    );
  }

  function selectChapterPre(_key) {
    appContext.loadApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/course/vdo/" + data.data.id,
      null,
      (res_data) => {
        setVdoList(res_data["data"]);
        setVdoListAll(res_data);
        setVdoSection(data["section"]);
        setTimeout(() => {
          setReloadQuizBox(true);
        }, "0");

        var obj = [];
        for (var i = 0; i < res_data["data"][_key]["pre_test"]["question"].length; i++) {
          var ans = {
            course_id: data["data"]["id"],
            lesson_id: res_data["data"][_key]["id"],
            question_id: res_data["data"][_key]["pre_test"]["question"][i]["id"],
            question_type: res_data["data"][_key]["pre_test"]["question"][i]["type"],
            answer: "",
          };
          obj.push(ans);
        }
        // answer_textarea
        // answer_input
        var collection = document.getElementsByClassName("answer_input");
        for (var i = 0; i < collection.length; i++) {
          collection[i].checked = false;
        }
        var collection = document.getElementsByClassName("answer_textarea");
        for (var i = 0; i < collection.length; i++) {
          collection[i].value = "";
        }
        setAnswerPreTest(obj);
        setModalPreTest(true);
      }
    );
    
  }

  function onAnswer(_key, _ans) {
    answer[_key]["answer"] = _ans;
  }
  function onAnswerPreTest(_key, _ans) {
    answerPreTest[_key]["answer"] = _ans;
  }
  function onSubmit(_key) {
    let bol = true;
    var errarr = [];
    for (var i = 0; i < answer.length; i++) {
      if (appContext.isNull(answer[i]["answer"])) {
        bol = false;
        errarr.push(answer[i]["question_id"]);
      }
    }
    setErrorArray(errarr);
    if (bol && !lockAddQuiz) {
      setLockAddQuiz(true);
      const formData = new URLSearchParams();
      formData.append("answer_data", JSON.stringify(answer));
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addQuiz",
        formData,
        (obj) => {
          if (obj["status"] == "success") {
            setLockAddQuiz(false);
            if(obj['check_status']==1&&!obj["unlocked_cert"]){
              setAutoShowResult(true);
            }
            setStateIndex({ activeIndexs: [] });
            setAnswer([]);
            // var collection = document.getElementsByClassName("answer_input");
            // for(var i = 0; i<collection.length; i++){
            //   collection[i].checked = false;
            // }
            // var collection = document.getElementsByClassName("answer_textarea");
            // for(var i = 0; i<collection.length; i++){
            //   collection[i].value = '';
            // }
            setReloadQuiz(true);
            setReloadQuizBox(false);
            setModalQuizOpen(false);
            setReloadVdo(true);
            setTimeout(() => {
              Swal.fire({
                text: translateEng('ส่งคำตอบสำเร็จ'),
                icon: "success",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              }).then((result) => {
                setReloadQuizBox(true);
                if(obj["unlocked_cert"]){
                  setModalCertShow(true);
                }
                // if(examAssessment){
                //   setExamAssessment(false);
                //   selectAssessment();
                // }
              });
            }, "0");
          }else if (obj["status"] == "answer_fail") {
            Swal.fire({
              text: "พบข้อผิดพลาด กรุณาทำแบบทดสอบอีกครั้ง",
              icon: "error",
              confirmButtonText: translateEng('ปิด'),
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              location.reload();
            });
          }
        }
      );
    } else {
      if(!lockAddQuiz){
        Swal.fire({
          text: translateEng('กรุณาตอบคำถามให้ครบถ้วน'),
          icon: "error",
          confirmButtonText: translateEng('ปิด'),
          confirmButtonColor: "#648d2f"
        });
      }
    }
  }
  function onSubmitPreTest(_key) {
    let bol = true;
    var errarr = [];
    for (var i = 0; i < answerPreTest.length; i++) {
      if (appContext.isNull(answerPreTest[i]["answer"])) {
        bol = false;
        errarr.push(answerPreTest[i]["question_id"]);
      }
    }
    setErrorArray(errarr);
    if (bol && !lockAddPreTest) {
      setLockAddPreTest(true);
      const formData = new URLSearchParams();
      formData.append("answer_data", JSON.stringify(answerPreTest));
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addPreTest",
        formData,
        (obj) => {
          if (obj["status"] == "success") {
            setLockAddPreTest(false);
            setAnswerPreTest([]);
            setModalPreTest(false);
            setReloadVdo(true);
            setReloadQuiz(true);
            setReloadQuizBox(false);
            setTimeout(() => {
              Swal.fire({
                text: translateEng('ส่งคำตอบสำเร็จ'),
                icon: "success",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              }).then((result) => {
                setReloadQuizBox(true);
              });
            }, "0");
          }else if (obj["status"] == "answer_fail") {
            Swal.fire({
              text: "พบข้อผิดพลาด กรุณาทำแบบทดสอบอีกครั้ง",
              icon: "error",
              confirmButtonText: translateEng('ปิด'),
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              location.reload();
            });
          }
        }
      );
    } else {
      if(!lockAddPreTest){
        Swal.fire({
          text: translateEng('กรุณาตอบคำถามให้ครบถ้วน'),
          icon: "error",
          confirmButtonText: translateEng('ปิด'),
          confirmButtonColor: "#648d2f"
        });
      }
    }
  }

  function onChoice(_key, _ans) {
    choice[_key]["answer"] = _ans;
  }
  function onAssessmentNext() {
    if(choice.length>0 && !appContext.isNull(choice[0]["answer"])){
      setAssessmentStep(2);
    }else{
      Swal.fire({
        text: translateEng('กรุณาตอบคำถามให้ครบถ้วน'),
        icon: "error",
        confirmButtonText: translateEng('ปิด'),
        confirmButtonColor: "#648d2f"
      });
    }
  }
  function onAssessment() {
    let bol = true;
    var errarr = [];
    // for (var i = 0; i < choice.length; i++) {
    //   if (appContext.isNull(choice[i]["answer"])) {
    //     bol = false;
    //     errarr.push(choice[i]["question_id"]);
    //   }
    // }
    setErrorArray(errarr);
    if (bol) {
      const formData = new URLSearchParams();
      formData.append("answer_data", JSON.stringify(choice));
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addAssessment",
        formData,
        (obj) => {
          if (obj["status"] == "success") {
            setModalAssessment(false);
            setReloadQuizBox(false);
            setReloadAsses(true);
            Swal.fire({
              text: translateEng('ส่งคำตอบสำเร็จ'),
              icon: "success",
              confirmButtonText: translateEng('ปิด'),
              confirmButtonColor: "#648d2f"
            }).then((result) => {
              setReloadQuizBox(true);
              if(reopenVdo){
                setReopenVdo(false);
                selectEp(keySelect);
              }
            });
          }
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาตอบคำถามให้ครบถ้วน'),
        icon: "error",
        confirmButtonText: translateEng('ปิด'),
        confirmButtonColor: "#648d2f"
      });
    }
  }
  const previewFile = (_key, event) => {
    event.preventDefault();
    // answer[_key]['answer'] = URL.createObjectURL(event.target.files[0]);
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      function () {
        if (_.startsWith(reader.result, "data:")) {
          appContext.loadApi(
            process.env.NEXT_PUBLIC_API + "/api/image/save",
            { image: reader.result },
            (data) => {
              if (data["status"] == "true") {
                answer[_key]["answer"] = data["path"];
              }
            }
          );
        }
      },
      false
    );
    if (file) {
      reader.readAsDataURL(file);
    }
  };
  const previewFilePreTest = (_key, event) => {
    event.preventDefault();
    // answer[_key]['answer'] = URL.createObjectURL(event.target.files[0]);
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      function () {
        if (_.startsWith(reader.result, "data:")) {
          appContext.loadApi(
            process.env.NEXT_PUBLIC_API + "/api/image/save",
            { image: reader.result },
            (data) => {
              if (data["status"] == "true") {
                answerPreTest[_key]["answer"] = data["path"];
              }
            }
          );
        }
      },
      false
    );
    if (file) {
      reader.readAsDataURL(file);
    }
  };
  const checkMyError = (_val) => {
    if (errorArray.indexOf(_val) > -1) {
      return true;
    }
    return false;
  };

  // update
  // update

  function courseDescription(_value) {
    const formData = new URLSearchParams();
    formData.append("course_id", data["data"]["id"]);
    formData.append("rate", _value);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseRate",
      formData,
      (obj) => {
        if (obj["status"] == "success") {
          setReloadStar(false);
          data["data"]["rate"] = obj["rate"];
          data["data"]["rating"] = obj["rating"];
          setTimeout(() => {
            setReloadStar(true);
          }, "0");
          forceUpdate();
        }
      }
    );
  }
  function audioReady(_sec) {
    if (audioRef) {
      setTimeout(() => {
        audioRef.current.seekTo(_sec, "seconds");
      }, "0");
    }
  }
  function selectAssessment() {
    const formQuiz = new URLSearchParams();
    formQuiz.append("course_id", data["data"]["id"]);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/checkAssessment",
      formQuiz,
      (res_quiz) => {
        if (res_quiz["status"] == "success"&&res_quiz["allow"]&&res_quiz["data"]&&res_quiz["data"].length>0) {
          setAssessmentStep(1);
          setAssessmentData(res_quiz['data']);

          setReloadVdo(true);
          setReloadQuiz(true);
          setModalVdoOpen(false);
          setReloadQuizBox(false);
          setPlayAudio(false);

          setModalAssessment(true);
          var ch = [];
          for (var i = 0; i < res_quiz['data'].length; i++) {
            var ans = {
              course_id: data["data"]["id"],
              question_id: res_quiz['data'][i]["id"],
              question_type: res_quiz['data'][i]["type"],
              answer: "",
            };
            ch.push(ans);
          }
          var collection = document.getElementsByClassName("choice_input");
          for (var i = 0; i < collection.length; i++) {
            collection[i].checked = false;
          }
          var collection = document.getElementsByClassName("choice_textarea");
          for (var i = 0; i < collection.length; i++) {
            collection[i].value = "";
          }
          setChoice(ch);
        }
      }
    );
  }
  function courseProgress(_data, _id, _last) {
    const formData = new URLSearchParams();
    formData.append("course_id", data["data"]["id"]);
    formData.append("lesson_id", _id);
    formData.append("watching_time", _data.playedSeconds);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseStamp",
      formData,
      (obj) => {
        if (obj["status"] == "success") {
          if(obj["unlocked_cert"]){
            setModalCertShow(true);
          }else{
            if(obj['open_assessment']){
              if(localStorage.getItem('stampProgress') && localStorage.getItem('stampProgress')!=null){
                var stampProgress = parseInt(localStorage.getItem('stampProgress'));
                if(Math.floor(Date.now() / 1000) - stampProgress>=60){
                  localStorage.setItem('stampProgress',Math.floor(Date.now() / 1000));
                  selectAssessment();
                  setReopenVdo(true);
                }
              }else{
                localStorage.setItem('stampProgress',Math.floor(Date.now() / 1000));
                selectAssessment();
                setReopenVdo(true);
              }
            }
          }
          data["data"]["progress_percent"] = obj["progress_percent"];
        }
      }
    );
  }
  function progressEnd(_id, _last) {
    setReloadVdo(true);
    setReloadQuiz(true);
    setModalVdoOpen(false);
    setReloadQuizBox(false);
    setPlayAudio(false);
    setTimeout(() => {
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/mdcu/course/vdo/" +
          data.data.id,
        null,
        (res) => {
          if (
            res["data"][keySelect]["quiz"].question &&
            res["data"][keySelect]["quiz"].question.length > 0 &&
            res["data"][keySelect]["quiz"].allowed &&
            res["data"][keySelect]["quiz"].first_time &&
            !res["data"][keySelect]["quiz"].check
          ) {
            selectQuiz(keySelect);
            // if (_last == "last") {
            //   setExamAssessment(true);
            // }
          }else{
            if (_last == "last") {
              // selectAssessment();
            }else{
              selectEp(keySelect + 1);
            }
          }
          setReloadQuizBox(true);
        }
      );
    }, "0");
    if (_last == "last") {
      if(modalCertShow){
        setModalCertShow(false);
        setModalCert(true);
      }
      if (data["data"]["type"] == "podcast") {
        setKeySelect(null);
        setReloadAudio(false);
        setTimeout(() => {
          setReloadAudio(true);
        }, "0");
      }
    }
  }
  function skipVdo(_id) {
    const formData = new URLSearchParams();
    formData.append("course_id", data["data"]["id"]);
    formData.append("lesson_id", _id);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/skipEp",
      formData,
      (obj) => {
        if (obj["status"] == "success") {
          if(obj["unlocked_cert"]){
            setModalCertShow(true);
          }
        }
      }
    );
  }
  function podcastProgress(_data, _id, _last) {
  }

  appContext.setToken(utoken);

  if (errorCode) {
    return <Error statusCode={errorCode} />;
  }

  function playerCallback(bol) {
    setModalVdoOpen(bol);
    setModalVdoAudio(bol);
    setReloadQuizBox(false);
    setReloadVdo(true);
    setReloadQuiz(true);
    if(!bol&&modalCertShow){
      setModalCertShow(false);
      setModalCert(true);
    }
    // setTimeout(() => {
    //   setReloadQuizBox(true);
    // }, "800");
  }

  function quizCallback(bol) {
    setModalQuizOpen(bol);
  }

  function preTestCallback(bol) {
    setModalPreTest(bol);


    setReloadVdo(true);
  }

  function assessmentCallback(bol) {
    setModalAssessment(bol);
    if(!bol && reopenVdo){
      setReopenVdo(false);
      selectEp(keySelect);
    }
  }
  function certCallback(bol) {
    setModalCert(bol);
  }
  function genOculus(_id){
    setModalVdoOpen(false);
    setPlayAudio(false);
    const formData = new URLSearchParams();
    formData.append("course_id", data["data"]["id"]);
    formData.append("lesson_id", _id);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/user/genOculus",
      formData,
      (pin_res) => {
        if(pin_res.status=='success'){
          setOculusPin(pin_res.pin);
          setModalOculus(true);
        }
      }
    );
  }
  function selectPreTest(_key){
    setModalVdoOpen(false);
    setPlayAudio(false);
    setKeySelect(_key);
    selectChapterPre(_key);
    const formData = new URLSearchParams();
    formData.append("lesson_id", vdoList[_key]["id"]);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/preTestSkip",
      formData,
      (pin_res) => {
      }
    );
  }
  function selectEp(_key) {
    if(vdoList[_key]["lock"]){
      if(data["data"]["is_soon"]){
        Swal.fire({
          text: "Coming soon",
          icon: "info",
          confirmButtonText: translateEng('ปิด'),
          confirmButtonColor: "#648d2f"
        });
      }else{
        if (vdoListAll.course_free) {
          Swal.fire({
            text: translateEng('คุณต้องการเริ่มเรียนใช่หรือไม่?'),
            icon: 'info',
            showCancelButton: true,
            confirmButtonColor: '#648d2f',
            cancelButtonColor: '#d33',
            confirmButtonText: translateEng('ใช่'),
            cancelButtonText: translateEng('ไม่')
          }).then((result) => {
            if (result.isConfirmed) {
              groupCategoryCallback(
                "free",
                vdoListAll.course_id,
                ""
              );
            }
          })
        }else{
          Swal.fire({
            text: translateEng('กรุณาซื้อคอร์สนี้ก่อนค่ะ'),
            icon: "info",
            confirmButtonText: translateEng('ปิด'),
            confirmButtonColor: "#648d2f"
          });
        }
      }
    }else{
      if(vdoList[_key]["ep_oculus"]==1){
        if(vdoList[_key]["pre_test"]["question"]&&vdoList[_key]["pre_test"]["question"].length>0&&vdoList[_key]["pre_test"]["allowed"]
        &&vdoList[_key]["pre_test"]["first_time"]&&!vdoList[_key]["pre_test"]["check"]&&vdoList[_key]["pre_test"]["is_show"]){
          // alert('pre_test_first');
          selectPreTest(_key);
        }else{
          setKeySelect(_key);
          if (data["data"]["type"] == "podcast") {
            setPlayAudio(true);
            setReloadAudio(false);
            if (vdoList[_key]["duration"] - vdoList[_key]["watching"] <= 1) {
              audioReady(0);
            } else {
              audioReady(vdoList[_key]["watching"]);
            }
            setTimeout(() => {
              setReloadAudio(true);
            }, "0");
          }else{
            location.href="/course/"+params.id+'?ep='+_key;
            // setModalVdoOpen(true);
          }
        }
      }else{
        setModalVdoOpen(false);
        setPlayAudio(false);
        const formData = new URLSearchParams();
        formData.append("course_id", data["data"]["id"]);
        formData.append("lesson_id", vdoList[_key]["id"]);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/user/genOculus",
          formData,
          (pin_res) => {
            if(pin_res.status=='success'){
              setOculusPin(pin_res.pin);
              setModalOculus(true);
            }
          }
        );
      }
    }
  }
  function selectQuiz(_key) {
    setQuizSelect(_key);
    setModalVdoOpen(false);
    selectChapter(_key);
  }
  function playVdo() {
    setModalVdoAudio(true);
  }
  function groupCategoryCallback(_type, _id, _title) {
    if (user) {
      if (_type == "cart") {
      //   const str="ขออภัย MDCU MedUMORE\n" +
      //   "ปิดปรับปรุงระบบชำระเงินชั่วคราว\n"+
      //   "ท่านจะได้รับการแจ้งเตือน\n"+
      //  "เมื่อการดำเนินการเสร็จสิ้น";
      //   Swal.fire({
      //     html: '<pre style="font-family: \'Kanit\'">' + str + '</pre>',
      //     icon: "warning",
      //     confirmButtonText: "ตกลง",
      //     confirmButtonColor: "#648d2f"
      //   }); 
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "course");
        formData.append("discount_code", discountCode);
        formData.append("discount_channel", discountChannel);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
          formData,
          (res_check) => {
            if(res_check['status']=='success'){
              if(res_check['count']==0){
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                  formData,
                  (data) => {
                    if(data['status']=='limit_buy'){
                      Swal.fire({
                        text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                        icon: "info",
                        confirmButtonText: "ปิด",
                        confirmButtonColor: "#648d2f"
                      }).then((result) => {
                      });
                    }else{
                      appContext.setReloadCart(true);
                      setReloadRelate(true);
          
                      document
                        .getElementsByClassName("main-header")[0]
                        .classList.add("active_cart");
                      document
                        .getElementsByClassName("group-menu-cart")[0]
                        .classList.add("on_show");
                      document.body.classList.add("open_cart");
                      document
                        .getElementsByClassName("group-menu-f-mobile")[0]
                        .classList.remove("on_show");
                    }
                  }
                );
              }else{
                Swal.fire({
                  html: translateEng('คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'),
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: translateEng('ยืนยัน'),
                  cancelButtonText: translateEng('ยกเลิก')
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                      formData,
                      (res_clear) => {
                        if(res_clear['status']=='success'){
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                            formData,
                            (data) => {
                              if(data['status']=='limit_buy'){
                                Swal.fire({
                                  text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                                  icon: "info",
                                  confirmButtonText: "ปิด",
                                  confirmButtonColor: "#648d2f"
                                }).then((result) => {
                                });
                              }else{
                                appContext.setReloadCart(true);
                                setReloadRelate(true);
                    
                                document
                                  .getElementsByClassName("main-header")[0]
                                  .classList.add("active_cart");
                                document
                                  .getElementsByClassName("group-menu-cart")[0]
                                  .classList.add("on_show");
                                document.body.classList.add("open_cart");
                                document
                                  .getElementsByClassName("group-menu-f-mobile")[0]
                                  .classList.remove("on_show");
                              }
                            }
                          );
                        }
                      }
                    );
                  }
                });
              }
            }
          }
        );
      } else if (_type == "group") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "group");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
          formData,
          (res_check) => {
            if(res_check['status']=='success'){
              if(res_check['count']==0){
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                  formData,
                  (data) => {
                    if(data['status']=='limit_buy'){
                      Swal.fire({
                        text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                        icon: "info",
                        confirmButtonText: "ปิด",
                        confirmButtonColor: "#648d2f"
                      }).then((result) => {
                      });
                    }else{
                      appContext.setReloadCart(true);
                      setReloadRelate(true);
          
                      document
                        .getElementsByClassName("main-header")[0]
                        .classList.add("active_cart");
                      document
                        .getElementsByClassName("group-menu-cart")[0]
                        .classList.add("on_show");
                      document.body.classList.add("open_cart");
                      document
                        .getElementsByClassName("group-menu-f-mobile")[0]
                        .classList.remove("on_show");
                    }
                  }
                );
              }else{
                Swal.fire({
                  html: translateEng('คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'),
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: translateEng('ยืนยัน'),
                  cancelButtonText: translateEng('ยกเลิก')
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                      formData,
                      (res_clear) => {
                        if(res_clear['status']=='success'){
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                            formData,
                            (data) => {
                              if(data['status']=='limit_buy'){
                                Swal.fire({
                                  text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                                  icon: "info",
                                  confirmButtonText: "ปิด",
                                  confirmButtonColor: "#648d2f"
                                }).then((result) => {
                                });
                              }else{
                                appContext.setReloadCart(true);
                                setReloadRelate(true);
                    
                                document
                                  .getElementsByClassName("main-header")[0]
                                  .classList.add("active_cart");
                                document
                                  .getElementsByClassName("group-menu-cart")[0]
                                  .classList.add("on_show");
                                document.body.classList.add("open_cart");
                                document
                                  .getElementsByClassName("group-menu-f-mobile")[0]
                                  .classList.remove("on_show");
                              }
                            }
                          );
                        }
                      }
                    );
                  }
                });
              }
            }
          }
        );
      } else if (_type == "favourite") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "course");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addFavourite",
          formData,
          (res_fav) => {
            if(res_fav['status']=='success'){
              if(res_fav['action']=='add'){
                document.querySelector(".favourite_class_"+_id).classList.add("active");
              }else{
                document.querySelector(".favourite_class_"+_id).classList.remove("active");
              }
            }
          }
        );
      } else if (_type == "free") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderFree",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              if(data["data"]["trailer_media"]==8){
                Swal.fire({
                  text: "สำเร็จ",
                  icon: "success",
                  confirmButtonText: translateEng('ปิด'),
                  confirmButtonColor: "#648d2f"
                }).then((result) => {
                  location.reload();
                });
              }else{
                // Swal.fire({
                //   text: "ลงทะเบียนสำเร็จ เริ่มเรียนได้เลย",
                //   icon: "success",
                //   confirmButtonText: translateEng('ปิด'),
                //   confirmButtonColor: "#648d2f"
                // }).then((result) => {
                //   location.reload();
                // });
                data["data"]["allowed"] = true;
                if(data["data"]["type"] == "zoom"){
                  data["data"]["zoom_join_url"] = obj["zoom_join_url"];
                  window.open(
                    obj["zoom_join_url"],
                    '_blank'
                  );
                }else{
                  setReloadVdo(true);
                  setReloadQuiz(true);
                  setModalVdoOpen(false);
                  setReloadQuizBox(false);
                  setPlayAudio(false);
                  setTimeout(() => {
                    appContext.loadApi(
                      process.env.NEXT_PUBLIC_API +
                        "/api/mdcu/course/vdo/" +
                        data.data.id,
                      null,
                      (data_res) => {
                        if (data_res && data_res["data"] && data_res["data"][keySelect]) {
                          if(vdoList[keySelect]["ep_oculus"]==1){
                            if(vdoList[keySelect]["pre_test"]["question"]&&vdoList[keySelect]["pre_test"]["question"].length>0&&vdoList[keySelect]["pre_test"]["allowed"]
                            &&vdoList[keySelect]["pre_test"]["first_time"]&&!vdoList[keySelect]["pre_test"]["check"]&&vdoList[keySelect]["pre_test"]["is_show"]){
                              // alert('pre_test_first');
                              selectPreTest(keySelect);
                            }else{
                              if (data_res["data"]["type"] == "podcast") {
                                setPlayAudio(true);
                                audioReady(vdoList[keySelect]["watching"]);
                              }else{
                                location.href="/course/"+params.id+'?ep='+keySelect;
                                // setModalVdoOpen(true);
                              }
                            }
                          }else{
                            setModalVdoOpen(false);
                            setPlayAudio(false);
                            const formData = new URLSearchParams();
                            formData.append("course_id", data["data"]["id"]);
                            formData.append("lesson_id", vdoList[keySelect]["id"]);
                            appContext.sendApi(
                              process.env.NEXT_PUBLIC_API + "/api/user/genOculus",
                              formData,
                              (pin_res) => {
                                if(pin_res.status=='success'){
                                  setOculusPin(pin_res.pin);
                                  setModalOculus(true);
                                }
                              }
                            );
                            
                          }
                        }
                        setReloadQuizBox(true);
                      }
                    );
                  }, "100");
                }
              }
            }else if(obj['status']=='limit_buy'){
              Swal.fire({
                text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                icon: "info",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              }).then((result) => {
              });
            } else {
              Swal.fire({
                text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      } else if (_type == "playlist") {
        setCourseId(_id);
        setPlaylistTitle("");
        setPlaylistSelect("");
        setAddPlaylist(true);
      } else if (_type == "vip") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderVip",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              if(data["data"]["trailer_media"]==8){
                Swal.fire({
                  text: "สำเร็จ",
                  icon: "success",
                  confirmButtonText: translateEng('ปิด'),
                  confirmButtonColor: "#648d2f"
                }).then((result) => {
                  location.reload();
                });
              }else{
                // Swal.fire({
                //   text: "ลงทะเบียนสำเร็จ เริ่มเรียนได้เลย",
                //   icon: "success",
                //   confirmButtonText: translateEng('ปิด'),
                //   confirmButtonColor: "#648d2f"
                // }).then((result) => {
                //   location.reload();
                // });
                data["data"]["allowed"] = true;
                if(data["data"]["type"] == "zoom"){
                  data["data"]["zoom_join_url"] = obj["zoom_join_url"];
                  window.open(
                    obj["zoom_join_url"],
                    '_blank'
                  );
                }else{
                  setReloadVdo(true);
                  setReloadQuiz(true);
                  setModalVdoOpen(false);
                  setReloadQuizBox(false);
                  setPlayAudio(false);
                  setTimeout(() => {
                    appContext.loadApi(
                      process.env.NEXT_PUBLIC_API +
                        "/api/mdcu/course/vdo/" +
                        data.data.id,
                      null,
                      (data_res) => {
                        if (data_res && data_res["data"] && data_res["data"][keySelect]) {
                          if(vdoList[keySelect]["ep_oculus"]==1){
                            if(vdoList[keySelect]["pre_test"]["question"]&&vdoList[keySelect]["pre_test"]["question"].length>0&&vdoList[keySelect]["pre_test"]["allowed"]
                            &&vdoList[keySelect]["pre_test"]["first_time"]&&!vdoList[keySelect]["pre_test"]["check"]&&vdoList[keySelect]["pre_test"]["is_show"]){
                              // alert('pre_test_first');
                              selectPreTest(keySelect);
                            }else{
                              if (data_res["data"]["type"] == "podcast") {
                                setPlayAudio(true);
                                audioReady(vdoList[keySelect]["watching"]);
                              }else{
                                location.href="/course/"+params.id+'?ep='+keySelect;
                                // setModalVdoOpen(true);
                              }
                            }
                          }else{
                            setModalVdoOpen(false);
                            setPlayAudio(false);
                            const formData = new URLSearchParams();
                            formData.append("course_id", data["data"]["id"]);
                            formData.append("lesson_id", vdoList[keySelect]["id"]);
                            appContext.sendApi(
                              process.env.NEXT_PUBLIC_API + "/api/user/genOculus",
                              formData,
                              (pin_res) => {
                                if(pin_res.status=='success'){
                                  setOculusPin(pin_res.pin);
                                  setModalOculus(true);
                                }
                              }
                            );
                          }
                        }
                        setReloadQuizBox(true);
                      }
                    );
                  }, "100");
                }
              }
            }else if(obj['status']=='limit_buy'){
              Swal.fire({
                text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                icon: "info",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              }).then((result) => {
              });
            } else {
              Swal.fire({
                text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      } else if (_type == "volume") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderVolume",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              if(data["data"]["trailer_media"]==8){
                Swal.fire({
                  text: "สำเร็จ",
                  icon: "success",
                  confirmButtonText: translateEng('ปิด'),
                  confirmButtonColor: "#648d2f"
                }).then((result) => {
                  location.reload();
                });
              }else{
                // Swal.fire({
                //   text: "ลงทะเบียนสำเร็จ เริ่มเรียนได้เลย",
                //   icon: "success",
                //   confirmButtonText: translateEng('ปิด'),
                //   confirmButtonColor: "#648d2f"
                // }).then((result) => {
                //   location.reload();
                // });
                data["data"]["allowed"] = true;
                if(data["data"]["type"] == "zoom"){
                  data["data"]["zoom_join_url"] = obj["zoom_join_url"];
                  window.open(
                    obj["zoom_join_url"],
                    '_blank'
                  );
                }else{
                  setReloadVdo(true);
                  setReloadQuiz(true);
                  setModalVdoOpen(false);
                  setReloadQuizBox(false);
                  setPlayAudio(false);
                  setTimeout(() => {
                    appContext.loadApi(
                      process.env.NEXT_PUBLIC_API +
                        "/api/mdcu/course/vdo/" +
                        data.data.id,
                      null,
                      (data_res) => {
                        if (data_res && data_res["data"] && data_res["data"][keySelect]) {
                          if(vdoList[keySelect]["ep_oculus"]==1){
                            if(vdoList[keySelect]["pre_test"]["question"]&&vdoList[keySelect]["pre_test"]["question"].length>0&&vdoList[keySelect]["pre_test"]["allowed"]
                            &&vdoList[keySelect]["pre_test"]["first_time"]&&!vdoList[keySelect]["pre_test"]["check"]&&vdoList[keySelect]["pre_test"]["is_show"]){
                              // alert('pre_test_first');
                              selectPreTest(keySelect);
                            }else{
                              if (data_res["data"]["type"] == "podcast") {
                                setPlayAudio(true);
                                audioReady(vdoList[keySelect]["watching"]);
                              }else{
                                location.href="/course/"+params.id+'?ep='+keySelect;
                                // setModalVdoOpen(true);
                              }
                            }
                          }else{
                            setModalVdoOpen(false);
                            setPlayAudio(false);
                            const formData = new URLSearchParams();
                            formData.append("course_id", data["data"]["id"]);
                            formData.append("lesson_id", vdoList[keySelect]["id"]);
                            appContext.sendApi(
                              process.env.NEXT_PUBLIC_API + "/api/user/genOculus",
                              formData,
                              (pin_res) => {
                                if(pin_res.status=='success'){
                                  setOculusPin(pin_res.pin);
                                  setModalOculus(true);
                                }
                              }
                            );
                          }
                        }
                        setReloadQuizBox(true);
                      }
                    );
                  }, "100");
                }
              }
            }else if(obj['status']=='limit_buy'){
              Swal.fire({
                text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                icon: "info",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              }).then((result) => {
              });
            } else {
              Swal.fire({
                text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      } else if (_type == "internal") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderInternal",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              if(data["data"]["trailer_media"]==8){
                Swal.fire({
                  text: "สำเร็จ",
                  icon: "success",
                  confirmButtonText: translateEng('ปิด'),
                  confirmButtonColor: "#648d2f"
                }).then((result) => {
                  location.reload();
                });
              }else{
                // Swal.fire({
                //   text: "ลงทะเบียนสำเร็จ เริ่มเรียนได้เลย",
                //   icon: "success",
                //   confirmButtonText: translateEng('ปิด'),
                //   confirmButtonColor: "#648d2f"
                // }).then((result) => {
                //   location.reload();
                // });
                data["data"]["allowed"] = true;
                if(data["data"]["type"] == "zoom"){
                  data["data"]["zoom_join_url"] = obj["zoom_join_url"];
                  window.open(
                    obj["zoom_join_url"],
                    '_blank'
                  );
                }else{
                  setReloadVdo(true);
                  setReloadQuiz(true);
                  setModalVdoOpen(false);
                  setReloadQuizBox(false);
                  setPlayAudio(false);
                  setTimeout(() => {
                    appContext.loadApi(
                      process.env.NEXT_PUBLIC_API +
                        "/api/mdcu/course/vdo/" +
                        data.data.id,
                      null,
                      (data_res) => {
                        if (data_res && data_res["data"] && data_res["data"][keySelect]) {
                          if(vdoList[keySelect]["ep_oculus"]==1){
                            if(vdoList[keySelect]["pre_test"]["question"]&&vdoList[keySelect]["pre_test"]["question"].length>0&&vdoList[keySelect]["pre_test"]["allowed"]
                            &&vdoList[keySelect]["pre_test"]["first_time"]&&!vdoList[keySelect]["pre_test"]["check"]&&vdoList[keySelect]["pre_test"]["is_show"]){
                              // alert('pre_test_first');
                              selectPreTest(keySelect);
                            }else{
                              if (data_res["data"]["type"] == "podcast") {
                                setPlayAudio(true);
                                audioReady(vdoList[keySelect]["watching"]);
                              }else{
                                location.href="/course/"+params.id+'?ep='+keySelect;
                                // setModalVdoOpen(true);
                              }
                            }
                          }else{
                            setModalVdoOpen(false);
                            setPlayAudio(false);
                            const formData = new URLSearchParams();
                            formData.append("course_id", data["data"]["id"]);
                            formData.append("lesson_id", vdoList[keySelect]["id"]);
                            appContext.sendApi(
                              process.env.NEXT_PUBLIC_API + "/api/user/genOculus",
                              formData,
                              (pin_res) => {
                                if(pin_res.status=='success'){
                                  setOculusPin(pin_res.pin);
                                  setModalOculus(true);
                                }
                              }
                            );
                          }
                        }
                        setReloadQuizBox(true);
                      }
                    );
                  }, "100");
                }
              }
            }else if(obj['status']=='limit_buy'){
              Swal.fire({
                text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                icon: "info",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              }).then((result) => {
              });
            } else {
              Swal.fire({
                text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      } 
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function submitPlaylist() {
    if (
      (playlistTitle != null && playlistTitle != "") ||
      (playlistSelect != null && playlistSelect != "")
    ) {
      const formData = new URLSearchParams();
      formData.append("course_id", courseId);
      formData.append("title", playlistTitle);
      formData.append("select", playlistSelect);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addPlaylist",
        formData,
        (res_play) => {
          // setReloadRelate(true);
          setAddPlaylist(false);
          if(res_play['status']=='success'){
            if(res_play['action']=='add'){
              document.querySelector(".playlist_class_"+courseId).classList.add("active");
              document.querySelector(".playlist_icon_"+courseId).classList.remove("icon-ic-circle-plus");
              document.querySelector(".playlist_icon_"+courseId).classList.add("icon-ic-tick-thanks");
            }else{
              document.querySelector(".playlist_class_"+courseId).classList.remove("active");
              document.querySelector(".playlist_icon_"+courseId).classList.remove("icon-ic-tick-thanks");
              document.querySelector(".playlist_icon_"+courseId).classList.add("icon-ic-circle-plus");
            }
          }
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาเลือกเพลย์ลิสต์'),
        icon: "error",
        confirmButtonText: translateEng('ปิด'),
        confirmButtonColor: "#648d2f"
      });
    }
  }
  function addDiscountCode(_code, _type) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("course_id", data["data"]["id"]);
      formData.append("content_type", "course");
      formData.append("discount_code", _code);
      formData.append("discount_channel", _type);
      setCoursePriceReload(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addDiscountCode",
        formData,
        (obj) => {
          setCoursePriceReload(false);
          if (obj["is_discount"]) {
            if (_type == "code") {
              Swal.fire({
                text: translateEng('ยินดีด้วย ใช้โค้ดส่วนลดสำเร็จ'),
                icon: "success",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: translateEng('ยินดีด้วย คุณได้รับการสนับสนุน'),
                icon: "success",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
              document.getElementById("input_code").value = _code;
            }
            if (data["data"]["is_promotion"] == 1) {
              if (obj["discount_type"] == "regular") {
                data["data"]["pro_price"] =
                  courseProPrice - obj["discount_value"];
              } else {
                data["data"]["pro_price"] =
                  courseProPrice - (courseProPrice * obj["discount_value"]) / 100;
              }
              if(data["data"]["pro_price"]<0){
                data["data"]["pro_price"] = 0;
              }
            } else {
              if (obj["discount_type"] == "regular") {
                data["data"]["price"] = coursePrice - obj["discount_value"];
              } else {
                data["data"]["price"] =
                  coursePrice - (coursePrice * obj["discount_value"]) / 100;
              }
              if(data["data"]["price"]<0){
                data["data"]["price"] = 0;
              }
            }
          } else {
            if (_type == "code") {
              Swal.fire({
                text: translateEng('ขออภัย ไม่พบโค้ดส่วนลด'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: translateEng('ขออภัย ไม่พบผู้สนับสนุน'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
            if (data["data"]["is_promotion"] == 1) {
              data["data"]["pro_price"] = courseProPrice;
            } else {
              data["data"]["price"] = coursePrice;
            }
          }
          setDiscountChannel(obj["discount_channel"]);
          setDiscountCode(obj["discount_code"]);
          setTimeout(() => {
            setCoursePriceReload(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function addLike(_comment_id) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("comment_id", _comment_id);
      setReloadComment(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addLike",
        formData,
        (obj) => {
          setReloadComment(false);
          setTimeout(() => {
            setReloadComment(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function addComment(_comment) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("comment", _comment);
      formData.append("course_id", data["data"]["id"]);
      formData.append("content_type", "course");
      setReloadComment(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addComment",
        formData,
        (obj) => {
          setReloadComment(false);
          setTimeout(() => {
            setReloadComment(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function handleClick(e, titleProps) {
    const { index } = titleProps;
    const { activeIndexs } = stateIndex;
    const newIndex = [];
    // const newIndex = activeIndexs;
    // selectChapter(titleProps.index);
    const currentIndexPosition = activeIndexs.indexOf(index);
    if (currentIndexPosition > -1) {
      newIndex.splice(currentIndexPosition, 1);
    } else {
      newIndex.push(index);
    }

    setStateIndex({ activeIndexs: newIndex });
  }
  function onStartExam(_id) {
    for (var i = 0; i < vdoList.length; i++) {
      if (vdoList[i]["id"] == _id) {
        selectQuiz(i);
        break;
      }
    }
  }

  const { activeIndexs } = stateIndex;

  if (data["documentList"] && data["documentList"].length > 0) {
    var panels = [
      {
        key: `panel-1`,
        title: translateEng('บทเรียนทั้งหมด'),
        content: {
          content: (
            <div className="container-tab">
              <div className="course-progress-result">
                <p>My Learning Progress : {data["data"]["progress_percent"]}%</p>
              </div>
              {reloadQuizBox && vdoList && vdoList.length > 0 ? (
                <ListVdoEp
                  courseAllow={data["data"]["allowed"]}
                  setOpen={appContext.setOpen}
                  callback={selectEp}
                  vdoList={vdoList}
                  vdoListAll={vdoListAll}
                  vdoSection={vdoSection}
                  checkFree={checkFree()}
                  cbQuiz={selectQuiz}
                  cbPreTest={selectPreTest}
                  cbStart={groupCategoryCallback}
                  isSoon={data["data"]["is_soon"]}
                  user={user}
                  isAssessment={isAssessment}
                  cbAssessment={selectAssessment}
                  lang={lang}
                ></ListVdoEp>
              ) : null}
            </div>
          ),
        },
      },
      ,
    ];
    // var panels = [
    //   {
    //     key: `panel-1`,
    //     title: translateEng('บทเรียนทั้งหมด'),
    //     content: {
    //       content: (
    //         <div className="container-tab">
    //           <div className="course-progress-result">
    //             <p>My Learning Progress : {data["data"]["progress_percent"]}%</p>
    //           </div>
    //           {reloadQuizBox && vdoList && vdoList.length > 0 ? (
    //             <ListVdoEp
    //               courseAllow={data["data"]["allowed"]}
    //               setOpen={appContext.setOpen}
    //               callback={selectEp}
    //               vdoList={vdoList}
    //               vdoSection={vdoSection}
    //               checkFree={checkFree()}
    //               vdoListAll={vdoListAll}
    //               cbQuiz={selectQuiz}
    //               cbPreTest={selectPreTest}
    //               cbStart={groupCategoryCallback}
    //               isSoon={data["data"]["is_soon"]}
    //               user={user}
    //               isAssessment={isAssessment}
    //               cbAssessment={selectAssessment}
    //               lang={lang}
    //             ></ListVdoEp>
    //           ) : null}
    //         </div>
    //       ),
    //     },
    //   },
    //   {
    //     key: `panel-2`,
    //     title: translateEng('เอกสารประกอบการเรียน'),
    //     content: {
    //       content: (
    //         <div className="container-tab">
    //           <div className="container-fluid  space-between-content coursejs">
    //             {data["documentList"].map((val, key) => (
    //               <div className="row" key={key}>
    //                 <div className="col-8 item">
    //                   <p>{val.title_th}</p>
    //                 </div>
    //                 <div className="col-4 item text-right">
    //                   <div>
    //                     <a href="#">{translateEng('ดาวน์โหลดไฟล์ในบทเรียน')}</a>
    //                     {val.file_pdf_th != null && val.file_pdf_th != "" ? (
    //                       <div className={styles.iconDiv}>
    //                         <a href={val.file_pdf_th}>
    //                           <Image alt="" layout="intrinsic" className={`${styles.iconDownload}`} src={"/assets/images/icon_pdf.png"} width={27} height={31}/>
    //                         </a>
    //                       </div>
    //                     ) : null}
    //                     {val.file_pptx_th != null && val.file_pptx_th != "" ? (
    //                       <div className={styles.iconDiv}>
    //                         <a href={val.file_pptx_th}>
    //                           <Image alt="" layout="intrinsic" className={`${styles.iconDownload}`} src={"/assets/images/icon_powerpoint.png"} width={27} height={31}/>
    //                         </a>
    //                       </div>
    //                     ) : null}
    //                     {val.file_xlsx_th != null && val.file_xlsx_th != "" ? (
    //                       <div className={styles.iconDiv}>
    //                         <a href={val.file_xlsx_th}>
    //                           <Image alt="" layout="intrinsic" className={`${styles.iconDownload}`} src={"/assets/images/icon_excel.png"} width={27} height={31}/>
    //                         </a>
    //                       </div>
    //                     ) : null}
    //                   </div>
    //                 </div>
    //               </div>
    //             ))}
    //           </div>
    //         </div>
    //       ),
    //     },
    //   },
    //   ,
    // ];
  } else {
    var panels = [
      {
        key: `panel-1`,
        title: translateEng('บทเรียนทั้งหมด'),
        content: {
          content: (
            <div className="container-tab">
              <div className="course-progress-result">
                <p>My Learning Progress : {data["data"]["progress_percent"]}%</p>
              </div>
              {reloadQuizBox && vdoList && vdoList.length > 0 ? (
                <ListVdoEp
                  courseAllow={data["data"]["allowed"]}
                  setOpen={appContext.setOpen}
                  callback={selectEp}
                  vdoList={vdoList}
                  vdoListAll={vdoListAll}
                  vdoSection={vdoSection}
                  checkFree={checkFree()}
                  cbQuiz={selectQuiz}
                  cbPreTest={selectPreTest}
                  cbStart={groupCategoryCallback}
                  isSoon={data["data"]["is_soon"]}
                  user={user}
                  isAssessment={isAssessment}
                  cbAssessment={selectAssessment}
                  lang={lang}
                ></ListVdoEp>
              ) : null}
            </div>
          ),
        },
      },
      ,
    ];
  }

  if (vdoList && vdoList.length > 0) {
    var quizLength = 0;
    for (var i = 0; i < vdoList.length; i++) {
      for (var j = 0; j < vdoList[i]["quiz"]["question"].length; j++) {
        quizLength++;
      }
    }
    if (quizLength == 0) {
     
      if (user) {
        var panes = [
          {
            menuItem: translateEng('รายละเอียด'),
            pane: {
              key: "tab1",
              content: (
                <div className="content-box">
                  <Accordion
                    defaultActiveIndex={[0]}
                    exclusive={false}
                    panels={panels}
                    fluid
                  />
                </div>
              ),
            },
          },
          {
            menuItem: translateEng('ถาม-ตอบ'),
            pane: {
              key: "tab2",
              content: (
                <div className="content-box">
                  {commentData ? (
                    <CommentZone
                      user={user}
                      addLike={addLike}
                      addComment={addComment}
                      data={commentData["commentData"]}
                      lang={lang}
                    ></CommentZone>
                  ) : null}
                </div>
              ),
            },
          },
        ];
      } else {
        var panes = [
          {
            menuItem: translateEng('รายละเอียด'),
            pane: {
              key: "tab1",
              content: (
                <div className="content-box">
                  <Accordion
                    defaultActiveIndex={[0]}
                    exclusive={false}
                    panels={panels}
                    fluid
                  />
                </div>
              ),
            },
          },
        ];
      }
    } else {
      if (user) {
        var panes = [
          {
            menuItem: translateEng('รายละเอียด'),
            pane: {
              key: "tab1",
              content: (
                <div className="content-box">
                  <Accordion
                    defaultActiveIndex={[0]}
                    exclusive={false}
                    panels={panels}
                    fluid
                  />
                </div>
              ),
            },
          },
          {
            menuItem: translateEng('แบบทดสอบ'),
            pane: {
              key: "tab2",
              content: (
                <div className="content-box">
                  {reloadQuizBox && quizData && quizData.length > 0
                    ? quizData.map((val, key) => (
                        <Accordion key={key}>
                          {val.allowed && val.first_time && !val.check ? (
                            val.question && val.question.length > 0 ? (
                              <div>
                                <Accordion.Title
                                  active={activeIndexs.includes(key)}
                                  index={key}
                                  onClick={() => onStartExam(val.id)}
                                >
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className={'icon-test'} src={"/assets/images/icontest.png"} width={48} height={48}/>
                                  </div>
                                  <span>{val.title}</span>
                                </Accordion.Title>
                              </div>
                            ) : null
                          ) : val.allowed && !val.first_time && !val.check ? (
                            val.question && val.question.length > 0 ? (
                              <div>
                                <Accordion.Title
                                  active={activeIndexs.includes(key)}
                                  index={key}
                                  onClick={handleClick}
                                >
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className={'icon-test'} src={"/assets/images/icontest.png"} width={48} height={48}/>
                                  </div>
                                  <span>{val.title}</span>
                                </Accordion.Title>
                                <Accordion.Content
                                  active={activeIndexs.includes(key)}
                                >
                                  <div className="exam-result">
                                    <div className="block-quiz">
                                      <div className="i-title-quiz">
                                        <h3>
                                          {translateEng('คะแนนล่าสุดของคุณ')} {val.last_point}{" "}
                                          {translateEng('คะแนน')}
                                          {val.remark != null &&
                                          val.remark != "" ? (
                                            <Popup
                                              hideOnScroll
                                              content={
                                                <>
                                                  <p
                                                    className="exam-remark"
                                                    dangerouslySetInnerHTML={{
                                                      __html: val.remark,
                                                    }}
                                                  ></p>
                                                </>
                                              }
                                              on="click"
                                              popper={{
                                                id: "popper-container-custom",
                                                style: { zIndex: 2000 },
                                              }}
                                              trigger={
                                                <i className="info circle icon"></i>
                                              }
                                            />
                                          ) : null}
                                        </h3>
                                      </div>
                                    </div>
                                    <div className="block-to-buy-course">
                                      <button
                                        className="btn-to-buy-course left"
                                        onClick={() => onStartExam(val.id)}
                                      >
                                        <span>{translateEng('เริ่มใหม่')}</span>
                                      </button>
                                    </div>
                                  </div>
                                </Accordion.Content>
                              </div>
                            ) : null
                          ) : !val.allowed && !val.first_time && !val.check ? (
                            val.question && val.question.length > 0 ? (
                              <div>
                                <Accordion.Title
                                  active={activeIndexs.includes(key)}
                                  index={key}
                                  onClick={handleClick}
                                >
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className={'icon-test'} src={"/assets/images/icontest.png"} width={48} height={48}/>
                                  </div>
                                  <span>{val.title}</span>
                                </Accordion.Title>
                                <Accordion.Content
                                  active={activeIndexs.includes(key)}
                                >
                                  <div className="exam-result">
                                    <div className="block-quiz">
                                      <div className="i-title-quiz">
                                        <h3>
                                          {translateEng('คะแนนล่าสุดของคุณ')} {val.last_point}{" "}
                                          {translateEng('คะแนน')}
                                        </h3>
                                      </div>
                                    </div>
                                  </div>
                                </Accordion.Content>
                              </div>
                            ) : null
                          ) : !val.allowed && !val.first_time && val.check ? (
                            val.question && val.question.length > 0 ? (
                              <div>
                                <Accordion.Title
                                  active={activeIndexs.includes(key)}
                                  index={key}
                                  onClick={handleClick}
                                >
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className={'icon-test'} src={"/assets/images/icontest.png"} width={48} height={48}/>
                                  </div>
                                  <span>{val.title}</span>
                                </Accordion.Title>
                                <Accordion.Content
                                  active={activeIndexs.includes(key)}
                                >
                                  <div className="exam-result">
                                    <div className="block-quiz">
                                      <div className="i-title-quiz">
                                        <h3>
                                          {translateEng('แบบทดสอบของคุณอยู่ระหว่างตรวจสอบ')}
                                        </h3>
                                      </div>
                                    </div>
                                  </div>
                                </Accordion.Content>
                              </div>
                            ) : null
                          ) : val.question && val.question.length > 0 ? (
                            <Accordion.Title
                              active={activeIndexs.includes(key)}
                              index={key}
                              onClick={handleClick}
                              className="disabled"
                            >
                              <Icon name="lock" />
                              <span>{val.title}</span>
                            </Accordion.Title>
                          ) : null}
                        </Accordion>
                      ))
                    : null}
                </div>
              ),
            },
          },
          {
            menuItem: translateEng('ถาม-ตอบ'),
            pane: {
              key: "tab3",
              content: (
                <div className="content-box">
                  {commentData ? (
                    <CommentZone
                      user={user}
                      addLike={addLike}
                      addComment={addComment}
                      data={commentData["commentData"]}
                      lang={lang}
                    ></CommentZone>
                  ) : null}
                </div>
              ),
            },
          },
        ];
      } else {
        var panes = [
          {
            menuItem: translateEng('รายละเอียด'),
            pane: {
              key: "tab1",
              content: (
                <div className="content-box">
                  <Accordion
                    defaultActiveIndex={[0]}
                    exclusive={false}
                    panels={panels}
                    fluid
                  />
                </div>
              ),
            },
          },
          {
            menuItem: translateEng('แบบทดสอบ'),
            pane: {
              key: "tab2",
              content: (
                <div className="content-box">
                  {reloadQuizBox && quizData && quizData.length > 0
                    ? quizData.map((val, key) => (
                        <Accordion key={key}>
                          {val.allowed && val.first_time && !val.check ? (
                            val.question && val.question.length > 0 ? (
                              <div>
                                <Accordion.Title
                                  active={activeIndexs.includes(key)}
                                  index={key}
                                  onClick={() => onStartExam(val.id)}
                                >
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className={'icon-test'} src={"/assets/images/icontest.png"} width={48} height={48}/>
                                  </div>
                                  <span>{val.title}</span>
                                </Accordion.Title>
                              </div>
                            ) : null
                          ) : val.allowed && !val.first_time && !val.check ? (
                            val.question && val.question.length > 0 ? (
                              <div>
                                <Accordion.Title
                                  active={activeIndexs.includes(key)}
                                  index={key}
                                  onClick={handleClick}
                                >
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className={'icon-test'} src={"/assets/images/icontest.png"} width={48} height={48}/>
                                  </div>
                                  <span>{val.title}</span>
                                </Accordion.Title>
                                <Accordion.Content
                                  active={activeIndexs.includes(key)}
                                >
                                  <div className="exam-result">
                                    <div className="block-quiz">
                                      <div className="i-title-quiz">
                                        <h3>
                                          {translateEng('คะแนนล่าสุดของคุณ')} {val.last_point}{" "}
                                          {translateEng('คะแนน')}
                                        </h3>
                                      </div>
                                    </div>
                                    <div className="block-to-buy-course">
                                      <button
                                        className="btn-to-buy-course left"
                                        onClick={() => onStartExam(val.id)}
                                      >
                                        <span>{translateEng('เริ่มใหม่')}</span>
                                      </button>
                                    </div>
                                  </div>
                                </Accordion.Content>
                              </div>
                            ) : null
                          ) : !val.allowed && !val.first_time && !val.check ? (
                            val.question && val.question.length > 0 ? (
                              <div>
                                <Accordion.Title
                                  active={activeIndexs.includes(key)}
                                  index={key}
                                  onClick={handleClick}
                                >
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className={'icon-test'} src={"/assets/images/icontest.png"} width={48} height={48}/>
                                  </div>
                                  <span>{val.title}</span>
                                </Accordion.Title>
                                <Accordion.Content
                                  active={activeIndexs.includes(key)}
                                >
                                  <div className="exam-result">
                                    <div className="block-quiz">
                                      <div className="i-title-quiz">
                                        <h3>
                                          {translateEng('คะแนนล่าสุดของคุณ')} {val.last_point}{" "}
                                          {translateEng('คะแนน')}
                                        </h3>
                                      </div>
                                    </div>
                                  </div>
                                </Accordion.Content>
                              </div>
                            ) : null
                          ) : !val.allowed && !val.first_time && val.check ? (
                            val.question && val.question.length > 0 ? (
                              <div>
                                <Accordion.Title
                                  active={activeIndexs.includes(key)}
                                  index={key}
                                  onClick={handleClick}
                                >
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className={'icon-test'} src={"/assets/images/icontest.png"} width={48} height={48}/>
                                  </div>
                                  <span>{val.title}</span>
                                </Accordion.Title>
                                <Accordion.Content
                                  active={activeIndexs.includes(key)}
                                >
                                  <div className="exam-result">
                                    <div className="block-quiz">
                                      <div className="i-title-quiz">
                                        <h3>
                                          {translateEng('แบบทดสอบของคุณอยู่ระหว่างตรวจสอบ')}
                                        </h3>
                                      </div>
                                    </div>
                                  </div>
                                </Accordion.Content>
                              </div>
                            ) : null
                          ) : val.question && val.question.length > 0 ? (
                            <Accordion.Title
                              active={activeIndexs.includes(key)}
                              index={key}
                              onClick={handleClick}
                              className="disabled"
                            >
                              <Icon name="lock" />
                              <span>{val.title}</span>
                            </Accordion.Title>
                          ) : null}
                        </Accordion>
                      ))
                    : null}
                </div>
              ),
            },
          },
        ];
      }
    }
  } else {
    if (user) {
      var panes = [
        {
          menuItem: translateEng('รายละเอียด'),
          pane: {
            key: "tab1",
            content: (
              <div className="content-box">
                <Accordion
                  defaultActiveIndex={[0]}
                  exclusive={false}
                  panels={panels}
                  fluid
                />
              </div>
            ),
          },
        },
        {
          menuItem: translateEng('ถาม-ตอบ'),
          pane: {
            key: "tab2",
            content: (
              <div className="content-box">
                {commentData ? (
                  <CommentZone
                    user={user}
                    addLike={addLike}
                    addComment={addComment}
                    data={commentData["commentData"]}
                    lang={lang}
                  ></CommentZone>
                ) : null}
              </div>
            ),
          },
        },
      ];
    } else {
      var panes = [
        {
          menuItem: translateEng('รายละเอียด'),
          pane: {
            key: "tab1",
            content: (
              <div className="content-box">
                <Accordion
                  defaultActiveIndex={[0]}
                  exclusive={false}
                  panels={panels}
                  fluid
                />
              </div>
            ),
          },
        },
      ];
    }
  }

  function translateEng(_value) {
    if(lang=='en'){
      if(_value=='ฟรี'){
        return "Free";
      }else if(_value=='รับฟรี'){
        return "Get Free";
      }else if(_value=='ซื้อ Ticket'){
        return "Buy Ticket";
      }else if(_value=='บาท'){
        return "Baht";
      }else if(_value=='คะแนน'){
        return "Points";
      }else if(_value=='ปกติ'){
        return "Normal";
      }else if(_value=='ราคา'){
        return "Price";
      }else if(_value=='สถิติ'){
        return "Statistics";
      }else if(_value=='ภาพรวมการทำแบบทดสอบ'){
        return "Examination overview";
      }else if(_value=='แบบทดสอบทั้งหมด'){
        return "All Examinations";
      }else if(_value=='ชุด'){
        return "Sets";
      }else if(_value=='ทำแล้ว'){
        return "Done";
      }else if(_value=='ผ่าน'){
        return "Pass";
      }else if(_value=='ไม่ผ่าน'){
        return "Fail";
      }else if(_value=='ยังไม่ได้ทำ'){
        return "Not yet";
      }else if(_value=='เฉลี่ยคะแนนที่ทำได้'){
        return "Average score";
      }else if(_value=='คะแนนสูงสุด'){
        return "Highest score";
      }else if(_value=='คะแนนที่ทำได้'){
        return "Score";
      }else if(_value=='คะแนนต่ำสุด'){
        return "Lowest score";
      }else if(_value=='ผลคะแนน'){
        return "Result";
      }else if(_value=='อันดับของคุณ จากผู้ทำแบบทดสอบทั้งหมด'){
        return "Your ranking";
      }else if(_value=='อันดับ'){
        return "Ranking";
      }else if(_value=='ชื่อ'){
        return "Name";
      }else if(_value=='วันที่ทำ'){
        return "Date";
      }else if(_value=='วันที่'){
        return "Date";
      }else if(_value=='รอการตรวจสอบ'){
        return "Pending";
      }else if(_value=='ประวัติการทำแบบทดสอบหลังเรียน'){
        return "Post-Examination history";
      }else if(_value=='ประวัติการทำแบบทดสอบก่อนเรียน'){
        return "Pre-Examination history";
      }else if(_value=='ทดสอบครั้งที่'){
        return "Round";
      }else if(_value=='ชื่อแบบทดสอบ'){
        return "Examination Title";
      }else if(_value=='สถานะ'){
        return "Status";
      }else if(_value=='ผลการทดสอบ'){
        return "Result";
      }else if(_value=='ตรวจแล้ว'){
        return "Checked";
      }else if(_value=='ส่งคำตอบสำเร็จ'){
        return "Submitted";
      }else if(_value=='ปิด'){
        return "Close";
      }else if(_value=='กรุณาตอบคำถามให้ครบถ้วน'){
        return "Please answer questions completely";
      }else if(_value=='คุณต้องการเริ่มเรียนใช่หรือไม่?'){
        return "Do you want to start studying?";
      }else if(_value=='ใช่'){
        return "Yes";
      }else if(_value=='ไม่'){
        return "No";
      }else if(_value=='กรุณาซื้อคอร์สนี้ก่อนค่ะ'){
        return "Please buy this course";
      }else if(_value=='คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'){
        return "You have another item in your cart<br>Want to buy this product?";
      }else if(_value=='ยืนยัน'){
        return "Submit";
      }else if(_value=='ยกเลิก'){
        return "Cancel";
      }else if(_value=='พบข้อผิดพลาด กรุณาลองอีกครั้ง'){
        return "Found an error, please try again";
      }else if(_value=='กรุณาเข้าสู่ระบบค่ะ'){
        return "Please login";
      }else if(_value=='กรุณาเลือกเพลย์ลิสต์'){
        return "Choose playlist";
      }else if(_value=='ยินดีด้วย ใช้โค้ดส่วนลดสำเร็จ'){
        return "Congratulations, the discount code has been used successfully";
      }else if(_value=='ยินดีด้วย คุณได้รับการสนับสนุน'){
        return "Congratulations, you are supported";
      }else if(_value=='ขออภัย ไม่พบโค้ดส่วนลด'){
        return "Sorry, the discount code could not be found";
      }else if(_value=='ขออภัย ไม่พบผู้สนับสนุน'){
        return "Sorry, the supporter could not be found";
      }else if(_value=='บทเรียนทั้งหมด'){
        return "All lessons";
      }else if(_value=='เอกสารประกอบการเรียน'){
        return "Documents";
      }else if(_value=='ดาวน์โหลดไฟล์ในบทเรียน'){
        return "Download";
      }else if(_value=='รายละเอียด'){
        return "Details";
      }else if(_value=='ถาม-ตอบ'){
        return "Q & A";
      }else if(_value=='แบบทดสอบ'){
        return "Examination";
      }else if(_value=='คะแนนล่าสุดของคุณ'){
        return "Recently score";
      }else if(_value=='เริ่มใหม่'){
        return "Restart";
      }else if(_value=='แบบทดสอบของคุณอยู่ระหว่างตรวจสอบ'){
        return "Your test is under review";
      }else if(_value=='โค้ดส่วนลด'){
        return "Discount code";
      }else if(_value=='เลือกรับ Scholarship จากผู้สนับสนุน'){
        return "Select scholarship";
      }else if(_value=='เพิ่มไปยังเพลย์ลิสต์ของคุณ'){
        return "Add to playlist";
      }else if(_value=='สร้างเพลย์ลิสต์'){
        return "Create playlist";
      }else if(_value=='เพลย์ลิสต์ของคุณ'){
        return "Your playlist";
      }else if(_value=='ป้อนชื่อเพลย์ลิสต์'){
        return "Enter plalist title";
      }else if(_value=='เริ่มเรียน'){
        return "Start learning";
      }else if(_value=='ซื้อคอร์สนี้'){
        return "Buy this course";
      }else if(_value=='คอร์สเกี่ยวข้อง'){
        return "Related course";
      }else if(_value=='ข้อที่'){
        return "No.";
      }else if(_value=='ส่งคำตอบ'){
        return "Submit answer";
      }else if(_value=='ข้าม'){
        return "Skip";
      }else if(_value=='ต่อไป'){
        return "Next";
      }else if(_value=='แบบประเมินความพึงพอใจ (ใช้เวลาไม่ถึง 1 นาที)'){
        return "Assessment (It takes less than 1 minute.)";
      }else if(_value=='คำตอบ'){
        return "Answer";
      }else if(_value=='คำตอบของท่าน'){
        return "Your answer";
      }else if(_value=='คำตอบที่ถูก'){
        return "Correct";
      }else if(_value=='คำอธิบาย'){
        return "Explain";
      }else if(_value=='ดูคำตอบ'){
        return "View";
      }else if(_value=='ยินดีด้วย!'){
        return "Congratulations!";
      }else if(_value=='คุณปลดล็อกใบประกาศ'){
        return "You unlocked the certificate";
      }else if(_value=='สามารถดาวน์โหลดใบประกาศได้ที่โปรไฟล์ของท่าน'){
        return "Certificate can be download at your profile";
      }else if(_value=='หรือคลิกที่นี่'){
        return "Or click here";
      }else if(_value=='คอร์ส'){
        return "Course";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        if(vdoStatus){
          setReloadVdo(true);
          setReloadQuiz(true);
          setModalVdoOpen(false);
          setReloadQuizBox(false);
          setPlayAudio(false);
          resumeVdo = true;
          setTimeout(() => {
            appContext.loadApi(
              process.env.NEXT_PUBLIC_API +
                "/api/mdcu/course/vdo/" +
                data.data.id,
              null,
              (res) => {
                setReloadQuizBox(true);
              }
            );
          }, "0");
        }
      }else{
        if(resumeVdo){
          resumeVdo = false;
          setKeySelect(epIndex);
          setModalVdoOpen(true);
        }
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);
  useEffect(() => {
    vdoStatus = modalVdoOpen;
  }, [modalVdoOpen]);  
  
  if (data["data"]["type"] == "podcast") {
    return (
      <>
        <div className="main-all page-course">
          <Head>
            {seo_data.seo ? (
              seo_data.seo.map((val, key) =>
                val.name == "title" ? (
                  <>
                    <title>{val.content}</title>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                ) : (
                  <>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                )
              )
            ) : (
              <>
                <title>MDCU : MedU MORE</title>
              </>
            )}

            <meta
              key={"twitter:card"}
              name={"twitter:card"}
              content="summary_large_image"
            />
            <meta key={"og:type"} name={"og:type"} content="website" />
          </Head>

          <Header></Header>
         
          <div className="main-body bg-white">
            <div className="fix-space"></div>
            <div className={`container custom-container space-between-content `}>
              <div className="podcast-page">
                <div className="row podcast-row">
                  <div className="col-6 first">
                    <CardVideo callback={playVdo} data={data["data"]}></CardVideo>
                    {coursePriceReload &&
                    data &&
                    data["data"] &&
                    data["data"]["is_soon"] == false ? (
                      data.data.allowed ||
                      data.data.order_status == 4 ||
                      data.data.order_status == 1 ? null : 
                      data.data.is_subscription ? (
                        <div className="block-course-detail-price text-center podcast-price">
                          <div className="description-big-price">Member</div>
                        </div>
                      ) :
                      data.data.is_internal || data.data.is_volume ? (
                        <div className="block-course-detail-price text-center podcast-price">
                          <div className="description-big-price">{translateEng('ฟรี')}</div>
                        </div>
                      ) : data.data.is_promotion == 1 ? (
                        <div className="block-course-detail-price text-center podcast-price">
                          <div className="description-sale">
                            <NumberFormat
                              value={data.data.price}
                              displayType={"text"}
                              thousandSeparator={true}
                              renderText={(value, props) => (
                                <span {...props}>{translateEng('ปกติ')} {value}</span>
                              )}
                            />
                          </div>
                          {data.data.pro_price == 0 ? (
                            <div className="description-big-price">{translateEng('ฟรี')}</div>
                          ) : (
                            <div className="description-big-price">
                              {translateEng('ราคา')}
                              <NumberFormat
                                value={data.data.pro_price}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <span {...props}>{value}</span>
                                )}
                              />
                              {translateEng('บาท')}
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="block-course-detail-price text-center podcast-price">
                          {data.data.price == 0 ? (
                            <div className="description-big-price">{translateEng('ฟรี')}</div>
                          ) : (
                            <div className="description-big-price">
                              {translateEng('ราคา')}
                              <NumberFormat
                                value={data.data.price}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <span {...props}>{value}</span>
                                )}
                              />
                              {translateEng('บาท')}
                            </div>
                          )}
                        </div>
                      )
                    ) : null}
                  </div>
                  <div className="col-6">
                    {reloadStar ? (
                      <CourseDetailDescription
                        data={data["data"]}
                        callback={courseDescription}
                        lang={lang}
                      ></CourseDetailDescription>
                    ) : null}
                    {data.data.allowed ||
                    data.data.order_status == 4 ||
                    data.data.order_status == 1 ? null : data.data.is_promotion ==
                      1 ? (
                      data.data.pro_price == 0 && discountCode=='' ? null : (
                        <div className="row podcastjs">
                          <div className="col-12 col-md-6">
                            <p className="space-between-content-top title-button-group">
                              {translateEng('โค้ดส่วนลด')}
                            </p>
                            <CourseDetailCode
                              callback={addDiscountCode}
                              lang={lang}
                            ></CourseDetailCode>
                          </div>
                          {data &&
                          data["scholarshipList"] &&
                          data["scholarshipList"].length > 0 ? (
                            <div className="col-12 col-md-6">
                              <p className="space-between-content-top title-button-group">
                                {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                              </p>
                              <CourseDetailScholarship
                                callback={addDiscountCode}
                                name="courseScholarship"
                                data={data["scholarshipList"]}
                              ></CourseDetailScholarship>
                            </div>
                          ) : null}
                        </div>
                      )
                    ) : data.data.price == 0 && discountCode=='' ? null : (
                      <div className="row podcastjs">
                        <div className="col-12 col-md-6">
                          <p className="space-between-content-top title-button-group">
                            {translateEng('โค้ดส่วนลด')}
                          </p>
                          <CourseDetailCode
                            callback={addDiscountCode}
                            lang={lang}
                          ></CourseDetailCode>
                        </div>
                        {data &&
                        data["scholarshipList"] &&
                        data["scholarshipList"].length > 0 ? (
                          <div className="col-12 col-md-6">
                            <p className="space-between-content-top title-button-group">
                              {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                            </p>
                            <CourseDetailScholarship
                              callback={addDiscountCode}
                              name="courseScholarship"
                              data={data["scholarshipList"]}
                            ></CourseDetailScholarship>
                          </div>
                        ) : null}
                      </div>
                    )}
                    <div className="space-between-content">
                      {reloadAudio ? (
                        <GroupAudioList
                          data={data["data"]}
                          addCart={groupCategoryCallback}
                          audioData={vdoList}
                          callback={selectEp}
                          keySelect={keySelect}
                        ></GroupAudioList>
                      ) : null}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {relateData&&relateData.length>0 ? (
              <GroupCategory
                type="normal"
                name={translateEng('คอร์สเกี่ยวข้อง')}
                bg={''}
                color="#6E953D"
                size={3.5}
                isLoop={false}
                index={20}
                data={relateData}
                callback={groupCategoryCallback}
              ></GroupCategory>
            ) : null}
            {playAudio ? (
              <ReactPlayer
                ref={audioRef}
                url={vdoList[keySelect]["vdo"]}
                id={`audio_id`}
                width="400px"
                height="50px"
                playing={true}
                controls={false}
                progressInterval={3000}
                onProgress={(progress) => {
                  if(keySelect == vdoList.length - 1){
                    courseProgress(progress, vdoList[keySelect]["id"], "last");
                  }else{
                    courseProgress(progress, vdoList[keySelect]["id"], "normal");
                  }
                }}
                onEnded={() => {
                  if(keySelect == vdoList.length - 1){
                    progressEnd(vdoList[keySelect]["id"], "last");
                  }else{
                    progressEnd(vdoList[keySelect]["id"], "normal");
                  }
                }}
              />
            ) : null}
            {modalVdoAudio &&
            data &&
            data["data"] &&
            data["data"]["link"] &&
            data["data"]["link"] != null &&
            data["data"]["link"] != "" &&
            data["data"]["link"] != "null" ? (
              <VdoModal
                callback={playerCallback}
                vdoData={data["data"]["link"]}
              ></VdoModal>
            ) : null}
            <Modal
              className="modalCreateList"
              onClose={() => setAddPlaylist(false)}
              onOpen={() => setAddPlaylist(true)}
              open={addPlaylist}
            >
              <Modal.Content className="modalCreateListContent">
                <div className="block-modal-CreateList">
                  <div className="inner">
                    <h3>{translateEng('เพิ่มไปยังเพลย์ลิสต์ของคุณ')}</h3>
                  </div>
                  <div className="fm-CreateList-select">
                    {playListOptions.length > 0 ? (
                      <div className="item-fm">
                        <Select
                          className="fm-control"
                          placeholder={translateEng('เพลย์ลิสต์ของคุณ')}
                          options={playListOptions}
                          onChange={(event, data) => {
                            setPlaylistSelect(data.value);
                          }}
                        />
                      </div>
                    ) : null}
                    <button
                      className="btn-add-list"
                      onClick={() => show_add_list()}
                    >
                      <span>{translateEng('สร้างเพลย์ลิสต์')}</span>
                    </button>
                  </div>
                  <div id="fmCreateListAdd" className="fm-CreateList-add">
                    <div className="item-fm">
                      <Input
                        id="create_list"
                        type="text"
                        className="fm-control"
                        placeholder={translateEng('ป้อนชื่อเพลย์ลิสต์')}
                        onChange={(event) => setPlaylistTitle(event.target.value)}
                      ></Input>
                    </div>
                  </div>
                  <div className="fm-CreateList-action">
                    <button
                      className="btn-add-list"
                      onClick={() => submitPlaylist()}
                    >
                      <span>ตกลง</span>
                    </button>
                  </div>
                </div>
              </Modal.Content>
            </Modal>
            <Footer></Footer>
          </div>
        </div>
        <Modal
          className='modalCreateList'
          onClose={() => setModalOculus(false)}
          onOpen={() => setModalOculus(true)}
          open={modalOculus}>
          <Modal.Content className="modalCreateListContent">
            <div className="block-modal-CreateList">
                <div className="oculus-pin">
                  <h3>Pin ของคุณคือ</h3>
                  <h2>{oculusPin}</h2>
                  <p>Pin จะมีอายุการใช้งาน 10 นาที</p>
                </div>
                <div className='fm-CreateList-action'>
                  <button className='btn-add-list' onClick={() => setModalOculus(false)}>
                    <span>ปิด</span>
                  </button>
                </div>
            </div>
          </Modal.Content>
        </Modal>
      </>
    );
  } else if (data["data"]["type"] == "zoom" || data["data"]["type"] == "CVLEARN" || data["data"]["type"] == "CHULAMOOC") {
    return (
      <>
        <div className="main-all page-course">
          <Head>
            {seo_data.seo ? (
              seo_data.seo.map((val, key) =>
                val.name == "title" ? (
                  <>
                    <title>{val.content}</title>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                ) : (
                  <>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                )
              )
            ) : (
              <>
                <title>MDCU : MedU MORE</title>
              </>
            )}

            <meta
              key={"twitter:card"}
              name={"twitter:card"}
              content="summary_large_image"
            />
            <meta key={"og:type"} name={"og:type"} content="website" />
          </Head>

          <Header></Header>

          <div className="main-body">
            <div className="fix-space"></div>
            <CourseDetailIntro
              type={data["data"]["type"]}
              data={data["data"]}
              ep={data["data"]["learned_ep"]}
              callback={selectEp}
              addCart={groupCategoryCallback}
            ></CourseDetailIntro>
            <div className="container-fluid bg-white course-page">
              <div className={`container  space-between-content`}>
                <div className="row intro_course-page">
                  <div className="col-12 col-md-9 col-xl-8">
                    {reloadStar ? (
                      <CourseDetailDescription
                        data={data["data"]}
                        callback={courseDescription}
                        lang={lang}
                      ></CourseDetailDescription>
                    ) : null}
                    {data.data.allowed ||
                    data.data.order_status == 4 ||
                    data.data.order_status == 1 ? null : data.data.is_promotion ==
                      1 ? (
                      data.data.pro_price == 0 && discountCode=='' ? null : (
                        <div className="row discount-support">
                          <div className="col-12 col-md-6">
                            <p
                              className={`space-between-content-top ${styles.titleButtonGroup}`}
                            >
                              {translateEng('โค้ดส่วนลด')}
                            </p>
                            <CourseDetailCode
                              callback={addDiscountCode}
                              lang={lang}
                            ></CourseDetailCode>
                          </div>

                          {data &&
                          data["scholarshipList"] &&
                          data["scholarshipList"].length > 0 ? (
                            <div className="col-12 col-md-6">
                              <p className="space-between-content-top title-button-group">
                                {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                              </p>
                              <CourseDetailScholarship
                                callback={addDiscountCode}
                                name="courseScholarship"
                                data={data["scholarshipList"]}
                              ></CourseDetailScholarship>
                            </div>
                          ) : null}
                        </div>
                      )
                    ) : data.data.price == 0 && discountCode=='' ? null : (
                      <div className="row discount-support">
                        <div className="col-12 col-md-6">
                          <p
                            className={`space-between-content-top ${styles.titleButtonGroup}`}
                          >
                            {translateEng('โค้ดส่วนลด')}
                          </p>
                          <CourseDetailCode
                            callback={addDiscountCode}
                            lang={lang}
                          ></CourseDetailCode>
                        </div>

                        {data &&
                        data["scholarshipList"] &&
                        data["scholarshipList"].length > 0 ? (
                          <div className="col-12 col-md-6">
                            <p className="space-between-content-top title-button-group">
                              {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                            </p>
                            <CourseDetailScholarship
                              callback={addDiscountCode}
                              name="courseScholarship"
                              data={data["scholarshipList"]}
                            ></CourseDetailScholarship>
                          </div>
                        ) : null}
                      </div>
                    )}
                  </div>
                  <div className={`col-12 col-md-3 col-xl-4`}>
                    {/* update */}
                    <div className="box-price-to-buy" ref={spaceMove}>
                      {coursePriceReload &&
                      data &&
                      data["data"] &&
                      data["data"]["is_soon"] == false ? (
                        data.data.allowed ||
                        data.data.order_status == 4 ||
                        data.data.order_status == 1 ? null : 
                        data.data.is_subscription ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-big-price">Member</div>
                          </div>
                        ) :
                        data.data.is_internal || data.data.is_volume || data.data.expire_date ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-big-price">{translateEng('ฟรี')}</div>
                          </div>
                        ) : data.data.is_promotion == 1 ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-sale">
                              <NumberFormat
                                value={data.data.price}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <span {...props}>{translateEng('ปกติ')} {value}</span>
                                )}
                              />
                            </div>
                            {data.data.pro_price == 0 ? (
                              <div className="description-big-price">{translateEng('ฟรี')}</div>
                            ) : (
                              <div className="description-big-price">
                                {translateEng('ราคา')}
                                <NumberFormat
                                  value={data.data.pro_price}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                  renderText={(value, props) => (
                                    <span {...props}>{value}</span>
                                  )}
                                />
                                {translateEng('บาท')}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="block-course-detail-price text-center">
                            {data.data.price == 0 ? (
                              <div className="description-big-price">{translateEng('ฟรี')}</div>
                            ) : (
                              <div className="description-big-price">
                                {translateEng('ราคา')}
                                <NumberFormat
                                  value={data.data.price}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                  renderText={(value, props) => (
                                    <span {...props}>{value}</span>
                                  )}
                                />
                                {translateEng('บาท')}
                              </div>
                            )}
                          </div>
                        )
                      ) : null}
                      {data["data"]["allowed"] ||
                      data.data.order_status == 4 ||
                      data.data.order_status == 1 ||
                      (data &&
                        data["data"] &&
                        data["data"]["is_soon"] == true) ? 
                        data["data"]["allowed"] ? (
                          <div className="block-to-buy-course">
                            {data["data"]["type"] == "zoom"?(
                              <button
                                className="btn-to-buy-course blue-zoom"
                                onClick={() =>
                                  window.open(
                                    data["data"]["zoom_join_url"],
                                    '_blank'
                                  )
                                }
                              >
                                <span>Join Zoom</span>
                              </button>
                            ):data["data"]["type"] == "CVLEARN"?(
                              <button
                                className="btn-to-buy-course"
                                onClick={() =>
                                  appContext.submitImmersiveCourse(data["data"]["partner_ref_id"],'cvlearn')
                                }
                              >
                                <span>เข้าเรียน</span>
                              </button>
                            ):(
                              <button
                                className="btn-to-buy-course"
                                onClick={() =>
                                  appContext.submitImmersiveCourse(data["data"]["partner_ref_id"],'mooc')
                                }
                              >
                                <span>เข้าเรียน</span>
                              </button>
                            )}
                          </div>
                        ):null : (
                        <div className="block-to-buy-course">
                          {data.data.is_standalone == 1 && (data.data.price == 0 ||
                          (data.data.is_promotion == 1 &&
                            data.data.pro_price == 0) ||
                          data.data.is_internal ||
                          data.data.is_subscription||
                          data.data.is_volume || 
                          data.data.expire_date)&&discountCode=='' ? (
                            data.data.is_subscription ? (
                              <button
                                className="btn-to-buy-course"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "vip",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            ):(
                              data.data.is_volume ? (
                                <button
                                  className="btn-to-buy-course"
                                  onClick={() =>
                                    groupCategoryCallback(
                                      "volume",
                                      data["data"]["id"],
                                      ""
                                    )
                                  }
                                >
                                  <span>{translateEng('เริ่มเรียน')}</span>
                                </button>
                              ):(
                                data.data.is_internal&&data.data.user_internal ? (
                                  <button
                                    className="btn-to-buy-course"
                                    onClick={() =>
                                      groupCategoryCallback(
                                        "internal",
                                        data["data"]["id"],
                                        ""
                                      )
                                    }
                                  >
                                    <span>{translateEng('เริ่มเรียน')}</span>
                                  </button>
                                ):(
                                  <button
                                    className="btn-to-buy-course"
                                    onClick={() =>
                                      groupCategoryCallback(
                                        "free",
                                        data["data"]["id"],
                                        ""
                                      )
                                    }
                                  >
                                    <span>{translateEng('เริ่มเรียน')}</span>
                                  </button>
                                )
                              )
                            )
                            
                          ) : (
                            <button
                              className="btn-to-buy-course"
                              onClick={() =>
                                groupCategoryCallback(
                                  "cart",
                                  data["data"]["id"],
                                  ""
                                )
                              }
                            >
                              <span>{translateEng('ซื้อคอร์สนี้')}</span>
                            </button>
                          )}
                        </div>
                      )}

                      {data["data"]["is_soon"] == true ? (
                        <div className="block-course-detail-price text-center">
                          <div className="description-big-price">Coming soon</div>
                        </div>
                      ) : null}

                      <div className="space-fix-price"></div>
                    </div>
                    {/* update */}
                  </div>
                </div>
                <div className="row">
                  <div className="col-12 course-page">
                    <div>
                      {vdoList && vdoList.length > 0 ? (
                        <Tab
                          panes={panes}
                          renderActiveOnly={false}
                          className={`space-between-content-top courseTab`}
                        />
                      ) : null}
                    </div>
                  </div>
                   <div className="col-12">
                     <CurriculumBackButton />
                  </div>
                  {/* ==================== [ update ] ==================== */}
                  <div className="col-12 course-page">
                    <div className="block-quick-result">
                      <Tab
                        panes={quickResult}
                        className={`space-between-content-top courseTab pd-0`}
                      />
                    </div>
                  </div>
                  {data.data && data.data.tag.length > 0 ? (
                    <div className="col-12 course-page">
                      <div className="block-quick-result">
                        <ListTags data={data.data.tag}></ListTags>
                      </div>
                    </div>
                  ) : null}
                  {/* ==================== [ update ] ==================== */}
                </div>
              </div>
            </div>

            {relateData&&relateData.length>0 ? (
              <GroupCategory
                type="normal"
                name={translateEng('คอร์สเกี่ยวข้อง')}
                bg={''}
                color="#6E953D"
                size={3.5}
                isLoop={false}
                index={20}
                data={relateData}
                callback={groupCategoryCallback}
              ></GroupCategory>
            ) : null}
            {modalVdoOpen && data && vdoList && vdoList.length > 0 ? (
              <ListVdoModal
                genOculus={genOculus}
                cbProgress={courseProgress}
                cbProgressEnd={progressEnd}
                callback={playerCallback}
                skipVdo={skipVdo}
                keySelect={keySelect}
                vdoList={vdoList}
                slug={params.id}
              ></ListVdoModal>
            ) : null}
            {modalResult && resultData &&  resultData.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => resultCallback(false)}
                onOpen={() => resultCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  {resultData.map((val_q, key_q) => (
                    <div className="block-quiz-border" key={key_q}>
                      <div className="block-quiz">
                        <div className="i-title-quiz">
                          <h3>
                            <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                          </h3>
                          {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                            <div className={styles.questionImageDiv}>
                              <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                            </div>
                          ):null}
                        </div>
                      </div>
                      <div className="block-quiz">
                        <div className="i-title-quiz">
                          {val_q.exam_type==1 ? (
                            val_q.is_answer==1 ? (
                              <>
                                <h3 className="result">
                                  <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title}
                                </h3>
                                {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                                  <div className={styles.answerImageResultDiv}>
                                    <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                                  </div>
                                ):null}
                              </>
                            ):(
                              val_q.is_right==1 ? (
                                <>
                                  <h3 className="result true">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title} <i className="icon-ic-tick"></i>
                                  </h3>
                                  {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                                    <div className={styles.answerImageResultDiv}>
                                      <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                                    </div>
                                  ):null}
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              ):(
                                <>
                                  <h3 className="result false">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title} <i className="icon-ic-close"></i>
                                  </h3>
                                  {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                                    <div className={styles.answerImageResultDiv}>
                                      <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                                    </div>
                                  ):null}
                                  <h3 className="description-true true">
                                    <span>{translateEng('คำตอบที่ถูก')} :</span> {val_q.true_answer}
                                  </h3>
                                  {val_q && val_q.true_answer_image && val_q.true_answer_image!=null&&val_q.true_answer_image!=''&&val_q.true_answer_image!='null' ?(
                                    <div className={styles.answerImageResultDiv}>
                                      <Image alt="" layout="fill" className="answer_image_result" src={val_q.true_answer_image} />
                                    </div>
                                  ):null}
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              )
                            )
                          ):(
                            val_q.exam_type==2 ? (
                              val_q.is_answer==1 ? (
                                <h3 className="result">
                                  <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_text}
                                </h3>
                              ):(
                                <>
                                  <h3 className="result">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_text}
                                  </h3>
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              )
                            ):(
                              val_q.is_answer==1 ? (
                                <h3 className="result">
                                  <span>{translateEng('คำตอบของท่าน')} :</span> <a href={val_q.file} target="_blank" rel="noopener noreferrer">{translateEng('ดูคำตอบ')}</a>
                                </h3>
                              ):(
                                <>
                                  <h3 className="result">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> <a href={val_q.file} target="_blank" rel="noopener noreferrer">{translateEng('ดูคำตอบ')}</a>
                                  </h3>
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              )
                            )
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </Modal.Content>
              </Modal>
            ) : null}
            {modalPreTest && data && vdoList && vdoList.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => preTestCallback(false)}
                onOpen={() => preTestCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  {vdoList[keySelect]["pre_test"].question.map((val_q, key_q) => (
                    <div className="block-quiz" key={key_q}>
                      <div className="i-title-quiz">
                        <h3>
                          <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question}
                        </h3>
                        {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                          <div className={styles.questionImageDiv}>
                            <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                          </div>
                        ):null}
                      </div>
                      {val_q.type == "choice" ? (
                        <div className="i-choose-quiz-row row">
                          {val_q.answer.map((val_ans, key_ans) => (
                            <div
                              className="col-12 col-md-6 col-choose-quiz"
                              key={key_ans}
                            >
                              <div
                                className="item-choose"
                                onClick={() => onAnswerPreTest(key_q, val_ans.id)}
                              >
                                <div className="fm-check">
                                  <input
                                    className="answer_input"
                                    type="radio"
                                    name={`answer_${key_q}`}
                                  />
                                  <div className="text">
                                    <div className="i_remark">
                                      <i className="icon-ic-circle" />
                                    </div>
                                    <p>{val_ans.answer}</p>
                                  </div>
                                  {val_ans && val_ans.image && val_ans.image!=null&&val_ans.image!=''&&val_ans.image!='null' ?(
                                    <div className={styles.answerImageDiv}>
                                      <Image alt="" layout="fill" className="answer_image" src={val_ans.image} />
                                    </div>
                                  ):null}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : val_q.type == "text" ? (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <TextArea
                                placeholder=""
                                className={
                                  "fm-control answer_textarea " +
                                  (checkMyError(val_q.id) ? "error" : "")
                                }
                                data-type="textaddress"
                                onInput={appContext.diFormPattern}
                                onChange={(event) =>
                                  onAnswerPreTest(key_q, event.target.value)
                                }
                              />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <input
                                className="answer_textarea"
                                type="file"
                                accept="image/jpeg,image/jpg,image/png,image/gif,image/xbm,image/xpm,image/wbmp,image/webp,image/bmp"
                                onChange={(e) => {
                                  previewFilePreTest(key_q, e);
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  <div className="block-to-buy-course">
                    <button
                      className="btn-to-buy-course left"
                      onClick={() => onSubmitPreTest(keySelect)}
                    >
                      <span>{translateEng('ส่งคำตอบ')}</span>
                    </button>
                  </div>
                </Modal.Content>
              </Modal>
            ) : null}
            {modalQuizOpen && data && vdoList && vdoList.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => quizCallback(false)}
                onOpen={() => quizCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  {vdoList[quizSelect]["quiz"].question.map((val_q, key_q) => (
                    <div className="block-quiz" key={key_q}>
                      <div className="i-title-quiz">
                        <h3>
                          <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question}
                        </h3>
                        {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                          <div className={styles.questionImageDiv}>
                            <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                          </div>
                        ):null}
                      </div>
                      {val_q.type == "choice" ? (
                        <div className="i-choose-quiz-row row">
                          {val_q.answer.map((val_ans, key_ans) => (
                            <div
                              className="col-12 col-md-6 col-choose-quiz"
                              key={key_ans}
                            >
                              <div
                                className="item-choose"
                                onClick={() => onAnswer(key_q, val_ans.id)}
                              >
                                <div className="fm-check">
                                  <input
                                    className="answer_input"
                                    type="radio"
                                    name={`answer_${key_q}`}
                                  />
                                  <div className="text">
                                    <div className="i_remark">
                                      <i className="icon-ic-circle" />
                                    </div>
                                    <p>{val_ans.answer}</p>
                                  </div>
                                  {val_ans && val_ans.image && val_ans.image!=null&&val_ans.image!=''&&val_ans.image!='null' ?(
                                    <div className={styles.answerImageDiv}>
                                      <Image alt="" layout="fill" className="answer_image" src={val_ans.image} />
                                    </div>
                                  ):null}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : val_q.type == "text" ? (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <TextArea
                                placeholder=""
                                className={
                                  "fm-control answer_textarea " +
                                  (checkMyError(val_q.id) ? "error" : "")
                                }
                                data-type="textaddress"
                                onInput={appContext.diFormPattern}
                                onChange={(event) =>
                                  onAnswer(key_q, event.target.value)
                                }
                              />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <input
                                className="answer_textarea"
                                type="file"
                                accept="image/jpeg,image/jpg,image/png,image/gif,image/xbm,image/xpm,image/wbmp,image/webp,image/bmp"
                                onChange={(e) => {
                                  previewFile(key_q, e);
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  <div className="block-to-buy-course">
                    <button
                      className="btn-to-buy-course left"
                      onClick={() => onSubmit(quizSelect)}
                    >
                      <span>{translateEng('ส่งคำตอบ')}</span>
                    </button>
                  </div>
                </Modal.Content>
              </Modal>
            ) : null}
            {modalAssessment && data && assessmentData && assessmentData.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => assessmentCallback(false)}
                onOpen={() => assessmentCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  <div className="block-quiz">
                    <div className="i-title-quiz">
                      <h2>
                        {translateEng('แบบประเมินความพึงพอใจ (ใช้เวลาไม่ถึง 1 นาที)')}
                      </h2>
                    </div>
                  </div>
                  {assessmentData.map((val_q, key_q) => (
                    key_q==0 ? (
                      assessmentStep == 1 ?(
                        <div className={`block-quiz assessment`} key={key_q}>
                          <div className="i-title-quiz">
                            <h3>
                              <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                            </h3>
                          </div>
                          {val_q.type == 1 ? (
                            <div className="i-choose-quiz-row row">
                              {val_q.choice.map((val_ans, key_ans) => (
                                <div
                                  className="col-12 col-md-6 col-choose-quiz"
                                  key={key_ans}
                                >
                                  <div
                                    className="item-choose"
                                    onClick={() => onChoice(key_q, val_ans.id)}
                                  >
                                    <div className="fm-check">
                                      <input
                                        className="choice_input"
                                        type="radio"
                                        name={`choice_${key_q}`}
                                      />
                                      <div className="text">
                                        <div className="i_remark">
                                          <i className="icon-ic-circle" />
                                        </div>
                                        <p>{val_ans.choice_th}</p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            val_q.type == 3 ? (
                              <div className="i-choose-quiz-row row">
                                {val_q.choice.map((val_ans, key_ans) => (
                                  <div
                                    className="col-4 col-md-2 col-choose-quiz"
                                    key={key_ans}
                                  >
                                    <div
                                      className="item-choose"
                                      onClick={() => onChoice(key_q, val_ans.id)}
                                    >
                                      <div className="fm-check">
                                        <input
                                          className="choice_input"
                                          type="radio"
                                          name={`choice_${key_q}`}
                                        />
                                        <div className="text with-icon">
                                          {val_ans && val_ans.icon && val_ans.icon!=null&&val_ans.icon!=''&&val_ans.icon!='null' ?(
                                            <div className={styles.assessmentIconDiv}>
                                              <Image alt="" layout="intrinsic" className={`${styles.iconThumb}`} src={val_ans.icon} width={500} height={500} />
                                            </div>
                                          ):null}
                                          <div className="i_remark">
                                            <i className="icon-ic-circle" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="i-choose-quiz-row row">
                                <div className="col-12 col-md-12 col-choose-quiz">
                                  <div className="item-fm">
                                    <TextArea
                                      placeholder=""
                                      className={
                                        "fm-control choice_textarea " +
                                        (checkMyError(val_q.id) ? "error" : "")
                                      }
                                      data-type="textaddress"
                                      onInput={appContext.diFormPattern}
                                      onChange={(event) =>
                                        onChoice(key_q, event.target.value)
                                      }
                                    />
                                  </div>
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      ):null
                    ):(
                      assessmentStep == 2 ?(
                        <div className={`block-quiz assessment`} key={key_q}>
                          <div className="i-title-quiz">
                            <h3>
                              <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                            </h3>
                          </div>
                          {val_q.type == 1 ? (
                            <div className="i-choose-quiz-row row">
                              {val_q.choice.map((val_ans, key_ans) => (
                                <div
                                  className="col-12 col-md-6 col-choose-quiz"
                                  key={key_ans}
                                >
                                  <div
                                    className="item-choose"
                                    onClick={() => onChoice(key_q, val_ans.id)}
                                  >
                                    <div className="fm-check">
                                      <input
                                        className="choice_input"
                                        type="radio"
                                        name={`choice_${key_q}`}
                                      />
                                      <div className="text">
                                        <div className="i_remark">
                                          <i className="icon-ic-circle" />
                                        </div>
                                        <p>{val_ans.choice_th}</p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            val_q.type == 3 ? (
                              <div className="i-choose-quiz-row row">
                                {val_q.choice.map((val_ans, key_ans) => (
                                  <div
                                    className="col-4 col-md-2 col-choose-quiz"
                                    key={key_ans}
                                  >
                                    <div
                                      className="item-choose"
                                      onClick={() => onChoice(key_q, val_ans.id)}
                                    >
                                      <div className="fm-check">
                                        <input
                                          className="choice_input"
                                          type="radio"
                                          name={`choice_${key_q}`}
                                        />
                                        <div className="text with-icon">
                                          {val_ans && val_ans.icon && val_ans.icon!=null && val_ans.icon!='' && val_ans.icon!='null' ?(
                                            <div className={styles.assessmentIconDiv}>
                                              <Image alt="" layout="intrinsic" className={`${styles.iconThumb}`} src={val_ans.icon} width={500} height={500} />
                                            </div>
                                          ):null}
                                          <div className="i_remark">
                                            <i className="icon-ic-circle" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="i-choose-quiz-row row">
                                <div className="col-12 col-md-12 col-choose-quiz">
                                  <div className="item-fm">
                                    <TextArea
                                      placeholder=""
                                      className={
                                        "fm-control choice_textarea " +
                                        (checkMyError(val_q.id) ? "error" : "")
                                      }
                                      data-type="textaddress"
                                      onInput={appContext.diFormPattern}
                                      onChange={(event) =>
                                        onChoice(key_q, event.target.value)
                                      }
                                    />
                                  </div>
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      ):null
                    )
                  ))}
                  {assessmentStep == 1 ?(
                    <div className="block-to-buy-course assessment-group-btn">
                      <button
                        className="btn-to-buy-course left"
                        onClick={() => assessmentCallback(false)}
                      >
                        <span>{translateEng('ข้าม')}</span>
                      </button>
                      <button
                        className="btn-to-buy-course right"
                        onClick={() => onAssessmentNext()}
                      >
                        <span>{translateEng('ต่อไป')}</span>
                      </button>
                    </div>
                  ):(
                    <div className="block-to-buy-course">
                      <button
                        className="btn-to-buy-course left"
                        onClick={() => onAssessment()}
                      >
                        <span>{translateEng('ส่งคำตอบ')}</span>
                      </button>
                    </div>
                  )}
                </Modal.Content>
              </Modal>
            ) : null}
            {modalCert ? (
              <Modal
                className={`modal-unlock-cert`}
                onClose={() => certCallback(false)}
                onOpen={() => certCallback(true)}
                open={true}
              >
                <Modal.Content className={`inner`}>
                  <div className="block-unlock-cert">
                    <div className="left">
                      <div className={styles.unlockCertIconDiv}>
                        <Image alt="" layout="intrinsic" src="/assets/images/unlock_cert.png" width={500} height={500} />
                      </div>
                    </div>
                    <div className="right">
                      <h3>{translateEng('ยินดีด้วย!')}</h3>
                      <h2>{translateEng('คุณปลดล็อกใบประกาศ')}</h2>
                      <p>{translateEng('คอร์ส')} {data["data"]["title"]}</p>
                      <div className="curve">
                        <span>{translateEng('สามารถดาวน์โหลดใบประกาศได้ที่โปรไฟล์ของท่าน')}</span>
                      </div>
                      <div className="action">
                        <Link href="/profile/certificate">
                          <Button className="green-button">
                            <span>{translateEng('หรือคลิกที่นี่')}</span>
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </Modal.Content>
              </Modal>
            ) : null}
            <Modal
              className="modalCreateList"
              onClose={() => setAddPlaylist(false)}
              onOpen={() => setAddPlaylist(true)}
              open={addPlaylist}
            >
              <Modal.Content className="modalCreateListContent">
                <div className="block-modal-CreateList">
                  <div className="inner">
                    <h3>{translateEng('เพิ่มไปยังเพลย์ลิสต์ของคุณ')}</h3>
                  </div>
                  <div className="fm-CreateList-select">
                    {playListOptions.length > 0 ? (
                      <div className="item-fm">
                        <Select
                          className="fm-control"
                          placeholder={translateEng('เพลย์ลิสต์ของคุณ')}
                          options={playListOptions}
                          onChange={(event, data) => {
                            setPlaylistSelect(data.value);
                          }}
                        />
                      </div>
                    ) : null}
                    <button
                      className="btn-add-list"
                      onClick={() => show_add_list()}
                    >
                      <span>{translateEng('สร้างเพลย์ลิสต์')}</span>
                    </button>
                  </div>
                  <div id="fmCreateListAdd" className="fm-CreateList-add">
                    <div className="item-fm">
                      <Input
                        id="create_list"
                        type="text"
                        className="fm-control"
                        placeholder={translateEng('ป้อนชื่อเพลย์ลิสต์')}
                        onChange={(event) => setPlaylistTitle(event.target.value)}
                      ></Input>
                    </div>
                  </div>
                  <div className="fm-CreateList-action">
                    <button
                      className="btn-add-list"
                      onClick={() => submitPlaylist()}
                    >
                      <span>ตกลง</span>
                    </button>
                  </div>
                </div>
              </Modal.Content>
            </Modal>
            <Footer></Footer>
            {/* update */}
            {coursePriceReload &&
            data &&
            data["data"] &&
            data["data"]["is_soon"] == false ? (
              data.data.allowed ||
              data.data.order_status == 4 ||
              data.data.order_status == 1 ? null : 
              data.data.is_subscription ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-default">Member</div>
                  </div>
                </div>
              ) :
              data.data.is_internal || data.data.is_volume ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-default">{translateEng('ฟรี')}</div>
                  </div>
                </div>
              ) : data.data.is_promotion == 1 ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-sell">
                      {translateEng('ปกติ')}
                      <NumberFormat
                        value={data.data.price}
                        displayType={"text"}
                        thousandSeparator={true}
                        renderText={(value, props) => (
                          <span {...props}>{value}</span>
                        )}
                      />
                    </div>
                    {data.data.pro_price == 0 || data.data.expire_date ? (
                      <div className="info-price-default">{translateEng('ฟรี')}</div>
                    ) : (
                      <div className="info-price-default">
                        {translateEng('ราคา')}
                        <NumberFormat
                          value={data.data.pro_price}
                          displayType={"text"}
                          thousandSeparator={true}
                          renderText={(value, props) => (
                            <span {...props}>{value}</span>
                          )}
                        />
                        {translateEng('บาท')}
                      </div>
                    )}
                  </div>
                  <div className="action-price">
                    {data && data["data"] && data.data.is_standalone == 1 && data["data"]["is_soon"] == false ? (
                      (data.data.price == 0 ||
                      (data.data.is_promotion == 1 && data.data.pro_price == 0) ||
                      data.data.is_internal||
                      data.data.is_subscription||
                      data.data.is_volume || 
                      data.data.expire_date)&&discountCode=='' ? (
                        data.data.is_subscription ? (
                          <button
                            className="btn-action-price"
                            onClick={() =>
                              groupCategoryCallback("vip", data["data"]["id"], "")
                            }
                          >
                            <span>{translateEng('เริ่มเรียน')}</span>
                          </button>
                        ):(
                          data.data.is_volume ? (
                            <button
                              className="btn-action-price"
                              onClick={() =>
                                groupCategoryCallback("volume", data["data"]["id"], "")
                              }
                            >
                              <span>{translateEng('เริ่มเรียน')}</span>
                            </button>
                          ):(
                            data.data.is_internal&&data.data.user_internal ? (
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback("internal", data["data"]["id"], "")
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            ):(
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback("free", data["data"]["id"], "")
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            )
                          )
                        )
                      ) : (
                        <button
                          className="btn-action-price"
                          onClick={() =>
                            groupCategoryCallback("cart", data["data"]["id"], "")
                          }
                        >
                          <span>{translateEng('ซื้อคอร์สนี้')}</span>
                        </button>
                      )
                    ) : null}
                  </div>
                </div>
              ) : (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    {data.data.price == 0 ? (
                      <div className="info-price-default">{translateEng('ฟรี')}</div>
                    ) : (
                      <div className="info-price-default">
                        {translateEng('ราคา')}
                        <NumberFormat
                          value={data.data.price}
                          displayType={"text"}
                          thousandSeparator={true}
                          renderText={(value, props) => (
                            <span {...props}>{value}</span>
                          )}
                        />
                        {translateEng('บาท')}
                      </div>
                    )}
                  </div>
                  <div className="action-price">
                    {data && data["data"] && data.data.is_standalone == 1 && data["data"]["is_soon"] == false ? (
                      (data.data.price == 0 ||
                      (data.data.is_promotion == 1 && data.data.pro_price == 0) ||
                      data.data.is_internal||
                      data.data.is_subscription||
                      data.data.is_volume || 
                      data.data.expire_date)&&discountCode=='' ? (
                        data.data.is_subscription ? (
                          <button
                            className="btn-action-price"
                            onClick={() =>
                              groupCategoryCallback("vip", data["data"]["id"], "")
                            }
                          >
                            <span>{translateEng('เริ่มเรียน')}</span>
                          </button>
                        ):(
                          data.data.is_volume ? (
                            <button
                              className="btn-action-price"
                              onClick={() =>
                                groupCategoryCallback("volume", data["data"]["id"], "")
                              }
                            >
                              <span>{translateEng('เริ่มเรียน')}</span>
                            </button>
                          ):(
                            data.data.is_internal&&data.data.user_internal ? (
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback("internal", data["data"]["id"], "")
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            ):(
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback("free", data["data"]["id"], "")
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            )
                          )
                        )
                      ) : (
                        <button
                          className="btn-action-price"
                          onClick={() =>
                            groupCategoryCallback("cart", data["data"]["id"], "")
                          }
                        >
                          <span>{translateEng('ซื้อคอร์สนี้')}</span>
                        </button>
                      )
                    ) : null}
                  </div>
                </div>
              )
            ) : null}
            {/* update */}
          </div>
        </div>
        <Modal
          className='modalCreateList'
          onClose={() => setModalOculus(false)}
          onOpen={() => setModalOculus(true)}
          open={modalOculus}>
          <Modal.Content className="modalCreateListContent">
            <div className="block-modal-CreateList">
                <div className="oculus-pin">
                  <h3>Pin ของคุณคือ</h3>
                  <h2>{oculusPin}</h2>
                  <p>Pin จะมีอายุการใช้งาน 10 นาที</p>
                </div>
                <div className='fm-CreateList-action'>
                  <button className='btn-add-list' onClick={() => setModalOculus(false)}>
                    <span>ปิด</span>
                  </button>
                </div>
            </div>
          </Modal.Content>
        </Modal>
      </>
    );
  } else if (data["data"]["type"] == "seminar") {
    return (
      <>
        <div className="main-all page-course">
          <Head>
            {seo_data.seo ? (
              seo_data.seo.map((val, key) =>
                val.name == "title" ? (
                  <>
                    <title>{val.content}</title>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                ) : (
                  <>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                )
              )
            ) : (
              <>
                <title>MDCU : MedU MORE</title>
              </>
            )}

            <meta
              key={"twitter:card"}
              name={"twitter:card"}
              content="summary_large_image"
            />
            <meta key={"og:type"} name={"og:type"} content="website" />
          </Head>

          <Header></Header>

          <div className="main-body">
            <div className="fix-space"></div>
            <CourseDetailIntro
              type="normal"
              data={data["data"]}
              ep={data["data"]["learned_ep"]}
              callback={selectEp}
              addCart={groupCategoryCallback}
            ></CourseDetailIntro>
            <div className="container-fluid bg-white course-page">
              <div className={`container  space-between-content`}>
                <div className="row intro_course-page">
                  <div className="col-12 col-md-9 col-xl-8">
                    {reloadStar ? (
                      <CourseDetailDescription
                        data={data["data"]}
                        callback={courseDescription}
                        lang={lang}
                      ></CourseDetailDescription>
                    ) : null}

                    {data.data.allowed ||
                    data.data.order_status == 4 ||
                    data.data.order_status == 1 ? null : data.data.is_promotion ==
                      1 ? (
                      data.data.pro_price == 0 && discountCode=='' ? null : (
                        <div className="row discount-support">
                          <div className="col-12 col-md-6">
                            <p
                              className={`space-between-content-top ${styles.titleButtonGroup}`}
                            >
                              {translateEng('โค้ดส่วนลด')}
                            </p>
                            <CourseDetailCode
                              callback={addDiscountCode}
                              lang={lang}
                            ></CourseDetailCode>
                          </div>

                          {data &&
                          data["scholarshipList"] &&
                          data["scholarshipList"].length > 0 ? (
                            <div className="col-12 col-md-6">
                              <p className="space-between-content-top title-button-group">
                                {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                              </p>
                              <CourseDetailScholarship
                                callback={addDiscountCode}
                                name="courseScholarship"
                                data={data["scholarshipList"]}
                              ></CourseDetailScholarship>
                            </div>
                          ) : null}
                        </div>
                      )
                    ) : data.data.price == 0 && discountCode=='' ? null : (
                      <div className="row discount-support">
                        <div className="col-12 col-md-6">
                          <p
                            className={`space-between-content-top ${styles.titleButtonGroup}`}
                          >
                            {translateEng('โค้ดส่วนลด')}
                          </p>
                          <CourseDetailCode
                            callback={addDiscountCode}
                            lang={lang}
                          ></CourseDetailCode>
                        </div>

                        {data &&
                        data["scholarshipList"] &&
                        data["scholarshipList"].length > 0 ? (
                          <div className="col-12 col-md-6">
                            <p className="space-between-content-top title-button-group">
                              {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                            </p>
                            <CourseDetailScholarship
                              callback={addDiscountCode}
                              name="courseScholarship"
                              data={data["scholarshipList"]}
                            ></CourseDetailScholarship>
                          </div>
                        ) : null}
                      </div>
                    )}
                  </div>
                  <div className={`col-12 col-md-3 col-xl-4`}>
                    {/* update */}
                    <div className="box-price-to-buy" ref={spaceMove}>
                      {coursePriceReload &&
                      data &&
                      data["data"] &&
                      data["data"]["is_soon"] == false ? (
                        data.data.allowed ||
                        data.data.order_status == 4 ||
                        data.data.order_status == 1 ? null : 
                        data.data.is_subscription ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-big-price">Member</div>
                          </div>
                        ) :
                        data.data.is_internal || data.data.is_volume || data.data.expire_date ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-big-price">{translateEng('ฟรี')}</div>
                          </div>
                        ) : data.data.is_promotion == 1 ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-sale">
                              <NumberFormat
                                value={data.data.price}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <span {...props}>{translateEng('ปกติ')} {value}</span>
                                )}
                              />
                            </div>
                            {data.data.pro_price == 0 ? (
                              <div className="description-big-price">{translateEng('ฟรี')}</div>
                            ) : (
                              <div className="description-big-price">
                                {translateEng('ราคา')}
                                <NumberFormat
                                  value={data.data.pro_price}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                  renderText={(value, props) => (
                                    <span {...props}>{value}</span>
                                  )}
                                />
                                {translateEng('บาท')}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="block-course-detail-price text-center">
                            {data.data.price == 0 ? (
                              <div className="description-big-price">{translateEng('ฟรี')}</div>
                            ) : (
                              <div className="description-big-price">
                                {translateEng('ราคา')}
                                <NumberFormat
                                  value={data.data.price}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                  renderText={(value, props) => (
                                    <span {...props}>{value}</span>
                                  )}
                                />
                                {translateEng('บาท')}
                              </div>
                            )}
                          </div>
                        )
                      ) : null}
                      {data["data"]["allowed"] ||
                      data.data.order_status == 4 ||
                      data.data.order_status == 1 ||
                      ((data &&
                        data["data"] &&
                        data["data"]["is_soon"] == true) || data.data.is_standalone == 0) ? null : (
                        <div className="block-to-buy-course">
                          {(data.data.price == 0 ||
                          (data.data.is_promotion == 1 &&
                            data.data.pro_price == 0) ||
                          data.data.is_internal||
                          data.data.is_subscription||
                          data.data.is_volume || 
                          data.data.expire_date)&&discountCode=='' ? (
                            data.data.is_subscription ? (
                              <button
                                className="btn-to-buy-course"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "vip",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            ):(
                              data.data.is_volume ? (
                                <button
                                  className="btn-to-buy-course"
                                  onClick={() =>
                                    groupCategoryCallback(
                                      "volume",
                                      data["data"]["id"],
                                      ""
                                    )
                                  }
                                >
                                  <span>{translateEng('เริ่มเรียน')}</span>
                                </button>
                              ):(
                                data.data.is_internal&&data.data.user_internal ? (
                                  <button
                                    className="btn-to-buy-course"
                                    onClick={() =>
                                      groupCategoryCallback(
                                        "internal",
                                        data["data"]["id"],
                                        ""
                                      )
                                    }
                                  >
                                    <span>{translateEng('เริ่มเรียน')}</span>
                                  </button>
                                ):(
                                  <button
                                    className="btn-to-buy-course"
                                    onClick={() =>
                                      groupCategoryCallback(
                                        "free",
                                        data["data"]["id"],
                                        ""
                                      )
                                    }
                                  >
                                    <span>{translateEng('เริ่มเรียน')}</span>
                                  </button>
                                )
                              )
                            )
                          ) : (
                            <button
                              className="btn-to-buy-course"
                              onClick={() =>
                                groupCategoryCallback(
                                  "cart",
                                  data["data"]["id"],
                                  ""
                                )
                              }
                            >
                              <span>{translateEng('ซื้อคอร์สนี้')}</span>
                            </button>
                          )}
                        </div>
                      )}

                      {data["data"]["is_soon"] == true ? (
                        <div className="block-course-detail-price text-center">
                          <div className="description-big-price">Coming soon</div>
                        </div>
                      ) : null}

                      <div className="space-fix-price"></div>
                    </div>
                    {/* update */}
                  </div>
                </div>
                <div className="row">
                  <div className="col-12 course-page">
                    <div>
                      {vdoList && vdoList.length > 0 ? (
                        <Tab
                          panes={panes}
                          renderActiveOnly={false}
                          className={`space-between-content-top courseTab`}
                        />
                      ) : null}
                    </div>
                  </div>
                   <div className="col-12">
                     <CurriculumBackButton />
                  </div>
                  {/* ==================== [ update ] ==================== */}
                  <div className="col-12 course-page">
                    <div className="block-quick-result">
                      <Tab
                        panes={quickResult}
                        className={`space-between-content-top courseTab pd-0`}
                      />
                    </div>
                  </div>
                  {data.data && data.data.tag.length > 0 ? (
                    <div className="col-12 course-page">
                      <div className="block-quick-result">
                        <ListTags data={data.data.tag}></ListTags>
                      </div>
                    </div>
                  ) : null}
                  {/* ==================== [ update ] ==================== */}
                </div>
              </div>
            </div>

            {relateData&&relateData.length>0 ? (
              <GroupCategory
                type="normal"
                name={translateEng('คอร์สเกี่ยวข้อง')}
                bg={''}
                color="#6E953D"
                size={3.5}
                isLoop={false}
                index={20}
                data={relateData}
                callback={groupCategoryCallback}
              ></GroupCategory>
            ) : null}
            {modalVdoOpen && data && vdoList && vdoList.length > 0 ? (
              <ListVdoModal
                genOculus={genOculus}
                cbProgress={courseProgress}
                cbProgressEnd={progressEnd}
                callback={playerCallback}
                skipVdo={skipVdo}
                keySelect={keySelect}
                vdoList={vdoList}
                slug={params.id}
              ></ListVdoModal>
            ) : null}
            {modalResult && resultData &&  resultData.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => resultCallback(false)}
                onOpen={() => resultCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  {resultData.map((val_q, key_q) => (
                    <div className="block-quiz-border" key={key_q}>
                      <div className="block-quiz">
                        <div className="i-title-quiz">
                          <h3>
                            <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                          </h3>
                          {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                            <div className={styles.questionImageDiv}>
                              <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                            </div>
                          ):null}
                        </div>
                      </div>
                      <div className="block-quiz">
                        <div className="i-title-quiz">
                          {val_q.exam_type==1 ? (
                            val_q.is_answer==1 ? (
                              <>
                                <h3 className="result">
                                  <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title}
                                </h3>
                                {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                                  <div className={styles.answerImageResultDiv}>
                                    <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                                  </div>
                                ):null}
                              </>
                            ):(
                              val_q.is_right==1 ? (
                                <>
                                  <h3 className="result true">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title} <i className="icon-ic-tick"></i>
                                  </h3>
                                  {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                                    <div className={styles.answerImageResultDiv}>
                                      <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                                    </div>
                                  ):null}
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              ):(
                                <>
                                  <h3 className="result false">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title} <i className="icon-ic-close"></i>
                                  </h3>
                                  {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                                    <div className={styles.answerImageResultDiv}>
                                      <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                                    </div>
                                  ):null}
                                  <h3 className="description-true true">
                                    <span>{translateEng('คำตอบที่ถูก')} :</span> {val_q.true_answer}
                                  </h3>
                                  {val_q && val_q.true_answer_image && val_q.true_answer_image!=null&&val_q.true_answer_image!=''&&val_q.true_answer_image!='null' ?(
                                    <div className={styles.answerImageResultDiv}>
                                      <Image alt="" layout="fill" className="answer_image_result" src={val_q.true_answer_image} />
                                    </div>
                                  ):null}
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              )
                            )
                          ):(
                            val_q.exam_type==2 ? (
                              val_q.is_answer==1 ? (
                                <h3 className="result">
                                  <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_text}
                                </h3>
                              ):(
                                <>
                                  <h3 className="result">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_text}
                                  </h3>
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              )
                            ):(
                              val_q.is_answer==1 ? (
                                <h3 className="result">
                                  <span>{translateEng('คำตอบของท่าน')} :</span> <a href={val_q.file} target="_blank" rel="noopener noreferrer">{translateEng('ดูคำตอบ')}</a>
                                </h3>
                              ):(
                                <>
                                  <h3 className="result">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> <a href={val_q.file} target="_blank" rel="noopener noreferrer">{translateEng('ดูคำตอบ')}</a>
                                  </h3>
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              )
                            )
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </Modal.Content>
              </Modal>
            ) : null}
            {modalPreTest && data && vdoList && vdoList.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => preTestCallback(false)}
                onOpen={() => preTestCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  {vdoList[keySelect]["pre_test"].question.map((val_q, key_q) => (
                    <div className="block-quiz" key={key_q}>
                      <div className="i-title-quiz">
                        <h3>
                          <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question}
                        </h3>
                        {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                          <div className={styles.questionImageDiv}>
                            <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                          </div>
                        ):null}
                      </div>
                      {val_q.type == "choice" ? (
                        <div className="i-choose-quiz-row row">
                          {val_q.answer.map((val_ans, key_ans) => (
                            <div
                              className="col-12 col-md-6 col-choose-quiz"
                              key={key_ans}
                            >
                              <div
                                className="item-choose"
                                onClick={() => onAnswerPreTest(key_q, val_ans.id)}
                              >
                                <div className="fm-check">
                                  <input
                                    className="answer_input"
                                    type="radio"
                                    name={`answer_${key_q}`}
                                  />
                                  <div className="text">
                                    <div className="i_remark">
                                      <i className="icon-ic-circle" />
                                    </div>
                                    <p>{val_ans.answer}</p>
                                  </div>
                                  {val_ans && val_ans.image && val_ans.image!=null&&val_ans.image!=''&&val_ans.image!='null' ?(
                                    <div className={styles.answerImageDiv}>
                                      <Image alt="" layout="fill" className="answer_image" src={val_ans.image} />
                                    </div>
                                  ):null}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : val_q.type == "text" ? (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <TextArea
                                placeholder=""
                                className={
                                  "fm-control answer_textarea " +
                                  (checkMyError(val_q.id) ? "error" : "")
                                }
                                data-type="textaddress"
                                onInput={appContext.diFormPattern}
                                onChange={(event) =>
                                  onAnswerPreTest(key_q, event.target.value)
                                }
                              />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <input
                                className="answer_textarea"
                                type="file"
                                accept="image/jpeg,image/jpg,image/png,image/gif,image/xbm,image/xpm,image/wbmp,image/webp,image/bmp"
                                onChange={(e) => {
                                  previewFilePreTest(key_q, e);
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  <div className="block-to-buy-course">
                    <button
                      className="btn-to-buy-course left"
                      onClick={() => onSubmitPreTest(keySelect)}
                    >
                      <span>{translateEng('ส่งคำตอบ')}</span>
                    </button>
                  </div>
                </Modal.Content>
              </Modal>
            ) : null}
            {modalQuizOpen && data && vdoList && vdoList.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => quizCallback(false)}
                onOpen={() => quizCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  {vdoList[quizSelect]["quiz"].question.map((val_q, key_q) => (
                    <div className="block-quiz" key={key_q}>
                      <div className="i-title-quiz">
                        <h3>
                          <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question}
                        </h3>
                        {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                          <div className={styles.questionImageDiv}>
                            <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                          </div>
                        ):null}
                      </div>
                      {val_q.type == "choice" ? (
                        <div className="i-choose-quiz-row row">
                          {val_q.answer.map((val_ans, key_ans) => (
                            <div
                              className="col-12 col-md-6 col-choose-quiz"
                              key={key_ans}
                            >
                              <div
                                className="item-choose"
                                onClick={() => onAnswer(key_q, val_ans.id)}
                              >
                                <div className="fm-check">
                                  <input
                                    className="answer_input"
                                    type="radio"
                                    name={`answer_${key_q}`}
                                  />
                                  <div className="text">
                                    <div className="i_remark">
                                      <i className="icon-ic-circle" />
                                    </div>
                                    <p>{val_ans.answer}</p>
                                  </div>
                                  {val_ans && val_ans.image && val_ans.image!=null&&val_ans.image!=''&&val_ans.image!='null' ?(
                                    <div className={styles.answerImageDiv}>
                                      <Image alt="" layout="fill" className="answer_image" src={val_ans.image} />
                                    </div>
                                  ):null}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : val_q.type == "text" ? (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <TextArea
                                placeholder=""
                                className={
                                  "fm-control answer_textarea " +
                                  (checkMyError(val_q.id) ? "error" : "")
                                }
                                data-type="textaddress"
                                onInput={appContext.diFormPattern}
                                onChange={(event) =>
                                  onAnswer(key_q, event.target.value)
                                }
                              />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <input
                                className="answer_textarea"
                                type="file"
                                accept="image/jpeg,image/jpg,image/png,image/gif,image/xbm,image/xpm,image/wbmp,image/webp,image/bmp"
                                onChange={(e) => {
                                  previewFile(key_q, e);
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  <div className="block-to-buy-course">
                    <button
                      className="btn-to-buy-course left"
                      onClick={() => onSubmit(quizSelect)}
                    >
                      <span>{translateEng('ส่งคำตอบ')}</span>
                    </button>
                  </div>
                </Modal.Content>
              </Modal>
            ) : null}
            {modalAssessment && data && assessmentData && assessmentData.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => assessmentCallback(false)}
                onOpen={() => assessmentCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  <div className="block-quiz">
                    <div className="i-title-quiz">
                      <h2>
                        {translateEng('แบบประเมินความพึงพอใจ (ใช้เวลาไม่ถึง 1 นาที)')}
                      </h2>
                    </div>
                  </div>
                  {assessmentData.map((val_q, key_q) => (
                    key_q==0 ? (
                      assessmentStep == 1 ?(
                        <div className={`block-quiz assessment`} key={key_q}>
                          <div className="i-title-quiz">
                            <h3>
                              <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                            </h3>
                          </div>
                          {val_q.type == 1 ? (
                            <div className="i-choose-quiz-row row">
                              {val_q.choice.map((val_ans, key_ans) => (
                                <div
                                  className="col-12 col-md-6 col-choose-quiz"
                                  key={key_ans}
                                >
                                  <div
                                    className="item-choose"
                                    onClick={() => onChoice(key_q, val_ans.id)}
                                  >
                                    <div className="fm-check">
                                      <input
                                        className="choice_input"
                                        type="radio"
                                        name={`choice_${key_q}`}
                                      />
                                      <div className="text">
                                        <div className="i_remark">
                                          <i className="icon-ic-circle" />
                                        </div>
                                        <p>{val_ans.choice_th}</p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            val_q.type == 3 ? (
                              <div className="i-choose-quiz-row row">
                                {val_q.choice.map((val_ans, key_ans) => (
                                  <div
                                    className="col-4 col-md-2 col-choose-quiz"
                                    key={key_ans}
                                  >
                                    <div
                                      className="item-choose"
                                      onClick={() => onChoice(key_q, val_ans.id)}
                                    >
                                      <div className="fm-check">
                                        <input
                                          className="choice_input"
                                          type="radio"
                                          name={`choice_${key_q}`}
                                        />
                                        <div className="text with-icon">
                                          {val_ans && val_ans.icon && val_ans.icon!=null && val_ans.icon!='' && val_ans.icon!='null' ? (
                                            <div className={styles.assessmentIconDiv}>
                                              <Image alt="" layout="intrinsic" className={`${styles.iconThumb}`} src={val_ans.icon} width={500} height={500} />
                                            </div>
                                          ):null}
                                          <div className="i_remark">
                                            <i className="icon-ic-circle" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="i-choose-quiz-row row">
                                <div className="col-12 col-md-12 col-choose-quiz">
                                  <div className="item-fm">
                                    <TextArea
                                      placeholder=""
                                      className={
                                        "fm-control choice_textarea " +
                                        (checkMyError(val_q.id) ? "error" : "")
                                      }
                                      data-type="textaddress"
                                      onInput={appContext.diFormPattern}
                                      onChange={(event) =>
                                        onChoice(key_q, event.target.value)
                                      }
                                    />
                                  </div>
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      ):null
                    ):(
                      assessmentStep == 2 ?(
                        <div className={`block-quiz assessment`} key={key_q}>
                          <div className="i-title-quiz">
                            <h3>
                              <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                            </h3>
                          </div>
                          {val_q.type == 1 ? (
                            <div className="i-choose-quiz-row row">
                              {val_q.choice.map((val_ans, key_ans) => (
                                <div
                                  className="col-12 col-md-6 col-choose-quiz"
                                  key={key_ans}
                                >
                                  <div
                                    className="item-choose"
                                    onClick={() => onChoice(key_q, val_ans.id)}
                                  >
                                    <div className="fm-check">
                                      <input
                                        className="choice_input"
                                        type="radio"
                                        name={`choice_${key_q}`}
                                      />
                                      <div className="text">
                                        <div className="i_remark">
                                          <i className="icon-ic-circle" />
                                        </div>
                                        <p>{val_ans.choice_th}</p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            val_q.type == 3 ? (
                              <div className="i-choose-quiz-row row">
                                {val_q.choice.map((val_ans, key_ans) => (
                                  <div
                                    className="col-4 col-md-2 col-choose-quiz"
                                    key={key_ans}
                                  >
                                    <div
                                      className="item-choose"
                                      onClick={() => onChoice(key_q, val_ans.id)}
                                    >
                                      <div className="fm-check">
                                        <input
                                          className="choice_input"
                                          type="radio"
                                          name={`choice_${key_q}`}
                                        />
                                        <div className="text with-icon">
                                          {val_ans && val_ans.icon && val_ans.icon!=null && val_ans.icon!='' && val_ans.icon!='null' ?(
                                            <div className={styles.assessmentIconDiv}>
                                              <Image alt="" layout="intrinsic" className={`${styles.iconThumb}`} src={val_ans.icon} width={500} height={500} />
                                            </div>
                                          ):null}
                                          <div className="i_remark">
                                            <i className="icon-ic-circle" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="i-choose-quiz-row row">
                                <div className="col-12 col-md-12 col-choose-quiz">
                                  <div className="item-fm">
                                    <TextArea
                                      placeholder=""
                                      className={
                                        "fm-control choice_textarea " +
                                        (checkMyError(val_q.id) ? "error" : "")
                                      }
                                      data-type="textaddress"
                                      onInput={appContext.diFormPattern}
                                      onChange={(event) =>
                                        onChoice(key_q, event.target.value)
                                      }
                                    />
                                  </div>
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      ):null
                    )
                  ))}
                  {assessmentStep == 1 ?(
                    <div className="block-to-buy-course assessment-group-btn">
                      <button
                        className="btn-to-buy-course left"
                        onClick={() => assessmentCallback(false)}
                      >
                        <span>{translateEng('ข้าม')}</span>
                      </button>
                      <button
                        className="btn-to-buy-course right"
                        onClick={() => onAssessmentNext()}
                      >
                        <span>{translateEng('ต่อไป')}</span>
                      </button>
                    </div>
                  ):(
                    <div className="block-to-buy-course">
                      <button
                        className="btn-to-buy-course left"
                        onClick={() => onAssessment()}
                      >
                        <span>{translateEng('ส่งคำตอบ')}</span>
                      </button>
                    </div>
                  )}
                </Modal.Content>
              </Modal>
            ) : null}
            {modalCert ? (
              <Modal
                className={`modal-unlock-cert`}
                onClose={() => certCallback(false)}
                onOpen={() => certCallback(true)}
                open={true}
              >
                <Modal.Content className={`inner`}>
                  <div className="block-unlock-cert">
                    <div className="left">
                      <div className={styles.unlockCertIconDiv}>
                        <Image alt="" layout="intrinsic" src="/assets/images/unlock_cert.png" width={500} height={500} />
                      </div>
                    </div>
                    <div className="right">
                      <h3>{translateEng('ยินดีด้วย!')}</h3>
                      <h2>{translateEng('คุณปลดล็อกใบประกาศ')}</h2>
                      <p>{translateEng('คอร์ส')} {data["data"]["title"]}</p>
                      <div className="curve">
                        <span>{translateEng('สามารถดาวน์โหลดใบประกาศได้ที่โปรไฟล์ของท่าน')}</span>
                      </div>
                      <div className="action">
                        <Link href="/profile/certificate">
                          <Button className="green-button">
                            <span>{translateEng('หรือคลิกที่นี่')}</span>
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </Modal.Content>
              </Modal>
            ) : null}
            <Modal
              className="modalCreateList"
              onClose={() => setAddPlaylist(false)}
              onOpen={() => setAddPlaylist(true)}
              open={addPlaylist}
            >
              <Modal.Content className="modalCreateListContent">
                <div className="block-modal-CreateList">
                  <div className="inner">
                    <h3>{translateEng('เพิ่มไปยังเพลย์ลิสต์ของคุณ')}</h3>
                  </div>
                  <div className="fm-CreateList-select">
                    {playListOptions.length > 0 ? (
                      <div className="item-fm">
                        <Select
                          className="fm-control"
                          placeholder={translateEng('เพลย์ลิสต์ของคุณ')}
                          options={playListOptions}
                          onChange={(event, data) => {
                            setPlaylistSelect(data.value);
                          }}
                        />
                      </div>
                    ) : null}
                    <button
                      className="btn-add-list"
                      onClick={() => show_add_list()}
                    >
                      <span>{translateEng('สร้างเพลย์ลิสต์')}</span>
                    </button>
                  </div>
                  <div id="fmCreateListAdd" className="fm-CreateList-add">
                    <div className="item-fm">
                      <Input
                        id="create_list"
                        type="text"
                        className="fm-control"
                        placeholder={translateEng('ป้อนชื่อเพลย์ลิสต์')}
                        onChange={(event) => setPlaylistTitle(event.target.value)}
                      ></Input>
                    </div>
                  </div>
                  <div className="fm-CreateList-action">
                    <button
                      className="btn-add-list"
                      onClick={() => submitPlaylist()}
                    >
                      <span>ตกลง</span>
                    </button>
                  </div>
                </div>
              </Modal.Content>
            </Modal>
            <Footer></Footer>
            {/* update */}
            {coursePriceReload &&
            data &&
            data["data"] &&
            data["data"]["is_soon"] == false ? (
              data.data.allowed ||
              data.data.order_status == 4 ||
              data.data.order_status == 1 ? null : 
              data.data.is_subscription ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-default">Member</div>
                  </div>
                </div>
              ) :
              data.data.is_internal || data.data.is_volume || data.data.expire_date ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-default">{translateEng('ฟรี')}</div>
                  </div>
                </div>
              ) : data.data.is_promotion == 1 ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-sell">
                      {translateEng('ปกติ')}
                      <NumberFormat
                        value={data.data.price}
                        displayType={"text"}
                        thousandSeparator={true}
                        renderText={(value, props) => (
                          <span {...props}>{value}</span>
                        )}
                      />
                    </div>
                    {data.data.pro_price == 0 ? (
                      <div className="info-price-default">{translateEng('ฟรี')}</div>
                    ) : (
                      <div className="info-price-default">
                        {translateEng('ราคา')}
                        <NumberFormat
                          value={data.data.pro_price}
                          displayType={"text"}
                          thousandSeparator={true}
                          renderText={(value, props) => (
                            <span {...props}>{value}</span>
                          )}
                        />
                        {translateEng('บาท')}
                      </div>
                    )}
                  </div>
                  <div className="action-price">
                    {data && data["data"] && data.data.is_standalone == 1 && data["data"]["is_soon"] == false ? (
                      (data.data.price == 0 ||
                      (data.data.is_promotion == 1 && data.data.pro_price == 0) ||
                      data.data.is_internal||
                      data.data.is_subscription||
                      data.data.is_volume || 
                      data.data.expire_date)&&discountCode=='' ? (
                        data.data.is_subscription ? (
                          <button
                            className="btn-action-price"
                            onClick={() =>
                              groupCategoryCallback("vip", data["data"]["id"], "")
                            }
                          >
                            <span>{translateEng('เริ่มเรียน')}</span>
                          </button>
                        ):(
                          data.data.is_volume ? (
                            <button
                              className="btn-action-price"
                              onClick={() =>
                                groupCategoryCallback("volume", data["data"]["id"], "")
                              }
                            >
                              <span>{translateEng('เริ่มเรียน')}</span>
                            </button>
                          ):(
                            data.data.is_internal&&data.data.user_internal ? (
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback("internal", data["data"]["id"], "")
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            ):(
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback("free", data["data"]["id"], "")
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            )
                          )
                        )
                        
                      ) : (
                        <button
                          className="btn-action-price"
                          onClick={() =>
                            groupCategoryCallback("cart", data["data"]["id"], "")
                          }
                        >
                          <span>{translateEng('ซื้อคอร์สนี้')}</span>
                        </button>
                      )
                    ) : null}
                  </div>
                </div>
              ) : (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    {data.data.price == 0 ? (
                      <div className="info-price-default">{translateEng('ฟรี')}</div>
                    ) : (
                      <div className="info-price-default">
                        {translateEng('ราคา')}
                        <NumberFormat
                          value={data.data.price}
                          displayType={"text"}
                          thousandSeparator={true}
                          renderText={(value, props) => (
                            <span {...props}>{value}</span>
                          )}
                        />
                        {translateEng('บาท')}
                      </div>
                    )}
                  </div>
                  <div className="action-price">
                    {data && data["data"] && data.data.is_standalone == 1 && data["data"]["is_soon"] == false ? (
                      (data.data.price == 0 ||
                      (data.data.is_promotion == 1 && data.data.pro_price == 0) ||
                      data.data.is_internal||
                      data.data.is_subscription||
                      data.data.is_volume)&&discountCode=='' ? (
                        data.data.is_subscription ? (
                          <button
                            className="btn-action-price"
                            onClick={() =>
                              groupCategoryCallback("vip", data["data"]["id"], "")
                            }
                          >
                            <span>{translateEng('เริ่มเรียน')}</span>
                          </button>
                        ):(
                          data.data.is_volume ? (
                            <button
                              className="btn-action-price"
                              onClick={() =>
                                groupCategoryCallback("volume", data["data"]["id"], "")
                              }
                            >
                              <span>{translateEng('เริ่มเรียน')}</span>
                            </button>
                          ):(
                            data.data.is_internal&&data.data.user_internal ? (
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback("internal", data["data"]["id"], "")
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            ):(
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback("free", data["data"]["id"], "")
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            )
                          )
                        )
                        
                      ) : (
                        <button
                          className="btn-action-price"
                          onClick={() =>
                            groupCategoryCallback("cart", data["data"]["id"], "")
                          }
                        >
                          <span>{translateEng('ซื้อคอร์สนี้')}</span>
                        </button>
                      )
                    ) : null}
                  </div>
                </div>
              )
            ) : null}
            {/* update */}
          </div>
        </div>
        <Modal
          className='modalCreateList'
          onClose={() => setModalOculus(false)}
          onOpen={() => setModalOculus(true)}
          open={modalOculus}>
          <Modal.Content className="modalCreateListContent">
            <div className="block-modal-CreateList">
                <div className="oculus-pin">
                  <h3>Pin ของคุณคือ</h3>
                  <h2>{oculusPin}</h2>
                  <p>Pin จะมีอายุการใช้งาน 10 นาที</p>
                </div>
                <div className='fm-CreateList-action'>
                  <button className='btn-add-list' onClick={() => setModalOculus(false)}>
                    <span>ปิด</span>
                  </button>
                </div>
            </div>
          </Modal.Content>
        </Modal>
      </>
    );
  } else if (data["data"]["type"] == "ticket") {
    return (
      <>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />

        <div className="main-all page-course">
          <Head>
            {seo_data.seo ? (
              seo_data.seo.map((val, key) =>
                val.name == "title" ? (
                  <>
                    <title>{val.content}</title>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                ) : (
                  <>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                )
              )
            ) : (
              <>
                <title>MDCU : MedU MORE</title>
              </>
            )}

            <meta
              key={"twitter:card"}
              name={"twitter:card"}
              content="summary_large_image"
            />
            <meta key={"og:type"} name={"og:type"} content="website" />
          </Head>

          <Header></Header>

          <div className="main-body">
            <div className="fix-space"></div>
            <CourseDetailIntro
              type="ticket"
              data={data["data"]}
              ep={data["data"]["learned_ep"]}
              callback={selectEp}
              addCart={groupCategoryCallback}
            ></CourseDetailIntro>

            <div className="container-fluid bg-white course-page">
              <div className={`container  space-between-content`}>
                <div className="row intro_course-page">
                  <div className="col-12 col-md-9 col-xl-8">
                    {reloadStar ? (
                      <CourseDetailDescription
                        data={data["data"]}
                        callback={courseDescription}
                        lang={lang}
                      ></CourseDetailDescription>
                    ) : null}
                    {data.data.allowed ||
                    data.data.order_status == 4 ||
                    data.data.order_status == 1 ? null : data.data
                        .is_promotion == 1 ? (
                      data.data.pro_price == 0 && discountCode=='' ? null : (
                        <div className="row discount-support">
                          <div className="col-12 col-md-6">
                            <p
                              className={`space-between-content-top ${styles.titleButtonGroup}`}
                            >
                              {translateEng('โค้ดส่วนลด')}
                            </p>
                            <CourseDetailCode
                              callback={addDiscountCode}
                              lang={lang}
                            ></CourseDetailCode>
                          </div>

                          {data &&
                          data["scholarshipList"] &&
                          data["scholarshipList"].length > 0 ? (
                            <div className="col-12 col-md-6">
                              <p className="space-between-content-top title-button-group">
                                {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                              </p>
                              <CourseDetailScholarship
                                callback={addDiscountCode}
                                name="courseScholarship"
                                data={data["scholarshipList"]}
                              ></CourseDetailScholarship>
                            </div>
                          ) : null}
                        </div>
                      )
                    ) : data.data.price == 0 && discountCode=='' ? null : (
                      <div className="row discount-support">
                        <div className="col-12 col-md-6">
                          <p
                            className={`space-between-content-top ${styles.titleButtonGroup}`}
                          >
                            {translateEng('โค้ดส่วนลด')}
                          </p>
                          <CourseDetailCode
                            callback={addDiscountCode}
                            lang={lang}
                          ></CourseDetailCode>
                        </div>

                        {data &&
                        data["scholarshipList"] &&
                        data["scholarshipList"].length > 0 ? (
                          <div className="col-12 col-md-6">
                            <p className="space-between-content-top title-button-group">
                              {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                            </p>
                            <CourseDetailScholarship
                              callback={addDiscountCode}
                              name="courseScholarship"
                              data={data["scholarshipList"]}
                            ></CourseDetailScholarship>
                          </div>
                        ) : null}
                      </div>
                    )}
                  </div>
                  <div className={`col-12 col-md-3 col-xl-4`}>
                    <div className="box-price-to-buy" ref={spaceMove}>
                      {coursePriceReload &&
                      data &&
                      data["data"] &&
                      data["data"]["is_soon"] == false ? (
                        data.data.allowed ||
                        data.data.order_status == 4 ||
                        data.data.order_status == 1 ? null :
                        data.data.is_subscription ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-big-price">Member</div>
                          </div>
                        ) : 
                        data.data.is_internal || data.data.is_volume ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-big-price">{translateEng('ฟรี')}</div>
                          </div>
                        ) : data.data.is_promotion == 1 ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-sale">
                              <NumberFormat
                                value={data.data.price}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <span {...props}>{translateEng('ปกติ')} {value}</span>
                                )}
                              />
                            </div>
                            {data.data.pro_price == 0 ? (
                              <div className="description-big-price">{translateEng('ฟรี')}</div>
                            ) : (
                              <div className="description-big-price">
                                {translateEng('ราคา')}
                                <NumberFormat
                                  value={data.data.pro_price}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                  renderText={(value, props) => (
                                    <span {...props}>{value}</span>
                                  )}
                                />
                                {translateEng('บาท')}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="block-course-detail-price text-center">
                            {data.data.price == 0 ? (
                              <div className="description-big-price">{translateEng('ฟรี')}</div>
                            ) : (
                              <div className="description-big-price">
                                {translateEng('ราคา')}
                                <NumberFormat
                                  value={data.data.price}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                  renderText={(value, props) => (
                                    <span {...props}>{value}</span>
                                  )}
                                />
                                {translateEng('บาท')}
                              </div>
                            )}
                          </div>
                        )
                      ) : null}
                      {data["data"]["allowed"] ||
                      data.data.order_status == 4 ||
                      data.data.order_status == 1 ||
                      (data &&
                        data["data"] &&
                        data["data"]["is_soon"] == true) ? null : (
                        <div className="block-to-buy-course">
                          {(data.data.price == 0 ||
                          (data.data.is_promotion == 1 &&
                            data.data.pro_price == 0) ||
                          data.data.is_internal||
                          data.data.is_subscription||
                          data.data.is_volume)&&discountCode=='' ? (
                            data.data.is_subscription ? (
                              <button
                                className="btn-to-buy-course"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "vip",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('รับฟรี')}</span>
                              </button>
                            ):(
                              data.data.is_volume ? (
                                <button
                                  className="btn-to-buy-course"
                                  onClick={() =>
                                    groupCategoryCallback(
                                      "volume",
                                      data["data"]["id"],
                                      ""
                                    )
                                  }
                                >
                                  <span>{translateEng('รับฟรี')}</span>
                                </button>
                              ):(
                                data.data.is_internal&&data.data.user_internal ? (
                                  <button
                                    className="btn-to-buy-course"
                                    onClick={() =>
                                      groupCategoryCallback(
                                        "internal",
                                        data["data"]["id"],
                                        ""
                                      )
                                    }
                                  >
                                    <span>{translateEng('รับฟรี')}</span>
                                  </button>
                                ):(
                                  <button
                                    className="btn-to-buy-course"
                                    onClick={() =>
                                      groupCategoryCallback(
                                        "free",
                                        data["data"]["id"],
                                        ""
                                      )
                                    }
                                  >
                                    <span>{translateEng('รับฟรี')}</span>
                                  </button>
                                )
                              )
                            )
                          ) : (
                            <button
                              className="btn-to-buy-course"
                              onClick={() =>
                                groupCategoryCallback(
                                  "cart",
                                  data["data"]["id"],
                                  ""
                                )
                              }
                            >
                              <span>{translateEng('ซื้อ Ticket')}</span>
                            </button>
                          )}
                        </div>
                      )}

                      {data["data"]["is_soon"] == true ? (
                        <div className="block-course-detail-price text-center">
                          <div className="description-big-price">
                            Coming soon
                          </div>
                        </div>
                      ) : null}

                      <div className="space-fix-price"></div>
                    </div>
                    {/* <iframe src="https://example.com/meetingsdk" allow="camera; microphone"></iframe> */}
                    {/* <div className="block-live-chat">
                      <div className="live-chat">
                        <div className="chat-top">
                          <h3>
                            Top Chat
                          </h3>
                          <button className="btn_show_chat">
                            <i className="icon-ic-down"></i>
                          </button>
                          <button className="btn_more">
                            <i className="icon-ic-dot-nav"></i>
                          </button>
                        </div>
                        <div className="chat-center">
                          <div className="item">
                            <div className="profile">
                              <div className="avatar">

                              </div>
                              <div className="">
                                
                              </div>
                            </div>

                          </div>
                        </div>
                        <div className="chat-bottom">

                        </div>
                      </div>
                    </div> */}
                  </div>
                </div>
                {/* <div className="row">
                  <div className="live">
                    <h3>Live</h3>
                  </div>
                </div> */}
                <div className="row">
                  <div className="col-12 course-page">
                    <div>
                      {vdoList && vdoList.length > 0 ? (
                        <Tab
                          panes={panes}
                          renderActiveOnly={false}
                          className={`space-between-content-top courseTab`}
                        />
                      ) : null}
                    </div>
                  </div>
                   <div className="col-12">
                     <CurriculumBackButton />
                  </div>
                  {/* ==================== [ update ] ==================== */}
                  <div className="col-12 course-page">
                    <div className="block-quick-result">
                      <Tab
                        panes={quickResult}
                        className={`space-between-content-top courseTab pd-0`}
                      />
                    </div>
                  </div>
                  {data.data && data.data.tag.length > 0 ? (
                    <div className="col-12 course-page">
                      <div className="block-quick-result">
                        <ListTags data={data.data.tag}></ListTags>
                      </div>
                    </div>
                  ) : null}
                  {/* ==================== [ update ] ==================== */}
                </div>
              </div>
            </div>
            <Footer></Footer>

            {/* update */}
            {coursePriceReload &&
            data &&
            data["data"] &&
            data["data"]["is_soon"] == false ? (
              data.data.allowed ||
              data.data.order_status == 4 ||
              data.data.order_status == 1 ? null : 
              data.data.is_subscription ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-default">Member</div>
                  </div>
                </div>
              ) :
              data.data.is_internal || data.data.is_volume ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-default">{translateEng('ฟรี')}</div>
                  </div>
                </div>
              ) : data.data.is_promotion == 1 ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-sell">
                      {translateEng('ปกติ')}
                      <NumberFormat
                        value={data.data.price}
                        displayType={"text"}
                        thousandSeparator={true}
                        renderText={(value, props) => (
                          <span {...props}>{value}</span>
                        )}
                      />
                    </div>
                    {data.data.pro_price == 0 ? (
                      <div className="info-price-default">{translateEng('ฟรี')}</div>
                    ) : (
                      <div className="info-price-default">
                        {translateEng('ราคา')}
                        <NumberFormat
                          value={data.data.pro_price}
                          displayType={"text"}
                          thousandSeparator={true}
                          renderText={(value, props) => (
                            <span {...props}>{value}</span>
                          )}
                        />
                        {translateEng('บาท')}
                      </div>
                    )}
                  </div>
                  <div className="action-price">
                    {data &&
                    data["data"] &&
                    data["data"]["is_soon"] == false ? (
                      (data.data.price == 0 ||
                      (data.data.is_promotion == 1 &&
                        data.data.pro_price == 0) ||
                      data.data.is_internal||
                      data.data.is_subscription||
                      data.data.is_volume)&&discountCode=='' ? (
                        data.data.is_subscription ? (
                          <button
                            className="btn-action-price"
                            onClick={() =>
                              groupCategoryCallback(
                                "vip",
                                data["data"]["id"],
                                ""
                              )
                            }
                          >
                            <span>{translateEng('รับฟรี')}</span>
                          </button>
                        ):(
                          data.data.is_volume ? (
                            <button
                              className="btn-action-price"
                              onClick={() =>
                                groupCategoryCallback(
                                  "volume",
                                  data["data"]["id"],
                                  ""
                                )
                              }
                            >
                              <span>{translateEng('รับฟรี')}</span>
                            </button>
                          ):(
                            data.data.is_internal&&data.data.user_internal ? (
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "internal",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('รับฟรี')}</span>
                              </button>
                            ):(
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "free",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('รับฟรี')}</span>
                              </button>
                            )
                          )
                        )
                      ) : (
                        <button
                          className="btn-action-price"
                          onClick={() =>
                            groupCategoryCallback(
                              "cart",
                              data["data"]["id"],
                              ""
                            )
                          }
                        >
                          <span>{translateEng('ซื้อ Ticket')}</span>
                        </button>
                      )
                    ) : null}
                  </div>
                </div>
              ) : (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    {data.data.price == 0 || data.data.expire_date ? (
                      <div className="info-price-default">{translateEng('ฟรี')}</div>
                    ) : (
                      <div className="info-price-default">
                        {translateEng('ราคา')}
                        <NumberFormat
                          value={data.data.price}
                          displayType={"text"}
                          thousandSeparator={true}
                          renderText={(value, props) => (
                            <span {...props}>{value}</span>
                          )}
                        />
                        {translateEng('บาท')}
                      </div>
                    )}
                  </div>
                  <div className="action-price">
                    {data &&
                    data["data"] &&
                    data["data"]["is_soon"] == false ? (
                      (data.data.price == 0 ||
                      (data.data.is_promotion == 1 &&
                        data.data.pro_price == 0) ||
                      data.data.is_internal||
                      data.data.is_subscription||
                      data.data.is_volume || 
                      data.data.expire_date)&&discountCode=='' ? (
                        data.data.is_subscription ? (
                          <button
                            className="btn-action-price"
                            onClick={() =>
                              groupCategoryCallback(
                                "vip",
                                data["data"]["id"],
                                ""
                              )
                            }
                          >
                            <span>{translateEng('รับฟรี')}</span>
                          </button>
                        ):(
                          data.data.is_volume ? (
                            <button
                              className="btn-action-price"
                              onClick={() =>
                                groupCategoryCallback(
                                  "volume",
                                  data["data"]["id"],
                                  ""
                                )
                              }
                            >
                              <span>{translateEng('รับฟรี')}</span>
                            </button>
                          ):(
                            data.data.is_internal&&data.data.user_internal ? (
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "internal",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('รับฟรี')}</span>
                              </button>
                            ):(
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "free",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('รับฟรี')}</span>
                              </button>
                            )
                          )
                        )
                      ) : (
                        <button
                          className="btn-action-price"
                          onClick={() =>
                            groupCategoryCallback(
                              "cart",
                              data["data"]["id"],
                              ""
                            )
                          }
                        >
                          <span>{translateEng('ซื้อ Ticket')}</span>
                        </button>
                      )
                    ) : null}
                  </div>
                </div>
              )
            ) : null}
            {/* update */}
          </div>
        </div>
      </>
    );
  } else {
    return (
      <>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />

        <div className="main-all page-course">
          <Head>
            {seo_data.seo ? (
              seo_data.seo.map((val, key) =>
                val.name == "title" ? (
                  <>
                    <title>{val.content}</title>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                ) : (
                  <>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                )
              )
            ) : (
              <>
                <title>MDCU : MedU MORE</title>
              </>
            )}

            <meta
              key={"twitter:card"}
              name={"twitter:card"}
              content="summary_large_image"
            />
            <meta key={"og:type"} name={"og:type"} content="website" />
          </Head>

          <Header></Header>

          <div className="main-body">
            <div className="fix-space"></div>
            <CourseDetailIntro
              type="normal"
              data={data["data"]}
              ep={data["data"]["learned_ep"]}
              callback={selectEp}
              addCart={groupCategoryCallback}
            ></CourseDetailIntro>

            <div className="container-fluid bg-white course-page">
              <div className={`container  space-between-content`}>
                <div className="row intro_course-page">
                  <div className="col-12 col-md-9 col-xl-8">
                    {reloadStar ? (
                      <CourseDetailDescription
                        data={data["data"]}
                        callback={courseDescription}
                        lang={lang}
                      ></CourseDetailDescription>
                    ) : null}
                    {data.data.allowed ||
                    data.data.order_status == 4 ||
                    data.data.order_status == 1 ? null : data.data
                        .is_promotion == 1 ? (
                      data.data.pro_price == 0 && discountCode=='' ? null : (
                        <div className="row discount-support">
                          <div className="col-12 col-md-6">
                            <p
                              className={`space-between-content-top ${styles.titleButtonGroup}`}
                            >
                              {translateEng('โค้ดส่วนลด')}
                            </p>
                            <CourseDetailCode
                              callback={addDiscountCode}
                              lang={lang}
                            ></CourseDetailCode>
                          </div>

                          {data &&
                          data["scholarshipList"] &&
                          data["scholarshipList"].length > 0 ? (
                            <div className="col-12 col-md-6">
                              <p className="space-between-content-top title-button-group">
                                {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                              </p>
                              <CourseDetailScholarship
                                callback={addDiscountCode}
                                name="courseScholarship"
                                data={data["scholarshipList"]}
                              ></CourseDetailScholarship>
                            </div>
                          ) : null}
                        </div>
                      )
                    ) : data.data.price == 0 && discountCode=='' ? null : (
                      <div className="row discount-support">
                        <div className="col-12 col-md-6">
                          <p
                            className={`space-between-content-top ${styles.titleButtonGroup}`}
                          >
                            {translateEng('โค้ดส่วนลด')}
                          </p>
                          <CourseDetailCode
                            callback={addDiscountCode}
                            lang={lang}
                          ></CourseDetailCode>
                        </div>

                        {data &&
                        data["scholarshipList"] &&
                        data["scholarshipList"].length > 0 ? (
                          <div className="col-12 col-md-6">
                            <p className="space-between-content-top title-button-group">
                              {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                            </p>
                            <CourseDetailScholarship
                              callback={addDiscountCode}
                              name="courseScholarship"
                              data={data["scholarshipList"]}
                            ></CourseDetailScholarship>
                          </div>
                        ) : null}
                      </div>
                    )}
                  </div>
                  <div className={`col-12 col-md-3 col-xl-4`}>
                    <div className="box-price-to-buy" ref={spaceMove}>
                      {coursePriceReload &&
                      data &&
                      data["data"] &&
                      data["data"]["is_soon"] == false ? (
                        data.data.allowed ||
                        data.data.order_status == 4 ||
                        data.data.order_status == 1 ||
                        data.data.is_standalone === 0 ? null :
                        data.data.is_subscription ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-big-price">Member</div>
                          </div>
                        ) : 
                        data.data.is_internal || data.data.is_volume || data.data.expire_date ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-big-price">{translateEng('ฟรี')}</div>
                          </div>
                        ) : data.data.is_promotion == 1 ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-sale">
                              <NumberFormat
                                value={data.data.price}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <span {...props}>{translateEng('ปกติ')} {value}</span>
                                )}
                              />
                            </div>
                            {data.data.pro_price == 0 ? (
                              <div className="description-big-price">{translateEng('ฟรี')}</div>
                            ) : (
                              <div className="description-big-price">
                                {translateEng('ราคา')}
                                <NumberFormat
                                  value={data.data.pro_price}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                  renderText={(value, props) => (
                                    <span {...props}>{value}</span>
                                  )}
                                />
                                {translateEng('บาท')}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="block-course-detail-price text-center">
                            {data.data.price == 0 ? (
                              <div className="description-big-price">{translateEng('ฟรี')}</div>
                            ) : (
                              <div className="description-big-price">
                                {translateEng('ราคา')}
                                <NumberFormat
                                  value={data.data.price}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                  renderText={(value, props) => (
                                    <span {...props}>{value}</span>
                                  )}
                                />
                                {translateEng('บาท')}
                              </div>
                            )}
                          </div>
                        )
                      ) : null}
                      {data["data"]["allowed"] ||
                      data.data.order_status == 4 ||
                      data.data.order_status == 1 ||
                      ((data &&
                        data["data"] &&
                        data["data"]["is_soon"] == true) || data.data.is_standalone == 0) ? null : (
                        <div className="block-to-buy-course">
                          {(data.data.price == 0 ||
                          (data.data.is_promotion == 1 &&
                            data.data.pro_price == 0) ||
                          data.data.is_internal||
                          data.data.is_subscription||
                          data.data.is_volume || 
                          data.data.expire_date)&&discountCode=='' ? (
                            data.data.is_subscription ? (
                              <button
                                className="btn-to-buy-course"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "vip",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            ):(
                              data.data.is_volume ? (
                                <button
                                  className="btn-to-buy-course"
                                  onClick={() =>
                                    groupCategoryCallback(
                                      "volume",
                                      data["data"]["id"],
                                      ""
                                    )
                                  }
                                >
                                  <span>{translateEng('เริ่มเรียน')}</span>
                                </button>
                              ):(
                                data.data.is_internal&&data.data.user_internal ? (
                                  <button
                                    className="btn-to-buy-course"
                                    onClick={() =>
                                      groupCategoryCallback(
                                        "internal",
                                        data["data"]["id"],
                                        ""
                                      )
                                    }
                                  >
                                    <span>{translateEng('เริ่มเรียน')}</span>
                                  </button>
                                ):(
                                  <button
                                    className="btn-to-buy-course"
                                    onClick={() =>
                                      groupCategoryCallback(
                                        "free",
                                        data["data"]["id"],
                                        ""
                                      )
                                    }
                                  >
                                    <span>{translateEng('เริ่มเรียน')}</span>
                                  </button>
                                )
                              )
                            )
                          ) : (
                            <button
                              className="btn-to-buy-course"
                              onClick={() =>
                                groupCategoryCallback(
                                  "cart",
                                  data["data"]["id"],
                                  ""
                                )
                              }
                            >
                              <span>{translateEng('ซื้อคอร์สนี้')}</span>
                            </button>
                          )}
                        </div>
                      )}

                      {data["data"]["is_soon"] == true ? (
                        <div className="block-course-detail-price text-center">
                          <div className="description-big-price">
                            Coming soon
                          </div>
                        </div>
                      ) : null}

                      <div className="space-fix-price"></div>
                    </div>
                    {/* <iframe src="https://example.com/meetingsdk" allow="camera; microphone"></iframe> */}
                    {/* <div className="block-live-chat">
                      <div className="live-chat">
                        <div className="chat-top">
                          <h3>
                            Top Chat
                          </h3>
                          <button className="btn_show_chat">
                            <i className="icon-ic-down"></i>
                          </button>
                          <button className="btn_more">
                            <i className="icon-ic-dot-nav"></i>
                          </button>
                        </div>
                        <div className="chat-center">
                          <div className="item">
                            <div className="profile">
                              <div className="avatar">

                              </div>
                              <div className="">
                                
                              </div>
                            </div>

                          </div>
                        </div>
                        <div className="chat-bottom">

                        </div>
                      </div>
                    </div> */}
                  </div>
                </div>
                {/* <div className="row">
                  <div className="live">
                    <h3>Live</h3>
                  </div>
                </div> */}
                <div className="row">
                  <div className="col-12 course-page">
                    <div>
                      {vdoList && vdoList.length > 0 ? (
                        <Tab
                          panes={panes}
                          renderActiveOnly={false}
                          className={`space-between-content-top courseTab`}
                        />
                      ) : null}
                    </div>
                  </div>
                   <div className="col-12">
                     <CurriculumBackButton />
                  </div>
                  {/* ==================== [ update ] ==================== */}
                  <div className="col-12 course-page">
                    <div className="block-quick-result">
                      <Tab
                        panes={quickResult}
                        className={`space-between-content-top courseTab pd-0`}
                      />
                    </div>
                  </div>
                  {data.data && data.data.tag.length > 0 ? (
                    <div className="col-12 course-page">
                      <div className="block-quick-result">
                        <ListTags data={data.data.tag}></ListTags>
                      </div>
                    </div>
                  ) : null}
                  {/* ==================== [ update ] ==================== */}

                 
                </div>
              </div>
            </div>

            {relateData&&relateData.length>0 ? (
              <GroupCategory
                type="normal"
                name={translateEng('คอร์สเกี่ยวข้อง')}
                bg={''}
                color="#6E953D"
                size={3.5}
                isLoop={false}
                index={20}
                data={relateData}
                callback={groupCategoryCallback}
              ></GroupCategory>
            ) : null}
            {modalVdoOpen && data && vdoList && vdoList.length > 0 ? (
              <ListVdoModal
                genOculus={genOculus}
                cbProgress={courseProgress}
                cbProgressEnd={progressEnd}
                callback={playerCallback}
                skipVdo={skipVdo}
                keySelect={keySelect}
                vdoList={vdoList}
                slug={params.id}
              ></ListVdoModal>
            ) : null}
            {modalResult && resultData &&  resultData.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => resultCallback(false)}
                onOpen={() => resultCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  {resultData.map((val_q, key_q) => (
                    <div className="block-quiz-border" key={key_q}>
                      <div className="block-quiz">
                        <div className="i-title-quiz">
                          <h3>
                            <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                          </h3>
                          {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                            <div className={styles.questionImageDiv}>
                              <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                            </div>
                          ):null}
                        </div>
                      </div>
                      <div className="block-quiz">
                        <div className="i-title-quiz">
                          {val_q.exam_type==1 ? (
                            val_q.is_answer==1 ? (
                              <>
                                <h3 className="result">
                                  <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title}
                                </h3>
                                {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                                  <div className={styles.answerImageResultDiv}>
                                    <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                                  </div>
                                ):null}
                              </>
                            ):(
                              val_q.is_right==1 ? (
                                <>
                                  <h3 className="result true">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title} <i className="icon-ic-tick"></i>
                                  </h3>
                                  {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                                    <div className={styles.answerImageResultDiv}>
                                      <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                                    </div>
                                  ):null}
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              ):(
                                <>
                                  <h3 className="result false">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_title} <i className="icon-ic-close"></i>
                                  </h3>
                                  {val_q && val_q.answer_image && val_q.answer_image!=null&&val_q.answer_image!=''&&val_q.answer_image!='null' ?(
                                    <div className={styles.answerImageResultDiv}>
                                      <Image alt="" layout="fill" className="answer_image_result" src={val_q.answer_image} />
                                    </div>
                                  ):null}
                                  <h3 className="description-true true">
                                    <span>{translateEng('คำตอบที่ถูก')} :</span> {val_q.true_answer}
                                  </h3>
                                  {val_q && val_q.true_answer_image && val_q.true_answer_image!=null&&val_q.true_answer_image!=''&&val_q.true_answer_image!='null' ?(
                                    <div className={styles.answerImageResultDiv}>
                                      <Image alt="" layout="fill" className="answer_image_result" src={val_q.true_answer_image} />
                                    </div>
                                  ):null}
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              )
                            )
                          ):(
                            val_q.exam_type==2 ? (
                              val_q.is_answer==1 ? (
                                <h3 className="result">
                                  <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_text}
                                </h3>
                              ):(
                                <>
                                  <h3 className="result">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> {val_q.answer_text}
                                  </h3>
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              )
                            ):(
                              val_q.is_answer==1 ? (
                                <h3 className="result">
                                  <span>{translateEng('คำตอบของท่าน')} :</span> <a href={val_q.file} target="_blank" rel="noopener noreferrer">{translateEng('ดูคำตอบ')}</a>
                                </h3>
                              ):(
                                <>
                                  <h3 className="result">
                                    <span>{translateEng('คำตอบของท่าน')} :</span> <a href={val_q.file} target="_blank" rel="noopener noreferrer">{translateEng('ดูคำตอบ')}</a>
                                  </h3>
                                  {val_q.answer_des!=null&&val_q.answer_des!=''&&val_q.answer_des!='null' ?(
                                    <h3
                                      className="description true"
                                      dangerouslySetInnerHTML={{
                                        __html: '<span>'+translateEng('คำอธิบาย')+' :</span> '+val_q.answer_des,
                                      }}
                                    ></h3>
                                  ):null}
                                </>
                              )
                            )
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </Modal.Content>
              </Modal>
            ) : null}
            {modalPreTest && data && vdoList && vdoList.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => preTestCallback(false)}
                onOpen={() => preTestCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  {vdoList[keySelect]["pre_test"].question.map((val_q, key_q) => (
                    <div className="block-quiz" key={key_q}>
                      <div className="i-title-quiz">
                        <h3>
                          <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question}
                        </h3>
                        {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                          <div className={styles.questionImageDiv}>
                            <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                          </div>
                        ):null}
                      </div>
                      {val_q.type == "choice" ? (
                        <div className="i-choose-quiz-row row">
                          {val_q.answer.map((val_ans, key_ans) => (
                            <div
                              className="col-12 col-md-6 col-choose-quiz"
                              key={key_ans}
                            >
                              <div
                                className="item-choose"
                                onClick={() => onAnswerPreTest(key_q, val_ans.id)}
                              >
                                <div className="fm-check">
                                  <input
                                    className="answer_input"
                                    type="radio"
                                    name={`answer_${key_q}`}
                                  />
                                  <div className="text">
                                    <div className="i_remark">
                                      <i className="icon-ic-circle" />
                                    </div>
                                    <p>{val_ans.answer}</p>
                                  </div>
                                  {val_ans && val_ans.image && val_ans.image!=null&&val_ans.image!=''&&val_ans.image!='null' ?(
                                    <div className={styles.answerImageDiv}>
                                      <Image alt="" layout="fill" className="answer_image" src={val_ans.image} />
                                    </div>
                                  ):null}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : val_q.type == "text" ? (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <TextArea
                                placeholder=""
                                className={
                                  "fm-control answer_textarea " +
                                  (checkMyError(val_q.id) ? "error" : "")
                                }
                                data-type="textaddress"
                                onInput={appContext.diFormPattern}
                                onChange={(event) =>
                                  onAnswerPreTest(key_q, event.target.value)
                                }
                              />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <input
                                className="answer_textarea"
                                type="file"
                                accept="image/jpeg,image/jpg,image/png,image/gif,image/xbm,image/xpm,image/wbmp,image/webp,image/bmp"
                                onChange={(e) => {
                                  previewFilePreTest(key_q, e);
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  <div className="block-to-buy-course">
                    <button
                      className="btn-to-buy-course left"
                      onClick={() => onSubmitPreTest(keySelect)}
                    >
                      <span>{translateEng('ส่งคำตอบ')}</span>
                    </button>
                  </div>
                </Modal.Content>
              </Modal>
            ) : null}
            {modalQuizOpen && data && vdoList && vdoList.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => quizCallback(false)}
                onOpen={() => quizCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  {vdoList[quizSelect]["quiz"].question.map((val_q, key_q) => (
                    <div className="block-quiz" key={key_q}>
                      <div className="i-title-quiz">
                        <h3>
                          <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question}
                        </h3>
                        {val_q && val_q.image && val_q.image!=null&&val_q.image!=''&&val_q.image!='null' ?(
                          <div className={styles.questionImageDiv}>
                            <Image alt="" layout="fill" className="question_image" src={`${val_q.image}`} />
                          </div>
                        ):null}
                      </div>
                      {val_q.type == "choice" ? (
                        <div className="i-choose-quiz-row row">
                          {val_q.answer.map((val_ans, key_ans) => (
                            <div
                              className="col-12 col-md-6 col-choose-quiz"
                              key={key_ans}
                            >
                              <div
                                className="item-choose"
                                onClick={() => onAnswer(key_q, val_ans.id)}
                              >
                                <div className="fm-check">
                                  <input
                                    className="answer_input"
                                    type="radio"
                                    name={`answer_${key_q}`}
                                  />
                                  <div className="text">
                                    <div className="i_remark">
                                      <i className="icon-ic-circle" />
                                    </div>
                                    <p>{val_ans.answer}</p>
                                  </div>
                                  {val_ans && val_ans.image && val_ans.image!=null&&val_ans.image!=''&&val_ans.image!='null' ?(
                                    <div className={styles.answerImageDiv}>
                                      <Image alt="" layout="fill" className="answer_image" src={val_ans.image} />
                                    </div>
                                  ):null}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : val_q.type == "text" ? (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <TextArea
                                placeholder=""
                                className={
                                  "fm-control answer_textarea " +
                                  (checkMyError(val_q.id) ? "error" : "")
                                }
                                data-type="textaddress"
                                onInput={appContext.diFormPattern}
                                onChange={(event) =>
                                  onAnswer(key_q, event.target.value)
                                }
                              />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="i-choose-quiz-row row">
                          <div className="col-12 col-md-12 col-choose-quiz">
                            <div className="item-fm">
                              <input
                                className="answer_textarea"
                                type="file"
                                accept="image/jpeg,image/jpg,image/png,image/gif,image/xbm,image/xpm,image/wbmp,image/webp,image/bmp"
                                onChange={(e) => {
                                  previewFile(key_q, e);
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  <div className="block-to-buy-course">
                    <button
                      className="btn-to-buy-course left"
                      onClick={() => onSubmit(quizSelect)}
                    >
                      <span>{translateEng('ส่งคำตอบ')}</span>
                    </button>
                  </div>
                </Modal.Content>
              </Modal>
            ) : null}
            {modalAssessment && data && assessmentData && assessmentData.length > 0 ? (
              <Modal
                className={`${stylesModal.modalVdo}`}
                onClose={() => assessmentCallback(false)}
                onOpen={() => assessmentCallback(true)}
                open={true}
              >
                <Modal.Content className={`${stylesModal.modaVdoContent}`}>
                  <div className="block-quiz">
                    <div className="i-title-quiz">
                      <h2>
                        {translateEng('แบบประเมินความพึงพอใจ (ใช้เวลาไม่ถึง 1 นาที)')}
                      </h2>
                    </div>
                  </div>
                  {assessmentData.map((val_q, key_q) => (
                    key_q==0 ? (
                      assessmentStep == 1 ?(
                        <div className={`block-quiz assessment`} key={key_q}>
                          <div className="i-title-quiz">
                            <h3>
                              <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                            </h3>
                          </div>
                          {val_q.type == 1 ? (
                            <div className="i-choose-quiz-row row">
                              {val_q.choice.map((val_ans, key_ans) => (
                                <div
                                  className="col-12 col-md-6 col-choose-quiz"
                                  key={key_ans}
                                >
                                  <div
                                    className="item-choose"
                                    onClick={() => onChoice(key_q, val_ans.id)}
                                  >
                                    <div className="fm-check">
                                      <input
                                        className="choice_input"
                                        type="radio"
                                        name={`choice_${key_q}`}
                                      />
                                      <div className="text">
                                        <div className="i_remark">
                                          <i className="icon-ic-circle" />
                                        </div>
                                        <p>{val_ans.choice_th}</p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            val_q.type == 3 ? (
                              <div className="i-choose-quiz-row row">
                                {val_q.choice.map((val_ans, key_ans) => (
                                  <div
                                    className="col-4 col-md-2 col-choose-quiz"
                                    key={key_ans}
                                  >
                                    <div
                                      className="item-choose"
                                      onClick={() => onChoice(key_q, val_ans.id)}
                                    >
                                      <div className="fm-check">
                                        <input
                                          className="choice_input"
                                          type="radio"
                                          name={`choice_${key_q}`}
                                        />
                                        <div className="text with-icon">
                                          {val_ans && val_ans.icon && val_ans.icon!=null && val_ans.icon!='' && val_ans.icon!='null' ? (
                                            <div className={styles.assessmentIconDiv}>
                                              <Image alt="" layout="intrinsic" className={`${styles.iconThumb}`} src={val_ans.icon} width={500} height={500} />
                                            </div>
                                          ):null}
                                          <div className="i_remark">
                                            <i className="icon-ic-circle" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="i-choose-quiz-row row">
                                <div className="col-12 col-md-12 col-choose-quiz">
                                  <div className="item-fm">
                                    <TextArea
                                      placeholder=""
                                      className={
                                        "fm-control choice_textarea " +
                                        (checkMyError(val_q.id) ? "error" : "")
                                      }
                                      data-type="textaddress"
                                      onInput={appContext.diFormPattern}
                                      onChange={(event) =>
                                        onChoice(key_q, event.target.value)
                                      }
                                    />
                                  </div>
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      ):null
                    ):(
                      assessmentStep == 2 ?(
                        <div className={`block-quiz assessment`} key={key_q}>
                          <div className="i-title-quiz">
                            <h3>
                              <span>{translateEng('ข้อที่')} {key_q + 1}.</span> {val_q.question_th}
                            </h3>
                          </div>
                          {val_q.type == 1 ? (
                            <div className="i-choose-quiz-row row">
                              {val_q.choice.map((val_ans, key_ans) => (
                                <div
                                  className="col-12 col-md-6 col-choose-quiz"
                                  key={key_ans}
                                >
                                  <div
                                    className="item-choose"
                                    onClick={() => onChoice(key_q, val_ans.id)}
                                  >
                                    <div className="fm-check">
                                      <input
                                        className="choice_input"
                                        type="radio"
                                        name={`choice_${key_q}`}
                                      />
                                      <div className="text">
                                        <div className="i_remark">
                                          <i className="icon-ic-circle" />
                                        </div>
                                        <p>{val_ans.choice_th}</p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            val_q.type == 3 ? (
                              <div className="i-choose-quiz-row row">
                                {val_q.choice.map((val_ans, key_ans) => (
                                  <div
                                    className="col-4 col-md-2 col-choose-quiz"
                                    key={key_ans}
                                  >
                                    <div
                                      className="item-choose"
                                      onClick={() => onChoice(key_q, val_ans.id)}
                                    >
                                      <div className="fm-check">
                                        <input
                                          className="choice_input"
                                          type="radio"
                                          name={`choice_${key_q}`}
                                        />
                                        <div className="text with-icon">
                                          {val_ans && val_ans.icon && val_ans.icon!=null && val_ans.icon!='' && val_ans.icon!='null' ? (
                                            <div className={styles.assessmentIconDiv}>
                                              <Image alt="" layout="intrinsic" className={`${styles.iconThumb}`} src={val_ans.icon} width={500} height={500} />
                                            </div>
                                          ):null}
                                          <div className="i_remark">
                                            <i className="icon-ic-circle" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="i-choose-quiz-row row">
                                <div className="col-12 col-md-12 col-choose-quiz">
                                  <div className="item-fm">
                                    <TextArea
                                      placeholder=""
                                      className={
                                        "fm-control choice_textarea " +
                                        (checkMyError(val_q.id) ? "error" : "")
                                      }
                                      data-type="textaddress"
                                      onInput={appContext.diFormPattern}
                                      onChange={(event) =>
                                        onChoice(key_q, event.target.value)
                                      }
                                    />
                                  </div>
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      ):null
                    )
                  ))}
                  {assessmentStep == 1 ?(
                    <div className="block-to-buy-course assessment-group-btn">
                      <button
                        className="btn-to-buy-course left"
                        onClick={() => assessmentCallback(false)}
                      >
                        <span>{translateEng('ข้าม')}</span>
                      </button>
                      <button
                        className="btn-to-buy-course right"
                        onClick={() => onAssessmentNext()}
                      >
                        <span>{translateEng('ต่อไป')}</span>
                      </button>
                    </div>
                  ):(
                    <div className="block-to-buy-course">
                      <button
                        className="btn-to-buy-course left"
                        onClick={() => onAssessment()}
                      >
                        <span>{translateEng('ส่งคำตอบ')}</span>
                      </button>
                    </div>
                  )}
                </Modal.Content>
              </Modal>
            ) : null}
            {modalCert ? (
              <Modal
                className={`modal-unlock-cert`}
                onClose={() => certCallback(false)}
                onOpen={() => certCallback(true)}
                open={true}
              >
                <Modal.Content className={`inner`}>
                  <div className="block-unlock-cert">
                    <div className="left">
                      <div className={styles.unlockCertIconDiv}>
                        <Image alt="" layout="intrinsic" src="/assets/images/unlock_cert.png" width={500} height={522} />
                      </div>
                    </div>
                    <div className="right">
                      <h3>{translateEng('ยินดีด้วย!')}</h3>
                      <h2>{translateEng('คุณปลดล็อกใบประกาศ')}</h2>
                      <p>{translateEng('คอร์ส')} {data["data"]["title"]}</p>
                      <div className="curve">
                        <span>{translateEng('สามารถดาวน์โหลดใบประกาศได้ที่โปรไฟล์ของท่าน')}</span>
                      </div>
                      <div className="action">
                        <Link href="/profile/certificate">
                          <Button className="green-button">
                            <span>{translateEng('หรือคลิกที่นี่')}</span>
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </Modal.Content>
              </Modal>
            ) : null}
            <Modal
              className="modalCreateList"
              onClose={() => setAddPlaylist(false)}
              onOpen={() => setAddPlaylist(true)}
              open={addPlaylist}
            >
              <Modal.Content className="modalCreateListContent">
                <div className="block-modal-CreateList">
                  <div className="inner">
                    <h3>{translateEng('เพิ่มไปยังเพลย์ลิสต์ของคุณ')}</h3>
                  </div>
                  <div className="fm-CreateList-select">
                    {playListOptions.length > 0 ? (
                      <div className="item-fm">
                        <Select
                          className="fm-control"
                          placeholder={translateEng('เพลย์ลิสต์ของคุณ')}
                          options={playListOptions}
                          onChange={(event, data) => {
                            setPlaylistSelect(data.value);
                          }}
                        />
                      </div>
                    ) : null}
                    <button
                      className="btn-add-list"
                      onClick={() => show_add_list()}
                    >
                      <span>{translateEng('สร้างเพลย์ลิสต์')}</span>
                    </button>
                  </div>
                  <div id="fmCreateListAdd" className="fm-CreateList-add">
                    <div className="item-fm">
                      <Input
                        id="create_list"
                        type="text"
                        className="fm-control"
                        placeholder={translateEng('ป้อนชื่อเพลย์ลิสต์')}
                        onChange={(event) =>
                          setPlaylistTitle(event.target.value)
                        }
                      ></Input>
                    </div>
                  </div>
                  <div className="fm-CreateList-action">
                    <button
                      className="btn-add-list"
                      onClick={() => submitPlaylist()}
                    >
                      <span>ตกลง</span>
                    </button>
                  </div>
                </div>
              </Modal.Content>
            </Modal>
            <Footer></Footer>

            {/* update */}
            {coursePriceReload &&
            data &&
            data["data"] &&
            data["data"]["is_soon"] == false ? (
              data.data.allowed ||
              data.data.order_status == 4 ||
              data.data.order_status == 1 ? null : 
              data.data.is_subscription ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-default">Member</div>
                  </div>
                </div>
              ) :
              data.data.is_internal || data.data.is_volume ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-default">{translateEng('ฟรี')}</div>
                  </div>
                </div>
              ) : data.data.is_promotion == 1 ? (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    <div className="info-price-sell">
                      {translateEng('ปกติ')}
                      <NumberFormat
                        value={data.data.price}
                        displayType={"text"}
                        thousandSeparator={true}
                        renderText={(value, props) => (
                          <span {...props}>{value}</span>
                        )}
                      />
                    </div>
                    {data.data.pro_price == 0 ? (
                      <div className="info-price-default">{translateEng('ฟรี')}</div>
                    ) : (
                      <div className="info-price-default">
                        {translateEng('ราคา')}
                        <NumberFormat
                          value={data.data.pro_price}
                          displayType={"text"}
                          thousandSeparator={true}
                          renderText={(value, props) => (
                            <span {...props}>{value}</span>
                          )}
                        />
                        {translateEng('บาท')}
                      </div>
                    )}
                  </div>
                  <div className="action-price">
                    {data &&
                    data["data"] && data.data.is_standalone == 1 &&
                    data["data"]["is_soon"] == false ? (
                      (data.data.price == 0 ||
                      (data.data.is_promotion == 1 &&
                        data.data.pro_price == 0) ||
                      data.data.is_internal||
                      data.data.is_subscription||
                      data.data.is_volume || 
                      data.data.expire_date)&&discountCode=='' ? (
                        data.data.is_subscription ? (
                          <button
                            className="btn-action-price"
                            onClick={() =>
                              groupCategoryCallback(
                                "vip",
                                data["data"]["id"],
                                ""
                              )
                            }
                          >
                            <span>{translateEng('เริ่มเรียน')}</span>
                          </button>
                        ):(
                          data.data.is_volume ? (
                            <button
                              className="btn-action-price"
                              onClick={() =>
                                groupCategoryCallback(
                                  "volume",
                                  data["data"]["id"],
                                  ""
                                )
                              }
                            >
                              <span>{translateEng('เริ่มเรียน')}</span>
                            </button>
                          ):(
                            data.data.is_internal&&data.data.user_internal ? (
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "internal",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            ):(
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "free",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            )
                          )
                        )
                      ) : (
                        <button
                          className="btn-action-price"
                          onClick={() =>
                            groupCategoryCallback(
                              "cart",
                              data["data"]["id"],
                              ""
                            )
                          }
                        >
                          <span>{translateEng('ซื้อคอร์สนี้')}</span>
                        </button>
                      )
                    ) : null}
                  </div>
                </div>
              ) : (
                <div className="block-info-price" ref={fixBuy}>
                  <div className="info-price">
                    {data.data.price == 0 || data.data.expire_date ? (
                      <div className="info-price-default">{translateEng('ฟรี')}</div>
                    ) : (
                      <div className="info-price-default">
                        {translateEng('ราคา')}
                        <NumberFormat
                          value={data.data.price}
                          displayType={"text"}
                          thousandSeparator={true}
                          renderText={(value, props) => (
                            <span {...props}>{value}</span>
                          )}
                        />
                        {translateEng('บาท')}
                      </div>
                    )}
                  </div>
                  <div className="action-price">
                    {data &&
                    data["data"] && data.data.is_standalone == 1 &&
                    data["data"]["is_soon"] == false ? (
                      (data.data.price == 0 ||
                      (data.data.is_promotion == 1 &&
                        data.data.pro_price == 0) ||
                      data.data.is_internal||
                      data.data.is_subscription||
                      data.data.is_volume || 
                      data.data.expire_date)&&discountCode=='' ? (
                        data.data.is_subscription ? (
                          <button
                            className="btn-action-price"
                            onClick={() =>
                              groupCategoryCallback(
                                "vip",
                                data["data"]["id"],
                                ""
                              )
                            }
                          >
                            <span>{translateEng('เริ่มเรียน')}</span>
                          </button>
                        ):(
                          data.data.is_volume ? (
                            <button
                              className="btn-action-price"
                              onClick={() =>
                                groupCategoryCallback(
                                  "volume",
                                  data["data"]["id"],
                                  ""
                                )
                              }
                            >
                              <span>{translateEng('เริ่มเรียน')}</span>
                            </button>
                          ):(
                            data.data.is_internal&&data.data.user_internal ? (
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "internal",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            ):(
                              <button
                                className="btn-action-price"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "free",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('เริ่มเรียน')}</span>
                              </button>
                            )
                          )
                        )
                      ) : (
                        <button
                          className="btn-action-price"
                          onClick={() =>
                            groupCategoryCallback(
                              "cart",
                              data["data"]["id"],
                              ""
                            )
                          }
                        >
                          <span>{translateEng('ซื้อคอร์สนี้')}</span>
                        </button>
                      )
                    ) : null}
                  </div>
                </div>
              )
            ) : null}
            {/* update */}
          </div>
        </div>
        <Modal
          className='modalCreateList'
          onClose={() => setModalOculus(false)}
          onOpen={() => setModalOculus(true)}
          open={modalOculus}>
          <Modal.Content className="modalCreateListContent">
            <div className="block-modal-CreateList">
                <div className="oculus-pin">
                  <h3>Pin ของคุณคือ</h3>
                  <h2>{oculusPin}</h2>
                  <p>Pin จะมีอายุการใช้งาน 10 นาที</p>
                </div>
                <div className='fm-CreateList-action'>
                  <button className='btn-add-list' onClick={() => setModalOculus(false)}>
                    <span>ปิด</span>
                  </button>
                </div>
            </div>
          </Modal.Content>
        </Modal>
      </>
    );
  }
}

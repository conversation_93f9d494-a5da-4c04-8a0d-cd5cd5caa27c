import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../../api/user";
import { useState, useEffect, useContext } from "react";

import { useRouter } from "next/router";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import Link from "next/link";
export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("code", params.code);
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/user/verify_live",
    {
      body: formData,
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const resdata = await res;
  const data = await res.json();

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/verify",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      user,
      params,
      utoken,
      data
    },
  };
}
export default function Verify({ seo_data, params,user,utoken,data }) {
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const router = useRouter();
  const [firstLoad, setFirstLoad] = useState(true);
  useEffect(() => {
    if(firstLoad){
      setFirstLoad(false);
      if (!user) {
        appContext.setOpen(true);
      }
    }
  }, [firstLoad]);
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div
          className={`container custom-container space-between-content thanks-page-container`}
        >
          {data && data.status && data.status=='not-login' ? (
            <div className="thanks-page white">
              <i className="icon-ic-remove-cart"></i>
              <h3>กรุณาเข้าสู่ระบบ</h3>
              <p>
                หากยังไม่เป็นสมาชิก กรุณาสมัครสมาชิกด้วยอีเมล์ที่ได้รับค่ะ
              </p>
              <div className="item-purchase-report border0">
                <div className="purchase-report-action">
                  <button className="btn-default" onClick={() => appContext.setOpen(true)}>
                    <span>เข้าสู่ระบบ</span>
                  </button>
                </div>
              </div>
            </div>
          ) : data && data.status && (data.status=='success'||data.status=='verify') ? (
            <div className="thanks-page white">
              <i className="icon-ic-tick-thanks"></i>
              <h3>ยืนยันตัวตนเรียบร้อยแล้ว</h3>
              <p>
                คุณสามารถเข้าเรียนได้โดยคลิกปุ่มด้านล่าง
              </p>
              <div className="item-purchase-report border0">
                <div className="purchase-report-action">
                  <Link href={`/live/${data.slug}`}>
                    <button className="btn-default">
                      <span>เข้าเรียน</span>
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          ) : (
            data && data.status && data.status=='diff-email' ? (
              <div className="thanks-page white alert">
                <i className="icon-ic-remove-cart"></i>
                <h3>ขออภัยการยืนยันตัวตนไม่ถูกต้อง</h3>
                <p>อีเมล์ของคุณไม่ตรงกับที่ลงทะเบียนไว้</p>
              </div>
            ) : (
              <div className="thanks-page white alert">
                <i className="icon-ic-remove-cart"></i>
                <h3>ขออภัยการยืนยันตัวตนไม่ถูกต้อง</h3>
                <p>
                  กรุณาตรวจสอบลิงค์ที่ได้รับจากอีเมล์
                </p>
              </div>
            )
          )}
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
}

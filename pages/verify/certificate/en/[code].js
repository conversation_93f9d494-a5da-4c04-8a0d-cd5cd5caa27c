import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "../../../api/user";
import { useState, useEffect, useContext } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import Image from "next/image";
import Link from "next/link";
export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("code", params.code);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/verify/certificate/en/" + params.code,
    {
      body: formData,
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const resdata = await res;
  const data = await res.json();

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/verify_certificate",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
    },
  };
}
export default function Verify({ seo_data, errorCode, data, params }) {
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div
          className={`container custom-container space-between-content thanks-page-container`}
        >
          {data.data && data.status == "success"? (
            <div className="thanks-page white certificate">
              <i className="icon-ic-tick-thanks"></i>
              <div className="certificate-block">
                <div className="certificate-section certificate-header">
                  <div className="certificate-text">
                    <h3>CERTIFICATE</h3>
                    <h4>OF COMPLETION THIS IS TO CERTIFY THAT</h4>
                  </div>
                </div>
                <div className="certificate-section certificate-body">
                  <div className="certificate-content">
                      <div className="content-space">
                          <div className="inner-space">
                            <h3 className="verify-cert-user">{data.data.user}</h3>
                          </div>
                      </div>
                      <div className="content">
                          <p>HAS COMPLETED THE ONLINE COURSE ENTITLED</p>
                          <h4 dangerouslySetInnerHTML={{ __html: data.data.name }}></h4>
                      </div>
                      <div className="content cme">
                        {data.data.cme !=0 ?(
                          <p>CME {data.data.cme} Credits</p>
                        ):null}
                        {data.data.course_duration!='' && data.data.course_progress!='' ?(
                          <p>Date : {data.data.date} Durations : {data.data.course_duration} Progress : {data.data.course_progress}</p>
                        ):(
                          <p>Date : {data.data.date}</p>
                        )}
                      </div>
                  </div>
                </div>
                <div className="certificate-section certificate-footer">
                  {data.data.type==1 || data.data.type==2 || data.data.type==3?(
                    <div className="certificate-signature">
                        {data.data.signature_en!=null &&data.data.signature_en!=''&&data.data.signature_en!='null' ? (
                          <div className="signature-img">
                            <Image
                              src={data.data.signature_en} 
                              alt=""
                              layout='fill'
                              objectFit='contain'
                              sizes="100%"
                            />
                          </div>
                        ):null}
                        <div className="signature-text">
                          <p dangerouslySetInnerHTML={{ __html: data.data.director_en }}></p>
                          <p dangerouslySetInnerHTML={{ __html: data.data.director_position_en }}></p>
                        </div>
                    </div>
                  ):null}
                  {data.data.type==2 || data.data.type==3?(
                    <div className="certificate-signature">
                        {data.data.signature_2_en!=null &&data.data.signature_2_en!=''&&data.data.signature_2_en!='null' ? (
                          <div className="signature-img">
                            <Image
                              src={data.data.signature_2_en} 
                              alt=""
                              layout='fill'
                              objectFit='contain'
                              sizes="100%"
                            />
                          </div>
                        ):null}
                        <div className="signature-text">
                          <p dangerouslySetInnerHTML={{ __html: data.data.director_2_en }}></p>
                          <p dangerouslySetInnerHTML={{ __html: data.data.director_position_2_en }}></p>
                        </div>
                    </div>
                  ):null}
                  {data.data.type==3?(
                    <div className="certificate-signature">
                        {data.data.signature_3_en!=null &&data.data.signature_3_en!=''&&data.data.signature_3_en!='null' ? (
                          <div className="signature-img">
                            <Image
                              src={data.data.signature_3_en} 
                              alt=""
                              layout='fill'
                              objectFit='contain'
                              sizes="100%"
                            />
                          </div>
                        ):null}
                        <div className="signature-text">
                          <p dangerouslySetInnerHTML={{ __html: data.data.director_3_en }}></p>
                          <p dangerouslySetInnerHTML={{ __html: data.data.director_position_3_en }}></p>
                        </div>
                    </div>
                  ):null}
                </div>
              </div>
            </div>
          ) : (
            <div className="thanks-page white alert certificate">
              <i className="icon-ic-remove-cart"></i>
              <h3>ขออภัย ไม่พบใบประกาศนียบัตร</h3>
              <p>
                ติดต่อสอบถามเพิ่มเติมได้ที่ 0 2256 4183 ,0 2256 4000
                (รพ.จุฬาฯ)
              </p>
            </div>
          )}
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
}

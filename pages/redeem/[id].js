import React from "react";
import Image from "next/image";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import _ from "lodash";

import { useSession, getSession } from "next-auth/react";
import { getUser } from "../api/user";
import { useRouter } from "next/router";
import Head from "next/head";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import Link from "next/link";
import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";
import { useForm } from "react-hook-form";
// Serverside & Api fetching
import Error from "next";
// Serverside & Api fetching
// Auth
import nookies from "nookies";
// Auth
import AppContext from "/libs/contexts/AppContext";
import Swal from "sweetalert2";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/redeemDetail/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["status"] == "false") {
    return {
      redirect: { destination: "/" },
    };
  }

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/reward",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      utoken,
      user,
    },
  };
}

export default function PointQR ({ seo_data,errorCode,data,params,utoken,user }) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const [lockTab, setLockTab] = useState(false);
  const [isExpire, setIsExpire] = useState(false);
  const [pageStatus, setPageStatus] = useState(data.status);
  const [expireTime, setExpireTime] = useState(data.data.coupon_expire);
  const [countdownMin, setCountdownMin] = React.useState("00");
  const [countdownSec, setCountdownSec] = React.useState("00");
  useEffect(() => {
    const interval = setInterval(() => {
      const formData = new URLSearchParams();
      formData.append("utoken", utoken);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/redeemDetail/" + params.id,
        formData,
        (res_data) => {
          data = res_data;
          setPageStatus(res_data.status);
          setExpireTime(res_data.data.coupon_expire);
        }
      );
    }, 5000);
    return () => clearInterval(interval);
  }, []);
  useEffect(() => {
    const interval_cd = setInterval(() => {
      if(new Date(data.data.coupon_expire) >= new Date()){
        var now = new Date().getTime();
        var distance = new Date(data.data.coupon_expire).getTime() - now;
        var days = Math.floor(distance / (1000 * 60 * 60 * 24));
        var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        var seconds = Math.floor((distance % (1000 * 60)) / 1000);

        minutes = minutes + (hours*60) + (days*24*60);
        if(minutes<10){
          minutes = '0'+minutes;
        }
        if(seconds<10){
          seconds = '0'+seconds;
        }
        setCountdownMin(minutes);
        setCountdownSec(seconds);
        setIsExpire(false);
      }else{
        setIsExpire(true);
        setCountdownMin('00');
        setCountdownSec('00');
      }
    }, 1000);
    return () => clearInterval(interval_cd);
  }, []);
  function convertToCustomDateFormat(sqlDateTime) {
    const months = [
      "Jan", "Feb", "Mar", "Apr", "May", "Jun",
      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    ];
  
    const dateParts = sqlDateTime.split('-');
    const year = parseInt(dateParts[0]);
    const month = months[parseInt(dateParts[1], 10) - 1];
    const day = parseInt(dateParts[2].slice(0, 2), 10);
  
    return `${month} ${day}, ${year}`;
  }
  function convertToTimeFormat(sqlDateTime) {
    const dateObj = new Date(sqlDateTime);
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const seconds = String(dateObj.getSeconds()).padStart(2, '0');
  
    return `${hours}:${minutes}:${seconds}`;
  }
  
  
  const [clickCount, setClickCount] = useState(0);
  const buttonRef = useRef(null);

  // Function to be executed when the button is clicked seven times
  function doSomethingAfterClicks() {
    if(!lockTab){
      setLockTab(true);
      const formData = new URLSearchParams();
      formData.append("code", params.id);
      formData.append("type", 2);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/user/verify_redeem",
        formData,
        (res_data) => {
          if(res_data && res_data.status && res_data.status=='success'){
            Swal.fire({
              text: 'ยืนยันรับของรางวัลสำเร็จ',
              icon: "success",
              confirmButtonText: 'ปิด',
              confirmButtonColor: "#648d2f"
            });
          }else if(res_data && res_data.status && res_data.status=='already'){
            Swal.fire({
              text: 'ขออภัยคุณเคยยืนยันรับของรางวัลแล้ว',
              icon: "error",
              confirmButtonText: 'ปิด',
              confirmButtonColor: "#648d2f"
            });
          }else{
            Swal.fire({
              text: 'ขออภัย พบข้อผิดพลาด กรุณาลองอีกครั้ง',
              icon: "error",
              confirmButtonText: 'ปิด',
              confirmButtonColor: "#648d2f"
            });
          }
        }
      );
    }
  }

  function confirmRedeem() {
    Swal.fire({
      html: 'คุณต้องการยืนยันการรับของรางวัลใช่หรือไม่?',
      icon: 'info',
      showCancelButton: true,
      confirmButtonColor: '#648d2f',
      cancelButtonColor: '#d33',
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก'
    }).then((result) => {
      if (result.isConfirmed) {
        if(!lockTab){
          setLockTab(true);
          const formData = new URLSearchParams();
          formData.append("code", params.id);
          formData.append("type", 2);
          appContext.sendApi(
            process.env.NEXT_PUBLIC_API + "/api/user/verify_redeem",
            formData,
            (res_data) => {
              if(res_data && res_data.status && res_data.status=='success'){
                Swal.fire({
                  text: 'ยืนยันรับของรางวัลสำเร็จ',
                  icon: "success",
                  confirmButtonText: 'ปิด',
                  confirmButtonColor: "#648d2f"
                });
              }else if(res_data && res_data.status && res_data.status=='already'){
                Swal.fire({
                  text: 'ขออภัยคุณเคยยืนยันรับของรางวัลแล้ว',
                  icon: "error",
                  confirmButtonText: 'ปิด',
                  confirmButtonColor: "#648d2f"
                });
              }else{
                Swal.fire({
                  text: 'ขออภัย พบข้อผิดพลาด กรุณาลองอีกครั้ง',
                  icon: "error",
                  confirmButtonText: 'ปิด',
                  confirmButtonColor: "#648d2f"
                });
              }
            }
          );
        }
      }
    })
  }

  // Function to handle the button click
  function handleClick() {
    setClickCount((prevClickCount) => prevClickCount + 1);
  }

  // Check if the clickCount reaches 7 and call the function
  useEffect(() => {
    if (clickCount === 7) {
      doSomethingAfterClicks();
      setClickCount(0); // Reset the click count for future clicks
    }
  }, [clickCount]);

  // Reset clickCount when clicking outside the button
  useEffect(() => {
    function handleClickOutside(event) {
      if (buttonRef.current && !buttonRef.current.contains(event.target)) {
        setClickCount(0);
      }
    }

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);
  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>
      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        {pageStatus!='redeemed'?(
          data && data.data ?(
            !isExpire?(
              <div className="point-page">
                <div className="text-title">
                  <p>
                    <i className="icon-ic-tick-thanks"></i>
                    Your Redeem is ready.
                  </p>
                </div>
                <div className="block-point-detail">
                  <div className="img-qr">
                    {data.data.coupon_qr && !appContext.isNull(data.data.coupon_qr)?(
                      <Image alt="" height={271} width={268} src={data.data.coupon_qr} />
                    ):null}
                  </div>
                  <p className="sub-text">
                    Show this QR Code to Cashier to paid <br/>
                    by wallet or collect  point.
                  </p>
                  <h3 className="title">
                    กรุณาอย่าปิดหน้าจอ
                  </h3>
                  <div className="action mg-top-10">
                    <Button className="btn-detail" onClick={() => confirmRedeem()}>
                      <span>ยืนยันการรับของรางวัล</span>
                    </Button>
                  </div>
                  <div className="block-content-qr">
                    <div className="code" ref={buttonRef} onClick={handleClick}>
                      <p>Code:</p>
                      {data.data.coupon_code && !appContext.isNull(data.data.coupon_code)?(
                        <h3>
                          {data.data.coupon_code}
                        </h3>
                      ):null}
                    </div>
                    <p>
                      Valid : <span>{convertToCustomDateFormat(expireTime)}</span>
                    </p>
                    <p>
                      Time : <span>{convertToTimeFormat(expireTime)}</span>
                    </p>
                    <div className="countdown">
                      <p>
                        Running Time 
                      </p>
                      <div className="inner">
                        <div className="item">
                          <div className="box-time">
                            <span>{countdownMin}</span>
                          </div>
                          <p>
                            Minutes
                          </p>
                        </div>
                        <div className="item">
                          <div className="box-time">
                            <span>{countdownSec}</span>
                          </div>
                          <p>
                            Seconds
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
            ):(
              <div className="point-page">
                <div className="logo-coupon">
                  <Image alt="" height={56} width={163} src={'/assets/images/logo-coupon.png'} />
                </div>
                <div className="block-point-detail">
                  <div className="content-detail">
                    <p className="text-succeed">
                      ขออภัย คูปองของคุณหมดอายุแล้ว
                    </p>
                  </div>
                </div>
                <div className="action">
                  <Link href="/profile/redemption">
                    <Button className="btn-detail">
                      <span>ปิด</span>
                    </Button>
                  </Link>
                </div>
              </div>
            )
          ):null
        ):(
          data && data.data ?(
            <div className="point-page">
              <div className="logo-coupon">
                <Image alt="" height={56} width={163} src={'/assets/images/logo-coupon.png'} />
              </div>
              <div className="block-point-detail">
                <div className="img-detail">
                  {data.data.coupon_image && !appContext.isNull(data.data.coupon_image)?(
                    <Image alt="" height={276} width={256} src={data.data.coupon_image} />
                  ):null}
                </div>
                <div className="content-detail">
                  <p className="text-succeed">
                    {data.data.coupon_code && !appContext.isNull(data.data.coupon_code)?(
                      'Code : '+data.data.coupon_code
                    ):null}
                    {data.data.coupon_code && !appContext.isNull(data.data.coupon_code)?(
                      <br/>
                    ):null}
                    โปรดตรวจสอบ <br/>
                    ว่าคุณได้รับเครื่องดื่มจากร้านค้าแล้ว
                  </p>
                </div>
              </div>
              <div className="action">
                <Link href="/profile/redemption">
                  <Button className="btn-detail">
                    <span>ปิด</span>
                  </Button>
                </Link>
              </div>
            </div>
          ):null
        )}
      </div>
      
      <Footer></Footer>
    </div>
  );
}

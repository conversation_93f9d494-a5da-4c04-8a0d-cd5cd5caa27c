import React from "react";
import Image from "next/image";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "./api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import Head from "next/head";
import Header from "../themes/header/header";
import Footer from "../themes/footer/footer";
import AddressContact from "../themes/components/addressContact";
import GroupRadio from "../themes/components/groupRadio";
import CourseDetailCode from "/themes/components/courseDetailCode";
import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";
import PaymentOption from "../themes/components/paymentOption";
import Swal from "sweetalert2";
import NumberFormat from "react-number-format";
import Payment2c2p from '../components/payment/Payment2c2p';
import PaymentOptions from '../components/checkout/PaymentOptions';
import OrderSummary from '../components/checkout/OrderSummary';
import TaxInfoForm from '../components/checkout/TaxInfoForm';
import GiftForm from '../components/checkout/GiftForm';

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const params = context.query;
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/checkout",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: { seo_data, utoken, user, params },
  };
}
export default function Checkout({ seo_data, utoken, user, params }) {
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const router = useRouter();
  const inputFiledata = useRef(null);
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [reloadCart, setReloadCart] = React.useState(true);
  const [cartData, setCartData] = React.useState(null);
  const [taxType, setTaxType] = React.useState(2);
  const [companyType, setCompanyType] = React.useState(1);
  const [giftType, setGiftType] = React.useState(2);
  const [reload, setReload] = React.useState(true);
  const [dataSendComplete, setDataSendComplete] = React.useState(false);

  const [reloadDistrict, setReloadDistrict] = React.useState(true);
  const [reloadSubDistrict, setReloadSubDistrict] = React.useState(true);
  const [provinceOptions, setProvinceOptions] = useState(null);
  const [districtOptions, setDistrictOptions] = useState(null);
  const [subdistrictOptions, setSubdistrictOptions] = useState(null);
  const [district, setDistrict] = useState(0);
  const [subdistrict, setSubdistrict] = useState(0);
  const [formType, setFormType] = useState(1);
  const [getReceipt, setGetReceipt] = useState(2);
  const [addressType, setAddressType] = useState(2);
  const [leftShow, setLeftShow] = useState(true);
  const { register, setValue, getValues } = useForm({
    defaultValues: {},
  });

  const payment2c2p = Payment2c2p({
    appContext,
    cartData,
    setCartData,
    setValue,
    getValues,
    taxType,
    setDataSendComplete,
    envNextPublicApi: process.env.NEXT_PUBLIC_API
  });

  const [selectedPayment, setSelectedPayment] = useState(null);

  function setPayment(_value) {
    setSelectedPayment(parseInt(_value));
    setValue("payment", parseInt(_value));

    localStorage.setItem('selected_payment', parseInt(_value));

    setTimeout(() => {
      updatePaymentSelection(parseInt(_value));
    }, 100);
  }

  function updatePaymentSelection(value) {
    const radioButtons = document.querySelectorAll('input[name="payment"]');
    radioButtons.forEach((radio) => {
      if (parseInt(radio.value) === value) {
        radio.checked = true;
      } else {
        radio.checked = false;
      }
    });

    const paymentContainers = document.querySelectorAll(".item-choose-payment");
    paymentContainers.forEach((container, index) => {
      if (index === value - 1) {
        container.classList.add("selected");
      } else {
        container.classList.remove("selected");
      }
    });
  }

  useEffect(() => {
    if (reload) {
      setReload(false);
      getProvince();
      
      const formData = new URLSearchParams();
      formData.append("tax_type", taxType);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/getCheckout",
        formData,
        (data) => {
          if (data["status"] != "success" || !data.data || data.data.length === 0) {
            Swal.fire({
              text: "ไม่มีสินค้าในตะกร้า",
              icon: "info",
              confirmButtonText: "ตกลง",
              confirmButtonColor: "#648d2f",
            }).then(() => {
              router.push('/profile/history');
            });
            return;
          }
        }
      );
      
      if (params.receipt) {
        setReceipt(1);
        setTimeout(() => {
          document.getElementById("receipt").checked = true;
          document.getElementById("receipt2").checked = false;
        }, "500");
      }
    }
  }, [reload]);

  useEffect(() => {
    if (user) {
      // if (user.name == "null") {
      setValue("name", null);
      // } else {
      //   setValue("name", user.name);
      // }
      // if (user.lastname == "null") {
      setValue("lastname", null);
      // } else {
      //   setValue("lastname", user.lastname);
      // }
      if (user.address == "null") {
        setValue("address", null);
      } else {
        setValue("address", user.address);
      }
      if (user.subdistrict == "null") {
        setValue("subdistrict", null);
      } else {
        setValue("subdistrict", user.subdistrict);
      }
      if (user.district == "null") {
        setValue("district", null);
      } else {
        setValue("district", user.district);
      }
      if (user.province == "null") {
        setValue("province", null);
      } else {
        setValue("province", user.province);
      }
      if (user.postcode == "null") {
        setValue("postcode", null);
      } else {
        setValue("postcode", user.postcode);
      }
      setValue("receipt", 2);
      setValue("receipt_type", 1);
      setValue("address_type", 1);
      // setValue("tax", 2);
      setValue("gift", 2);
      setValue("gift_email", null);
      setValue("gift_message", null);
      setValue("iden_no", null);
      setValue("note", null);
      setValue("payment", null);
      setValue("cart_data", null);
      setValue("utm_source", null);
      document.getElementById("receipt2").checked = true;
    }
  }, [user]);

  useEffect(() => {
    if (reloadCart) {
      setReloadCart(false);
      const formData = new URLSearchParams();
      formData.append("tax_type", taxType);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/getCheckout",
        formData,
        (data) => {
          if (data["status"] != "success") {
            location.href = "/";
          }
          if (!data.data || data.data.length === 0) {
            router.push('/profile/history');
            return;
          }
          if (Number(data["total_price"]) == 0) {
            // location.href="/";
            setPayment(3);
            setLeftShow(false);
          }
          setCartData(data);
        }
      );
    }
  }, [reloadCart]);

  useEffect(() => {
    if (reloadDistrict) {
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/area/district/" +
          getValues("province"),
        null,
        (data) => {
          setDistrictOptions(data.data);
          setSubdistrictOptions(null);
          setReloadDistrict(false);
        }
      );
    }
  }, [reloadDistrict]);

  useEffect(() => {
    if (reloadSubDistrict) {
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/area/subdistrict/" +
          getValues("district"),
        null,
        (data) => {
          setSubdistrictOptions(data.data);
          setReloadSubDistrict(false);
        }
      );
    }
  }, [reloadSubDistrict]);

  const getProvince = () => {
    appContext.loadApi(
      process.env.NEXT_PUBLIC_API + "/api/area/province",
      null,
      (data) => {
        setProvinceOptions(data.data);
      }
    );
  };

  function addDiscountCode(_code, _type) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("discount_code", _code);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addDiscountWeb",
        formData,
        (obj) => {
          if (obj["is_discount"]) {
            Swal.fire({
              text: "ยินดีด้วย ใช้คูปองสำเร็จ",
              icon: "success",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f",
            });
          } else {
            Swal.fire({
              text: "ขออภัย ไม่พบคูปอง",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f",
            });
          }
          setReloadCart(true);
          appContext.setReloadCart(true);
        }
      );
    } else {
      Swal.fire({
        text: "กรุณาเข้าสู่ระบบค่ะ",
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f",
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }

  function setReceipt(_value) {
    setGetReceipt(parseInt(_value));
    setValue("receipt", parseInt(_value));
    setFormType(1);
    setValue("receipt_type", 1);
    setAddressType(2);
    setValue("address_type", 2);
    setTaxType(2);
    // setValue("tax", 2);
    setCompanyType(1);
    setValue("company_type", 1);
    setValue("company_branch", "");
    setTimeout(() => {
      if (_value == 1) {
        document.getElementById("receipt_type").checked = true;
        // document.getElementById("address_type").checked = true;
      }
    }, "0");
    setReloadCart(true);
  }
  function setReceiptType(_value) {
    setFormType(parseInt(_value));
    setValue("receipt_type", parseInt(_value));
    setAddressType(2);
    setValue("address_type", 2);
    setTaxType(2);
    // setValue("tax", 2);
    setCompanyType(1);
    setValue("company_type", 1);
    setValue("company_branch", "");
    setTimeout(() => {
      if (_value == 2) {
        document.getElementById("company_type").checked = true;
        // document.getElementById("tax2").checked = true;
      }
      // document.getElementById("address_type").checked = true;
    }, "0");
    setReloadCart(true);
  }
  function setAddress(_value) {
    setAddressType(parseInt(2));
    setValue("address_type", parseInt(2));
    setTaxType(2);
    // setValue("tax", 2);
    setCompanyType(1);
    setValue("company_type", 1);
    setValue("company_branch", "");
    setTimeout(() => {
      if (formType == 2) {
        // document.getElementById("tax2").checked = true;
      }
    }, "0");
    setReloadCart(true);
  }
  function setTax(_value) {
    setTaxType(parseInt(_value));
    // setValue("tax", parseInt(_value));
    setReloadCart(true);
    setCompanyType(1);
    setValue("company_type", 1);
    setValue("company_branch", "");
    if (_value == 1) {
      setAddressType(parseInt(2));
      setValue("address_type", parseInt(2));
      setTimeout(() => {
        // document.getElementById("address_type2").checked = true;
        // document.getElementById("tax").checked = true;
      }, "0");
    }
  }
  function setCompany(_value) {
    setCompanyType(parseInt(_value));
    setValue("company_type", parseInt(_value));
    setValue("company_branch", "");
    setReloadCart(true);
  }
  function onSubmit() {
    let register_data = getValues();

    // ตรวจสอบว่าค่า payment มีการเลือกจริงหรือไม่
    if (!register_data["payment"]) {
      // ตรวจสอบจาก radio buttons
      const radioButtons = document.querySelectorAll('input[name="payment"]');
      let selectedRadio = false;
      radioButtons.forEach((radio) => {
        if (radio.checked) {
          selectedRadio = true;
          register_data["payment"] = parseInt(radio.value);
          setValue("payment", parseInt(radio.value));
        }
      });

      if (!selectedRadio) {
        appContext.setLoading(false);
        
        Swal.fire({
          text: "กรุณาเลือกช่องทางชำระเงิน",
          icon: "error",
          confirmButtonText: "ปิด",
          confirmButtonColor: "#648d2f",
        });
        return;
      }
      
      appContext.setLoading(true);
    } else {
      appContext.setLoading(true);
    }

    checkPaymentMethod();
  }

  function checkPaymentMethod() {
    let register_data = getValues();

    appContext.setLoading(true);
    if (cartData.cart_live) {
      const formCheck = new URLSearchParams();
      formCheck.append("course_id", cartData.data[0]["course_id"]);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/checkLive",
        formCheck,
        (res_live) => {
          if (
            res_live["limit"] == 0 ||
            (res_live["limit"] != 0 && res_live["remain"] != 0)
          ) {
            if (res_live["limit"] == 0) {
              const boxes = document.querySelectorAll(".remove_submit_btn");
              boxes.forEach((box) => {
                box.remove();
              });
              if (
                register_data["payment"] == 1 ||
                register_data["payment"] == 2
              ) {
                // เรียกใช้ submit2c2p จาก component
                payment2c2p.submit2c2p();
              } else {
                sentDataToAddOrder();
              }
            } else {
              // html: "กรุณากดยืนยันเพื่อลงทะเบียน<br>Live Streaming<br>จำนวนผู้ลงทะเบียน "+(res_live['limit']-res_live['remain'])+'/'+res_live['limit'],
              appContext.setLoading(false);
              Swal.fire({
                html: "กรุณากดยืนยันเพื่อลงทะเบียน<br>Live Streaming",
                icon: "info",
                showCancelButton: true,
                confirmButtonColor: "#648d2f",
                cancelButtonColor: "#d33",
                confirmButtonText: "ยืนยัน",
                cancelButtonText: "ยกเลิก",
              }).then((result) => {
                if (result.value) {
                  appContext.setLoading(true);
                  const boxes = document.querySelectorAll(".remove_submit_btn");
                  boxes.forEach((box) => {
                    box.remove();
                  });
                  if (
                    register_data["payment"] == 1 ||
                    register_data["payment"] == 2
                  ) {
                    // เรียกใช้ submit2c2p จาก component
                    payment2c2p.submit2c2p();
                  } else {
                    sentDataToAddOrder();
                  }
                }
              });
            }
          } else {
            appContext.setLoading(false);
            Swal.fire({
              html: "Live Streaming<br>มีผู้ลงทะเบียนครบตามจำนวนแล้ว",
              icon: "error",
              confirmButtonText: "ปิด",
              confirmButtonColor: "#648d2f",
            });
          }
        }
      );
    } else {
      if (
        cartData.yearly_member &&
        cartData.yearly_member != "" &&
        cartData.cart_type != "subscription" &&
        cartData.yearly_setting &&
        cartData.yearly_setting != "" &&
        cartData.total_price_excl_ebook >= cartData.yearly_setting.value &&
        cartData.learner_type &&
        (cartData.learner_type == 1 ||
          cartData.learner_type == 3 ||
          cartData.learner_type == 4)
      ) {
        if (
          cartData.yearly_setting.qty &&
          cartData.yearly_setting.qty != 0 &&
          cartData.yearly_setting.qty != "0" &&
          cartData.yearly_setting.qty != "" &&
          cartData.yearly_setting.qty != null &&
          cartData.yearly_setting.qty != "null" &&
          cartData.count_excl_ebook &&
          cartData.count_excl_ebook != 0 &&
          cartData.count_excl_ebook != "0" &&
          cartData.count_excl_ebook != "" &&
          cartData.count_excl_ebook != null &&
          cartData.count_excl_ebook != "null" &&
          cartData.yearly_setting.qty > cartData.count_excl_ebook
        ) {
          const boxes = document.querySelectorAll(".remove_submit_btn");
          boxes.forEach((box) => {
            box.remove();
          });
          if (register_data["payment"] == 1 || register_data["payment"] == 2) {
            // เรียกใช้ submit2c2p จาก component
            payment2c2p.submit2c2p();
          } else {
            sentDataToAddOrder();
          }
        } else {
          appContext.setLoading(false);
          Swal.fire({
            html: "คุณต้องการซื้อ Yearly Member<br>เพื่อความคุ้มค่ามากกว่าหรือไม่?",
            icon: "info",
            showCancelButton: true,
            confirmButtonColor: "#648d2f",
            cancelButtonColor: "#d33",
            confirmButtonText: "ซื้อ Yearly Member",
            cancelButtonText: "ซื้อสินค้านี้",
          }).then((result) => {
            if (result.value) {
              appContext.setLoading(true);
              const formData = new URLSearchParams();
              formData.append("course_id", cartData.yearly_member.id);
              formData.append("content_type", "subscription");
              appContext.sendApi(
                process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
                formData,
                (res_check) => {
                  if (res_check["status"] == "success") {
                    if (res_check["count"] == 0) {
                      appContext.sendApi(
                        process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                        formData,
                        (data) => {
                          if (data["status"] == "success") {
                            location.reload();
                          } else if (data["status"] == "limit") {
                            appContext.setLoading(false);
                            Swal.fire({
                              text: "ขออภัยค่ะ คุณไม่สามารถต่ออายุเกิน 2 ปีได้",
                              icon: "info",
                              confirmButtonText: "ปิด",
                              confirmButtonColor: "#648d2f",
                            }).then((result) => {
                              location.reload();
                            });
                          } else if (data["status"] == "limit_buy") {
                            appContext.setLoading(false);
                            Swal.fire({
                              text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                              icon: "info",
                              confirmButtonText: "ปิด",
                              confirmButtonColor: "#648d2f",
                            }).then((result) => {
                              location.reload();
                            });
                          }
                        }
                      );
                    } else {
                      appContext.sendApi(
                        process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                        formData,
                        (res_clear) => {
                          if (res_clear["status"] == "success") {
                            appContext.sendApi(
                              process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                              formData,
                              (data) => {
                                if (data["status"] == "success") {
                                  location.reload();
                                } else if (data["status"] == "limit") {
                                  appContext.setLoading(false);
                                  Swal.fire({
                                    text: "ขออภัยค่ะ คุณไม่สามารถต่ออายุเกิน 2 ปีได้",
                                    icon: "info",
                                    confirmButtonText: "ปิด",
                                    confirmButtonColor: "#648d2f",
                                  }).then((result) => {
                                    location.reload();
                                  });
                                } else if (data["status"] == "limit_buy") {
                                  appContext.setLoading(false);
                                  Swal.fire({
                                    text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                                    icon: "info",
                                    confirmButtonText: "ปิด",
                                    confirmButtonColor: "#648d2f",
                                  }).then((result) => {
                                    location.reload();
                                  });
                                }
                              }
                            );
                          }
                        }
                      );
                    }
                  }
                }
              );
            } else {
              const boxes = document.querySelectorAll(".remove_submit_btn");
              boxes.forEach((box) => {
                box.remove();
              });
              if (
                register_data["payment"] == 1 ||
                register_data["payment"] == 2
              ) {
                // เรียกใช้ submit2c2p จาก component
                payment2c2p.submit2c2p();
              } else {
                sentDataToAddOrder();
              }
            }
          });
        }
      } else {
        const boxes = document.querySelectorAll(".remove_submit_btn");
        boxes.forEach((box) => {
          box.remove();
        });
        if (register_data["payment"] == 1 || register_data["payment"] == 2) {
          // เรียกใช้ submit2c2p จาก component
          payment2c2p.submit2c2p();
        } else {
          sentDataToAddOrder();
        }
      }
    }
  }

  function sentDataToAddOrder() {
    const formData = new URLSearchParams();
    formData.append("tax_type", taxType);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/getCheckout",
      formData,
      (data) => {
        if (data["status"] != "success") {
          location.href = "/";
        }
        if (Number(data["total_price"]) == 0) {
          // location.href="/";
          setPayment(3);
          setLeftShow(false);
        }
        setCartData(data);
        setValue("cart_data", JSON.stringify(data));
        if (!appContext.isNull(appContext.getLocal("utm_source"))) {
          setValue("utm_source", appContext.getLocal("utm_source"));
        }
        let form_data = getValues();
        appContext.loadApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrder",
          form_data,
          (obj) => {
            if (obj["status"] == "success") {
              appContext.removeLocal("utm_source");
              appContext.removeLocal("utm_time");
              location.href = "/order/" + obj["order_id_encrypt"];
            }
          }
        );
      }
    );
  }

  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>

      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className={`container custom-container space-between-content`}>
          <div className="checkout-page">
            <div className="left">
              {leftShow ? (
                <>
                  <TaxInfoForm
                    user={user}
                    getReceipt={getReceipt}
                    setReceipt={setReceipt}
                    formType={formType}
                    setReceiptType={setReceiptType}
                    addressType={addressType}
                    companyType={companyType}
                    setCompany={setCompany}
                    setTax={setTax}
                    taxType={taxType}
                    provinceOptions={provinceOptions}
                    districtOptions={districtOptions}
                    subdistrictOptions={subdistrictOptions}
                    setReloadDistrict={setReloadDistrict}
                    setReloadSubDistrict={setReloadSubDistrict}
                    register={register}
                    getValues={getValues}
                    setValue={setValue}
                    appContext={appContext}
                    setAddress={setAddress}
                  />
                  
                  <PaymentOptions 
                    setPayment={setPayment}
                    selectedPayment={selectedPayment}
                    setValue={setValue}
                    getValues={getValues}
                  />
                  
                  {/* แทนที่ส่วนของฟอร์มส่งของขวัญด้วย GiftForm component */}
                  <GiftForm
                    giftType={giftType}
                    setGiftType={setGiftType}
                    register={register}
                    getValues={getValues}
                    setValue={setValue}
                    appContext={appContext}
                  />
                </>
              ) : (
                <GiftForm
                  giftType={giftType}
                  setGiftType={setGiftType}
                  register={register}
                  getValues={getValues}
                  setValue={setValue}
                  appContext={appContext}
                />
              )}
            </div>
            
            <OrderSummary
              cartData={cartData}
              appContext={appContext}
              addDiscountCode={addDiscountCode}
              dataSendComplete={dataSendComplete}
              leftShow={leftShow}
              onSubmit={onSubmit}
            />
          </div>
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
}

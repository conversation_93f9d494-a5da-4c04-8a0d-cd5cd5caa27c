import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "./api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import Head from "next/head";
import Header from "../themes/header/header";
import Footer from "../themes/footer/footer";
import Image from "next/image";

import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
  Accordion,
  Tab,
} from "semantic-ui-react";
import Swal from "sweetalert2";
import NumberFormat from "react-number-format";
export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const params = context.query;
  // if (!user) {
  //   return {
  //     redirect: { destination: "/" },
  //   };
  // }
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";


  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/user/pageSubscription",
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["status"] == "false") {
    return {
      redirect: { destination: "/" },
    };
  }

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/subscription",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: { seo_data, utoken, user, params , data},
  };
}
export default function Subscription({
  seo_data,
  utoken,
  user,
  params,
  data
}) {
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);
  function translateEng(_value) {
    if(lang=='en'){
      if(_value=='ปิด'){
        return "Close";
      }else if(_value=='พบข้อผิดพลาด กรุณาลองอีกครั้ง'){
        return "Found an error, please try again";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  function groupCategoryCallback(_type, _id ,_title) {
    console.log(_type);
    
    if (user) {
      if (_type == "subscription") {
        // const str="ขออภัย MDCU MedUMORE\n" +
        // "ปิดปรับปรุงระบบชำระเงินชั่วคราว\n"+
        // "ท่านจะได้รับการแจ้งเตือน\n"+
        // "เมื่อการดำเนินการเสร็จสิ้น";
        // Swal.fire({
        //   html: '<pre style="font-family: \'Kanit\'">' + str + '</pre>',
        //   icon: "warning",
        //   confirmButtonText: "ตกลง",
        //   confirmButtonColor: "#648d2f"
        // });
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "subscription");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
          formData,
          (res_check) => {
            if(res_check['status']=='success'){
              if(res_check['count']==0){
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                  formData,
                  (data) => {
                    if(data['status']=='success'){
                      appContext.setReloadCart(true);
                      appContext.setReloadCourse(true);
          
                      document
                        .getElementsByClassName("main-header")[0]
                        .classList.add("active_cart");
                      document
                        .getElementsByClassName("group-menu-cart")[0]
                        .classList.add("on_show");
                      document.body.classList.add("open_cart");
                      document
                        .getElementsByClassName("group-menu-f-mobile")[0]
                        .classList.remove("on_show");
                    }else if(data['status']=='limit'){
                      Swal.fire({
                        text: "ขออภัยค่ะ คุณไม่สามารถต่ออายุเกิน 2 ปีได้",
                        icon: "info",
                        confirmButtonText: "ปิด",
                        confirmButtonColor: "#648d2f"
                      });
                    }else if(data['status']=='limit_buy'){
                      Swal.fire({
                        text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                        icon: "info",
                        confirmButtonText: "ปิด",
                        confirmButtonColor: "#648d2f"
                      });
                    }
                  }
                );
              }else{
                Swal.fire({
                  html: "คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?",
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: 'ยืนยัน',
                  cancelButtonText: 'ยกเลิก'
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                      formData,
                      (res_clear) => {
                        if(res_clear['status']=='success'){
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                            formData,
                            (data) => {
                              if(data['status']=='success'){
                                appContext.setReloadCart(true);
                                appContext.setReloadCourse(true);
                    
                                document
                                  .getElementsByClassName("main-header")[0]
                                  .classList.add("active_cart");
                                document
                                  .getElementsByClassName("group-menu-cart")[0]
                                  .classList.add("on_show");
                                document.body.classList.add("open_cart");
                                document
                                  .getElementsByClassName("group-menu-f-mobile")[0]
                                  .classList.remove("on_show");
                              }else if(data['status']=='limit'){
                                Swal.fire({
                                  text: "ขออภัยค่ะ คุณไม่สามารถต่ออายุเกิน 2 ปีได้",
                                  icon: "info",
                                  confirmButtonText: "ปิด",
                                  confirmButtonColor: "#648d2f"
                                });
                              }else if(data['status']=='limit_buy'){
                                Swal.fire({
                                  text: "ขออภัยค่ะ คุณซื้อครบกำหนดแล้ว",
                                  icon: "info",
                                  confirmButtonText: "ปิด",
                                  confirmButtonColor: "#648d2f"
                                });
                              }
                            }
                          );
                        }
                      }
                    );
                  }
                });
              }
            }
          }
        );
      }else if (_type == "subscription_free") {
        const formData = new URLSearchParams();
        formData.append("subscription_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addSubscriptionFree",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              Swal.fire({
                text: "ลงทะเบียนสำเร็จ เริ่มเรียนได้เลย",
                icon: "success",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              }).then((result) => {
                if(lang=='en'){
                  location.href="/en/profile/yearly-member";
                }else{
                  location.href="/profile/yearly-member";
                }
              });
            } else if(obj["status"] == "not_expire"){
              Swal.fire({
                text: translateEng('ขออภัยคุณเป็น Yearly Member แล้ว'),
                icon: "info",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      }
    } else {
      Swal.fire({
        text: "กรุณาเข้าสู่ระบบค่ะ",
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  return ( 
      <div className="main-all page-faqs">
        <Head>
          {seo_data.seo ? (
            seo_data.seo.map((val, key) =>
              val.name == "title" ? (
                <>
                  <title>{val.content}</title>
                  <meta key={key} name={val.name} content={val.content} />
                  <meta
                    key={"og:" + key}
                    name={"og:" + val.name}
                    content={val.content}
                  />
                  <meta
                    key={"twitter:" + key}
                    name={"twitter:" + val.name}
                    content={val.content}
                  />
                </>
              ) : (
                <>
                  <meta key={key} name={val.name} content={val.content} />
                  <meta
                    key={"og:" + key}
                    name={"og:" + val.name}
                    content={val.content}
                  />
                  <meta
                    key={"twitter:" + key}
                    name={"twitter:" + val.name}
                    content={val.content}
                  />
                </>
              )
            )
          ) : (
            <>
              <title>MDCU : MedU MORE</title>
            </>
          )}

          <meta
            key={"twitter:card"}
            name={"twitter:card"}
            content="summary_large_image"
          />
          <meta key={"og:type"} name={"og:type"} content="website" />
        </Head>

        <Header></Header>
        <div className="main-body subscription-page">
          <div className="fix-space"></div>
          <div className="block-subscription-page">
            <div className="banner-subscription">
              <div className="banner-subscription_Img d-none d-xl-block">
                {data && data['data'] && data['data']['banner'] && data['data']['banner']!=null && data['data']['banner']!='' && data['data']['banner']!='null' ? (
                  <Image className="thumb" src={data['data']['banner']} alt="" layout="fill" objectFit="cover" objectPosition="center" />
                ):null}
              </div>
              <div className="banner-subscription_Img d-block d-xl-none">
                {data && data['data'] && data['data']['banner_m'] && data['data']['banner_m']!=null && data['data']['banner_m']!='' && data['data']['banner_m']!='null' ? (
                  <Image className="thumb" src={data['data']['banner_m']} alt="" layout="fill" objectFit="cover" objectPosition="center" />
                ):null}
              </div>
            </div>
            <div className="list-content-subscription">
              <div className="container custom-container">
                <div className="inner-list">
                  <div className="box-subscription" dangerouslySetInnerHTML={{__html: data['data']['left_details'],}}></div>
                  <div className="box-subscription active">
                    <h3>
                      {data['data']['title']}
                    </h3>
                    <div dangerouslySetInnerHTML={{__html: data['data']['details'],}}></div>
                    <div className="subscription-action">
                      {user && (user.user_type==1||user.user_type==3||user.user_type==4) ? (
                        user.internal_status==1||user.internal_status==2?(
                          <button className="btn-subscription-buy" onClick={() => groupCategoryCallback("subscription_free", data['data']['id'], "")}><span>ฟรี</span></button>
                        ):(
                          <button className="btn-subscription-buy" onClick={() => groupCategoryCallback("subscription", data['data']['id'], "")}><span>ซื้อแพ็คเกจ</span></button>
                        )
                      ):(
                        !user ? (
                          <button className="btn-subscription-buy" onClick={() => {Swal.fire({
                            text: "กรุณาเข้าสู่ระบบค่ะ",
                            icon: "info",
                            confirmButtonText: "ตกลง",
                            confirmButtonColor: "#648d2f"
                          }).then((result) => {
                            appContext.setOpen(true);
                          })}}><span>ซื้อแพ็คเกจ</span></button>
                        ):null
                        // <button className="btn-subscription-buy"><span>เร็ว ๆ นี้</span></button>
                      )}
                      <p>
                        {data['data']['remark']}
                      </p>
                    </div>
                  </div>
                  <div className="box-subscription" dangerouslySetInnerHTML={{__html: data['data']['right_details'],}}></div>
                </div>
                <div className="note-text" dangerouslySetInnerHTML={{__html: data['data']['bottom_details'],}}></div>
              </div>
            </div>
          </div>
        </div>
        <Footer></Footer>
      </div> 
  );
}

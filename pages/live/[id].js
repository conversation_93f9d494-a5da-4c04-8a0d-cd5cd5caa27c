import React from "react";
import Image from "next/image";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import Router, { useRouter } from "next/router";
import { getUser } from "../api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import { useForm } from "react-hook-form";
import Head from "next/head";
import Link from "next/link";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import CourseDetailIntro from "/themes/components/courseDetailIntro";
import CourseDetailDescription from "/themes/components/courseDetailDescription";
import CourseDetailPrice from "/themes/components/courseDetailPrice";
import CourseDetailCode from "/themes/components/courseDetailCode";
import CourseDetailScholarship from "/themes/components/courseDetailScholarship";
import GroupCategory from "/themes/components/groupCategory";
import CommentZone from "/themes/components/commentZone";
import ListVdoModal from "/themes/components/listVdoModal";
import GroupAudioList from "/themes/components/groupAudioList";
import ListVdoEp from "/themes/components/listVdoEp";
import VdoModal from "/themes/components/vdoModal";
import CourseBanner from "/themes/components/courseBanner";
import EbookBanner from "/themes/components/ebookBanner";
import RankingViewHistory from "/themes/components/rankingViewHistory";
import ReactPlayer from "react-player";
import NumberFormat from "react-number-format";
import 'bootstrap/dist/css/bootstrap.css'
import ListTags from "/themes/components/listTagsCourse";
import axios from 'axios';
import { IoMdDownload } from "react-icons/io";
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import { Pie } from "react-chartjs-2";
ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

import {
  Menu,
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
  Accordion,
  Tab,
  Popup,
} from "semantic-ui-react";
import "semantic-ui-css/semantic.min.css";
import stylesModal from "/public/assets/css/component/listQuizModal.module.css";
import styles from "/public/assets/css/pages/course.module.css";
import Swal from "sweetalert2";

import moment from "moment";
// import { gsap } from "gsap";
// import { ScrollTrigger } from "gsap/dist/ScrollTrigger";

let isStampLive = false;
let isStartZoom = false;
let isEndZoom = false;
export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/course/get/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();
  if (data["status"] == "false" || data["data"]["type"] != "live") {
    return {
      redirect: { destination: "/" },
    };
  }

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/course/" + params.id,
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  //View Log
  const formDataView = new URLSearchParams();
  formDataView.append("utoken", utoken);
  formDataView.append("course_id", data.data.id);

  const res_view = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseView",
    {
      body: formDataView,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCodeView = res_view.ok ? false : res_view.statusCode;
  const data_view = await res_view.json();
  //View Log

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      utoken,
      user
    },
  };
}

export default function Course({
  seo_data,
  errorCode,
  data,
  params,
  utoken,
  user
}) {
  // console.log("---Course---");
  // console.log(data);
  // console.log("***Course***");
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  // section zoom
    const authEndpoint = '/api/zoom-auth';
    const sdkKey = process.env.NEXT_PUBLIC_ZOOM_ACCOUNT_ID;
    const role = 0;
    const registrantToken = ''; //optional
    const zakToken = ''; //optional

    const leaveUrl = !process.env.NEXT_PUBLIC_APPDOMAIN||(process.env.NEXT_PUBLIC_APPDOMAIN&&process.env.NEXT_PUBLIC_APPDOMAIN!='https://www.medumore.org')?'https://uat.medumore.org':'https://www.medumore.org';
    const meetingNumber = data.data.webminar_id;
    const passWord = data.data.zoom_password; //พาสของ meetingNumber
    const userName = user&&user.name&&user.name!=null&&user.name!=''?user.name:'';
    const userEmail = user&&user.email&&user.email!=null&&user.email!=''?user.email:''; //optional

    const [signature, setSignature] = useState('');
    const zmmtgRootRef = useRef(null);
    const [componentClient, setComponentClient] = useState(null);
    const [zoomPlayed, setZoomPlayed] = useState(false);
    const getReact = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/3.11.2/lib/vendor/react.min.js";
      script.onload = () => getReactDom();
      document.body.appendChild(script);
    };
    const getReactDom = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/3.11.2/lib/vendor/react-dom.min.js";
      script.onload = () => getRedux();
      document.body.appendChild(script);
    };
    const getRedux = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/3.11.2/lib/vendor/redux.min.js";
      script.onload = () => getReduxThunk();
      document.body.appendChild(script);
    };
    const getReduxThunk = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/3.11.2/lib/vendor/redux-thunk.min.js";
      script.onload = () => getLodash();
      document.body.appendChild(script);
    };
    const getLodash = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/3.11.2/lib/vendor/lodash.min.js";
      script.onload = () => startApp();
      document.body.appendChild(script);
    };
    const startApp = async () =>{
      loadSignature();
      getClientView();
      // getComponentView();
    }
    const loadSignature = async () =>{
      let payload = Router.query;
      payload.meetingNumber = meetingNumber;
      payload.role = role;
      const { data } = await axios({
        url: authEndpoint,
        method: "post",
        data: payload,
      })
        .then((response) => {
          return response;
        })
        .catch((error) => {
          return error;
        });
      if (data) {
        setSignature(data.signature);
      }
    }
    {/* For Client View */}
    const getClientView = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/zoom-meeting-3.11.2.min.js";
      script.onload = () => setupClientView();
      document.body.appendChild(script);
    };
    const setupClientView = async () => {
      window.ZoomMtg.setZoomJSLib('https://source.zoom.us/3.11.2/lib', '/av')

      window.ZoomMtg.preLoadWasm()
      window.ZoomMtg.prepareWebSDK()
      // loads language files, also passes any error messages to the ui
      window.ZoomMtg.i18n.load('en-US')
      window.ZoomMtg.i18n.reload('en-US')
    };
    const startMeetingClientView = () => {
      // console.log('startMeeting')
      if (zmmtgRootRef.current) {
        zmmtgRootRef.current.style.display = 'block';
      }
      window.ZoomMtg.init({
        leaveUrl: leaveUrl,
        disablePreview: true,
        success: (success) => {
          // console.log(success)
          window.ZoomMtg.join({
            signature: signature,
            sdkKey: sdkKey,
            meetingNumber: meetingNumber,
            passWord: passWord,
            userName: userName,
            userEmail: userEmail,
            tk: registrantToken,
            zak: zakToken,
            success: (success) => {
              console.log(success)
            },
            error: (error) => {
              console.log(error)
            },
          })
        },
        error: (error) => {
          console.log(error)
        }
      });
      isStampLive = true;
    }; 
    
    const [counter, setCounter] = useState(0);

    // This function will be called every 3 seconds
    const stampLive = () => {
      setCounter(prevCounter => prevCounter + 1);
      // Add your logic here
      // console.log(isStampLive);
      if(isStampLive && (new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && data["data"]["allowed"])){
        const formData = new URLSearchParams();
        formData.append("course_id", data["data"]["id"]);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addLiveStamp",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              console.log('stampLive');
            }
          }
        );
      }
    };

    useEffect(() => {
      const intervalId = setInterval(stampLive, 3000);

      // Clear the interval when the component unmounts
      return () => {
        clearInterval(intervalId);
      };
    }, []);
    {/* For Client View */}
    {/* For Component View */}
    const getComponentView = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/3.11.2/zoom-meeting-embedded-3.11.2.min.js";
      script.onload = () => setupComponentView();
      document.body.appendChild(script);
    };
    const setupComponentView = async () => {
      let meetingSDKElement = document.getElementById('meetingSDKElement')
      const client = window.ZoomMtgEmbedded.createClient();
      client.init({
        zoomAppRoot: meetingSDKElement,
        language: 'en-US',
        customize: {
          video: {
            defaultViewType:'speaker'
          }
        }
      })
      setComponentClient(client);
    };
    const startMeetingComponentView = () => {
      // console.log('signature:'+signature);
      // console.log('sdkKey:'+sdkKey);
      // console.log('meetingNumber:'+meetingNumber);
      // console.log('passWord:'+passWord);
      // console.log('userName:'+userName);
      // console.log('userEmail:'+userEmail);
      componentClient.join({
        signature: signature,
        sdkKey: sdkKey,
        meetingNumber: meetingNumber,
        password: passWord,
        userName: userName,
        userEmail: userEmail,
        tk: registrantToken,
        zak: zakToken
      })
    }; 
    useEffect(() => {
      if(!zoomPlayed && signature!=null && signature!=''){
        setZoomPlayed(true);
        setTimeout(() => {
          
          startMeetingClientView();
          var mainMenuEl = document.getElementById("header_position");
          mainMenuEl.classList.add("hide_menu_bar");
          // leave-meeting-options__btn--default
        }, 500);
      }
      // if(componentClient!=null && !zoomPlayed && signature!=null && signature!=''){
      //   setZoomPlayed(true);
      //   startMeetingComponentView();
      // }
    }, [componentClient,signature]);
    {/* For Component View */}
    // Load Zoom SDK Flow
  // section zoom

  const [firstLoad, setFirstLoad] = useState(true);
  useEffect(() => {
    if (firstLoad) {
      setFirstLoad(false);
      if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && data["data"]["allowed"] && !isStartZoom){
        isStartZoom = true;
        getReact();
      }
    }
  }, [firstLoad]);
  
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [chatStatus, setChatStatus] = useState(true);
  const [relateData, setRelateData] = useState(null);
  const [reloadRelate, setReloadRelate] = useState(true);
  const [discountCode, setDiscountCode] = useState("");
  const [discountChannel, setDiscountChannel] = useState("");
  const [courseProPrice, setCourseProPrice] = useState(
    data["data"]["pro_price"]
  );
  const [coursePrice, setCoursePrice] = useState(data["data"]["price"]);
  const [coursePriceReload, setCoursePriceReload] = useState(true);
  const [reloadStar, setReloadStar] = useState(true);

  const [errorArray, setErrorArray] = useState([]);
  const [reloadVdo, setReloadVdo] = useState(true);
  const [vdoList, setVdoList] = useState(null);
  const [vdoListAll, setVdoListAll] = useState(null);
  const [courseId, setCourseId] = useState(null);

  
  const [reloadComment, setReloadComment] = useState(true);
  const [commentData, setCommentData] = useState(null);
  const [commentInput, setCommentInput] = useState('');

  useEffect(() => {
    if (reloadComment) {
      setReloadComment(false);
      appContext.loadApiNoLoader(
        process.env.NEXT_PUBLIC_API +
          "/api/mdcu/course/comment/" +
          data.data.id,
        null,
        (res_data) => {
          setCommentData(res_data);
          // setTimeout(() => {
          //   if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date()){
          //     var objDiv = document.getElementById("chat_center_box");
          //     objDiv.scrollTop = objDiv.scrollHeight;
          //   }
          // }, 50);
        }
      );
    }
  }, [reloadComment]);

  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date()){
  //       setReloadComment(true);
  //     }
  //   }, 5000);
  //   return () => clearInterval(interval);
  // }, []);

  
  const [countdownDay, setCountdownDay] = React.useState("00");
  const [countdownHour, setCountdownHour] = React.useState("00");
  const [countdownMin, setCountdownMin] = useState('00');
  const [countdownSec, setCountdownSec] = useState('00');
  useEffect(() => {
    const interval_cd = setInterval(() => {
      if(new Date(data.data.started_time) >= new Date()){
        var now = new Date().getTime();
        var distance = new Date(data.data.started_time).getTime() - now;
        var days = Math.floor(distance / (1000 * 60 * 60 * 24));
        var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        var seconds = Math.floor((distance % (1000 * 60)) / 1000);

        if(days<10){
          days = '0'+days;
        }
        if(hours<10){
          hours = '0'+hours;
        }
        if(minutes<10){
          minutes = '0'+minutes;
        }
        if(seconds<10){
          seconds = '0'+seconds;
        }
        setCountdownDay(days);
        setCountdownHour(hours);
        setCountdownMin(minutes);
        setCountdownSec(seconds);
        if(hours=='00'&&minutes=='00'&&seconds=='00'&&!isStartZoom){
          isStartZoom = true;
          getReact();
        }
      }
      if(new Date(data.data.end_time) <= new Date() && !isEndZoom && isStartZoom){
        isEndZoom = true;
        location.reload();
      }
    }, 1000);
    return () => clearInterval(interval_cd);
  }, []);

  function addLike(_comment_id) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("comment_id", _comment_id);
      setReloadComment(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addLike",
        formData,
        (obj) => {
          // console.log(obj);
          setReloadComment(false);
          setTimeout(() => {
            setReloadComment(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }

  function submitComment() {
    if(!appContext.isNull(commentInput)){
      addComment(commentInput)
    }else{
      Swal.fire({
        text: translateEng('กรุณากรอกความคิดเห็น'),
        icon: 'error',
        confirmButtonText: translateEng('ปิด'),
        confirmButtonColor: "#648d2f"
      })
    }
    setCommentInput('');
  }

  function addComment(_comment) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("comment", _comment);
      formData.append("course_id", data["data"]["id"]);
      formData.append("content_type", "course");
      setReloadComment(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addComment",
        formData,
        (obj) => {
          // console.log(obj);
          setReloadComment(false);
          setTimeout(() => {
            setReloadComment(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }

  const schemaData = {
    "@context": "https://schema.org/",
    "@type": "Course",
    "name": data["data"]["title"],
    "image": data["data"]["image"],
    "description": data["data"]["subtitle_th"],
    "brand": "MedUMore",
    "provider": {
      "@type": "Person",
      "name": data["data"]["speaker_name"]
    },
    "publisher": {
      "@type": "Organization",
      "name": "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย",
      "logo": "https://www.md.chula.ac.th/wp-content/uploads/2016/02/MDCU-Logo-300x300.jpg",
      "url": "https://www.md.chula.ac.th/"
    },
    "author": {
      "@type": "Organization",
      "logo": "https://www.md.chula.ac.th/wp-content/uploads/2016/02/MDCU-Logo-300x300.jpg",
      "name": "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย",
      "url": "https://www.md.chula.ac.th/"
    }
  };
  useEffect(() => {
    if (reloadRelate) {
      setReloadRelate(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/mdcu/course/relate/" +
          data.data.id +
          "/" +
          data.data.type,
        null,
        (res_relate) => {
          if(res_relate['status']=='success'){
            setRelateData(res_relate.data);
            // console.log(res_relate)
          }
        }
      );
    }
  }, [reloadRelate]);
  useEffect(() => {
    if (reloadVdo) {
      setReloadVdo(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/course/vdo/" + data.data.id,
        null,
        (data) => {
          setVdoList(data["data"]);
          setVdoListAll(data);
        }
      );
    }
  }, [reloadVdo]);

  const checkMyError = (_val) => {
    if (errorArray.indexOf(_val) > -1) {
      return true;
    }
    return false;
  };

  function courseDescription(_value) {
    const formData = new URLSearchParams();
    formData.append("course_id", data["data"]["id"]);
    formData.append("rate", _value);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseRate",
      formData,
      (obj) => {
        if (obj["status"] == "success") {
          setReloadStar(false);
          data["data"]["rate"] = obj["rate"];
          data["data"]["rating"] = obj["rating"];
          setTimeout(() => {
            setReloadStar(true);
          }, "0");
          forceUpdate();
        }
      }
    );
  }
  function checkFree(){
    if((data.data.price == 0 || (data.data.is_promotion == 1 && data.data.pro_price == 0) || (data.data.is_internal&&data.data.user_internal) || data.data.is_subscription || data.data.is_volume)&&discountCode==''){
      return true;
    }else{
      return false;
    }
  }

  appContext.setToken(utoken);

  if (errorCode) {
    return <Error statusCode={errorCode} />;
  }
  function groupCategoryCallback(_type, _id, _title) {
    if (user) {
      if(_type == 'favourite'){
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "course");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addFavourite",
          formData,
          (res_fav) => {
            // console.log(data);
            // setReloadRelate(true);
            if(res_fav['status']=='success'){
              if(res_fav['action']=='add'){
                document.querySelector(".favourite_class_"+_id).classList.add("active");
              }else{
                document.querySelector(".favourite_class_"+_id).classList.remove("active");
              }
            }
          }
        );
      }else{
        const formCheck = new URLSearchParams();
        formCheck.append("course_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkLive",
          formCheck,
          (res_live) => {
            if(res_live['limit']==0||(res_live['limit']!=0 && res_live['remain']!=0)){
              if(res_live['limit']==0){
                if (_type == "cart") {
                  const formData = new URLSearchParams();
                  formData.append("course_id", _id);
                  formData.append("content_type", "course");
                  formData.append("discount_code", discountCode);
                  formData.append("discount_channel", discountChannel);
                  appContext.sendApi(
                    process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
                    formData,
                    (res_check) => {
                      if(res_check['status']=='success'){
                        if(res_check['count']==0){
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                            formData,
                            (data) => {
                              // console.log(data);
                              appContext.setReloadCart(true);
                              setReloadRelate(true);
                  
                              document
                                .getElementsByClassName("main-header")[0]
                                .classList.add("active_cart");
                              document
                                .getElementsByClassName("group-menu-cart")[0]
                                .classList.add("on_show");
                              document.body.classList.add("open_cart");
                              document
                                .getElementsByClassName("group-menu-f-mobile")[0]
                                .classList.remove("on_show");
                            }
                          );
                        }else{
                          Swal.fire({
                            html: translateEng('คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'),
                            icon: "info",
                            showCancelButton: true,
                            confirmButtonColor: '#648d2f',
                            cancelButtonColor: '#d33',
                            confirmButtonText: translateEng('ยืนยัน'),
                            cancelButtonText: translateEng('ยกเลิก')
                          }).then((result) => {
                            if (result.value) {
                              appContext.sendApi(
                                process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                                formData,
                                (res_clear) => {
                                  if(res_clear['status']=='success'){
                                    appContext.sendApi(
                                      process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                                      formData,
                                      (data) => {
                                        // console.log(data);
                                        appContext.setReloadCart(true);
                                        setReloadRelate(true);
                            
                                        document
                                          .getElementsByClassName("main-header")[0]
                                          .classList.add("active_cart");
                                        document
                                          .getElementsByClassName("group-menu-cart")[0]
                                          .classList.add("on_show");
                                        document.body.classList.add("open_cart");
                                        document
                                          .getElementsByClassName("group-menu-f-mobile")[0]
                                          .classList.remove("on_show");
                                      }
                                    );
                                  }
                                }
                              );
                            }
                          });
                        }
                      }
                    }
                  );
                } else if (_type == "free") {
                  const formData = new URLSearchParams();
                  formData.append("course_id", _id);
                  appContext.sendApi(
                    process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderFree",
                    formData,
                    (obj) => {
                      if (obj["status"] == "success") {
                        data["data"]["allowed"] = true;
                        if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && !isStartZoom){
                          isStartZoom = true;
                          getReact();
                        }
                        setReloadVdo(true);
                      } else {
                        Swal.fire({
                          text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                          icon: "error",
                          confirmButtonText: translateEng('ปิด'),
                          confirmButtonColor: "#648d2f"
                        });
                      }
                    }
                  );
                } else if (_type == "vip") {
                  const formData = new URLSearchParams();
                  formData.append("course_id", _id);
                  appContext.sendApi(
                    process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderVip",
                    formData,
                    (obj) => {
                      if (obj["status"] == "success") {
                        data["data"]["allowed"] = true;
                        if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && !isStartZoom){
                          isStartZoom = true;
                          getReact();
                        }
                        setReloadVdo(true);
                      } else {
                        Swal.fire({
                          text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                          icon: "error",
                          confirmButtonText: translateEng('ปิด'),
                          confirmButtonColor: "#648d2f"
                        });
                      }
                    }
                  );
                } else if (_type == "volume") {
                  const formData = new URLSearchParams();
                  formData.append("course_id", _id);
                  appContext.sendApi(
                    process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderVolume",
                    formData,
                    (obj) => {
                      if (obj["status"] == "success") {
                        data["data"]["allowed"] = true;
                        if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && !isStartZoom){
                          isStartZoom = true;
                          getReact();
                        }
                        setReloadVdo(true);
                      } else {
                        Swal.fire({
                          text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                          icon: "error",
                          confirmButtonText: translateEng('ปิด'),
                          confirmButtonColor: "#648d2f"
                        });
                      }
                    }
                  );
                } else if (_type == "internal") {
                  const formData = new URLSearchParams();
                  formData.append("course_id", _id);
                  appContext.sendApi(
                    process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderInternal",
                    formData,
                    (obj) => {
                      if (obj["status"] == "success") {
                        data["data"]["allowed"] = true;
                        if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && !isStartZoom){
                          isStartZoom = true;
                          getReact();
                        }
                        setReloadVdo(true);
                      } else {
                        Swal.fire({
                          text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                          icon: "error",
                          confirmButtonText: translateEng('ปิด'),
                          confirmButtonColor: "#648d2f"
                        });
                      }
                    }
                  );
                } 
              }else{
                // html: "กรุณากดยืนยันเพื่อลงทะเบียน<br>Live Streaming<br>จำนวนผู้ลงทะเบียน "+(res_live['limit']-res_live['remain'])+'/'+res_live['limit'],
                Swal.fire({
                  html: "กรุณากดยืนยันเพื่อลงทะเบียน<br>Live Streaming",
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: translateEng('ยืนยัน'),
                  cancelButtonText: translateEng('ยกเลิก')
                }).then((result) => {
                  if (result.value) {
                    if (_type == "cart") {
                      const formData = new URLSearchParams();
                      formData.append("course_id", _id);
                      formData.append("content_type", "course");
                      formData.append("discount_code", discountCode);
                      formData.append("discount_channel", discountChannel);
                      appContext.sendApi(
                        process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
                        formData,
                        (res_check) => {
                          if(res_check['status']=='success'){
                            if(res_check['count']==0){
                              appContext.sendApi(
                                process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                                formData,
                                (data) => {
                                  // console.log(data);
                                  appContext.setReloadCart(true);
                                  setReloadRelate(true);
                      
                                  document
                                    .getElementsByClassName("main-header")[0]
                                    .classList.add("active_cart");
                                  document
                                    .getElementsByClassName("group-menu-cart")[0]
                                    .classList.add("on_show");
                                  document.body.classList.add("open_cart");
                                  document
                                    .getElementsByClassName("group-menu-f-mobile")[0]
                                    .classList.remove("on_show");
                                }
                              );
                            }else{
                              Swal.fire({
                                html: translateEng('คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'),
                                icon: "info",
                                showCancelButton: true,
                                confirmButtonColor: '#648d2f',
                                cancelButtonColor: '#d33',
                                confirmButtonText: translateEng('ยืนยัน'),
                                cancelButtonText: translateEng('ยกเลิก')
                              }).then((result) => {
                                if (result.value) {
                                  appContext.sendApi(
                                    process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                                    formData,
                                    (res_clear) => {
                                      if(res_clear['status']=='success'){
                                        appContext.sendApi(
                                          process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                                          formData,
                                          (data) => {
                                            // console.log(data);
                                            appContext.setReloadCart(true);
                                            setReloadRelate(true);
                                
                                            document
                                              .getElementsByClassName("main-header")[0]
                                              .classList.add("active_cart");
                                            document
                                              .getElementsByClassName("group-menu-cart")[0]
                                              .classList.add("on_show");
                                            document.body.classList.add("open_cart");
                                            document
                                              .getElementsByClassName("group-menu-f-mobile")[0]
                                              .classList.remove("on_show");
                                          }
                                        );
                                      }
                                    }
                                  );
                                }
                              });
                            }
                          }
                        }
                      );
                    } else if (_type == "free") {
                      const formData = new URLSearchParams();
                      formData.append("course_id", _id);
                      appContext.sendApi(
                        process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderFree",
                        formData,
                        (obj) => {
                          if (obj["status"] == "success") {
                            data["data"]["allowed"] = true;
                            if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && !isStartZoom){
                              isStartZoom = true;
                              getReact();
                            }
                            setReloadVdo(true);
                          } else {
                            Swal.fire({
                              text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                              icon: "error",
                              confirmButtonText: translateEng('ปิด'),
                              confirmButtonColor: "#648d2f"
                            });
                          }
                        }
                      );
                    } else if (_type == "vip") {
                      const formData = new URLSearchParams();
                      formData.append("course_id", _id);
                      appContext.sendApi(
                        process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderVip",
                        formData,
                        (obj) => {
                          if (obj["status"] == "success") {
                            data["data"]["allowed"] = true;
                            if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && !isStartZoom){
                              isStartZoom = true;
                              getReact();
                            }
                            setReloadVdo(true);
                          } else {
                            Swal.fire({
                              text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                              icon: "error",
                              confirmButtonText: translateEng('ปิด'),
                              confirmButtonColor: "#648d2f"
                            });
                          }
                        }
                      );
                    } else if (_type == "volume") {
                      const formData = new URLSearchParams();
                      formData.append("course_id", _id);
                      appContext.sendApi(
                        process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderVolume",
                        formData,
                        (obj) => {
                          if (obj["status"] == "success") {
                            data["data"]["allowed"] = true;
                            if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && !isStartZoom){
                              isStartZoom = true;
                              getReact();
                            }
                            setReloadVdo(true);
                          } else {
                            Swal.fire({
                              text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                              icon: "error",
                              confirmButtonText: translateEng('ปิด'),
                              confirmButtonColor: "#648d2f"
                            });
                          }
                        }
                      );
                    } else if (_type == "internal") {
                      const formData = new URLSearchParams();
                      formData.append("course_id", _id);
                      appContext.sendApi(
                        process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderInternal",
                        formData,
                        (obj) => {
                          if (obj["status"] == "success") {
                            data["data"]["allowed"] = true;
                            if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && !isStartZoom){
                              isStartZoom = true;
                              getReact();
                            }
                            setReloadVdo(true);
                          } else {
                            Swal.fire({
                              text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                              icon: "error",
                              confirmButtonText: translateEng('ปิด'),
                              confirmButtonColor: "#648d2f"
                            });
                          }
                        }
                      );
                    } 
                  }
                });
              }
            }else{
              Swal.fire({
                html: "Live Streaming<br>มีผู้ลงทะเบียนครบตามจำนวนแล้ว",
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      }
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function addDiscountCode(_code, _type) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("course_id", data["data"]["id"]);
      formData.append("content_type", "course");
      formData.append("discount_code", _code);
      formData.append("discount_channel", _type);
      setCoursePriceReload(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addDiscountCode",
        formData,
        (obj) => {
          // console.log(obj);
          setCoursePriceReload(false);
          if (obj["is_discount"]) {
            if (_type == "code") {
              Swal.fire({
                text: translateEng('ยินดีด้วย ใช้โค้ดส่วนลดสำเร็จ'),
                icon: "success",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: translateEng('ยินดีด้วย คุณได้รับการสนับสนุน'),
                icon: "success",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
              document.getElementById("input_code").value = _code;
            }
            if (data["data"]["is_promotion"] == 1) {
              if (obj["discount_type"] == "regular") {
                data["data"]["pro_price"] =
                  courseProPrice - obj["discount_value"];
              } else {
                data["data"]["pro_price"] =
                  courseProPrice - (courseProPrice * obj["discount_value"]) / 100;
              }
              if(data["data"]["pro_price"]<0){
                data["data"]["pro_price"] = 0;
              }
            } else {
              if (obj["discount_type"] == "regular") {
                data["data"]["price"] = coursePrice - obj["discount_value"];
              } else {
                data["data"]["price"] =
                  coursePrice - (coursePrice * obj["discount_value"]) / 100;
              }
              if(data["data"]["price"]<0){
                data["data"]["price"] = 0;
              }
            }
          } else {
            if (_type == "code") {
              Swal.fire({
                text: translateEng('ขออภัย ไม่พบโค้ดส่วนลด'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: translateEng('ขออภัย ไม่พบผู้สนับสนุน'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
            if (data["data"]["is_promotion"] == 1) {
              data["data"]["pro_price"] = courseProPrice;
            } else {
              data["data"]["price"] = coursePrice;
            }
          }
          setDiscountChannel(obj["discount_channel"]);
          setDiscountCode(obj["discount_code"]);
          setTimeout(() => {
            setCoursePriceReload(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }

  function translateEng(_value) {
    if(lang=='en'){
      if(_value=='ฟรี'){
        return "Free";
      }else if(_value=='บาท'){
        return "Baht";
      }else if(_value=='อ่าน'){
        return "Read";
      }else if(_value=='คะแนน'){
        return "Points";
      }else if(_value=='ปกติ'){
        return "Normal";
      }else if(_value=='ราคา'){
        return "Price";
      }else if(_value=='สถิติ'){
        return "Statistics";
      }else if(_value=='ภาพรวมการทำแบบทดสอบ'){
        return "Examination overview";
      }else if(_value=='แบบทดสอบทั้งหมด'){
        return "All Examinations";
      }else if(_value=='ชุด'){
        return "Sets";
      }else if(_value=='ทำแล้ว'){
        return "Done";
      }else if(_value=='ผ่าน'){
        return "Pass";
      }else if(_value=='ไม่ผ่าน'){
        return "Fail";
      }else if(_value=='ยังไม่ได้ทำ'){
        return "Not yet";
      }else if(_value=='เฉลี่ยคะแนนที่ทำได้'){
        return "Average score";
      }else if(_value=='คะแนนสูงสุด'){
        return "Highest score";
      }else if(_value=='คะแนนที่ทำได้'){
        return "Score";
      }else if(_value=='คะแนนต่ำสุด'){
        return "Lowest score";
      }else if(_value=='ผลคะแนน'){
        return "Result";
      }else if(_value=='อันดับของคุณ จากผู้ทำแบบทดสอบทั้งหมด'){
        return "Your ranking";
      }else if(_value=='อันดับ'){
        return "Ranking";
      }else if(_value=='ชื่อ'){
        return "Name";
      }else if(_value=='วันที่ทำ'){
        return "Date";
      }else if(_value=='วันที่'){
        return "Date";
      }else if(_value=='รอการตรวจสอบ'){
        return "Pending";
      }else if(_value=='ประวัติการทำแบบทดสอบหลังเรียน'){
        return "Post-Examination history";
      }else if(_value=='ประวัติการทำแบบทดสอบก่อนเรียน'){
        return "Pre-Examination history";
      }else if(_value=='ทดสอบครั้งที่'){
        return "Round";
      }else if(_value=='ชื่อแบบทดสอบ'){
        return "Examination Title";
      }else if(_value=='สถานะ'){
        return "Status";
      }else if(_value=='ผลการทดสอบ'){
        return "Result";
      }else if(_value=='ตรวจแล้ว'){
        return "Checked";
      }else if(_value=='ส่งคำตอบสำเร็จ'){
        return "Submitted";
      }else if(_value=='ปิด'){
        return "Close";
      }else if(_value=='กรุณาตอบคำถามให้ครบถ้วน'){
        return "Please answer questions completely";
      }else if(_value=='คุณต้องการเริ่มเรียนใช่หรือไม่?'){
        return "Do you want to start studying?";
      }else if(_value=='คุณต้องการรับ E-Book ฟรีใช่หรือไม่?'){
        return "Do you want to get a free E-Book?";
      }else if(_value=='ใช่'){
        return "Yes";
      }else if(_value=='ไม่'){
        return "No";
      }else if(_value=='กรุณาซื้อคอร์สนี้ก่อนค่ะ'){
        return "Please buy this course";
      }else if(_value=='กรุณาซื้อ E-Book นี้ก่อนค่ะ'){
        return "Please buy this E-Book";
      }else if(_value=='คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'){
        return "You have another item in your cart<br>Want to buy this product?";
      }else if(_value=='ยืนยัน'){
        return "Submit";
      }else if(_value=='ยกเลิก'){
        return "Cancel";
      }else if(_value=='พบข้อผิดพลาด กรุณาลองอีกครั้ง'){
        return "Found an error, please try again";
      }else if(_value=='กรุณาเข้าสู่ระบบค่ะ'){
        return "Please login";
      }else if(_value=='กรุณาเลือกเพลย์ลิสต์'){
        return "Choose playlist";
      }else if(_value=='ยินดีด้วย ใช้โค้ดส่วนลดสำเร็จ'){
        return "Congratulations, the discount code has been used successfully";
      }else if(_value=='ยินดีด้วย คุณได้รับการสนับสนุน'){
        return "Congratulations, you are supported";
      }else if(_value=='ขออภัย ไม่พบโค้ดส่วนลด'){
        return "Sorry, the discount code could not be found";
      }else if(_value=='ขออภัย ไม่พบผู้สนับสนุน'){
        return "Sorry, the supporter could not be found";
      }else if(_value=='บทเรียนทั้งหมด'){
        return "All lessons";
      }else if(_value=='เอกสารประกอบการเรียน'){
        return "Documents";
      }else if(_value=='ดาวน์โหลดไฟล์ในบทเรียน'){
        return "Download";
      }else if(_value=='รายละเอียด'){
        return "Details";
      }else if(_value=='ถาม-ตอบ'){
        return "Q & A";
      }else if(_value=='แบบทดสอบ'){
        return "Examination";
      }else if(_value=='คะแนนล่าสุดของคุณ'){
        return "Recently score";
      }else if(_value=='เริ่มใหม่'){
        return "Restart";
      }else if(_value=='แบบทดสอบของคุณอยู่ระหว่างตรวจสอบ'){
        return "Your test is under review";
      }else if(_value=='โค้ดส่วนลด'){
        return "Discount code";
      }else if(_value=='เลือกรับ Scholarship จากผู้สนับสนุน'){
        return "Select scholarship";
      }else if(_value=='เพิ่มไปยังเพลย์ลิสต์ของคุณ'){
        return "Add to playlist";
      }else if(_value=='สร้างเพลย์ลิสต์'){
        return "Create playlist";
      }else if(_value=='เพลย์ลิสต์ของคุณ'){
        return "Your playlist";
      }else if(_value=='ป้อนชื่อเพลย์ลิสต์'){
        return "Enter plalist title";
      }else if(_value=='เริ่มเรียน'){
        return "Start learning";
      }else if(_value=='รับชม'){
        return "Get Free";
      }else if(_value=='ซื้อคอร์สนี้'){
        return "Buy this course";
      }else if(_value=='ซื้อ E-Book'){
        return "Buy E-Book";
      }else if(_value=='คอร์สเกี่ยวข้อง'){
        return "Related course";
      }else if(_value=='E-Book ที่เกี่ยวข้อง'){
        return "Related E-Book";
      }else if(_value=='ข้อที่'){
        return "No.";
      }else if(_value=='ส่งคำตอบ'){
        return "Submit answer";
      }else if(_value=='ข้าม'){
        return "Skip";
      }else if(_value=='ต่อไป'){
        return "Next";
      }else if(_value=='แบบประเมินความพึงพอใจ (ใช้เวลาไม่ถึง 1 นาที)'){
        return "Assessment (It takes less than 1 minute.)";
      }else if(_value=='คำตอบ'){
        return "Answer";
      }else if(_value=='คำตอบที่ถูก'){
        return "Correct";
      }else if(_value=='คำอธิบาย'){
        return "Explain";
      }else if(_value=='ดูคำตอบ'){
        return "View";
      }else if(_value=='ยินดีด้วย!'){
        return "Congratulations!";
      }else if(_value=='คุณปลดล็อกใบประกาศ'){
        return "You unlocked the certificate";
      }else if(_value=='สามารถดาวน์โหลดใบประกาศได้ที่โปรไฟล์ของท่าน'){
        return "Certificate can be download at your profile";
      }else if(_value=='หรือคลิกที่นี่'){
        return "Or click here";
      }else if(_value=='คอร์ส'){
        return "Course";
      }else if(_value=='ดาวน์โหลด'){
        return "Download";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  
  return (
    <>
      <div className={`main-all page-course page-live`}>
        <Head>
          {seo_data.seo ? (
            seo_data.seo.map((val, key) =>
              val.name == "title" ? (
                <>
                  <title>{val.content}</title>
                  <meta key={key} name={val.name} content={val.content} />
                  <meta
                    key={"og:" + key}
                    name={"og:" + val.name}
                    content={val.content}
                  />
                  <meta
                    key={"twitter:" + key}
                    name={"twitter:" + val.name}
                    content={val.content}
                  />
                </>
              ) : (
                <>
                  <meta key={key} name={val.name} content={val.content} />
                  <meta
                    key={"og:" + key}
                    name={"og:" + val.name}
                    content={val.content}
                  />
                  <meta
                    key={"twitter:" + key}
                    name={"twitter:" + val.name}
                    content={val.content}
                  />
                </>
              )
            )
          ) : (
            <>
              <title>MDCU : MedU MORE</title>
            </>
          )}
          <meta httpEquiv="origin-trial" content="AjNmBPFbjjAEVRsfmqv9wCWO4/0XlDqB6UmyHgjY1EXH3NGul9VkdwvX/FQtFqeX2f7JMH18F639zJhr2NsX/AoAAAB6eyJvcmlnaW4iOiJodHRwczovL3d3dy5tZWR1bW9yZS5vcmc6NDQzIiwiZmVhdHVyZSI6IlVucmVzdHJpY3RlZFNoYXJlZEFycmF5QnVmZmVyIiwiZXhwaXJ5IjoxNzA5ODU1OTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZX0=" />
          {/* <meta httpEquiv="origin-trial" content="AsxKovR6tqp57CGRQP+ALcLxVeTl1ty65TwxOk5fwj52ZRs+nGbgkrT21J7EgvsdzDJ5LuBf1j77SSqnc7l4dwYAAAB6eyJvcmlnaW4iOiJodHRwczovL3VhdC5tZWR1bW9yZS5vcmc6NDQzIiwiZmVhdHVyZSI6IlVucmVzdHJpY3RlZFNoYXJlZEFycmF5QnVmZmVyIiwiZXhwaXJ5IjoxNzA5ODU1OTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZX0=" /> */}
          <meta
            key={"twitter:card"}
            name={"twitter:card"}
            content="summary_large_image"
          />
          <meta key={"og:type"} name={"og:type"} content="website" />
        </Head>
        <Header></Header>
        <div className="main-body bg-white">
          <div className="fix-space-live"></div>
          {!(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && data["data"]["allowed"])?(
            <CourseBanner data={data["data"]}></CourseBanner>
          ):null}
          {new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && data["data"]["allowed"] ?(
            <>
              <div className="block-live-content">
                {/* <button className={`btn_view_chat ${!chatStatus ? 'hide-chat':''}`} onClick={() =>setChatStatus(true)}>
                  <i className="icon-ic-document"></i>
                </button> */}
                {/* <div className={`block-live-chat ${!chatStatus ? 'hide-chat':''}`}>
                  <div className="live-chat">
                    <div className="chat-top">
                      <h3>
                        Live Chat
                      </h3>
                      <button className="btn_more" onClick={() =>setChatStatus(false)}>
                        <i className="icon-ic-close"></i>
                      </button>
                    </div>
                    <div className="chat-center" id="chat_center_box">
                      {commentData && commentData["commentData"] ?(
                        commentData["commentData"].map((val, key) => (
                          <div key={key} className="item">
                            <div className="profile">
                              <div className="avatar">
                                {val.user_avatar && val.user_avatar!=null && val.user_avatar!='' && val.user_avatar!='null'?
                                  <div className="BlockImgSize">
                                    <Image alt="" layout="intrinsic" src={val.user_avatar}  width={48} height={48} />
                                  </div>
                                  :
                                  <div className="BlockImgSize">
                                    <Image alt="" layout="intrinsic" src={"/assets/images/user-key.png"} width={48} height={48} />
                                  </div>
                                }
                              </div>
                              <div className="name">
                                <p>
                                  {val.user_name}
                                </p>
                              </div>
                            </div>
                            <div className="detail">
                              <p dangerouslySetInnerHTML={{__html: val.comment}}></p>
                            </div>
                          </div>
                        ))
                      ):null}
                    </div>
                    <div className="chat-bottom">
                      <div className="profile">
                        <div className="avatar">
                          {user && user.avatar && user.avatar!=null && user.avatar!='' && user.avatar!='null' ? (
                              <Image alt="" layout="intrinsic" src={user.avatar} width={48} height={48} />
                          ):(
                            <Image alt="" layout="intrinsic" src="/assets/images/iuser.png" width={48} height={48} />
                          )}
                        </div>
                        {user ?(
                          <div className="name">
                            <p>
                              {user.name} {user.lastname}
                            </p>
                          </div>
                        ):null}
                      </div>
                      <div className="answer">
                        <div className="box-input">
                          <div className="ui input">
                            <input value={commentInput} onKeyDown={(e)=>{if (e.key === 'Enter') {submitComment()}}} onChange={(event) => setCommentInput(event.target.value)} type="text" placeholder="พูดอะไรหน่อย..." />
                          </div>
                        </div>
                        <div className="sent">
                          <button className="btn_sent" onClick={() =>submitComment()}>
                            <i className="icon-ic-shape-right"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div> */}
                <div className={`block-live-chat ${!chatStatus ? 'hide-chat':''}`}>
                  <div className="live-chat">
                    <div className="chat-top">
                      <div className="img cursor" onClick={() =>location.href="/"}>
                        <Image
                          className=""
                          src="/assets/images/header-logo.png"
                          alt=""
                          layout="fill"
                          objectFit="contain"
                        />
                      </div>
                      <button className="btn_more" onClick={() =>setChatStatus(!chatStatus)}>
                        <i className="icon-ic-left"></i>
                      </button>
                    </div>
                    <div className="live-details">
                      <div className="col-12">
                        {reloadStar ? (
                          <CourseDetailDescription
                            liveLogo={data['data']['live_logo']}
                            data={data["data"]}
                            callback={courseDescription}
                            lang={lang}
                          ></CourseDetailDescription>
                        ) : null}
                      </div>
                    </div>
                  </div>
                </div>
                {/* <iframe className="block-live-iframe" src={data.data.zoom_join_url} allow="camera; microphone"></iframe> */}
                {/* For Client View */}
                {/* <button onClick={startMeetingClientView}>Start Webinar (ClientView)</button> */}
                <div id="zmmtg-root" ref={zmmtgRootRef} />

                {/* For Component View */}
                {/* <button className="posabsolute" onClick={startMeetingComponentView}>Start Webinar (ComponentView)</button> */}
                {/* <div className="block-live-iframe"> */}
                  {/* <div id="meetingSDKElement"></div> */}
                {/* </div> */}
              </div>
              {/* <div className="live-bottom-sec">
                <div className="loader-sec">
                  <div className="lds-ring"><div></div><div></div><div></div><div></div></div>
                  <div className="loader-time">
                    <span>{countdownMin}:{countdownSec}</span>
                  </div>
                </div>
                <div className="btn-sec">
                  <button className="btn-leave" onClick={() =>location.href="/profile/live"}>
                    Leave
                  </button>
                </div>
              </div> */}
            </>
          ):null}
          {!(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && data["data"]["allowed"])?(
            <div className={`container custom-container space-between-content `}>
              <div className="podcast-page ebook-page">
                <div className="row podcast-row">
                  <div className="col-6 first">
                    <EbookBanner data={data["data"]}></EbookBanner>
                  </div>
                  <div className="col-6">
                    {reloadStar ? (
                      <CourseDetailDescription
                        data={data["data"]}
                        callback={courseDescription}
                        lang={lang}
                        countdownDay={countdownDay}
                        countdownHour={countdownHour}
                        countdownMin={countdownMin}
                        countdownSec={countdownSec}
                      ></CourseDetailDescription>
                    ) : null}
                  </div>
                </div>
                <div className="row ebook-price">
                  <div className="col-12 col-md-6 col-lg-6 col-xl-6">
                    {
                      data["data"]["allowed"] || data.data.order_status == 4 || data.data.order_status == 1 || (data && data["data"] && data["data"]["is_soon"] == true) ? 
                      null : (
                        <div className="block-to-buy-course">
                          {(data.data.price == 0 || (data.data.is_promotion == 1 && data.data.pro_price == 0) || (data.data.is_internal&&data.data.user_internal) || data.data.is_subscription|| data.data.is_volume)&&discountCode=='' ? 
                          null : (
                            new Date(data.data.end_time) >= new Date() ?(
                              <div className="row discount-support">
                                <div className="col-12 col-lg-12 col-xl-6">
                                  <p
                                    className={`space-between-content-top ${styles.titleButtonGroup}`}
                                  >
                                    {translateEng('โค้ดส่วนลด')}
                                  </p>
                                  <CourseDetailCode
                                    callback={addDiscountCode}
                                    lang={lang}
                                  ></CourseDetailCode>
                                </div>

                                {data &&
                                data["scholarshipList"] &&
                                data["scholarshipList"].length > 0 ? (
                                  <div className="col-12 col-lg-12 col-xl-6">
                                    <p className="space-between-content-top title-button-group">
                                      {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                                    </p>
                                    <CourseDetailScholarship
                                      callback={addDiscountCode}
                                      name="courseScholarship"
                                      data={data["scholarshipList"]}
                                    ></CourseDetailScholarship>
                                  </div>
                                ) : null}
                              </div>
                            ):null
                          )}
                        </div>
                      )
                    }
                  </div>
                  <div className="col-12 col-md-6 col-lg-6 col-xl-6">
                    <div className="box-price-to-buy">
                      {coursePriceReload && data && data["data"] && data["data"]["is_soon"] == false ? (
                        data.data.allowed || data.data.order_status == 4 || data.data.order_status == 1 ? null : 
                        data.data.is_subscription ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-big-price">Member</div>
                          </div>
                        ) :
                        (data.data.is_internal&&data.data.user_internal) || data.data.is_volume ? (
                          <div className="no-block-course-detail-price text-center">
                            <div className="description-big-price">{translateEng('ฟรี')}</div>
                          </div>
                        ) : data.data.is_promotion == 1 ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-sale">
                              <NumberFormat
                                value={data.data.price}
                                displayType={"text"}
                                thousandSeparator={true}
                                renderText={(value, props) => (
                                  <span {...props}>{translateEng('ปกติ')} {value}</span>
                                )}
                              />
                            </div>
                            {data.data.pro_price == 0 ? (
                              <div className="description-big-price">{translateEng('ฟรี')}</div>
                            ) : (
                              <div className="description-big-price">
                                {translateEng('ราคา')}
                                <NumberFormat
                                  value={data.data.pro_price}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                  renderText={(value, props) => (
                                    <span {...props}>{value}</span>
                                  )}
                                />
                                {translateEng('บาท')}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="no-block-course-detail-price text-center">
                            {data.data.price == 0 ? (
                              <div className="description-big-price">{translateEng('ฟรี')}</div>
                            ) : (
                              <div className="description-big-price">
                                {translateEng('ราคา')}
                                <NumberFormat
                                  value={data.data.price}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                  renderText={(value, props) => (
                                    <span {...props}>{value}</span>
                                  )}
                                />
                                {translateEng('บาท')}
                              </div>
                            )}
                          </div>
                        )
                      ) : null}
                      {data["data"]["allowed"] ||
                      data.data.order_status == 4 ||
                      data.data.order_status == 1 ||
                      (data &&
                        data["data"] &&
                        data["data"]["is_soon"] == true) ? 
                        data["data"]["allowed"] ? null:null : (
                        <div className="block-to-buy-course">
                          {(data.data.price == 0 ||
                          (data.data.is_promotion == 1 &&
                            data.data.pro_price == 0) ||
                          (data.data.is_internal&&data.data.user_internal) ||
                          data.data.is_subscription||
                          data.data.is_volume)&&discountCode=='' ? (
                            data.data.is_subscription ? (
                              new Date(data.data.end_time) >= new Date() ?(
                                <button
                                  className="btn-to-buy-course"
                                  onClick={() =>
                                    groupCategoryCallback(
                                      "vip",
                                      data["data"]["id"],
                                      ""
                                    )
                                  }
                                >
                                  <span>{translateEng('รับชม')}</span>
                                </button>
                              ):null
                            ):(
                              data.data.is_volume ? (
                                new Date(data.data.end_time) >= new Date() ?(
                                  <button
                                    className="btn-to-buy-course"
                                    onClick={() =>
                                      groupCategoryCallback(
                                        "volume",
                                        data["data"]["id"],
                                        ""
                                      )
                                    }
                                  >
                                    <span>{translateEng('รับชม')}</span>
                                  </button>
                                ):null
                              ):(
                                data.data.is_internal&&data.data.user_internal ? (
                                  new Date(data.data.end_time) >= new Date() ?(
                                    <button
                                      className="btn-to-buy-course"
                                      onClick={() =>
                                        groupCategoryCallback(
                                          "internal",
                                          data["data"]["id"],
                                          ""
                                        )
                                      }
                                    >
                                      <span>{translateEng('รับชม')}</span>
                                    </button>
                                  ):null
                                ):(
                                  new Date(data.data.end_time) >= new Date() ?(
                                    <button
                                      className="btn-to-buy-course"
                                      onClick={() =>
                                        groupCategoryCallback(
                                          "free",
                                          data["data"]["id"],
                                          ""
                                        )
                                      }
                                    >
                                      <span>{translateEng('รับชม')}</span>
                                    </button>
                                  ):null
                                )
                              )
                            )
                            
                          ) : (
                            new Date(data.data.end_time) >= new Date() ?(
                              <button
                                className="btn-to-buy-course"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "cart",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('ซื้อ Live')}</span>
                              </button>
                            ):null
                          )}
                        </div>
                      )}

                      {data["data"]["is_soon"] == true ? (
                        <div className="block-course-detail-price text-center">
                          <div className="description-big-price">Coming soon</div>
                        </div>
                      ) : null}
                      <div className="space-fix-price"></div>
                    </div>
                  </div>
                  {data.data && data.data.tag.length > 0 ? (
                    <div className="col-12 course-page">
                      <div className="block-quick-result">
                        <ListTags data={data.data.tag}></ListTags>
                      </div>
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          ):null}
          {!(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && data["data"]["allowed"]) && relateData&&relateData.length>0 ? (
            <GroupCategory
              type="relate"
              name={translateEng('Live ที่เกี่ยวข้อง')}
              bg={''}
              color="#6E953D"
              size={3.5}
              isLoop={false}
              index={20}
              data={relateData}
              callback={groupCategoryCallback}
            ></GroupCategory>
          ) : null}
          {!(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && data["data"]["allowed"]) && new Date(data.data.end_time) <= new Date() && commentData ? (
            <div className={`container custom-container space-between-content `}>
              <div className="external-comment-head">
                <h3>ถาม-ตอบ</h3>
              </div>
              <div className="external-comment-zone">
                <CommentZone
                  user={user}
                  addLike={addLike}
                  addComment={addComment}
                  data={commentData["commentData"].toReversed()}
                  lang={lang}
                ></CommentZone>
              </div>
            </div>
          ) : null}
          {!(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && data["data"]["allowed"])?(
            <Footer></Footer>
          ):null}
        </div>
      </div>
    </>
  );
}

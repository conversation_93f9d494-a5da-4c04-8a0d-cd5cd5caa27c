import React from "react";
import Image from "next/image";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import Router, { useRouter } from "next/router";
import { getUser } from "../../api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import { useForm } from "react-hook-form";
import Head from "next/head";
import Link from "next/link";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import CourseDetailIntro from "/themes/components/courseDetailIntro";
import CourseDetailDescription from "/themes/components/courseDetailDescription";
import CourseDetailPrice from "/themes/components/courseDetailPrice";
import CourseDetailCode from "/themes/components/courseDetailCode";
import CourseDetailScholarship from "/themes/components/courseDetailScholarship";
import GroupCategory from "/themes/components/groupCategory";
import CommentZone from "/themes/components/commentZone";
import ListVdoModal from "/themes/components/listVdoModal";
import GroupAudioList from "/themes/components/groupAudioList";
import ListVdoEp from "/themes/components/listVdoEp";
import VdoModal from "/themes/components/vdoModal";
import CourseBanner from "/themes/components/courseBanner";
import EbookBanner from "/themes/components/ebookBanner";
import RankingViewHistory from "/themes/components/rankingViewHistory";
import ReactPlayer from "react-player";
import NumberFormat from "react-number-format";
import 'bootstrap/dist/css/bootstrap.css'
import ListTags from "/themes/components/listTagsCourse";
import axios from 'axios';
import { IoMdDownload } from "react-icons/io";
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import { Pie } from "react-chartjs-2";
ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

import {
  Menu,
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
  Accordion,
  Tab,
  Popup,
} from "semantic-ui-react";
import "semantic-ui-css/semantic.min.css";
import stylesModal from "/public/assets/css/component/listQuizModal.module.css";
import styles from "/public/assets/css/pages/course.module.css";
import Swal from "sweetalert2";

import moment from "moment";
// import { gsap } from "gsap";
// import { ScrollTrigger } from "gsap/dist/ScrollTrigger";

let isStampLive = false;
let isStartZoom = false;
let isEndZoom = false;
export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const params = context.query;
  const utoken = params.utoken;
  const user = await getUser(utoken);
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/course/get/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();
  if (data["status"] == "false" || data["data"]["type"] != "live") {
    return {
      redirect: { destination: "/" },
    };
  }

  return {
    props: {
      session: await getSession(context),
      data,
      utoken,
      user
    },
  };
}

export default function Course({
  data,
  utoken,
  user
}) {
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  // section zoom
    const authEndpoint = '/api/zoom-auth';
    const sdkKey = process.env.NEXT_PUBLIC_ZOOM_ACCOUNT_ID;
    const role = 0;
    const registrantToken = ''; //optional
    const zakToken = ''; //optional

    const leaveUrl = !process.env.NEXT_PUBLIC_APPDOMAIN||(process.env.NEXT_PUBLIC_APPDOMAIN&&process.env.NEXT_PUBLIC_APPDOMAIN!='https://www.medumore.org')?'https://uat.medumore.org':'https://www.medumore.org';
    const meetingNumber = data.data.webminar_id;
    const passWord = data.data.zoom_password; //พาสของ meetingNumber
    const userName = user&&user.name&&user.name!=null&&user.name!=''?user.name:'';
    const userEmail = user&&user.email&&user.email!=null&&user.email!=''?user.email:''; //optional

    const [signature, setSignature] = useState('');
    const zmmtgRootRef = useRef(null);
    const [componentClient, setComponentClient] = useState(null);
    const [zoomPlayed, setZoomPlayed] = useState(false);
    const getReact = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/2.17.0/lib/vendor/react.min.js";
      script.onload = () => getReactDom();
      document.body.appendChild(script);
    };
    const getReactDom = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/2.17.0/lib/vendor/react-dom.min.js";
      script.onload = () => getRedux();
      document.body.appendChild(script);
    };
    const getRedux = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/2.17.0/lib/vendor/redux.min.js";
      script.onload = () => getReduxThunk();
      document.body.appendChild(script);
    };
    const getReduxThunk = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/2.17.0/lib/vendor/redux-thunk.min.js";
      script.onload = () => getLodash();
      document.body.appendChild(script);
    };
    const getLodash = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/2.17.0/lib/vendor/lodash.min.js";
      script.onload = () => startApp();
      document.body.appendChild(script);
    };
    const startApp = async () =>{
      loadSignature();
      getClientView();
      // getComponentView();
    }
    const loadSignature = async () =>{
      let payload = Router.query;
      payload.meetingNumber = meetingNumber;
      payload.role = role;
      const { data } = await axios({
        url: authEndpoint,
        method: "post",
        data: payload,
      })
        .then((response) => {
          return response;
        })
        .catch((error) => {
          return error;
        });
      if (data) {
        setSignature(data.signature);
      }
    }
    {/* For Client View */}
    const getClientView = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/zoom-meeting-2.17.0.min.js";
      script.onload = () => setupClientView();
      document.body.appendChild(script);
    };
    const setupClientView = async () => {
      window.ZoomMtg.setZoomJSLib('https://source.zoom.us/2.17.0/lib', '/av')

      window.ZoomMtg.preLoadWasm()
      window.ZoomMtg.prepareWebSDK()
      // loads language files, also passes any error messages to the ui
      window.ZoomMtg.i18n.load('en-US')
      window.ZoomMtg.i18n.reload('en-US')
    };
    const startMeetingClientView = () => {
      // console.log('startMeeting')
      if (zmmtgRootRef.current) {
        zmmtgRootRef.current.style.display = 'block';
      }
      window.ZoomMtg.init({
        leaveUrl: leaveUrl,
        disablePreview: true,
        success: (success) => {
          // console.log(success)
          window.ZoomMtg.join({
            signature: signature,
            sdkKey: sdkKey,
            meetingNumber: meetingNumber,
            passWord: passWord,
            userName: userName,
            userEmail: userEmail,
            tk: registrantToken,
            zak: zakToken,
            success: (success) => {
              console.log(success)
            },
            error: (error) => {
              console.log(error)
            },
          })
        },
        error: (error) => {
          console.log(error)
        }
      });
      isStampLive = true;
    }; 
    
    const [counter, setCounter] = useState(0);

    // This function will be called every 3 seconds
    const stampLive = () => {
      setCounter(prevCounter => prevCounter + 1);
      // Add your logic here
      // console.log(isStampLive);
      if(isStampLive && (new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && data["data"]["allowed"])){
        const formData = new URLSearchParams();
        formData.append("course_id", data["data"]["id"]);
        formData.append("utoken", utoken);
        appContext.sendApiToken(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addLiveStamp",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              console.log('stampLive');
            }
          }
        );
      }
    };

    useEffect(() => {
      const intervalId = setInterval(stampLive, 3000);

      // Clear the interval when the component unmounts
      return () => {
        clearInterval(intervalId);
      };
    }, []);
    {/* For Client View */}
    {/* For Component View */}
    const getComponentView = async () => {
      const script = document.createElement("script");
      script.src = "https://source.zoom.us/2.17.0/zoom-meeting-embedded-2.17.0.min.js";
      script.onload = () => setupComponentView();
      document.body.appendChild(script);
    };
    const setupComponentView = async () => {
      let meetingSDKElement = document.getElementById('meetingSDKElement')
      const client = window.ZoomMtgEmbedded.createClient();
      client.init({
        zoomAppRoot: meetingSDKElement,
        language: 'en-US',
        customize: {
          video: {
            defaultViewType:'speaker'
          }
        }
      })
      setComponentClient(client);
    };
    const startMeetingComponentView = () => {
      // console.log('signature:'+signature);
      // console.log('sdkKey:'+sdkKey);
      // console.log('meetingNumber:'+meetingNumber);
      // console.log('passWord:'+passWord);
      // console.log('userName:'+userName);
      // console.log('userEmail:'+userEmail);
      componentClient.join({
        signature: signature,
        sdkKey: sdkKey,
        meetingNumber: meetingNumber,
        password: passWord,
        userName: userName,
        userEmail: userEmail,
        tk: registrantToken,
        zak: zakToken
      })
    }; 
    useEffect(() => {
      if(!zoomPlayed && signature!=null && signature!=''){
        setZoomPlayed(true);
        setTimeout(() => {
          
          startMeetingClientView();
          var mainMenuEl = document.getElementById("header_position");
          mainMenuEl.classList.add("hide_menu_bar");
          // leave-meeting-options__btn--default
        }, 500);
      }
      // if(componentClient!=null && !zoomPlayed && signature!=null && signature!=''){
      //   setZoomPlayed(true);
      //   startMeetingComponentView();
      // }
    }, [componentClient,signature]);
    {/* For Component View */}
    // Load Zoom SDK Flow
  // section zoom

  const [firstLoad, setFirstLoad] = useState(true);
  useEffect(() => {
    if (firstLoad) {
      setFirstLoad(false);
      if(new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && data["data"]["allowed"] && !isStartZoom){
        isStartZoom = true;
        getReact();
      }
    }
  }, [firstLoad]);
  
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [chatStatus, setChatStatus] = useState(false);
  const [reloadStar, setReloadStar] = useState(true);
  useEffect(() => {
    const interval_cd = setInterval(() => {
      if(new Date(data.data.started_time) >= new Date()){
        var now = new Date().getTime();
        var distance = new Date(data.data.started_time).getTime() - now;
        var days = Math.floor(distance / (1000 * 60 * 60 * 24));
        var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        var seconds = Math.floor((distance % (1000 * 60)) / 1000);
        if(days<10){
          days = '0'+days;
        }
        if(hours<10){
          hours = '0'+hours;
        }
        if(minutes<10){
          minutes = '0'+minutes;
        }
        if(seconds<10){
          seconds = '0'+seconds;
        }
        if(hours=='00'&&minutes=='00'&&seconds=='00'&&!isStartZoom){
          isStartZoom = true;
          getReact();
        }
      }
      if(new Date(data.data.end_time) <= new Date() && !isEndZoom && isStartZoom){
        isEndZoom = true;
        window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'closeWebView' }));
      }
    }, 1000);
    return () => clearInterval(interval_cd);
  }, []);
  function courseDescription(_value) {
    const formData = new URLSearchParams();
    formData.append("course_id", data["data"]["id"]);
    formData.append("rate", _value);
    formData.append("utoken", utoken);
    appContext.sendApiToken(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseRate",
      formData,
      (obj) => {
        if (obj["status"] == "success") {
          setReloadStar(false);
          data["data"]["rate"] = obj["rate"];
          data["data"]["rating"] = obj["rating"];
          setTimeout(() => {
            setReloadStar(true);
          }, "0");
          forceUpdate();
        }
      }
    );
  }
  return (
    <>
      <style>{`
        #onetrust-consent-sdk{
          display:none!important;
        }
        body{
          background:black!important;
        }
        .icon-ic-lock{
          color:white!important;
        }
      `}</style>
      <div className={`main-all page-course page-live`}>
        <div className="main-body bg-white">
          <div className="fix-space-live"></div>
          {new Date(data.data.started_time) <= new Date() && new Date(data.data.end_time) >= new Date() && data["data"]["allowed"] ?(
            <div className="block-live-content">
              <div className={`block-live-chat ${!chatStatus ? 'hide-chat':''}`}>
                <div className="live-chat">
                  <div className="chat-top">
                    <div className="img cursor" onClick={() =>location.href="/"}>
                      <Image
                        className=""
                        src="/assets/images/header-logo.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                      />
                    </div>
                    <button className="btn_more" onClick={() =>setChatStatus(!chatStatus)}>
                      <i className="icon-ic-left"></i>
                    </button>
                  </div>
                  <div className="live-details">
                    <div className="col-12">
                      {reloadStar ? (
                        <CourseDetailDescription
                          liveLogo={data['data']['live_logo']}
                          data={data["data"]}
                          callback={courseDescription}
                          lang={lang}
                        ></CourseDetailDescription>
                      ) : null}
                    </div>
                  </div>
                </div>
              </div>
              <div id="zmmtg-root" ref={zmmtgRootRef} />
            </div>
          ):null}
        </div>
      </div>
    </>
  );
}

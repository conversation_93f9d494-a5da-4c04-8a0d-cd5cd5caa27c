import React from "react";

// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "./api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import Head from "next/head";
import Header from "../themes/header/header";
import Footer from "../themes/footer/footer";
import AddressContact from "../themes/components/addressContact";
import GroupRadio from "../themes/components/groupRadio";
import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
  Accordion,
  Tab,
} from "semantic-ui-react";
import Image from "next/image";
import Swal from "sweetalert2";
import NumberFormat from "react-number-format";
export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const params = context.query;
  if (!user) {
    return {
      redirect: { destination: "/" },
    };
  }
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/faq/get",
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();
  if (data["status"] == "false") {
    return {
      redirect: { destination: "/" },
    };
  }

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/faq",
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  return {
    props: { seo_data, utoken, user, params ,data},
  };
}
export default function Checkout({
  seo_data,
  utoken,
  user,
  params,
  data
}) {
  const appContext = useContext(AppContext);
  appContext.setToken(utoken);
  const router = useRouter();
  var panels = [];
  for(var i = 0; i<data.data.log.length; i++){
    var item = {
      key: `panel-1`,
      title: data.data.log[i].question_th,
      content: {
        content: (
          <div className="container-tab">
            <div className="html-content" dangerouslySetInnerHTML={{ __html: data.data.log[i].answer_th }}></div>
          </div>
        ),
      }
    }
    panels.push(item)
  }
  return ( 
      <div className="main-all page-faqs">
        <Head>
          {seo_data.seo ? (
            seo_data.seo.map((val, key) =>
              val.name == "title" ? (
                <>
                  <title>{val.content}</title>
                  <meta key={key} name={val.name} content={val.content} />
                  <meta
                    key={"og:" + key}
                    name={"og:" + val.name}
                    content={val.content}
                  />
                  <meta
                    key={"twitter:" + key}
                    name={"twitter:" + val.name}
                    content={val.content}
                  />
                </>
              ) : (
                <>
                  <meta key={key} name={val.name} content={val.content} />
                  <meta
                    key={"og:" + key}
                    name={"og:" + val.name}
                    content={val.content}
                  />
                  <meta
                    key={"twitter:" + key}
                    name={"twitter:" + val.name}
                    content={val.content}
                  />
                </>
              )
            )
          ) : (
            <>
              <title>MDCU : MedU MORE</title>
            </>
          )}

          <meta
            key={"twitter:card"}
            name={"twitter:card"}
            content="summary_large_image"
          />
          <meta key={"og:type"} name={"og:type"} content="website" />
        </Head>

        <Header></Header>
        <div className="main-body">
          <div className="fix-space"></div>
          <div className="news-banner d-none d-xl-block d-md-block">
            <div  className="imgResponsiveCustom">
              {data && data.data && data.data.banner_d && data.data.banner_d != null && data.data.banner_d != '' && data.data.banner_d != 'null' ? (
                <Image
                  className=" cursor-pointer"
                  src={data.data.banner_d}
                  layout='fill'
                  objectFit='contain'
                  sizes="100%"
                  alt=""
                />
              ):null}
            </div>
          </div>
          <div className="news-banner d-block d-md-none d-xl-none">
            <div  className="imgResponsiveCustom">
            {data && data.data && data.data.banner_m && data.data.banner_m != null && data.data.banner_m != '' && data.data.banner_m != 'null' ? (
              <Image
                className=" cursor-pointer"
                src={data.data.banner_m}
                layout='fill'
                objectFit='contain'
                sizes="100%"
                alt=""
              />
            ):null}
            </div>
          </div>
          <div className="container-fluid bg-white course-page">
            <div className={`container  space-between-content`}>
              <div className="col-12 course-page">
                <div>
                    <Tab
                      panes={[
                        {
                          menuItem: data.data.title_th,
                          pane: {
                            key: "tab1",
                            content: (
                              <div className="content-box">
                                <Accordion
                                  defaultActiveIndex={[0]}
                                  exclusive={false}
                                  panels={panels}
                                  fluid
                                />
                              </div>
                            ),
                          },
                        }
                      ]}
                      renderActiveOnly={false}
                      className={`space-between-content-top courseTab`}
                    />
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer></Footer>
      </div> 
  );
}

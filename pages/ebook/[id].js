import React from "react";
import Image from "next/image";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import Router, { useRouter } from "next/router";
import { getUser } from "../api/user";
import { useState, useEffect, useContext, useRef, useReducer } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";
import { useForm } from "react-hook-form";
import Head from "next/head";
import Link from "next/link";
import Header from "/themes/header/header";
import Footer from "/themes/footer/footer";
import CourseDetailIntro from "/themes/components/courseDetailIntro";
import CourseDetailDescription from "/themes/components/courseDetailDescription";
import CourseDetailPrice from "/themes/components/courseDetailPrice";
import CourseDetailCode from "/themes/components/courseDetailCode";
import CourseDetailScholarship from "/themes/components/courseDetailScholarship";
import GroupCategory from "/themes/components/groupCategory";
import CommentZone from "/themes/components/commentZone";
import ListVdoModal from "/themes/components/listVdoModal";
import GroupAudioList from "/themes/components/groupAudioList";
import ListVdoEp from "/themes/components/listVdoEp";
import VdoModal from "/themes/components/vdoModal";
import CourseBanner from "/themes/components/courseBanner";
import EbookBanner from "/themes/components/ebookBanner";
import RankingViewHistory from "/themes/components/rankingViewHistory";
import ReactPlayer from "react-player";
import NumberFormat from "react-number-format";
import ListTags from "/themes/components/listTagsCourse";
import { IoMdDownload } from "react-icons/io";
import { useDraggable } from 'react-use-draggable-scroll';
import { Document, Page,pdfjs } from 'react-pdf';
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import { Pie } from "react-chartjs-2";
ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

import {
  Menu,
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
  Accordion,
  Tab,
  Popup,
} from "semantic-ui-react";
import "semantic-ui-css/semantic.min.css";
import stylesModal from "/public/assets/css/component/listQuizModal.module.css";
import styles from "/public/assets/css/pages/course.module.css";
import Swal from "sweetalert2";

import moment from "moment";
// import { gsap } from "gsap";
// import { ScrollTrigger } from "gsap/dist/ScrollTrigger";

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const user = await getUser(cookies[process.env.NEXT_PUBLIC_APP + "_token"]);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";

  const params = context.query;
  const formData = new URLSearchParams();
  formData.append("utoken", utoken);

  const res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/course/get/" + params.id,
    {
      body: formData,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCode = res.ok ? false : res.statusCode;
  const data = await res.json();

  if (data["status"] == "false" || data["data"]["type"] != "Ebook") {
    return {
      redirect: { destination: "/" },
    };
  }

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/seo/course/" + params.id,
    {
      method: "POST",
    }
  );
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode;
  const seo_data = await seo_res.json();
  //SEO

  //View Log
  const formDataView = new URLSearchParams();
  formDataView.append("utoken", utoken);
  formDataView.append("course_id", data.data.id);

  const res_view = await fetch(
    process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseView",
    {
      body: formDataView,
      // headers: {
      //   //   'Authorization': 'Basic ' + base64.encode("APIKEY:X"),
      //   "Content-Type": "multipart/form-data",
      // },
      method: "POST",
    }
  );
  const errorCodeView = res_view.ok ? false : res_view.statusCode;
  const data_view = await res_view.json();
  //View Log

  return {
    props: {
      session: await getSession(context),
      seo_data,
      errorCode,
      data,
      params,
      utoken,
      user,
    },
  };
}

export default function Course({
  seo_data,
  errorCode,
  data,
  params,
  utoken,
  user,
}) {
  // console.log("---Course---");
  // console.log(data);
  // console.log("***Course***");
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);
  const [file, setFile] = useState(null);
  const [firstLoad, setFirstLoad] = useState(true);
  const [numPages, setNumPages] = useState(null);
  const [pagePreview, setPagePreview] = useState([]);
  const [pageNumber, setPageNumber] = useState(1);
  const [nowPage, setNowPage] = useState(0);
  const [showControl, setShowControl] = useState(false);
  const [allPages, setAllPages] = useState(1);
  
  const [reloadComment, setReloadComment] = useState(true);
  const [commentData, setCommentData] = useState(null);
  const [isDownload , setIsDownload] = useState(false);
  useEffect(() => {
    if (firstLoad) {
      setFirstLoad(false);
      if(data["documentList"] && data["documentList"].length>0){
        setFile(process.env.NEXT_PUBLIC_API+"/ebook_preview/"+data["documentList"][0]['id_encrypt']);
        if(data["documentList"][0]["preview_page"]!=null && data["documentList"][0]["preview_page"]!='' && data["documentList"][0]["preview_page"]!='null'){
          setPagePreview(data["documentList"][0]["preview_page"].split(","));
        }else{
          setPagePreview([]);
        }
      }else{
        setFile(null);
        setPagePreview([]);
      }
    }
  }, [firstLoad]);
  useEffect(() => {
    if (reloadComment) {
      setReloadComment(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/mdcu/course/comment/" +
          data.data.id,
        null,
        (data) => {
          setCommentData(data);
        }
      );
    }
  }, [reloadComment]);
  function addLike(_comment_id) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("comment_id", _comment_id);
      setReloadComment(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addLike",
        formData,
        (obj) => {
          // console.log(obj);
          setReloadComment(false);
          setTimeout(() => {
            setReloadComment(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function addComment(_comment) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("comment", _comment);
      formData.append("course_id", data["data"]["id"]);
      formData.append("content_type", "course");
      setReloadComment(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addComment",
        formData,
        (obj) => {
          // console.log(obj);
          setReloadComment(false);
          setTimeout(() => {
            setReloadComment(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }

  var panes = [
    {
      menuItem: translateEng('ถาม-ตอบ'),
      pane: {
        key: "tab1",
        content: (
          <div className="content-box">
            {commentData ? (
              <CommentZone
                user={user}
                addLike={addLike}
                addComment={addComment}
                data={commentData["commentData"]}
                lang={lang}
              ></CommentZone>
            ) : null}
          </div>
        ),
      },
    },
  ];

  function onFileChange(event) {
    setFile(event.target.files[0]);
  }
  function ebookScrollRight() {
    var elements = document.getElementsByClassName('react-pdf__Page');
    var obj_width = elements[0].offsetWidth;
    ref.current.scrollLeft = obj_width * (nowPage+1);
  }
  function ebookScrollLeft() {
    var elements = document.getElementsByClassName('react-pdf__Page');
    var obj_width = elements[0].offsetWidth;
    ref.current.scrollLeft = obj_width * (nowPage-1);
  }
  function onDocumentLoadSuccess({ numPages: nextNumPages }) {
    setNumPages(nextNumPages);
  }

  function goToPreviousPage() {
    if (pageNumber > 1) {
      setPageNumber(pageNumber - 1);
    }
  }

  function goToNextPage() {
    if (pageNumber < numPages) {
      setPageNumber(pageNumber + 1);
    }
  }
  
  const ref = useRef(null);
  const { events: events } = useDraggable(ref, {
      applyRubberBandEffect: true,
  });
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [relateData, setRelateData] = useState(null);
  const [reloadRelate, setReloadRelate] = useState(true);
  const [discountCode, setDiscountCode] = useState("");
  const [discountChannel, setDiscountChannel] = useState("");
  const [courseProPrice, setCourseProPrice] = useState(
    data["data"]["pro_price"]
  );
  const [coursePrice, setCoursePrice] = useState(data["data"]["price"]);
  const [coursePriceReload, setCoursePriceReload] = useState(true);
  const [reloadStar, setReloadStar] = useState(true);

  const [errorArray, setErrorArray] = useState([]);
  const [reloadVdo, setReloadVdo] = useState(true);
  const [vdoList, setVdoList] = useState(null);
  const [vdoListAll, setVdoListAll] = useState(null);
  const [courseId, setCourseId] = useState(null);
  const [fullScreen, setFullScreen] = useState(false);
  const [toggleZoom, setToggleZoom] = useState(false);
  const [singlePage, setSinglePage] = useState(false);
  const schemaData = {
    "@context": "https://schema.org/",
    "@type": "Course",
    "name": data["data"]["title"],
    "image": data["data"]["image"],
    "description": data["data"]["subtitle_th"],
    "brand": "MedUMore",
    "provider": {
      "@type": "Person",
      "name": data["data"]["speaker_name"]
    },
    "publisher": {
      "@type": "Organization",
      "name": "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย",
      "logo": "https://www.md.chula.ac.th/wp-content/uploads/2016/02/MDCU-Logo-300x300.jpg",
      "url": "https://www.md.chula.ac.th/"
    },
    "author": {
      "@type": "Organization",
      "logo": "https://www.md.chula.ac.th/wp-content/uploads/2016/02/MDCU-Logo-300x300.jpg",
      "name": "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย",
      "url": "https://www.md.chula.ac.th/"
    }
  };
  useEffect(() => {
    if (reloadRelate) {
      setReloadRelate(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          "/api/mdcu/course/relate/" +
          data.data.id +
          "/" +
          data.data.type,
        null,
        (res_relate) => {
          if(res_relate['status']=='success'){
            setRelateData(res_relate.data);
            // console.log(res_relate)
          }
        }
      );
    }
  }, [reloadRelate]);
  useEffect(() => {
    if (reloadVdo) {
      setReloadVdo(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/course/vdo/" + data.data.id,
        null,
        (data) => {
          setVdoList(data["data"]);
          setVdoListAll(data);
        }
      );
    }
  }, [reloadVdo]);

  const checkMyError = (_val) => {
    if (errorArray.indexOf(_val) > -1) {
      return true;
    }
    return false;
  };

  function courseDescription(_value) {
    const formData = new URLSearchParams();
    formData.append("course_id", data["data"]["id"]);
    formData.append("rate", _value);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/addCourseRate",
      formData,
      (obj) => {
        if (obj["status"] == "success") {
          setReloadStar(false);
          data["data"]["rate"] = obj["rate"];
          data["data"]["rating"] = obj["rating"];
          setTimeout(() => {
            setReloadStar(true);
          }, "0");
          forceUpdate();
        }
      }
    );
  }
  function checkFree(){
    if((data.data.price == 0 || (data.data.is_promotion == 1 && data.data.pro_price == 0) || data.data.is_internal || data.data.is_subscription || data.data.is_volume)&&discountCode==''){
      return true;
    }else{
      return false;
    }
  }
  function selectLockDoc() {
    if (user) {
      if(data["data"]["is_soon"]){
        Swal.fire({
          text: "Coming soon",
          icon: "info",
          confirmButtonText: translateEng('ปิด'),
          confirmButtonColor: "#648d2f"
        });
      }else{
        if (checkFree()) {
          Swal.fire({
            text: translateEng('คุณต้องการรับ E-Book ฟรีใช่หรือไม่?'),
            icon: 'info',
            showCancelButton: true,
            confirmButtonColor: '#648d2f',
            cancelButtonColor: '#d33',
            confirmButtonText: translateEng('ใช่'),
            cancelButtonText: translateEng('ไม่')
          }).then((result) => {
            if (result.isConfirmed) {
              groupCategoryCallback(
                "free",
                data["data"]["id"],
                ""
              );
            }
          })
        }else{
          Swal.fire({
            text: translateEng('กรุณาซื้อ E-Book นี้ก่อนค่ะ'),
            icon: "info",
            confirmButtonText: translateEng('ปิด'),
            confirmButtonColor: "#648d2f"
          });
        }
      }
    }else{
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: translateEng('ตกลง'),
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  
  const [popupShow, setPopupShow] = useState(false);
  function handleScroll() {
    try {
      var elements = document.getElementsByClassName('react-pdf__Page');
      var all_page = Math.ceil(ref.current.scrollWidth / (elements[0].offsetWidth));
      setAllPages(all_page);
      var obj_width = elements[0].offsetWidth;
      setNowPage(Math.ceil(ref.current.scrollLeft / obj_width));
    }
    catch(err) {
    }
    if(!popupShow && !data["data"]["allowed"]){
      var last_pos = ref.current.scrollWidth - (elements[0].offsetWidth*2.1);
      if(last_pos<=ref.current.scrollLeft){
        setPopupShow(true);
        if (checkFree()) {
          Swal.fire({
            text: translateEng('รับ E-Book เล่มนี้ฟรี เพื่ออ่านต่อ'),
            icon: 'info',
            showCancelButton: false,
            confirmButtonColor: '#648d2f',
            confirmButtonText: translateEng('รับเลย')
          }).then((result) => {
            if (result.isConfirmed) {
              setFullScreen(false);
              groupCategoryCallback(
                "free",
                data["data"]["id"],
                ""
              );
            }
          })
        }else{
          Swal.fire({
            text: translateEng('ซื้อ E-Book เล่มนี้เพื่ออ่านต่อ'),
            icon: 'info',
            showCancelButton: false,
            confirmButtonColor: '#648d2f',
            confirmButtonText: translateEng('ซื้อเลย')
          }).then((result) => {
            if (result.isConfirmed) {
              setFullScreen(false);
              groupCategoryCallback(
                "cart",
                data["data"]["id"],
                ""
              );
            }
          })
        }
      }
    }
  }

  appContext.setToken(utoken);

  if (errorCode) {
    return <Error statusCode={errorCode} />;
  }
  function groupCategoryCallback(_type, _id, _title) {
    if (user) {
      if (_type == "cart") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "course");
        formData.append("discount_code", discountCode);
        formData.append("discount_channel", discountChannel);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
          formData,
          (res_check) => {
            if(res_check['status']=='success'){
              if(res_check['count']==0){
                Swal.fire({
                  html: '<div class="swal-condition-ebook"><h3>นโยบายการซื้อหนังสืออิเล็กทรอนิกส์ (E-Book)</h3><p>1.สิทธิ์การเข้าถึง: ผู้ซื้อจะได้รับสิทธิ์ในการเข้าถึงหนังสืออิเล็กทรอนิกส์ที่ซื้อเท่านั้น ซึ่งเป็นการเข้าถึงผ่านแพลตฟอร์ม MDCU MedUMORE หรืออุปกรณ์อื่น</p><p>2.การจำกัดการเผยแพร่: ผู้ซื้อไม่ได้รับสิทธิ์ในการจำหน่ายหรือเผยแพร่หนังสืออิเล็กทรอนิกส์ต่อสู่บุคคลหรือช่องทางอื่น หากมีการจำหน่ายหรือเผยแพร่เนื้อหา จะต้องได้รับการอนุญาตจากผู้ที่มีสิทธิ์ทางกฎหมายหรือเจ้าของลิขสิทธิ์เท่านั้น</p><p>3.การคุ้มครองลิขสิทธิ์: การซื้อหนังสืออิเล็กทรอนิกส์ไม่ยกเว้นการคุ้มครองลิขสิทธิ์ ผู้ซื้อต้องปฏิบัติตามกฎหมายลิขสิทธิ์และข้อกำหนดที่กำหนดโดยผู้จัดจำหน่ายหรือผู้ให้บริการ</p><p>4.การจำกัดการคัดลอกและแจกจ่าย: ผู้ซื้ออาจได้รับการจำกัดในการคัดลอกหรือแจกจ่ายเนื้อหาที่ซื้อ โดยจะปรากฏชื่อ อีเมล และเบอร์โทรศัพท์ของผู้ซื้อ ภายในหนังสือที่ซื้อ</p></div>',
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: translateEng('ยืนยัน'),
                  cancelButtonText: translateEng('ยกเลิก')
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                      formData,
                      (data) => {
                        // console.log(data);
                        appContext.setReloadCart(true);
                        setReloadRelate(true);
            
                        document
                          .getElementsByClassName("main-header")[0]
                          .classList.add("active_cart");
                        document
                          .getElementsByClassName("group-menu-cart")[0]
                          .classList.add("on_show");
                        document.body.classList.add("open_cart");
                        document
                          .getElementsByClassName("group-menu-f-mobile")[0]
                          .classList.remove("on_show");
                      }
                    );
                  }
                });
              }else{
                Swal.fire({
                  html: translateEng('คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'),
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: translateEng('ยืนยัน'),
                  cancelButtonText: translateEng('ยกเลิก')
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                      formData,
                      (res_clear) => {
                        if(res_clear['status']=='success'){
                          Swal.fire({
                            html: '<div class="swal-condition-ebook"><h3>นโยบายการซื้อหนังสืออิเล็กทรอนิกส์ (E-Book)</h3><p>1.สิทธิ์การเข้าถึง: ผู้ซื้อจะได้รับสิทธิ์ในการเข้าถึงหนังสืออิเล็กทรอนิกส์ที่ซื้อเท่านั้น ซึ่งเป็นการเข้าถึงผ่านแพลตฟอร์ม MDCU MedUMORE หรืออุปกรณ์อื่น</p><p>2.การจำกัดการเผยแพร่: ผู้ซื้อไม่ได้รับสิทธิ์ในการจำหน่ายหรือเผยแพร่หนังสืออิเล็กทรอนิกส์ต่อสู่บุคคลหรือช่องทางอื่น หากมีการจำหน่ายหรือเผยแพร่เนื้อหา จะต้องได้รับการอนุญาตจากผู้ที่มีสิทธิ์ทางกฎหมายหรือเจ้าของลิขสิทธิ์เท่านั้น</p><p>3.การคุ้มครองลิขสิทธิ์: การซื้อหนังสืออิเล็กทรอนิกส์ไม่ยกเว้นการคุ้มครองลิขสิทธิ์ ผู้ซื้อต้องปฏิบัติตามกฎหมายลิขสิทธิ์และข้อกำหนดที่กำหนดโดยผู้จัดจำหน่ายหรือผู้ให้บริการ</p><p>4.การจำกัดการคัดลอกและแจกจ่าย: ผู้ซื้ออาจได้รับการจำกัดในการคัดลอกหรือแจกจ่ายเนื้อหาที่ซื้อ โดยจะปรากฏชื่อ อีเมล และเบอร์โทรศัพท์ของผู้ซื้อ ภายในหนังสือที่ซื้อ</p></div>',
                            icon: "info",
                            showCancelButton: true,
                            confirmButtonColor: '#648d2f',
                            cancelButtonColor: '#d33',
                            confirmButtonText: translateEng('ยืนยัน'),
                            cancelButtonText: translateEng('ยกเลิก')
                          }).then((result) => {
                            if (result.value) {
                              appContext.sendApi(
                                process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                                formData,
                                (data) => {
                                  // console.log(data);
                                  appContext.setReloadCart(true);
                                  setReloadRelate(true);
                      
                                  document
                                    .getElementsByClassName("main-header")[0]
                                    .classList.add("active_cart");
                                  document
                                    .getElementsByClassName("group-menu-cart")[0]
                                    .classList.add("on_show");
                                  document.body.classList.add("open_cart");
                                  document
                                    .getElementsByClassName("group-menu-f-mobile")[0]
                                    .classList.remove("on_show");
                                }
                              );
                            }
                          });
                        }
                      }
                    );
                  }
                });
              }
            }
          }
        );
      } else if (_type == "favourite") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "course");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addFavourite",
          formData,
          (res_fav) => {
            // console.log(data);
            // setReloadRelate(true);
            if(res_fav['status']=='success'){
              if(res_fav['action']=='add'){
                document.querySelector(".favourite_class_"+_id).classList.add("active");
              }else{
                document.querySelector(".favourite_class_"+_id).classList.remove("active");
              }
            }
          }
        );
      } else if (_type == "free") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderFree",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              data["data"]["allowed"] = true;
              setReloadVdo(true);
            } else {
              Swal.fire({
                text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      } else if (_type == "vip") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderVip",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              data["data"]["allowed"] = true;
              setReloadVdo(true);
            } else {
              Swal.fire({
                text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      } else if (_type == "volume") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderVolume",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              data["data"]["allowed"] = true;
              setReloadVdo(true);
            } else {
              Swal.fire({
                text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      } else if (_type == "internal") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderInternal",
          formData,
          (obj) => {
            if (obj["status"] == "success") {
              data["data"]["allowed"] = true;
              setReloadVdo(true);
            } else {
              Swal.fire({
                text: translateEng('พบข้อผิดพลาด กรุณาลองอีกครั้ง'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
          }
        );
      } 
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function addDiscountCode(_code, _type) {
    if (user) {
      const formData = new URLSearchParams();
      formData.append("course_id", data["data"]["id"]);
      formData.append("content_type", "course");
      formData.append("discount_code", _code);
      formData.append("discount_channel", _type);
      setCoursePriceReload(true);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addDiscountCode",
        formData,
        (obj) => {
          // console.log(obj);
          setCoursePriceReload(false);
          if (obj["is_discount"]) {
            if (_type == "code") {
              Swal.fire({
                text: translateEng('ยินดีด้วย ใช้โค้ดส่วนลดสำเร็จ'),
                icon: "success",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: translateEng('ยินดีด้วย คุณได้รับการสนับสนุน'),
                icon: "success",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
              document.getElementById("input_code").value = _code;
            }
            if (data["data"]["is_promotion"] == 1) {
              if (obj["discount_type"] == "regular") {
                data["data"]["pro_price"] =
                  courseProPrice - obj["discount_value"];
              } else {
                data["data"]["pro_price"] =
                  courseProPrice - (courseProPrice * obj["discount_value"]) / 100;
              }
              if(data["data"]["pro_price"]<0){
                data["data"]["pro_price"] = 0;
              }
            } else {
              if (obj["discount_type"] == "regular") {
                data["data"]["price"] = coursePrice - obj["discount_value"];
              } else {
                data["data"]["price"] =
                  coursePrice - (coursePrice * obj["discount_value"]) / 100;
              }
              if(data["data"]["price"]<0){
                data["data"]["price"] = 0;
              }
            }
          } else {
            if (_type == "code") {
              Swal.fire({
                text: translateEng('ขออภัย ไม่พบโค้ดส่วนลด'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            } else {
              Swal.fire({
                text: translateEng('ขออภัย ไม่พบผู้สนับสนุน'),
                icon: "error",
                confirmButtonText: translateEng('ปิด'),
                confirmButtonColor: "#648d2f"
              });
            }
            if (data["data"]["is_promotion"] == 1) {
              data["data"]["pro_price"] = courseProPrice;
            } else {
              data["data"]["price"] = coursePrice;
            }
          }
          setDiscountChannel(obj["discount_channel"]);
          setDiscountCode(obj["discount_code"]);
          setTimeout(() => {
            setCoursePriceReload(true);
          }, "0");
        }
      );
    } else {
      Swal.fire({
        text: translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }

  function translateEng(_value) {
    if(lang=='en'){
      if(_value=='ฟรี'){
        return "Free";
      }else if(_value=='บาท'){
        return "Baht";
      }else if(_value=='อ่าน'){
        return "Read";
      }else if(_value=='คะแนน'){
        return "Points";
      }else if(_value=='ปกติ'){
        return "Normal";
      }else if(_value=='ราคา'){
        return "Price";
      }else if(_value=='สถิติ'){
        return "Statistics";
      }else if(_value=='ภาพรวมการทำแบบทดสอบ'){
        return "Examination overview";
      }else if(_value=='แบบทดสอบทั้งหมด'){
        return "All Examinations";
      }else if(_value=='ชุด'){
        return "Sets";
      }else if(_value=='ทำแล้ว'){
        return "Done";
      }else if(_value=='ผ่าน'){
        return "Pass";
      }else if(_value=='ไม่ผ่าน'){
        return "Fail";
      }else if(_value=='ยังไม่ได้ทำ'){
        return "Not yet";
      }else if(_value=='เฉลี่ยคะแนนที่ทำได้'){
        return "Average score";
      }else if(_value=='คะแนนสูงสุด'){
        return "Highest score";
      }else if(_value=='คะแนนที่ทำได้'){
        return "Score";
      }else if(_value=='คะแนนต่ำสุด'){
        return "Lowest score";
      }else if(_value=='ผลคะแนน'){
        return "Result";
      }else if(_value=='อันดับของคุณ จากผู้ทำแบบทดสอบทั้งหมด'){
        return "Your ranking";
      }else if(_value=='อันดับ'){
        return "Ranking";
      }else if(_value=='ชื่อ'){
        return "Name";
      }else if(_value=='วันที่ทำ'){
        return "Date";
      }else if(_value=='วันที่'){
        return "Date";
      }else if(_value=='รอการตรวจสอบ'){
        return "Pending";
      }else if(_value=='ประวัติการทำแบบทดสอบหลังเรียน'){
        return "Post-Examination history";
      }else if(_value=='ประวัติการทำแบบทดสอบก่อนเรียน'){
        return "Pre-Examination history";
      }else if(_value=='ทดสอบครั้งที่'){
        return "Round";
      }else if(_value=='ชื่อแบบทดสอบ'){
        return "Examination Title";
      }else if(_value=='สถานะ'){
        return "Status";
      }else if(_value=='ผลการทดสอบ'){
        return "Result";
      }else if(_value=='ตรวจแล้ว'){
        return "Checked";
      }else if(_value=='ส่งคำตอบสำเร็จ'){
        return "Submitted";
      }else if(_value=='ปิด'){
        return "Close";
      }else if(_value=='กรุณาตอบคำถามให้ครบถ้วน'){
        return "Please answer questions completely";
      }else if(_value=='คุณต้องการเริ่มเรียนใช่หรือไม่?'){
        return "Do you want to start studying?";
      }else if(_value=='คุณต้องการรับ E-Book ฟรีใช่หรือไม่?'){
        return "Do you want to get a free E-Book?";
      }else if(_value=='ใช่'){
        return "Yes";
      }else if(_value=='ไม่'){
        return "No";
      }else if(_value=='กรุณาซื้อคอร์สนี้ก่อนค่ะ'){
        return "Please buy this course";
      }else if(_value=='กรุณาซื้อ E-Book นี้ก่อนค่ะ'){
        return "Please buy this E-Book";
      }else if(_value=='คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?'){
        return "You have another item in your cart<br>Want to buy this product?";
      }else if(_value=='ยืนยัน'){
        return "Submit";
      }else if(_value=='ยกเลิก'){
        return "Cancel";
      }else if(_value=='พบข้อผิดพลาด กรุณาลองอีกครั้ง'){
        return "Found an error, please try again";
      }else if(_value=='กรุณาเข้าสู่ระบบค่ะ'){
        return "Please login";
      }else if(_value=='กรุณาเลือกเพลย์ลิสต์'){
        return "Choose playlist";
      }else if(_value=='ยินดีด้วย ใช้โค้ดส่วนลดสำเร็จ'){
        return "Congratulations, the discount code has been used successfully";
      }else if(_value=='ยินดีด้วย คุณได้รับการสนับสนุน'){
        return "Congratulations, you are supported";
      }else if(_value=='ขออภัย ไม่พบโค้ดส่วนลด'){
        return "Sorry, the discount code could not be found";
      }else if(_value=='ขออภัย ไม่พบผู้สนับสนุน'){
        return "Sorry, the supporter could not be found";
      }else if(_value=='บทเรียนทั้งหมด'){
        return "All lessons";
      }else if(_value=='เอกสารประกอบการเรียน'){
        return "Documents";
      }else if(_value=='ดาวน์โหลดไฟล์ในบทเรียน'){
        return "Download";
      }else if(_value=='รายละเอียด'){
        return "Details";
      }else if(_value=='ถาม-ตอบ'){
        return "Q & A";
      }else if(_value=='แบบทดสอบ'){
        return "Examination";
      }else if(_value=='คะแนนล่าสุดของคุณ'){
        return "Recently score";
      }else if(_value=='เริ่มใหม่'){
        return "Restart";
      }else if(_value=='แบบทดสอบของคุณอยู่ระหว่างตรวจสอบ'){
        return "Your test is under review";
      }else if(_value=='โค้ดส่วนลด'){
        return "Discount code";
      }else if(_value=='เลือกรับ Scholarship จากผู้สนับสนุน'){
        return "Select scholarship";
      }else if(_value=='เพิ่มไปยังเพลย์ลิสต์ของคุณ'){
        return "Add to playlist";
      }else if(_value=='สร้างเพลย์ลิสต์'){
        return "Create playlist";
      }else if(_value=='เพลย์ลิสต์ของคุณ'){
        return "Your playlist";
      }else if(_value=='ป้อนชื่อเพลย์ลิสต์'){
        return "Enter plalist title";
      }else if(_value=='เริ่มเรียน'){
        return "Start learning";
      }else if(_value=='รับฟรี'){
        return "Get Free";
      }else if(_value=='ซื้อคอร์สนี้'){
        return "Buy this course";
      }else if(_value=='ซื้อ E-Book'){
        return "Buy E-Book";
      }else if(_value=='คอร์สเกี่ยวข้อง'){
        return "Related course";
      }else if(_value=='E-Book ที่เกี่ยวข้อง'){
        return "Related E-Book";
      }else if(_value=='ข้อที่'){
        return "No.";
      }else if(_value=='ส่งคำตอบ'){
        return "Submit answer";
      }else if(_value=='ข้าม'){
        return "Skip";
      }else if(_value=='ต่อไป'){
        return "Next";
      }else if(_value=='แบบประเมินความพึงพอใจ (ใช้เวลาไม่ถึง 1 นาที)'){
        return "Assessment (It takes less than 1 minute.)";
      }else if(_value=='คำตอบ'){
        return "Answer";
      }else if(_value=='คำตอบที่ถูก'){
        return "Correct";
      }else if(_value=='คำอธิบาย'){
        return "Explain";
      }else if(_value=='ดูคำตอบ'){
        return "View";
      }else if(_value=='ยินดีด้วย!'){
        return "Congratulations!";
      }else if(_value=='คุณปลดล็อกใบประกาศ'){
        return "You unlocked the certificate";
      }else if(_value=='สามารถดาวน์โหลดใบประกาศได้ที่โปรไฟล์ของท่าน'){
        return "Certificate can be download at your profile";
      }else if(_value=='หรือคลิกที่นี่'){
        return "Or click here";
      }else if(_value=='คอร์ส'){
        return "Course";
      }else if(_value=='ดาวน์โหลด'){
        return "Download";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  return (
    <>
      <div className={`main-all page-course ${fullScreen ? "full_ebook":""} ${singlePage ? "single_page":""} ${toggleZoom?'is_zoom':''}`}>
        {!fullScreen?(
          <Head>
            {seo_data.seo ? (
              seo_data.seo.map((val, key) =>
                val.name == "title" ? (
                  <>
                    <title>{val.content}</title>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                ) : (
                  <>
                    <meta key={key} name={val.name} content={val.content} />
                    <meta
                      key={"og:" + key}
                      name={"og:" + val.name}
                      content={val.content}
                    />
                    <meta
                      key={"twitter:" + key}
                      name={"twitter:" + val.name}
                      content={val.content}
                    />
                  </>
                )
              )
            ) : (
              <>
                <title>MDCU : MedU MORE</title>
              </>
            )}

            <meta
              key={"twitter:card"}
              name={"twitter:card"}
              content="summary_large_image"
            />
            <meta key={"og:type"} name={"og:type"} content="website" />
          </Head>
        ):null}
        {!fullScreen?(
          <Header></Header>
        ):null}
        <div className="main-body bg-white">
          {!fullScreen?(
            <div className="fix-space"></div>
          ):null}
          {!fullScreen?(
            <CourseBanner data={data["data"]}></CourseBanner>
          ):null}
          <div className={`container custom-container space-between-content `}>
            <div className="podcast-page ebook-page">
              {!fullScreen?(
                <div className="row podcast-row">
                  <div className="col-6 first">
                    <EbookBanner data={data["data"]}></EbookBanner>
                  </div>
                  <div className="col-6">
                    {reloadStar ? (
                      <CourseDetailDescription
                        data={data["data"]}
                        callback={courseDescription}
                        lang={lang}
                      ></CourseDetailDescription>
                    ) : null}
                  </div>
                </div>
              ):null}
              {fullScreen?(
                <div className="left-ebook-page" onClick={() => {ebookScrollLeft()}}>
                  {nowPage>0?(
                    <i className="icon-ic-left"></i>
                  ):null}
                  <div className="switch-view">
                    <div className="action-out">
                      <div className={`action ${showControl?'active':''}`} onClick={() => {
                        if(showControl){
                          setShowControl(false);
                        }else{
                          setShowControl(true);
                        }
                      }}>
                        <i className="icon-ic-right"></i>
                      </div>
                    </div>
                    <div className={`switch-button ${showControl?'active':''}`}>
                      <div className={`item ${!singlePage?'active':''}`} onClick={() => {setSinglePage(false)}}>
                        <i className="icon-ic-lessons"></i>
                      </div>
                      <div className={`item ${singlePage?'active':''}`} onClick={() => {setSinglePage(true)}}>
                        <i className="icon-ic-document"></i>
                      </div>
                    </div>
                  </div>
                </div>
              ):null}
              <div className="row ebook-price">
                {!fullScreen?(
                  <>
                    <div className="col-12 col-md-6 col-lg-6 col-xl-6">
                        
                        {
                          data["data"]["allowed"] || data.data.order_status == 4 || data.data.order_status == 1 || (data && data["data"] && data["data"]["is_soon"] == true) ? 
                          null : (
                            <div className="block-to-buy-course">
                              {(data.data.price == 0 || (data.data.is_promotion == 1 && data.data.pro_price == 0) || data.data.is_internal || data.data.is_subscription|| data.data.is_volume)&&discountCode=='' ? 
                              null : (
                                <div className="row discount-support">
                                  <div className="col-12 col-lg-12 col-xl-6">
                                    <p
                                      className={`space-between-content-top ${styles.titleButtonGroup}`}
                                    >
                                      {translateEng('โค้ดส่วนลด')}
                                    </p>
                                    <CourseDetailCode
                                      callback={addDiscountCode}
                                      lang={lang}
                                    ></CourseDetailCode>
                                  </div>

                                  {data &&
                                  data["scholarshipList"] &&
                                  data["scholarshipList"].length > 0 ? (
                                    <div className="col-12 col-lg-12 col-xl-6">
                                      <p className="space-between-content-top title-button-group">
                                        {translateEng('เลือกรับ Scholarship จากผู้สนับสนุน')}
                                      </p>
                                      <CourseDetailScholarship
                                        callback={addDiscountCode}
                                        name="courseScholarship"
                                        data={data["scholarshipList"]}
                                      ></CourseDetailScholarship>
                                    </div>
                                  ) : null}
                                </div>
                              )}
                            </div>
                          )
                        }
                    </div>
                    <div className="col-12 col-md-6 col-lg-6 col-xl-6">
                      <div className="box-price-to-buy">
                        {coursePriceReload && data && data["data"] && data["data"]["is_soon"] == false ? (
                          data.data.allowed || data.data.order_status == 4 || data.data.order_status == 1 ? null : 
                          data.data.is_subscription ? (
                            <div className="block-course-detail-price text-center">
                              <div className="description-big-price">Member</div>
                            </div>
                          ) :
                          data.data.is_internal || data.data.is_volume ? (
                            <div className="block-course-detail-price text-center">
                              <div className="description-big-price">{translateEng('ฟรี')}</div>
                            </div>
                          ) : data.data.is_promotion == 1 ? (
                            <div className="block-course-detail-price text-center">
                              <div className="description-sale">
                                <NumberFormat
                                  value={data.data.price}
                                  displayType={"text"}
                                  thousandSeparator={true}
                                  renderText={(value, props) => (
                                    <span {...props}>{translateEng('ปกติ')} {value}</span>
                                  )}
                                />
                              </div>
                              {data.data.pro_price == 0 ? (
                                <div className="description-big-price">{translateEng('ฟรี')}</div>
                              ) : (
                                <div className="description-big-price">
                                  {translateEng('ราคา')}
                                  <NumberFormat
                                    value={data.data.pro_price}
                                    displayType={"text"}
                                    thousandSeparator={true}
                                    renderText={(value, props) => (
                                      <span {...props}>{value}</span>
                                    )}
                                  />
                                  {translateEng('บาท')}
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="block-course-detail-price text-center">
                              {data.data.price == 0 ? (
                                <div className="description-big-price">{translateEng('ฟรี')}</div>
                              ) : (
                                <div className="description-big-price">
                                  {translateEng('ราคา')}
                                  <NumberFormat
                                    value={data.data.price}
                                    displayType={"text"}
                                    thousandSeparator={true}
                                    renderText={(value, props) => (
                                      <span {...props}>{value}</span>
                                    )}
                                  />
                                  {translateEng('บาท')}
                                </div>
                              )}
                            </div>
                          )
                        ) : null}
                        {data["data"]["allowed"] ||
                        data.data.order_status == 4 ||
                        data.data.order_status == 1 ||
                        (data &&
                          data["data"] &&
                          data["data"]["is_soon"] == true) ? 
                          data["data"]["allowed"] ? null:null : (
                          <div className="block-to-buy-course">
                            {(data.data.price == 0 ||
                            (data.data.is_promotion == 1 &&
                              data.data.pro_price == 0) ||
                            data.data.is_internal ||
                            data.data.is_subscription||
                            data.data.is_volume)&&discountCode=='' ? (
                              data.data.is_subscription ? (
                                <button
                                  className="btn-to-buy-course"
                                  onClick={() =>
                                    groupCategoryCallback(
                                      "vip",
                                      data["data"]["id"],
                                      ""
                                    )
                                  }
                                >
                                  <span>{translateEng('รับฟรี')}</span>
                                </button>
                              ):(
                                data.data.is_volume ? (
                                  <button
                                    className="btn-to-buy-course"
                                    onClick={() =>
                                      groupCategoryCallback(
                                        "volume",
                                        data["data"]["id"],
                                        ""
                                      )
                                    }
                                  >
                                    <span>{translateEng('รับฟรี')}</span>
                                  </button>
                                ):(
                                  data.data.is_internal&&data.data.user_internal ? (
                                    <button
                                      className="btn-to-buy-course"
                                      onClick={() =>
                                        groupCategoryCallback(
                                          "internal",
                                          data["data"]["id"],
                                          ""
                                        )
                                      }
                                    >
                                      <span>{translateEng('รับฟรี')}</span>
                                    </button>
                                  ):(
                                    <button
                                      className="btn-to-buy-course"
                                      onClick={() =>
                                        groupCategoryCallback(
                                          "free",
                                          data["data"]["id"],
                                          ""
                                        )
                                      }
                                    >
                                      <span>{translateEng('รับฟรี')}</span>
                                    </button>
                                  )
                                )
                              )
                              
                            ) : (
                              <button
                                className="btn-to-buy-course"
                                onClick={() =>
                                  groupCategoryCallback(
                                    "cart",
                                    data["data"]["id"],
                                    ""
                                  )
                                }
                              >
                                <span>{translateEng('ซื้อ E-Book')}</span>
                              </button>
                            )}
                          </div>
                        )}

                        {data["data"]["is_soon"] == true ? (
                          <div className="block-course-detail-price text-center">
                            <div className="description-big-price">Coming soon</div>
                          </div>
                        ) : null}
                        <div className="space-fix-price"></div>
                      </div>
                    </div>
                    {data["documentList"] && data["documentList"].length>0 ?(
                      <div className="ebook-block">
                        {data["documentList"].map((val, key) =>
                          <div className="ebook-item" key={key}>
                              {val.thumb && val.thumb!=null && val.thumb!='' && val.thumb!='null' ?(
                                <div className="thumb" onClick={() => {setFile(process.env.NEXT_PUBLIC_API+"/ebook_preview/"+val.id_encrypt);}}>
                                  <Image src={val.thumb} alt="" layout="fill" objectFit="contain"/> 
                                  {!data["data"]["allowed"] ?(
                                    <div className="lock" onClick={() => selectLockDoc()}>
                                      <i className={`icon-ic-lock`}></i>
                                    </div>
                                  ):null}
                                </div>
                              ):null}
                              <div className="title">
                                <h3>{val.title_th}</h3>
                              </div>
                              {!data["data"]["allowed"] ?(
                                <div className="download">
                                  <i className={`icon-ic-lock`} onClick={() => selectLockDoc()}></i>
                                  <p onClick={() => selectLockDoc()}>{translateEng('ดาวน์โหลด')}</p>
                                </div>
                              ):(
                                !val.view_doc &&  data["data"]["is_download"] ?(
                                  <a className="download" onClick={() => {
                                    if(data["data"]["is_promotion"]==1){
                                      if(data["data"]["pro_price"]!=0){
                                        Swal.fire({
                                          html: translateEng('กรุณากดยืนยัน เพื่อรับ E-Book<br>โดยจะมีข้อมูลของท่านเป็นลายน้ำอยู่บน E-Book'),
                                          icon: 'info',
                                          showCancelButton: true,
                                          confirmButtonColor: '#648d2f',
                                          cancelButtonColor: '#d33',
                                          confirmButtonText: translateEng('ใช่'),
                                          cancelButtonText: translateEng('ไม่')
                                        }).then((result) => {
                                          if (result.isConfirmed) {
                                            val.view_doc = true;
                                            window.open(
                                              process.env.NEXT_PUBLIC_API+"/ebook/"+utoken+'/'+val.id_encrypt,
                                              '_blank'
                                            );
                                          }
                                        })
                                      }else{
                                        window.open(
                                          process.env.NEXT_PUBLIC_API+"/ebook/"+utoken+'/'+val.id_encrypt,
                                          '_blank'
                                        );
                                      }
                                    }else{
                                      if(data["data"]["price"]!=0){
                                        Swal.fire({
                                          html: translateEng('กรุณากดยืนยัน เพื่อรับ E-Book<br>โดยจะมีข้อมูลของท่านเป็นลายน้ำอยู่บน E-Book'),
                                          icon: 'info',
                                          showCancelButton: true,
                                          confirmButtonColor: '#648d2f',
                                          cancelButtonColor: '#d33',
                                          confirmButtonText: translateEng('ใช่'),
                                          cancelButtonText: translateEng('ไม่')
                                        }).then((result) => {
                                          if (result.isConfirmed) {
                                            val.view_doc = true;
                                            window.open(
                                              process.env.NEXT_PUBLIC_API+"/ebook/"+utoken+'/'+val.id_encrypt,
                                              '_blank'
                                            );
                                          }
                                        })
                                      }else{
                                        window.open(
                                          process.env.NEXT_PUBLIC_API+"/ebook/"+utoken+'/'+val.id_encrypt,
                                          '_blank'
                                        );
                                      }
                                    }
                                  }}>
                                    <IoMdDownload />
                                    <p>{translateEng('ดาวน์โหลด')}</p>
                                  </a>
                                ):(
                                  <a className="download"
                                    onClick={() => {setFile(process.env.NEXT_PUBLIC_API+"/ebook_preview/"+val.id_encrypt);setFullScreen(true);}}
                                  >
                                    <i className="eye icon"></i>
                                    <p>{translateEng('อ่าน')}</p>
                                  </a>
                                )
                              )}
                          </div>
                        )}
                      </div>
                    ):null}
                  </>
                ):null}
                {!data["data"]["allowed"] ?(
                  <div className="preview-pdf-block">
                    <div ref={ref} onScroll={() => {handleScroll()}} {...events} className={`react-pdf-container`}>
                      {file!=null && file!='' && file!='null' ?(
                        <Document file={file} onLoadSuccess={onDocumentLoadSuccess}>
                          {Array.from({ length: numPages }, (_, index) => (
                            (pagePreview.indexOf((index+1).toString()))>-1?(
                              <div className="block-page-pdf">
                                <Page
                                  onClick={() => {setFullScreen(true)}}
                                  key={`page_${index + 1}`}
                                  pageNumber={index + 1}
                                  renderAnnotationLayer={false}
                                  renderTextLayer={false}
                                />
                              </div>
                            ):null
                          ))}
                        </Document>
                      ):null}
                    </div>
                  </div>
                ):(
                  <div className={`preview-pdf-block`}>
                    <div ref={ref} onScroll={() => {handleScroll()}} {...events} className={`react-pdf-container`}>
                      {file!=null && file!='' && file!='null' ?(
                        <Document file={file} onLoadSuccess={onDocumentLoadSuccess}>
                          {Array.from({ length: numPages }, (_, index) => (
                            <div className="block-page-pdf">
                              <Page
                                onClick={() => {setFullScreen(true)}}
                                key={`page_${index + 1}`}
                                pageNumber={index + 1}
                                renderAnnotationLayer={false}
                                renderTextLayer={false}
                              />
                              <div className="number-pdf-page">
                                <p>หน้า {(index+1)+' จาก '+(numPages)}</p>
                              </div>
                            </div>
                          ))}
                        </Document>
                      ):null}
                    </div>
                  </div>
                )}
                {!fullScreen?(
                  <Tab
                    panes={panes}
                    renderActiveOnly={false}
                    className={`tab-comment-ebook space-between-content-top courseTab`}
                  />
                ):null}
                {!fullScreen?(
                  data.data && data.data.tag.length > 0 ? (
                    <div className="col-12 course-page">
                      <div className="block-quick-result">
                        <ListTags data={data.data.tag}></ListTags>
                      </div>
                    </div>
                  ) : null
                ):null}
              </div>
              {fullScreen?(
                <div className="right-ebook-page">
                  {(singlePage&&(nowPage+1)<allPages)||(!singlePage&&screen.width>991&&(nowPage+2)<allPages)||(!singlePage&&screen.width<=991 && (nowPage+1)<allPages)||allPages==1?(
                    <div className="outer-next-ebook" onClick={() => {ebookScrollRight()}}>
                      <i className="icon-ic-right"></i>
                    </div>
                  ):null}
                  <div className="close-ebook">
                    <div className="action" onClick={() => {setFullScreen(false);}}>
                      <i className="icon-ic-close"></i>
                    </div>
                    <div className={`action zoom ${toggleZoom?'zoom_now':''}`} onClick={() => {setToggleZoom(!toggleZoom);}}>
                      {toggleZoom?(
                        <i className="icon-ic-search-x"></i>
                      ):(
                        <i className="icon-ic-search-no-results"></i>
                      )}
                    </div>
                  </div>
                </div>
              ):null}
            </div>
          </div>
          {!fullScreen?(
            relateData&&relateData.length>0 ? (
              <GroupCategory
                type="relate"
                name={translateEng('E-Book ที่เกี่ยวข้อง')}
                bg={''}
                color="#6E953D"
                size={3.5}
                isLoop={false}
                index={20}
                data={relateData}
                callback={groupCategoryCallback}
              ></GroupCategory>
            ) : null
          ):null}
          {/* {!fullScreen?( */}
            <Footer></Footer>
          {/* ):null} */}
        </div>
      </div>
    </>
  );
}

import React from "react";
// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "./api/user";
import { useState, useEffect, useContext } from "react";
import AppContext from "/libs/contexts/AppContext";
import _ from "lodash";
// Serverside & Api fetching
import { useSession, getSession } from "next-auth/react";
import Error from "next";

import Head from "next/head";
import Header from "../themes/header/header";
import Footer from "../themes/footer/footer";
import HeroBanner from "../themes/components/heroBanner";
import SlideBanner from "../themes/components/slideBanner";
import GroupCategory from "../themes/components/groupCategory";
import GroupSocialShare from "../themes/components/groupSocialShare";
import ListTags from "../themes/components/listTags";
import HomeSponsors from "../themes/components/homeSponsors";
import ButtonCreateList from "../themes/components/buttonCreateList";
import Image from 'next/image';

import Swal from "sweetalert2";
import {
  Menu,
  Button,
  Modal,
  Select,
  Input,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";


import { createSnowfall } from '../libs/snowfall'; 

import { useRouter } from "next/router";
import { fetchHomeData } from "@/services/homeService";

function show_add_list() {
  document.getElementById("fmCreateListAdd").classList.toggle('active');
}
export async function getServerSideProps(context) {
   return await fetchHomeData(context);
}

export default function Home({ seo_data, utoken, user,banner_data,sponsor_data,search_data,list_arr_data,settingCenter, coursesLatest, error, message}) {
  // Handle error case
  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h1>Error Loading Page</h1>
        <p>{message || 'An error occurred while loading the page.'}</p>
        <button onClick={() => window.location.reload()}>Retry</button>
      </div>
    );
  }

  // console.log('*****seo_data*****')
  // console.log(seo_data)
  const { data: session, status: session_status } = useSession();
  const appContext = useContext(AppContext);
  const [homeBanner, setHomeBanner] = useState(banner_data);
  const [homeCourse, setHomeCourse] = useState(null);
  const [homeSponsor, setHomeSponsor] = useState(sponsor_data);
  const [homeSearch, setHomeSearch] = useState(search_data);
  const [reload, setReload] = useState(true);
  const [firstload, setFirstload] = useState(true);
  const [loadMore, setLoadMore] = useState(false);
  const [firstPopup, setFirstPopup] = useState(true);
  const [popShow, setPopShow] = useState(false);
  const [offset, setOffset] = useState(0);
  const [scrollLoad, setScrollLoad] = useState(true);
  const [addPlaylist, setAddPlaylist] = useState(false);
  const [courseId, setCourseId] = useState(null);
  const [playlistTitle, setPlaylistTitle] = useState(null);
  const [playlistSelect, setPlaylistSelect] = useState(null);
  const [homeFilterId, setHomeFilterId] = useState(null);
  const [playListOptions, setPlayListOptions] = useState([]);
  const [thisPage, setThisPage] = useState(1);

  const [conversationHistory, setConversationHistory] = useState([]);
  const [showAlertVerifyDoctor, setShowAlertVerifyDoctor] = useState(false);
  const [countdownCloseAlert, setCountdownCloseAlert] = useState(59);


  // ฟังก์ชันสำหรับบันทึกการสนทนา
  const addConversation = (newMessage) => {
    setConversationHistory((prevHistory) => [...prevHistory, newMessage]);
  };


  // ดึงข้อมูล history จาก server หรือ local storage เมื่อ component โหลด
  useEffect(() => {
    appContext.setToken(utoken);
    // สมมติว่าคุณ fetch ข้อมูลจาก API หรือ local storage
    const fetchConversationHistory = () => {
      const storedHistory = JSON.parse(localStorage.getItem('conversationHistory')) || [];
      setConversationHistory(storedHistory);
    };

    fetchConversationHistory();
  }, []);
  // === for heroBanner end ===


 
  useEffect(() => {
    if (!user) {
      console.log('not login');
      return; // ดักกรณีที่ user เป็น null หรือ undefined
    }
    const timer = setInterval(() => {
      setCountdownCloseAlert((prevTime) => (prevTime > 0 ? prevTime - 1 : 0));
    }, 1000);

    if ( user.user_type == 1 && 
      (user.verify_doctor_status == "CANCEL" || user.verify_doctor_status == "EXPIRED" ||user.verify_doctor_status == "" || user.verify_doctor_status == null) 
    ) {
      localStorage.setItem("verify_mdeconnect_page", "HOME");
      setShowAlertVerifyDoctor(true);
      setTimeout(() => {
        setShowAlertVerifyDoctor(false);
      }, (1000*300)); // หายไปหลัง 60 วินาที
    }
  }, [user]);


  const schemaData = {
    "@context": "https://schema.org/",
    "@type": "WebSite",
    "url": "https://www.medumore.org",
    "name": seo_data["data"]["meta_title"],
    "image": seo_data["data"]["meta_image"],
    "description": seo_data["data"]["meta_description"],
    "brand": "MedUMore",
  };

  useEffect(() => {
    const onScroll = () => setOffset(window.pageYOffset);
    window.removeEventListener("scroll", onScroll);
    window.addEventListener("scroll", onScroll, { passive: true });
    var footer_real_pos = document.getElementById("footer_position").offsetTop + document.getElementById("header_position").offsetHeight - (screen.height * 2);
    if(window.pageYOffset>=footer_real_pos && !firstload && loadMore && thisPage<list_arr_data.allpage){
      setLoadMore(false);
      setThisPage(thisPage+1);
      appContext.setReloadCourse(true);
    }
  }, [offset]);

  function toDate(s) {
    s = s.split(/\D/g);
    return new Date(s[2],--s[1],s[0]);
  }
  function afterNow(s) {
    var now = new Date();
    return +toDate(s) > +now;
  }

  const [homePopup, setHomePopup] = useState(settingCenter);
  const [imageLink, setImageLink] = useState(null);
  useEffect(() => {
    if(firstPopup){
      setFirstPopup(false);
      if(homePopup && homePopup.status && homePopup.status=='success' && (homePopup.image && homePopup.image != null && homePopup.image != '')){
        setTimeout(() => {
          setImageLink(homePopup.image);
          setPopShow(true);
        }, homePopup.delay*1000);
      }
    }
  }, [firstPopup]);
  
  useEffect(() => {
    if(appContext.reloadCourse){
      appContext.setReloadCourse(false);
      appContext.setLoadPercent(true);
      const filter_search = new URLSearchParams();
      filter_search.append("filter_id", homeFilterId);
      filter_search.append("page", thisPage);
      filter_search.append("list_arr", JSON.stringify(list_arr_data.list_array))
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/homeCourse",
        filter_search,
        (data) => {
          setLoadMore(true);
          setFirstload(false);
          if(thisPage==1){
            setHomeCourse(data.homeContent);
          }else{
            var rewrite = homeCourse;
            for(var i = 0; i<data.homeContent.length; i++){
              rewrite.push(data.homeContent[i]);
            }
            setHomeCourse(rewrite);
          }
          setPlayListOptions(data.userPlaylist);
          appContext.setLoadPercent(false);
        }
      );
    }
  }, [appContext.reloadCourse]);

  const router = useRouter();

  useEffect(() => {
    // ฟังก์ชันสำหรับลบ snowContainer
    const removeSnow = () => {
      const snowContainer = document.querySelector(
        'div[style*="pointer-events: none"]'
      );
      if (snowContainer) {
        snowContainer.remove();
      }
    };

    // กำหนดช่วงวันที่ที่มีหิมะตก
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth(); // เดือนเริ่มจาก 0 (มกราคมคือ 0)
    const currentDay = currentDate.getDate(); // วันที่ในเดือน

    const isSnowSeason =
      (currentMonth === 11 && currentDay >= 15) || // 15 ธันวาคมถึง 31 ธันวาคม
      (currentMonth === 0 && currentDay <= 15); // 1 มกราคมถึง 15 มกราคม

    // ตรวจสอบเงื่อนไข
    if (router.pathname === "/" && isSnowSeason) {
      // ตรวจสอบว่าหิมะถูกสร้างไปแล้วหรือยัง
      const existingSnowContainer = document.querySelector(
        'div[style*="pointer-events: none"]'
      );
      if (!existingSnowContainer) {
        createSnowfall();
      }
    } else {
      removeSnow(); // ลบหิมะเมื่อไม่ได้อยู่ในช่วงวันที่หรือหน้าแรก
    }

    // ลบหิมะเมื่อออกจากหน้าเดิม
    return () => {
      removeSnow();
    };
  }, [router.pathname]);

  


  function groupCategoryCallback(_type, _id ,_title) {
    if (user) {
      if (_type == "cart") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "course");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
          formData,
          (res_check) => {
            if(res_check['status']=='success'){
              if(res_check['count']==0){
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                  formData,
                  (data) => {
                    // console.log(data);
                    appContext.setReloadCart(true);

                    document
                      .getElementsByClassName("main-header")[0]
                      .classList.add("active_cart");
                    document
                      .getElementsByClassName("group-menu-cart")[0]
                      .classList.add("on_show");
                    document.body.classList.add("open_cart");
                    document
                      .getElementsByClassName("group-menu-f-mobile")[0]
                      .classList.remove("on_show");
                  }
                );
              }else{
                Swal.fire({
                  html: "คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?",
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: 'ยืนยัน',
                  cancelButtonText: 'ยกเลิก'
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                      formData,
                      (res_clear) => {
                        if(res_clear['status']=='success'){
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                            formData,
                            (data) => {
                              // console.log(data);
                              appContext.setReloadCart(true);
          
                              document
                                .getElementsByClassName("main-header")[0]
                                .classList.add("active_cart");
                              document
                                .getElementsByClassName("group-menu-cart")[0]
                                .classList.add("on_show");
                              document.body.classList.add("open_cart");
                              document
                                .getElementsByClassName("group-menu-f-mobile")[0]
                                .classList.remove("on_show");
                            }
                          );
                        }
                      }
                    );
                  }
                });
              }
            }
          }
        );
      } else if (_type == "group") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "group");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/checkCart",
          formData,
          (res_check) => {
            if(res_check['status']=='success'){
              if(res_check['count']==0){
                appContext.sendApi(
                  process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                  formData,
                  (data) => {
                    // console.log(data);
                    if(data['status']&&data['status']=='free'){
                      Swal.fire({
                        html: "สำเร็จ<br>สามารถตรวจสอบคอร์สของคุณได้ที่หน้าโปรไฟล์",
                        icon: "success",
                        confirmButtonText: "ปิด",
                        confirmButtonColor: "#648d2f"
                      }).then((result) => {
                      });
                    }else{
                      appContext.setReloadCart(true);
          
                      document
                        .getElementsByClassName("main-header")[0]
                        .classList.add("active_cart");
                      document
                        .getElementsByClassName("group-menu-cart")[0]
                        .classList.add("on_show");
                      document.body.classList.add("open_cart");
                      document
                        .getElementsByClassName("group-menu-f-mobile")[0]
                        .classList.remove("on_show");
                    }
                  }
                );
              }else{
                Swal.fire({
                  html: "คุณมีสินค้าอื่นอยู่ในตะกร้า<br>ต้องการซื้อสินค้านี้ใช่หรือไม่?",
                  icon: "info",
                  showCancelButton: true,
                  confirmButtonColor: '#648d2f',
                  cancelButtonColor: '#d33',
                  confirmButtonText: 'ยืนยัน',
                  cancelButtonText: 'ยกเลิก'
                }).then((result) => {
                  if (result.value) {
                    appContext.sendApi(
                      process.env.NEXT_PUBLIC_API + "/api/mdcu/clearCart",
                      formData,
                      (res_clear) => {
                        if(res_clear['status']=='success'){
                          appContext.sendApi(
                            process.env.NEXT_PUBLIC_API + "/api/mdcu/addCart",
                            formData,
                            (data) => {
                              // console.log(data);
                              if(data['status']&&data['status']=='free'){
                                Swal.fire({
                                  html: "สำเร็จ<br>สามารถตรวจสอบคอร์สของคุณได้ที่หน้าโปรไฟล์",
                                  icon: "success",
                                  confirmButtonText: "ปิด",
                                  confirmButtonColor: "#648d2f"
                                }).then((result) => {
                                });
                              }else{
                                appContext.setReloadCart(true);
                    
                                document
                                  .getElementsByClassName("main-header")[0]
                                  .classList.add("active_cart");
                                document
                                  .getElementsByClassName("group-menu-cart")[0]
                                  .classList.add("on_show");
                                document.body.classList.add("open_cart");
                                document
                                  .getElementsByClassName("group-menu-f-mobile")[0]
                                  .classList.remove("on_show");
                              }
                            }
                          );
                        }
                      }
                    );
                  }
                });
              }
            }
          }
        );
      } else if (_type == "favourite") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        formData.append("content_type", "course");
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addFavourite",
          formData,
          (res_fav) => {
            // console.log(data);
            // appContext.setReloadCourse(true);
            if(res_fav['status']=='success'){
              if(res_fav['action']=='add'){
                document.querySelector(".favourite_class_"+_id).classList.add("active");
              }else{
                document.querySelector(".favourite_class_"+_id).classList.remove("active");
              }
            }
          }
        );
      } else if (_type == "free") {
        const formData = new URLSearchParams();
        formData.append("course_id", _id);
        appContext.sendApi(
          process.env.NEXT_PUBLIC_API + "/api/mdcu/addOrderFree",
          formData,
          (data) => {
            if (data["status"] == "success") {
              Swal.fire({
                text: "ลงทะเบียนสำเร็จ เริ่มเรียนได้เลย",
                icon: "success",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              }).then((result) => {
              });
            }else{
              Swal.fire({
                text: "พบข้อผิดพลาด กรุณาลองอีกครั้ง",
                icon: "error",
                confirmButtonText: "ปิด",
                confirmButtonColor: "#648d2f"
              })
            }
          }
        );
      } else if (_type == "playlist") {
        setCourseId(_id);
        setPlaylistTitle('');
        setPlaylistSelect('');
        setAddPlaylist(true);
      }
    } else {
      Swal.fire({
        text: "กรุณาเข้าสู่ระบบค่ะ",
        icon: "info",
        confirmButtonText: "ตกลง",
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        appContext.setOpen(true);
      });
    }
  }
  function submitPlaylist() {
    if((playlistTitle!=null&&playlistTitle!='')||(playlistSelect!=null&&playlistSelect!='')){
      const formData = new URLSearchParams();
      formData.append("course_id", courseId);
      formData.append("title", playlistTitle);
      formData.append("select", playlistSelect);
      appContext.sendApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/addPlaylist",
        formData,
        (res_play) => {
          // appContext.setReloadCourse(true);
          setAddPlaylist(false);
          if(res_play['status']=='success'){
            if(res_play['action']=='add'){
              document.querySelector(".playlist_class_"+courseId).classList.add("active");
              document.querySelector(".playlist_icon_"+courseId).classList.remove("icon-ic-circle-plus");
              document.querySelector(".playlist_icon_"+courseId).classList.add("icon-ic-tick-thanks");
            }else{
              document.querySelector(".playlist_class_"+courseId).classList.remove("active");
              document.querySelector(".playlist_icon_"+courseId).classList.remove("icon-ic-tick-thanks");
              document.querySelector(".playlist_icon_"+courseId).classList.add("icon-ic-circle-plus");
            }
          }
        }
      );
    }else{
      Swal.fire({
        text: "กรุณาเลือกเพลย์ลิสต์",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f"
      });
    }
  }
  function homeFilter(_id) {
    setHomeFilterId(_id);
    setThisPage(1);
    appContext.setReloadCourse(true);
  }

  function routeToVerifyDoctor(){
    router.push('/profile/edit'); // ทำการ redirect ไปที่หน้าโฮม
  }
  return (
    <>
     <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />

    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == "title" ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={"og:" + key}
                  name={"og:" + val.name}
                  content={val.content}
                />
                <meta
                  key={"twitter:" + key}
                  name={"twitter:" + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={"twitter:card"}
          name={"twitter:card"}
          content="summary_large_image"
        />
        <meta key={"og:type"} name={"og:type"} content="website" />
      </Head>
      <Header></Header>
      <div className="main-body">
        {/* <div className="fix-space"></div>
      <h1>- - this MDCU : MedU MORE - -</h1>
      <i className='icon-ic-bell'></i> */}


        {homeBanner && homeSearch ? (
          <HeroBanner
            data={homeBanner["heroBanner"]}
            widget={homeSearch}
            user={user}
            coursesLatest={coursesLatest}
            callback={homeFilter}
            conversationHistory={conversationHistory}
            onAddConversation={addConversation}
          ></HeroBanner>

    
        ) : null}
        <div className="fix-space"></div>
        <div>
          {showAlertVerifyDoctor && (
            <div className="alert alert-warning fixed-bottom left-0 m-3" role="alert">
              คุณยังไม่ได้ยืนยันการเป็นแพทย์!
              &nbsp;<a href="#" onClick={(e) => { e.preventDefault(); routeToVerifyDoctor(); }}>ไปยืนยันเลย</a>
              &nbsp;({countdownCloseAlert}) 
            </div>
          )}
          {/* เนื้อหาของหน้าเว็บ */}
        </div>
        {seo_data.seo ? (
          <div className="hidden-seo">
            {seo_data.seo.map((val, key) =>
              val.content == 'null' || val.content == null || val.content == '' || val.content.indexOf('.jpg') > -1 || val.content.indexOf('.jpeg') > -1 || val.content.indexOf('.png') > -1 || val.content.indexOf('.webp') > -1 ? null:(
                <p key={key}>{val.content}</p>
              )
            )}
          </div>
        ) :null}
        {homeCourse &&
        homeCourse.length > 0
        ? homeCourse.map((val, key) =>
            val.type == 1 ? (
              val.data.length > 0 ? (
                <GroupCategory
                  key={key}
                  type="normal"
                  name={val.name}
                  bg={val.bg}
                  color={val.color}
                  size={val.size}
                  isLoop={val.is_loop}
                  index={40 - key}
                  data={val.data}
                  callback={groupCategoryCallback}
                ></GroupCategory>
              ) : null
            ) : val.type == 2 ? (
              val.data.length > 0 ? (
                <GroupCategory
                  key={key}
                  type="normal"
                  name={val.name}
                  bg={val.bg}
                  color={val.color}
                  size={val.size}
                  isLoop={val.is_loop}
                  index={40 - key}
                  data={val.data}
                  callback={groupCategoryCallback}
                ></GroupCategory>
              ) : null
            ) : val.type == 3 ? (
              val.data.length > 0 ? (
                <GroupCategory
                  key={key}
                  type="infographic"
                  name={val.name}
                  bg={val.bg}
                  color={val.color}
                  size={val.size}
                  isLoop={val.is_loop}
                  index={40 - key}
                  data={val.data}
                ></GroupCategory>
              ) : null
            ) : val.type == 4 || val.type == 20 ? (
              val.data.length > 0 ? (
                <GroupCategory
                  key={key}
                  type="news"
                  name={val.name}
                  bg={val.bg}
                  color={val.color}
                  size={val.size}
                  isLoop={val.is_loop}
                  index={40 - key}
                  data={val.data}
                ></GroupCategory>
              ) : null
            ) : val.type == 5 ? (
              val.data.length > 0 ? (
                <div
                  key={key}
                  className={`container-fluid pd-0`}
                >
                  <SlideBanner type="single" data={val.data}></SlideBanner>
                </div>
              ) : null
            ) : val.type == 6 ? (
              val.data.length > 0 ? (
                <div
                  key={key}
                  className={`container-fluid pd-0`}
                >
                  <SlideBanner type="multi" data={val.data}></SlideBanner>
                </div>
              ) : null
            ) : val.type == 7 ? (
              val.data.length > 0 ? (
                <GroupCategory
                  key={key}
                  type="normal"
                  name={val.name}
                  bg={val.bg}
                  color={val.color}
                  size={val.size}
                  isLoop={val.is_loop}
                  index={40 - key}
                  data={val.data}
                  callback={groupCategoryCallback}
                ></GroupCategory>
              ) : null
            // ) : val.type == 8 || val.type == 9 || val.type == 11 || val.type == 12 || val.type == 14 || val.type == 15 || val.type == 16 || val.type == 33 || val.type == 18 || val.type == 32 || val.type == 34 ? (
              
          ) : val.type == 8 || val.type == 9 || val.type == 11 || val.type == 12 || val.type == 14 || val.type == 15 || val.type == 16 || val.type == 33 || val.type == 18 || val.type == 32 ? (
              val.data.length > 0 ? (
                <GroupCategory
                  key={key}
                  type="normal"
                  name={val.name}
                  bg={val.bg}
                  color={val.color}
                  size={val.size}
                  isLoop={val.is_loop}
                  index={40 - key}
                  data={val.data}
                  callback={groupCategoryCallback}
                ></GroupCategory>
              ) : null
            ) : val.type == 34 ? (
              val.data.length > 0 ? (
              <GroupSocialShare
              name={val.name}
              bg="/images/banner.jpg"
              color="#333"
              index={1}
              isLoop={false}
              size={4}
              data={val.data}
              callback={(item) => console.log("Clicked item:", item)}
              ></GroupSocialShare>
              ) : null
            ) : (
              val.type == 17 ? (
                val.data.data.length > 0 ? (
                  <GroupCategory
                  key={key}
                  type="series"
                  title={val.data.title}
                  subtitle={val.data.subtitle}
                  backgroundImage={val.data.background}
                  backgroundColor="#e5f2c3"
                  color={val.color}
                  size={val.size}
                  isLoop={val.is_loop}
                  index={40 - key}
                  callback={groupCategoryCallback}
                  data={val.data.data}
                ></GroupCategory>
                ) : null
              ):val.type == 13 ? (
                val.data.length > 0 ? (
                  <GroupCategory
                    key={key}
                    type="continue"
                    name={val.name}
                    bg={val.bg}
                    color={val.color}
                    size={val.size}
                    isLoop={val.is_loop}
                    index={40 - key}
                    data={val.data}
                    callback={groupCategoryCallback}
                  ></GroupCategory>
                ) : null
              ):val.type == 19 ? (
                val.data.data.length > 0 ? (
                  <GroupCategory
                    key={key}
                    type="seriesBanner"
                    title={val.data.title}
                    subtitle={val.data.subtitle}
                    backgroundImage={val.data.background}
                    color={val.color}
                    size={val.size}
                    isLoop={val.is_loop}
                    index={40 - key}
                    callback={groupCategoryCallback}
                    data={val.data.data}
                    groupId={val.data.id}
                    price={val.data.price}
                    isBuy={val.data.is_buy}
                    isInternal={val.data.internal_free}
                    buttonColor="#ffffff"
                    buttonBackgroundColor="#6E953D"
                  ></GroupCategory>
                ) : null
              ):val.type == 10 ? (
                val.data.length > 0 ? (
                  <GroupCategory
                    key={key}
                    type="popular"
                    name={val.name}
                    bg={val.bg}
                    color={val.color}
                    size={val.size}
                    isLoop={val.is_loop}
                    index={40 - key}
                    data={val.data}
                    callback={groupCategoryCallback}
                  ></GroupCategory>
                ) : null
              ):val.type == 21 ? (
                val.data.length > 0 ? (
                  <GroupCategory
                    key={key}
                    type="speaker"
                    name={val.name}
                    bg={val.bg}
                    color={val.color}
                    size={val.size}
                    isLoop={val.is_loop}
                    index={40 - key}
                    data={val.data}
                    callback={groupCategoryCallback}
                  ></GroupCategory>
                ) : null
              ):val.type == 31 ? (
                val.data.length > 0 ? (
                  <GroupCategory
                    key={key}
                    type="group"
                    name={val.name}
                    bg={val.bg}
                    color={val.color}
                    size={val.size}
                    isLoop={val.is_loop}
                    index={40 - key}
                    data={val.data}
                    callback={groupCategoryCallback}
                  ></GroupCategory>
                ) : null
              ):null
            )
          )
        : null}
        
        {homeSponsor && homeSponsor["sponsor"] ? (
          <div className={`container-fluid`}>
            <HomeSponsors data={homeSponsor["sponsor"]}></HomeSponsors>
          </div>
        ) : null}
        {homeSearch && homeSearch["tag"] && homeSearch["tag"].length > 0 ? (
          <div className={`container-fluid`}>
            <ListTags data={homeSearch["tag"]}></ListTags>
          </div>
        ) : null}
      </div>
      <Modal
        className='modalCreateList'
        onClose={() => setAddPlaylist(false)}
        onOpen={() => setAddPlaylist(true)}
        open={addPlaylist}>
        <Modal.Content className="modalCreateListContent">
          <div className="block-modal-CreateList">
              <div className="inner">
                <h3>เพิ่มไปยังเพลย์ลิสต์ของคุณ</h3>
              </div>
              <div className="fm-CreateList-select">
                {playListOptions.length>0?(
                  <div className="item-fm">
                    <Select
                      className="fm-control"
                      placeholder="เพลย์ลิสต์ของคุณ"
                      options={playListOptions}
                      onChange={(event, data) => {
                        setPlaylistSelect(data.value);
                      }}
                    />
                  </div>
                ):null}
                <button className='btn-add-list' onClick={() => show_add_list()}>
                  <span>สร้างเพลย์ลิสต์</span>
                </button>
              </div>
              <div id='fmCreateListAdd' className='fm-CreateList-add'>
                <div className="item-fm">
                <Input
                  id="create_list"
                  type="text"
                  className="fm-control"
                  placeholder="ป้อนชื่อเพลย์ลิสต์"
                  onChange={(event) =>
                    setPlaylistTitle(event.target.value)
                  }
                ></Input>
                </div>
              </div>
              <div className='fm-CreateList-action'>
                <button className='btn-add-list' onClick={() => submitPlaylist()}>
                  <span>ตกลง</span>
                </button>
              </div>
          </div>
        </Modal.Content>
      </Modal>
      <Modal
        className={`modal-popup`}
        onClose={() => setPopShow(false)}
        open={popShow}
      >
        <Modal.Content className={``}>
          <div className={``}>
            <div className="modal-video-block">
              <div className="blockSizeModalVideo">
                <Image
                  src={imageLink}
                  alt=""
                  onClick={() => setPopShow(false)}
                  layout='fill'
                  objectFit="contain"
                  className="ItemsSizeModalVideo"
                />
              </div>
            </div>
          </div>
        </Modal.Content>
      </Modal>
      <Footer></Footer>
    </div>
    </>
  );
}

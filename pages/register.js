import React from 'react'
import Image from 'next/image'
import { useState, useEffect, useContext, useRef, useReducer } from 'react'
import _ from 'lodash'

import { useRouter } from 'next/router'
import Head from 'next/head'
import Header from '../themes/header/header'
import Footer from '../themes/footer/footer'
import Link from 'next/link'
const crypto = require('crypto')
import {
  Button,
  Modal,
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from 'semantic-ui-react'
import { useForm } from 'react-hook-form'
// Serverside & Api fetching
import Error from 'next'
import { URLSearchParams } from 'url'
// Serverside & Api fetching
// Auth
import nookies from 'nookies'
// Auth
import AppContext from '/libs/contexts/AppContext'
import Swal from 'sweetalert2'
import  MdeconnectPinInput  from '@/themes/components/mdeconnectPinInput'
import Divider from '@/themes/components/common/Divider'

export async function getServerSideProps(context) {
  const cookies = nookies.get(context)
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + '_token'] || ''
  const params = context.query

  //SEO
  const seo_res = await fetch(
    process.env.NEXT_PUBLIC_API + '/api/seo/register',
    {
      method: 'POST',
    }
  )
  const seo_errorCode = seo_res.ok ? false : seo_res.statusCode
  const seo_data = await seo_res.json()
  //SEO

  return {
    props: { seo_data, params },
  }
}

export default function Register({ seo_data, params }) {
  const router = useRouter()
  const { locale, pathname, asPath } = router
  const [lang, setLang] = useState(locale)
  const appContext = useContext(AppContext)
  const inputFiledata = useRef(null)
  const [, forceUpdate] = useReducer((x) => x + 1, 0)
  const [reload, setReload] = React.useState(true)
  const [userLoad, setUserLoad] = React.useState(true)
  const [reloadDistrict, setReloadDistrict] = React.useState(false)
  const [reloadSubDistrict, setReloadSubDistrict] = React.useState(false)
  const [provinceOptions, setProvinceOptions] = useState(null)
  const [districtOptions, setDistrictOptions] = useState(null)
  const [subdistrictOptions, setSubdistrictOptions] = useState(null)
  const [memberOptions, setMemberOptions] = useState(null)
  const [subLearnerOptions, setSubLearnerOptions] = useState(null)
  const [subLearner, setSubLearner] = useState(false)
  const [subLearnerEtc, setSubLearnerEtc] = useState(false)
  const [errorArray, setErrorArray] = useState([])
  const [district, setDistrict] = useState(0)
  const [subdistrict, setSubdistrict] = useState(0)
  const { register, setValue, getValues } = useForm({
    defaultValues: {},
  })
  const [medical, setMedical] = useState(false)
  const [nurse, setNurse] = useState(false)
  const [showInternal, setShowInternal] = useState(false)
  const [showEmail, setShowEmail] = useState(false)
  const [internal, setInternal] = useState([
    { key: '', value: '', text: 'ประเภทบุคลากร' },
    {
      key: 'se3',
      value: 3,
      text: 'บุคลากรคณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย',
    },
    { key: 'se1', value: 1, text: 'บุคลากรโรงพยาบาลจุฬาลงกรณ์' },
    { key: 'se2', value: 2, text: 'บุคลากรโรงพยาบาลอื่นๆ' },
  ])
  const [showPinPassword, setShowPinPassword] = useState(false)

  useEffect(() => {
    if (reload) {
      setReload(false)
      setValue('login_type', 'email')
      getProvince()
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + '/api/mdcu/userType',
        null,
        (data) => {
          if (lang == 'en') {
            for (var i = 0; i < data.data.length; i++) {
              if (data.data[i]['value'] == '') {
                data.data[i]['text'] = 'Member Type'
              } else if (data.data[i]['value'] == 1) {
                data.data[i]['text'] = 'Physician'
              } else if (data.data[i]['value'] == 2) {
                data.data[i]['text'] = 'Public'
              } else if (data.data[i]['value'] == 3) {
                data.data[i]['text'] = 'Healthcare Provider'
              } else if (data.data[i]['value'] == 4) {
                data.data[i]['text'] = 'Medical Or Paramedical Student'
              } else if (data.data[i]['value'] == 5) {
                data.data[i]['text'] = 'Other Student'
              } else if (data.data[i]['value'] == 14) {
                data.data[i]['text'] = 'International Partner'
              } else if (data.data[i]['value'] == 15) {
                data.data[i]['text'] = 'Special Partner'
              }
            }
          }

          setMemberOptions(data.data)
          setSubLearnerOptions(data.sub_learner)
        }
      )
      setTimeout(() => {
        if (
          !appContext.isNull(appContext.jGet('status')) &&
          appContext.jGet('status') == 'internal_email'
        ) {
          if (lang == 'en') {
            Swal.fire({
              text: 'Please correct Institutional email',
              icon: 'info',
              confirmButtonText: 'ปิด',
              confirmButtonColor: '#648d2f',
            })
          } else {
            Swal.fire({
              text: 'กรุณากรอกอีเมล์หน่วยงานค่ะ',
              icon: 'info',
              confirmButtonText: 'ปิด',
              confirmButtonColor: '#648d2f',
            })
          }
        }
      }, '500')
    }
  }, [reload])


  useEffect(() => {
    if (appContext.user) {
      setUserLoad(false)
      setValue('name', appContext.user.name)
      if (appContext.user.lastname != 'null') {
        setValue('lastname', appContext.user.lastname)
      } else {
        setValue('lastname', '')
      }
      if (appContext.user.email != 'null') {
        setValue('email', appContext.user.email)
      } else {
        setValue('email', '')
      }
      if (appContext.user.internal_email != 'null') {
        setValue('internal_email', appContext.user.internal_email)
      } else {
        setValue('internal_email', '')
      }
      setValue('password', appContext.user.password)
      if (appContext.user.mobile != 'null') {
        setValue('mobile', appContext.user.mobile)
      } else {
        setValue('mobile', '')
      }
      if (appContext.user.address != 'null') {
        setValue('address', appContext.user.address)
      } else {
        setValue('address', '')
      }
      if (appContext.user.subdistrict != 'null') {
        setValue('subdistrict', appContext.user.subdistrict)
      } else {
        setValue('subdistrict', '')
      }
      if (appContext.user.district != 'null') {
        setValue('district', appContext.user.district)
      } else {
        setValue('district', '')
      }
      if (appContext.user.province != 'null') {
        setValue('province', appContext.user.province)
      } else {
        setValue('province', '')
      }
      if (appContext.user.postcode != 'null') {
        setValue('postcode', appContext.user.postcode)
      } else {
        setValue('postcode', '')
      }
      if (appContext.user.medical_id != 'null') {
        setValue('medical_id', appContext.user.medical_id)
      } else {
        setValue('medical_id', '')
      }
      if (appContext.user.n_id != 'null') {
        setValue('n_id', appContext.user.n_id)
      } else {
        setValue('n_id', '')
      }
      if (appContext.user.internal != 'null') {
        setValue('internal', appContext.user.internal)
      } else {
        setValue('internal', '')
      }
      if (appContext.user.user_type != 'null') {
        setValue('user_type', appContext.user.user_type)
      } else {
        setValue('user_type', '')
      }
      
    
  
      if (appContext.user.user_type == 4 || appContext.user.user_type == 5) {
        if (lang == 'en') {
          setInternal([
            { key: '', value: '', text: 'User Affiliation ' },
            // { key: "se4", value: 4, text: "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย" },
            {
              key: 'se5',
              value: 5,
              text: 'Other Faculties in Chulalongkorn University',
            },
            { key: 'se6', value: 6, text: 'Other universities' },
          ])
        } else {
          setInternal([
            { key: '', value: '', text: 'ประเภทบุคลากร' },
            // { key: "se4", value: 4, text: "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย" },
            { key: 'se5', value: 5, text: 'จุฬาลงกรณ์มหาวิทยาลัย' },
            { key: 'se6', value: 6, text: 'มหาวิทยาลัยอื่นๆ' },
          ])
        }
      } else {
        if (lang == 'en') {
          setInternal([
            { key: '', value: '', text: 'User Affiliation ' },
            {
              key: 'se3',
              value: 3,
              text: 'Faculty of Medicine, Chulalongkorn University',
            },
            {
              key: 'se1',
              value: 1,
              text: 'King Chulalongkorn Memorial Hospital',
            },
            { key: 'se2', value: 2, text: 'Other hospitals' },
          ])
        } else {
          setInternal([
            { key: '', value: '', text: 'ประเภทบุคลากร' },
            {
              key: 'se3',
              value: 3,
              text: 'บุคลากรคณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย',
            },
            { key: 'se1', value: 1, text: 'บุคลากรโรงพยาบาลจุฬาลงกรณ์' },
            { key: 'se2', value: 2, text: 'บุคลากรโรงพยาบาลอื่นๆ' },
          ])
        }
      }
      if (appContext.user.user_type == 1) {
        setMedical(true)
      }
      if (appContext.user.user_type == 3) {
        setSubLearner(true)
        if (appContext.user.sub_learner == 2) {
          setNurse(true)
        }
      }
      if (appContext.user.sub_learner != 'null') {
        setValue('sub_learner', appContext.user.sub_learner)
      } else {
        setValue('sub_learner', '')
      }
      if (appContext.user.sub_learner == 1) {
        setSubLearnerEtc(true)
      }
      if (appContext.user.sub_learner_etc != 'null') {
        setValue('sub_learner_etc', appContext.user.sub_learner_etc)
      } else {
        setValue('sub_learner_etc', '')
      }
      if (
        appContext.user.user_type == 1 ||
        appContext.user.user_type == 3 ||
        appContext.user.user_type == 4 ||
        appContext.user.user_type == 5
      ) {
        setShowInternal(true)
        if (
          appContext.user.internal == 1 ||
          appContext.user.internal == 3 ||
          appContext.user.internal == 4 ||
          appContext.user.internal == 5
        ) {
          if (
            appContext.user.email != null &&
            appContext.user.email != '' &&
            appContext.user.email != 'null' &&
            (appContext.user.email.indexOf('@student.chula.ac.th') > -1 ||
              appContext.user.email.indexOf('@chulahospital.org') > -1 ||
              appContext.user.email.indexOf('@chula.md') > -1 ||
              appContext.user.email.indexOf('@chula.ac.th') > -1)
          ) {
            setShowEmail(false)
          } else {
            setShowEmail(true)
          }
        }
      }
      if (appContext.user.avatar && appContext.user.avatar != '') {
        setValue('avatar', appContext.user.avatar)
      } else {
        setValue('avatar', '/assets/images/user-key.png')
      }

      //   login_type
      if (appContext.user.line_id != '') {
        setValue('login_type', 'line')
      } else if (appContext.user.facebook_id != '') {
        setValue('login_type', 'facebook')
      } else if (appContext.user.google_id != '') {
        setValue('login_type', 'google')
      }
      setTimeout(() => {
        setUserLoad(true)
      }, '0')
    }
  }, [appContext.user])

  useEffect(() => {
    if (reloadDistrict) {
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          '/api/area/district/' +
          getValues('province'),
        null,
        (data) => {
          setDistrictOptions(data.data)
          setSubdistrictOptions(null)
          setReloadDistrict(false)
        }
      )
    }
  }, [reloadDistrict])

  useEffect(() => {
    if (reloadSubDistrict) {
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API +
          '/api/area/subdistrict/' +
          getValues('district'),
        null,
        (data) => {
          setSubdistrictOptions(data.data)
          setReloadSubDistrict(false)
        }
      )
    }
  }, [reloadSubDistrict])

  let maxW = 500
  let maxH = 500

  // const MDEconnect_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
  // MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8RAczzQghSBi1SqfXzv0
  // TQ5V4obSlghFNsEYonDcuaktcz3RdJld+YMzTlMUJ011I1qNMzrN5Oq8a+JrBoCj
  // no+AA+GHMIlUBmZcAyR2iBZILjklaJA4gGlJa873t++hnvqu3oZHtQEWGPOVig1h
  // s41ENZvCfdBmBrCq99CB6sMXDh/dIkigYSrg6oO0TJTKrcI+ng/0Jn8T7sNENEtu
  // bFBwhpzfW85ZoA86wlSlGAYuhxSmCPJQHjcPxZg1CtKsSFI8mAlC+XhlRPduNAWC
  // fw4UxO8meKC70wcK05clnGbd071DXsOX872HhLgOpaAagWc3JnxdPIg4L2xVCHU+
  // GwIDAQAB
  // -----END PUBLIC KEY-----`

 const MDEconnect_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAn2XDVgavpvILEgm5iHpa
/tXFs54TsaKsZHwYbD6EfydcH7z5/hDUqVEbsiSOT2Y2vs7HSSQEtcalGrc6dRnx
1qE8UmgN9bleUPXG8A6HPuHADnUgBKqQfOD9JZAZpNL5oEMGgvEjA7KKjE5PptV+
r2E/Ke+by/8/+M1d2w9RX3sQRcuVn3WXYNteSE2A7rVSY1lr+8R9YHjnAzTp4nnZ
gZRLEy45eb6ANHWx1d4Gix2gDzEBVV7/+r00YAJFr0F8usJ6I7eFSOVbxGJAEcFp
yBEHCgAM7qnvjAAQtDhOoszkIKYbbnEm/XUxKQgTiRWA0+pALFwnKtYvzbB5Nmzn
YQIDAQAB
-----END PUBLIC KEY-----`



  async function importPublicKey(pem) {
    // Remove header, footer and whitespace
    const pemContents = pem
      .replace('-----BEGIN PUBLIC KEY-----', '')
      .replace('-----END PUBLIC KEY-----', '')
      .replace(/\s/g, '')

    // Convert base64 to binary
    const binaryDer = window.atob(pemContents)
    const arrayBuffer = new Uint8Array(binaryDer.length)
    for (let i = 0; i < binaryDer.length; i++) {
      arrayBuffer[i] = binaryDer.charCodeAt(i)
    }

    // Import the key
    return window.crypto.subtle.importKey(
      'spki',
      arrayBuffer,
      {
        name: 'RSA-OAEP',
        hash: { name: 'SHA-256' },
      },
      false,
      ['encrypt']
    )
  }

  // Function to encrypt data
  async function encryptPin(publicKey, data) {
    const encoder = new TextEncoder()
    const encodedData = encoder.encode(data)

    const encryptedData = await window.crypto.subtle.encrypt(
      {
        name: 'RSA-OAEP',
      },
      publicKey,
      encodedData
    )

    return btoa(String.fromCharCode(...new Uint8Array(encryptedData)))
  }

  const previewFile = (event) => {
    event.preventDefault()
    var img = document.createElement('img')
    img.onload = () => {
      var canvas = document.createElement('canvas')
      var ctx = canvas.getContext('2d')
      var iw = img.width
      var ih = img.height
      var scale = Math.min(maxW / iw, maxH / ih)
      var iwScaled = iw * scale
      var ihScaled = ih * scale
      canvas.width = iwScaled
      canvas.height = ihScaled
      ctx.drawImage(img, 0, 0, iwScaled, ihScaled)
      setValue('avatar', canvas.toDataURL())
      forceUpdate()
    }
    try {
      img.src = URL.createObjectURL(event.target.files[0])
    } catch (e) {}
  }

  const onSubmit = () => {
    let register_data = getValues()
    let bol = true
    // setErrorArray
    var errarr = []
    if (!appContext.isEmail(register_data['email'])) {
      bol = false
      errarr.push('email')
    }
    if (appContext.isNull(register_data['name'])) {
      bol = false
      errarr.push('name')
    }
    if (appContext.isNull(register_data['lastname'])) {
      bol = false
      errarr.push('lastname')
    }
    if (appContext.isNull(register_data['user_type'])) {
      bol = false
      errarr.push('user_type')
    }
    if (
      !appContext.isNull(register_data['user_type']) &&
      register_data['user_type'] == 1 &&
      !appContext.isMed(register_data['medical_id'])
    ) {
      bol = false
      errarr.push('medical_id')
    }
    if (
      !appContext.isNull(register_data['user_type']) &&
      register_data['user_type'] == 1 &&
      (!appContext.isThaiOrSpace(register_data['name']) ||
        !appContext.isThaiOrSpace(register_data['lastname']))
    ) {
      if (
        !appContext.isNull(register_data['user_type']) &&
        register_data['user_type'] == 1 &&
        !appContext.isThaiOrSpace(register_data['name'])
      ) {
        bol = false
        errarr.push('name')
      }
      if (
        !appContext.isNull(register_data['user_type']) &&
        register_data['user_type'] == 1 &&
        !appContext.isThaiOrSpace(register_data['lastname'])
      ) {
        bol = false
        errarr.push('lastname')
      }
      errarr.push('thainame')
    }
    if (
      !appContext.isNull(register_data['user_type']) &&
      register_data['user_type'] == 3 &&
      !appContext.isNull(register_data['sub_learner']) &&
      register_data['sub_learner'] == 2 &&
      !appContext.isNurse(register_data['n_id'])
    ) {
      bol = false
      errarr.push('n_id')
    }
    if (
      !appContext.isNull(register_data['user_type']) &&
      register_data['user_type'] == 3 &&
      appContext.isNull(register_data['sub_learner'])
    ) {
      bol = false
      errarr.push('sub_learner')
    }
    if (
      !appContext.isNull(register_data['user_type']) &&
      register_data['user_type'] == 3 &&
      !appContext.isNull(register_data['sub_learner']) &&
      register_data['sub_learner'] == 1 &&
      appContext.isNull(register_data['sub_learner_etc'])
    ) {
      bol = false
      errarr.push('sub_learner_etc')
    }
    if (
      !appContext.isNull(register_data['user_type']) &&
      (register_data['user_type'] == 1 ||
        register_data['user_type'] == 3 ||
        register_data['user_type'] == 4 ||
        register_data['user_type'] == 5) &&
      appContext.isNull(register_data['internal'])
    ) {
      bol = false
      errarr.push('internal')
    }
    if (
      !appContext.isNull(register_data['internal']) &&
      (register_data['internal'] == 1 ||
        register_data['internal'] == 3 ||
        register_data['internal'] == 4 ||
        register_data['internal'] == 5) &&
      appContext.isNull(register_data['internal_email'])
    ) {
      if (
        register_data['email'] != null &&
        register_data['email'] != '' &&
        register_data['email'] != 'null' &&
        (register_data['email'].indexOf('@student.chula.ac.th') > -1 ||
          register_data['email'].indexOf('@chulahospital.org') > -1 ||
          register_data['email'].indexOf('@chula.md') > -1 ||
          register_data['email'].indexOf('@chula.ac.th') > -1)
      ) {
      } else {
        bol = false
        errarr.push('internal_email')
      }
    }
    // if (appContext.isNull(register_data["address"])) {
    //   bol = false;
    //   errarr.push("address");
    // }
    // if (appContext.isNull(register_data["province"])) {
    //   bol = false;
    //   errarr.push("province");
    // }
    // if (appContext.isNull(register_data["district"])) {
    //   bol = false;
    //   errarr.push("district");
    // }
    // if (appContext.isNull(register_data["subdistrict"])) {
    //   bol = false;
    //   errarr.push("subdistrict");
    // }
    // if (!appContext.isPostal(register_data["postcode"])) {
    //   bol = false;
    //   errarr.push("postcode");
    // }
    if (!register_data['consent']) {
      bol = false
      errarr.push('consent')
    }

    if (!appContext.user) {
      if (!appContext.isPassword(register_data['password'])) {
        bol = false
        errarr.push('password')
      }
    }

    setErrorArray(errarr)
    if (bol) {
      if (_.startsWith(register_data['avatar'], 'data:')) {
        appContext.loadApi(
          process.env.NEXT_PUBLIC_API + '/api/image/save',
          { image: register_data['avatar'] },
          (data) => {
            // console.log(data);
            if (data['status'] == 'true') {
              setValue('avatar', data['path'])
              onSubmitRegister()
            }
          }
        )
      } else {
        onSubmitRegister()
      }
    } else {
      if (errarr.includes('thainame')) {
        if (lang == 'en') {
          Swal.fire({
            html: 'For the benefit of receiving CME points,<br>please verify that your name matches<br>the professional certificate and is in Thai language.',
            icon: 'error',
            confirmButtonText: 'Close',
            confirmButtonColor: '#648d2f',
          })
        } else {
          Swal.fire({
            html: 'เพื่อประโยชน์ในการได้รับคะแนน CME ของท่าน<br>กรุณาตรวจสอบว่า ชื่อ-นามสกุลของท่าน<br>ตรงกับใบประกอบวิชาชีพ และเป็นภาษาไทย',
            icon: 'error',
            confirmButtonText: 'ปิด',
            confirmButtonColor: '#648d2f',
          })
        }
      } else {
        if (lang == 'en') {
          Swal.fire({
            text: 'Please confirm your information',
            icon: 'error',
            confirmButtonText: 'Close',
            confirmButtonColor: '#648d2f',
          })
        } else {
          Swal.fire({
            text: 'กรุณาตรวจสอบข้อมูล',
            icon: 'error',
            confirmButtonText: 'ปิด',
            confirmButtonColor: '#648d2f',
          })
        }
      }
    }
  }

  const showPassword = () => {
    var attr_pass = document
      .getElementById('input_password')
      .getAttribute('type')
    if (attr_pass == 'password') {
      document.getElementById('input_password').setAttribute('type', 'text')
    } else {
      document.getElementById('input_password').setAttribute('type', 'password')
    }
    document.getElementById('input_eye').classList.toggle('slash')
  }

  const onSubmitRegister = async () => {
    var txt_close = ''
    var _text1,
      _text2,
      _text3,
      _text4,
      _text5,
      _text6,
      _text7,
      _text8,
      _text9,
      _text10,
      _text11,
      _text12,
      _text14 = '',
      _text15,
      _text16,
      _text17,
      _text18,
      _text19
    if (lang == 'en') {
      txt_close = 'Close'
      _text1 = 'Your license will be later verified'
      _text2 =
        'Successful registration<br>Please verify your identity at <br>Email : '
      _text3 =
        'before accessing<br>(Email maybe in Junkmail)<br>A problem with authentication. Contact <a href="https://line.me/R/ti/p/@562tfgma" target="_blank">to report usage problems.'
      _text4 = 'This email has already been used. '
      _text5 = 'This institutional email has already been used.'
      _text6 = 'This phone number has already been used.'
      _text7 = 'This medical license ID has already been used.'
      _text8 = 'Please fill in the registration form.'
      _text9 = 'License number not found.'
      _text10 = 'Your information and license do not match.'
      _text11 = 'Your license will be later verified'
      _text12 = 'Your license will be later verified'
      _text14 = "Please confirm the doctor's identity with MD E-connect."
      _text15 = ''
      _text16 = ''
      _text17 = ''
      _text18 = ''
      _text19 = ''
    } else {
      txt_close = ' ปิด'
      _text1 = 'เลขใบประกอบวิชาชีพของท่านจะถูกตรวจสอบในภายหลัง'
      _text2 = 'สมัครสมาชิกสำเร็จ<br>กรุณายืนยันตัวตนที่<br>Email : '
      _text3 =
        ' ก่อนเข้าใช้งาน<br>(Email อาจจะอยู่ใน Junkmail)<br>พบปัญหาการยืนยันตัวตน ติดต่อ <a href="https://line.me/R/ti/p/@562tfgma" target="_blank">แจ้งปัญหาการใช้งาน</a>'
      _text4 = 'อีเมลมีผู้ใช้งานแล้ว'
      _text5 = 'อีเมลหน่วยงานมีผู้ใช้งานแล้ว'
      _text6 = 'เบอร์โทรศัพท์มีผู้ใช้งานแล้ว'
      _text7 = 'เลขใบประกอบวิชาชีพมีผู้ใช้งานแล้ว'
      _text8 = 'กรุณาตรวจสอบข้อมูล'
      _text9 =
        'ไม่พบเลขใบประกอบวิชาชีพของท่าน กรุณาตรวจสอบข้อมูล และทำรายการใหม่อีกครั้ง'
      _text10 = 'ข้อมูลของคุณกับใบประกอบวิชาชีพไม่ตรงกัน'
      _text11 =
        'ขณะนี้ทาง ศนพ กำลังนำเข้าข้อมูลของท่าน<br>เลขใบประกอบวิชาชีพของท่านจะถูกตรวจสอบในภายหลัง'
      _text12 = 'ระบบจะตรวจสอบใบประกอบวิชาชีพในภายหลัง'
      _text14 = 'กรุณายืนยันตัวตนแพทย์กับ MD E-connect'
      _text15 = 'ข้อมูลของท่านไม่ถูกต้อง กรุณาตรวจสอบ และทำรายการใหม่อีกครั้ง'
      _text16 =
        'สถานะเลขใบประกอบวิชาชีพของท่านไม่ตรงตามเงื่อนไข กรุณาติดต่อแพทยสภา และทำรายการใหม่อีกครั้ง'
      _text17 =
        'ข้อมูลแพทย์หรือเลขใบประกอบวิชาชีพของท่านไม่ถูกต้อง กรุณาติดต่อแพทยสภา และทำรายการใหม่อีกครั้ง'
      _text18 = 'ข้อมูลของคุณกับเลขใบประกอบวิชาชีพไม่ตรงกัน'
      _text19 = 'ไม่สามารถดำเนินการได้ในขณะนี้ กรุณาลองใหม่อีกครั้ง'
    }

    let register_data = getValues()
    let pinAuthen = null
    if (register_data.user_type == 1 && register_data.medical_id) {
      const publicKey = await importPublicKey(MDEconnect_PUBLIC_KEY)
      pinAuthen = await encryptPin(publicKey, register_data.medical_pincode)
      register_data.medical_pincode = pinAuthen
    }
    if (pinAuthen) {
      localStorage.setItem('pinAuthen', pinAuthen)
      localStorage.setItem('doctorId', register_data.medical_id)
      localStorage.setItem('verify_mdeconnect_page', 'REGISTER')
    }

    appContext.loadApi(
      process.env.NEXT_PUBLIC_API + '/api/user/register',
      register_data,
      (data) => {
        if (data['status'] == 'success') {
          if (data['verify_with_mdeconnect']) {
            Swal.fire({
              html: _text14,
              icon: 'success',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            }).then((result) => {
              router.replace('/verify-with-mdeconnect')
            })
          } else {
            if (data['change_medical']) {
              Swal.fire({
                html: _text1, //  "เลขใบประกอบวิชาชีพของท่านจะถูกตรวจสอบในภายหลัง";
                icon: 'success',
                confirmButtonText: txt_close,
                confirmButtonColor: '#648d2f',
              }).then((result) => {
                appContext.setToken(data['utoken'])
                if (localStorage.getItem('backpath')) {
                  if (data['type'] == 'create') {
                    Swal.fire({
                      html: _text2 + data['user_email'] + _text3,
                      icon: 'success',
                      confirmButtonText: txt_close,
                      confirmButtonColor: '#648d2f',
                    }).then((result) => {
                      router.replace(localStorage.getItem('backpath'))
                    })
                  } else {
                    if (
                      localStorage.getItem('backgift') &&
                      localStorage.getItem('backgift') != ''
                    ) {
                      router.replace(localStorage.getItem('backgift'))
                    } else {
                      router.replace(localStorage.getItem('backpath'))
                    }
                  }
                } else {
                  if (data['type'] == 'create') {
                    Swal.fire({
                      html: _text2 + data['user_email'] + _text3,
                      icon: 'success',
                      confirmButtonText: txt_close,
                      confirmButtonColor: '#648d2f',
                    }).then((result) => {
                      router.replace('/')
                    })
                  } else {
                    if (
                      localStorage.getItem('backgift') &&
                      localStorage.getItem('backgift') != ''
                    ) {
                      router.replace(localStorage.getItem('backgift'))
                    } else {
                      router.replace('/')
                    }
                  }
                }
              })
            } else if (data['verify_fail'] == 'true') {
              Swal.fire({
                html: _text11, // "ขณะนี้ทาง ศนพ กำลังนำเข้าข้อมูลของท่าน<br>เลขใบประกอบวิชาชีพของท่านจะถูกตรวจสอบในภายหลัง"
                icon: 'success',
                confirmButtonText: txt_close,
                confirmButtonColor: '#648d2f',
              }).then((result) => {
                appContext.setToken(data['utoken'])
                if (localStorage.getItem('backpath')) {
                  if (data['type'] == 'create') {
                    Swal.fire({
                      html: _text2 + data['user_email'] + _text3,
                      icon: 'success',
                      confirmButtonText: txt_close,
                      confirmButtonColor: '#648d2f',
                    }).then((result) => {
                      router.replace(localStorage.getItem('backpath'))
                    })
                  } else {
                    if (
                      localStorage.getItem('backgift') &&
                      localStorage.getItem('backgift') != ''
                    ) {
                      router.replace(localStorage.getItem('backgift'))
                    } else {
                      router.replace(localStorage.getItem('backpath'))
                    }
                  }
                } else {
                  if (data['type'] == 'create') {
                    Swal.fire({
                      html: _text2 + data['user_email'] + _text3,
                      icon: 'success',
                      confirmButtonText: txt_close,
                      confirmButtonColor: '#648d2f',
                    }).then((result) => {
                      router.replace('/')
                    })
                  } else {
                    if (
                      localStorage.getItem('backgift') &&
                      localStorage.getItem('backgift') != ''
                    ) {
                      router.replace(localStorage.getItem('backgift'))
                    } else {
                      router.replace('/')
                    }
                  }
                }
              })
            } else if (data['api_fail'] == 'true') {
              Swal.fire({
                html: _text12, // "ระบบจะตรวจสอบใบประกอบวิชาชีพในภายหลัง"
                icon: 'success',
                confirmButtonText: txt_close,
                confirmButtonColor: '#648d2f',
              }).then((result) => {
                appContext.setToken(data['utoken'])
                if (localStorage.getItem('backpath')) {
                  if (data['type'] == 'create') {
                    Swal.fire({
                      html: _text2 + data['user_email'] + _text3,
                      icon: 'success',
                      confirmButtonText: txt_close,
                      confirmButtonColor: '#648d2f',
                    }).then((result) => {
                      router.replace(localStorage.getItem('backpath'))
                    })
                  } else {
                    if (
                      localStorage.getItem('backgift') &&
                      localStorage.getItem('backgift') != ''
                    ) {
                      router.replace(localStorage.getItem('backgift'))
                    } else {
                      router.replace(localStorage.getItem('backpath'))
                    }
                  }
                } else {
                  if (data['type'] == 'create') {
                    Swal.fire({
                      html: _text2 + data['user_email'] + _text3,
                      icon: 'success',
                      confirmButtonText: txt_close,
                      confirmButtonColor: '#648d2f',
                    }).then((result) => {
                      router.replace('/')
                    })
                  } else {
                    if (
                      localStorage.getItem('backgift') &&
                      localStorage.getItem('backgift') != ''
                    ) {
                      router.replace(localStorage.getItem('backgift'))
                    } else {
                      router.replace('/')
                    }
                  }
                }
              })
            } else {
              appContext.setToken(data['utoken'])
              if (localStorage.getItem('backpath')) {
                if (data['type'] == 'create') {
                  Swal.fire({
                    html: _text2 + data['user_email'] + _text3,
                    icon: 'success',
                    confirmButtonText: txt_close,
                    confirmButtonColor: '#648d2f',
                  }).then((result) => {
                    router.replace(localStorage.getItem('backpath'))
                  })
                } else {
                  if (
                    localStorage.getItem('backgift') &&
                    localStorage.getItem('backgift') != ''
                  ) {
                    router.replace(localStorage.getItem('backgift'))
                  } else {
                    router.replace(localStorage.getItem('backpath'))
                  }
                }
              } else {
                if (data['type'] == 'create') {
                  Swal.fire({
                    html: _text2 + data['user_email'] + _text3,
                    icon: 'success',
                    confirmButtonText: txt_close,
                    confirmButtonColor: '#648d2f',
                  }).then((result) => {
                    router.replace('/')
                  })
                } else {
                  if (
                    localStorage.getItem('backgift') &&
                    localStorage.getItem('backgift') != ''
                  ) {
                    router.replace(localStorage.getItem('backgift'))
                  } else {
                    router.replace('/')
                  }
                }
              }
            }
          }
        } else {
          if (data['status'] == 'duplicate_email') {
            Swal.fire({
              text: _text4,
              icon: 'error',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            })
          } else if (data['status'] == 'duplicate_email_internal') {
            Swal.fire({
              text: _text5,
              icon: 'error',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            })
          } else if (data['status'] == 'duplicate_mobile') {
            Swal.fire({
              text: _text6,
              icon: 'error',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            })
          } else if (data['status'] == 'duplicate_medical_id') {
            Swal.fire({
              text: _text7,
              icon: 'error',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            })
          } else if (data['status'] == 'verify_medical_fail') {
            Swal.fire({
              text: _text9,
              icon: 'error',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            })
          } else if (data['status'] == 'medical_data_not_match') {
            Swal.fire({
              text: _text10,
              icon: 'error',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            })
          } else if (data['status'] == 'md_invalid_request') {
            Swal.fire({
              text: _text15,
              icon: 'error',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            })
          } else if (data['status'] == 'md_invalid_docter_status') {
            Swal.fire({
              text: _text16,
              icon: 'error',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            })
          } else if (data['status'] == 'md_invalid_doctor_info') {
            Swal.fire({
              text: _text18,
              icon: 'error',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            })
          } else if (data['status'] == 'md_internal_server_error') {
            Swal.fire({
              text: _text19,
              icon: 'error',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            })
          } else {
            Swal.fire({
              text: _text8,
              icon: 'error',
              confirmButtonText: txt_close,
              confirmButtonColor: '#648d2f',
            })
          }
        }
      }
    )
  }

  const getProvince = () => {
    appContext.loadApi(
      process.env.NEXT_PUBLIC_API + '/api/area/province',
      null,
      (data) => {
        setProvinceOptions(data.data)
      }
    )
  }

  const checkMyError = (_val) => {
    if (errorArray.indexOf(_val) > -1) {
      return true
    }
    return false
  }

  const checkMedical = (_val) => {
    setValue('user_type', _val)
    if (_val == 1) {
      setMedical(true)
    } else {
      setMedical(false)
    }
    if (_val == 3) {
      setSubLearner(true)
    } else {
      setSubLearner(false)
    }
    if (_val == 1 || _val == 3 || _val == 4 || _val == 5) {
      setShowInternal(true)
    } else {
      setShowInternal(false)
    }
    if (_val == 4 || _val == 5) {
      if (lang == 'en') {
        setInternal([
          { key: '', value: '', text: 'User Affiliation ' },
          // { key: "se4", value: 4, text: "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย" },
          {
            key: 'se5',
            value: 5,
            text: 'Other Faculties in Chulalongkorn University',
          },
          { key: 'se6', value: 6, text: 'Other universities' },
        ])
      } else {
        setInternal([
          { key: '', value: '', text: 'ประเภทบุคลากร' },
          // { key: "se4", value: 4, text: "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย" },
          { key: 'se5', value: 5, text: 'จุฬาลงกรณ์มหาวิทยาลัย' },
          { key: 'se6', value: 6, text: 'มหาวิทยาลัยอื่นๆ' },
        ])
      }
    } else {
      if (lang == 'en') {
        setInternal([
          { key: '', value: '', text: 'User Affiliation ' },
          {
            key: 'se3',
            value: 3,
            text: 'Faculty of Medicine, Chulalongkorn University',
          },
          {
            key: 'se1',
            value: 1,
            text: 'King Chulalongkorn Memorial Hospital',
          },
          { key: 'se2', value: 2, text: 'Other hospitals' },
        ])
      } else {
        setInternal([
          { key: '', value: '', text: 'ประเภทบุคลากร' },
          {
            key: 'se3',
            value: 3,
            text: 'บุคลากรคณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย',
          },
          { key: 'se1', value: 1, text: 'บุคลากรโรงพยาบาลจุฬาลงกรณ์' },
          { key: 'se2', value: 2, text: 'บุคลากรโรงพยาบาลอื่นๆ' },
        ])
      }
    }
    // setValue("internal", "");
    setValue('sub_learner', '')
    setValue('sub_learner_etc', '')
    setNurse(false)
    setSubLearnerEtc(false)
  }
  const checkSubLearner = (_val) => {
    setValue('sub_learner', _val)
    if (_val == 2) {
      setNurse(true)
    } else {
      setNurse(false)
    }
    if (_val == 1) {
      setSubLearnerEtc(true)
    } else {
      setSubLearnerEtc(false)
    }
    setValue('sub_learner_etc', '')
  }

  const checkInternal = (_val) => {
    setValue('internal', _val)
    if (_val == 1 || _val == 3 || _val == 4 || _val == 5) {
      if (
        getValues('email') != null &&
        getValues('email') != '' &&
        getValues('email') != 'null' &&
        (getValues('email').indexOf('@student.chula.ac.th') > -1 ||
          getValues('email').indexOf('@chulahospital.org') > -1 ||
          getValues('email').indexOf('@chula.md') > -1 ||
          getValues('email').indexOf('@chula.ac.th') > -1)
      ) {
        setShowEmail(false)
      } else {
        setShowEmail(true)
      }
    } else {
      setShowEmail(false)
    }
  }

  const handleShowPinPassword = () => {
    setShowPinPassword(!showPinPassword)
  }

  return (
    <div className="main-all">
      <Head>
        {seo_data.seo ? (
          seo_data.seo.map((val, key) =>
            val.name == 'title' ? (
              <>
                <title>{val.content}</title>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={'og:' + key}
                  name={'og:' + val.name}
                  content={val.content}
                />
                <meta
                  key={'twitter:' + key}
                  name={'twitter:' + val.name}
                  content={val.content}
                />
              </>
            ) : (
              <>
                <meta key={key} name={val.name} content={val.content} />
                <meta
                  key={'og:' + key}
                  name={'og:' + val.name}
                  content={val.content}
                />
                <meta
                  key={'twitter:' + key}
                  name={'twitter:' + val.name}
                  content={val.content}
                />
              </>
            )
          )
        ) : (
          <>
            <title>MDCU : MedU MORE</title>
          </>
        )}

        <meta
          key={'twitter:card'}
          name={'twitter:card'}
          content="summary_large_image"
        />
        <meta key={'og:type'} name={'og:type'} content="website" />
      </Head>
      <Header></Header>
      <div className="main-body">
        <div className="fix-space"></div>
        <div className="register-page">
          <div className="register-block-form">
            <div className="block-form-inner">
              {/* ===== */}
              <div className="row">
                <div className="col-12">
                  <div className="register-block-form-title">
                    <h3>REGISTER</h3>
                    {lang == 'en' ? (
                      <p>Please fill in the registration form.</p>
                    ) : (
                      <p>กรอกข้อมูลเพื่อสมัครสมาชิก</p>
                    )}
                  </div>
                </div>
              </div>
              {/* ===== */}
              <div className="row-fm-register row">
                <div className="col-fm-register col-12">
                  <div className="item-fm">
                    {lang == 'en' ? (
                      <p className="fm-title">Information</p>
                    ) : (
                      <p className="fm-title">ข้อมูล</p>
                    )}
                  </div>
                </div>
                <div className="block-profile-register">
                  <div className="my-info">
                    <div className="info-avatar">
                      <div className="inner">
                        <button
                          className="invisible"
                          onClick={() => {
                            inputFiledata.current.click()
                          }}
                        >
                          <div className="profileAvatarImg">
                            {getValues('avatar') == null ||
                            getValues('avatar') == '' ||
                            getValues('avatar') == 'null' ? (
                              <div className="imgResponsiveCustom">
                                <Image
                                  className=" cursor-pointer"
                                  src={'/assets/images/user-key.png'}
                                  layout="fill"
                                  objectFit="contain"
                                  sizes="100%"
                                  alt=""
                                />
                              </div>
                            ) : (
                              <div className="imgResponsiveCustom">
                                <Image
                                  className=" cursor-pointer"
                                  src={getValues('avatar')}
                                  layout="fill"
                                  objectFit="contain"
                                  sizes="100%"
                                  alt=""
                                />
                              </div>
                            )}
                          </div>
                        </button>
                        <button
                          className="btn-edit-img"
                          onClick={() => {
                            inputFiledata.current.click()
                          }}
                        >
                          <i className="icon-ic-image"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {!appContext.user ? (
                  <>
                    <div className="col-fm-register col-12 col-md-6">
                      <div className="item-fm">
                        {lang == 'en' ? (
                          <Input
                            type="email"
                            className="fm-control"
                            placeholder="Email"
                          >
                            <input
                              {...register('email')}
                              maxLength={100}
                              data-type="email"
                              onInput={appContext.diFormPattern}
                              className={checkMyError('email') ? 'error' : ''}
                            />
                          </Input>
                        ) : (
                          <Input
                            type="email"
                            className="fm-control"
                            placeholder="อีเมล์ *"
                          >
                            <input
                              {...register('email')}
                              maxLength={100}
                              data-type="email"
                              onInput={appContext.diFormPattern}
                              className={checkMyError('email') ? 'error' : ''}
                            />
                          </Input>
                        )}
                      </div>
                    </div>
                    <div className="col-fm-register col-12 col-md-6">
                      <div className="item-fm">
                        {lang == 'en' ? (
                          <Input
                            id="input_password"
                            type="password"
                            className="fm-control"
                            placeholder="Password (At least 6 characters.)"
                          >
                            <input
                              {...register('password')}
                              maxLength={25}
                              data-type="password"
                              onInput={appContext.diFormPattern}
                              className={
                                checkMyError('password') ? 'error' : ''
                              }
                            />
                          </Input>
                        ) : (
                          
                          <Input
                            id="input_password"
                            type="password"
                            className="fm-control "
                            placeholder="รหัสผ่าน (อย่างน้อย 6 ตัวอักษร) *"
                          >
                         
                            <input
                              {...register('password')}
                              maxLength={25}
                              data-type="password"
                              onInput={appContext.diFormPattern}
                              className={
                                checkMyError('password') ? 'error' : ''
                              }
                            />
                          </Input>
                        )}

                        <div className="eye-button">
                          <i
                            id="input_eye"
                            className="eye icon"
                            onClick={showPassword}
                          ></i>
                        </div>
                      </div>
                    </div>
                  </>
                ) : null}

                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    {lang == 'en' ? (
                      <Input
                        type="text"
                        className="fm-control"
                        placeholder={
                          medical ? 'First name (Thai language) *' : 'First name *'
                        }
                      >
                        <input
                          {...register('name')}
                          maxLength={100}
                          data-type="textaddress"
                          onInput={appContext.diFormPattern}
                          className={checkMyError('name') ? 'error' : ''}
                        />
                      </Input>
                    ) : (
                      <Input
                        type="text"
                        className="fm-control"
                        placeholder={medical ? 'ชื่อ (ภาษาไทย) *' : 'ชื่อ *'}
                      >
                        <input
                          {...register('name')}
                          maxLength={100}
                          data-type="textaddress"
                          onInput={appContext.diFormPattern}
                          className={checkMyError('name') ? 'error' : ''}
                        />
                      </Input>
                    )}
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    {lang == 'en' ? (
                      <Input
                        type="text"
                        className="fm-control"
                        placeholder={
                          medical ? 'Surname (Thai language) *' : 'Surname *'
                        }
                      >
                        <input
                          {...register('lastname')}
                          maxLength={100}
                          data-type="textaddress"
                          onInput={appContext.diFormPattern}
                          className={checkMyError('lastname') ? 'error' : ''}
                        />
                      </Input>
                    ) : (
                      <Input
                        type="text"
                        className="fm-control"
                        placeholder={medical ? 'นามสกุล (ภาษาไทย) *' : 'นามสกุล *'}
                      >
                        <input
                          {...register('lastname')}
                          maxLength={100}
                          data-type="textaddress"
                          onInput={appContext.diFormPattern}
                          className={checkMyError('lastname') ? 'error' : ''}
                        />
                      </Input>
                    )}
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    {lang == 'en' ? (
                      <Input
                        type="tel"
                        className="fm-control"
                        placeholder="Phone number"
                      >
                        <input
                          {...register('mobile')}
                          maxLength={10}
                          data-type="number"
                          onInput={appContext.diFormPattern}
                          className={checkMyError('mobile') ? 'error' : ''}
                        />
                      </Input>
                    ) : (
                      <Input
                        type="tel"
                        className="fm-control"
                        placeholder="เบอร์โทรศัพท์"
                      >
                        <input
                          {...register('mobile')}
                          maxLength={10}
                          data-type="number"
                          onInput={appContext.diFormPattern}
                          className={checkMyError('mobile') ? 'error' : ''}
                        />
                      </Input>
                    )}
                  </div>
                </div>
                {userLoad ? (
                  <div className="col-fm-register col-12 col-md-6">
                    <div className="item-fm">
                      {lang == 'en' ? (
                        <Select
                          placeholder="Member Type"
                          options={memberOptions}
                          defaultValue={getValues('user_type')}
                          onChange={(event, data) => {
                            checkMedical(data.value)
                          }}
                          className={
                            'fm-control ' +
                            (checkMyError('user_type') ? 'error' : '')
                          }
                        />
                      ) : (
                        <Select
                          placeholder="ประเภทสมาชิก *"
                          options={memberOptions}
                          defaultValue={getValues('user_type')}
                          onChange={(event, data) => {
                            checkMedical(data.value)
                          }}
                          className={
                            'fm-control ' +
                            (checkMyError('user_type') ? 'error' : '')
                          }
                        />
                      )}
                    </div>
                  </div>
                ) : null}
                {appContext.user ? (
                  subLearner ? (
                    <div className="col-fm-register col-12 col-md-6">
                      <div className="item-fm">
                        <Select
                          placeholder="ประเภทย่อย"
                          options={subLearnerOptions}
                          defaultValue={appContext.user.sub_learner}
                          onChange={(event, data) => {
                            checkSubLearner(data.value)
                          }}
                          className={
                            'fm-control ' +
                            (checkMyError('sub_learner') ? 'error' : '')
                          }
                        />
                      </div>
                    </div>
                  ) : null
                ) : subLearner ? (
                  <div className="col-fm-register col-12 col-md-6">
                    <div className="item-fm">
                      <Select
                        placeholder="ประเภทย่อย"
                        options={subLearnerOptions}
                        onChange={(event, data) => {
                          checkSubLearner(data.value)
                        }}
                        className={
                          'fm-control ' +
                          (checkMyError('sub_learner') ? 'error' : '')
                        }
                      />
                    </div>
                  </div>
                ) : null}
                {subLearnerEtc ? (
                  <div className="col-fm-register col-12 col-md-6">
                    <div className="item-fm">
                      <Input
                        type="text"
                        className="fm-control"
                        placeholder="ระบุประเภทย่อย"
                      >
                        <input
                          {...register('sub_learner_etc')}
                          maxLength={100}
                          data-type="textaddress"
                          onInput={appContext.diFormPattern}
                          className={
                            checkMyError('sub_learner_etc') ? 'error' : ''
                          }
                        />
                      </Input>
                    </div>
                  </div>
                ) : null}
                {appContext.user ? (
                  <>
                    {medical ? (
                      <>
                        <div className="col-fm-register col-12 col-md-6">
                          <div className="item-fm">
                            {appContext.user.medical_id != null &&
                            appContext.user.medical_id != '' &&
                            appContext.user.medical_id != 'null' ? (
                              lang == 'en' ? (
                                <Input
                                  type="text"
                                  className="fm-control"
                                  placeholder="This medical license ID has already been used."
                                  disabled
                                >
                                  <input
                                    {...register('medical_id')}
                                    maxLength={6}
                                    data-type="number"
                                    onInput={appContext.diFormPattern}
                                    className={
                                      checkMyError('medical_id') ? 'error' : ''
                                    }
                                  />
                                </Input>
                              ) : (
                                <Input
                                  type="text"
                                  className="fm-control"
                                  placeholder="เลขใบประกอบวิชาชีพ (กรอกเฉพาะตัวเลข 4 หรือ 6 หลัก)"
                                  disabled
                                >
                                  <input
                                    {...register('medical_id')}
                                    maxLength={6}
                                    data-type="number"
                                    onInput={appContext.diFormPattern}
                                    className={
                                      checkMyError('medical_id') ? 'error' : ''
                                    }
                                  />
                                </Input>
                              )
                            ) : lang == 'en' ? (
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="This medical license ID has already been used."
                                disabled
                              >
                                <input
                                  {...register('medical_id')}
                                  maxLength={6}
                                  data-type="number"
                                  onInput={appContext.diFormPattern}
                                  className={
                                    checkMyError('medical_id') ? 'error' : ''
                                  }
                                />
                              </Input>
                            ) : (
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="เลขใบประกอบวิชาชีพ (กรอกเฉพาะตัวเลข 4 หรือ 6 หลัก)"
                                disabled
                              >
                                <input
                                  {...register('medical_id')}
                                  maxLength={6}
                                  data-type="number"
                                  onInput={appContext.diFormPattern}
                                  className={
                                    checkMyError('medical_id') ? 'error' : ''
                                  }
                                />
                              </Input>
                            )}
                          </div>
                        </div>

                        {/* // new input pin start */}
                        {/* <div className="col-fm-register col-12 col-md-6">
                          <div className="item-fm">
                            {lang == "en" ? (
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="MD e-Service PIN Code"
                              >
                                <input
                                  {...register("medical_pincode")}
                                  maxLength={6}
                                  data-type="number"
                                  onInput={appContext.diFormPattern}
                                  className={
                                    checkMyError("medical_pincode")
                                      ? "error"
                                      : ""
                                  }
                                />
                              </Input>
                            ) : (
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="รหัส PIN ของบริการ MD e-Service"
                              >
                                <input
                                  {...register("medical_pincode")}
                                  maxLength={6}
                                  data-type="number"
                                  type="password"
                                  onInput={appContext.diFormPattern}
                                  className={
                                    checkMyError("medical_pincode")
                                      ? "error"
                                      : ""
                                  }
                                />
                              </Input>
                            )}
                          </div>
                        </div>  */}
                        {/* // new input pin end */}
                      </>
                    ) : null}
                    {nurse ? (
                      <div className="col-fm-register col-12 col-md-6">
                        <div className="item-fm">
                          {appContext.user.n_id != null &&
                          appContext.user.n_id != '' &&
                          appContext.user.n_id != 'null' ? (
                            lang == 'en' ? (
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="Medical license ID."
                                disabled
                              >
                                <input
                                  {...register('n_id')}
                                  maxLength={10}
                                  data-type="number"
                                  onInput={appContext.diFormPattern}
                                  className={
                                    checkMyError('n_id') ? 'error' : ''
                                  }
                                />
                              </Input>
                            ) : (
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="เลขใบประกอบวิชาชีพ (กรอกเฉพาะตัวเลข 10 หลัก)"
                                disabled
                              >
                                <input
                                  {...register('n_id')}
                                  maxLength={10}
                                  data-type="number"
                                  onInput={appContext.diFormPattern}
                                  className={
                                    checkMyError('n_id') ? 'error' : ''
                                  }
                                />
                              </Input>
                            )
                          ) : lang == 'en' ? (
                            <Input
                              type="text"
                              className="fm-control"
                              placeholder="Medical license ID."
                            >
                              <input
                                {...register('n_id')}
                                maxLength={10}
                                data-type="number"
                                onInput={appContext.diFormPattern}
                                className={checkMyError('n_id') ? 'error' : ''}
                              />
                            </Input>
                          ) : (
                            <Input
                              type="text"
                              className="fm-control"
                              placeholder="เลขใบประกอบวิชาชีพ (กรอกเฉพาะตัวเลข 10 หลัก)"
                            >
                              <input
                                {...register('n_id')}
                                maxLength={10}
                                data-type="number"
                                onInput={appContext.diFormPattern}
                                className={checkMyError('n_id') ? 'error' : ''}
                              />
                            </Input>
                          )}
                        </div>
                      </div>
                    ) : null}
                    <></>
                    {showInternal ? (
                      <>
                        <div className="col-fm-register col-12 col-md-6">
                          <div className="item-fm">
                            <Select
                              placeholder="ประเภทบุคลากร *"
                              options={internal}
                              defaultValue={getValues('internal')}
                              onChange={(event, data) => {
                                checkInternal(data.value)
                              }}
                              className={
                                'fm-control ' +
                                (checkMyError('internal') ? 'error' : '')
                              }
                            />
                          </div>
                        </div>
                        {showEmail ? (
                          appContext.user.verify_internal == 2 ? (
                            <div className="col-fm-register col-12 col-md-6">
                              <div className="item-fm">
                                <Input
                                  type="email"
                                  className="fm-control"
                                  placeholder="อีเมล์หน่วยงาน"
                                >
                                  <input
                                    {...register('internal_email')}
                                    maxLength={100}
                                    data-type="email"
                                    onInput={appContext.diFormPattern}
                                    className={
                                      checkMyError('internal_email')
                                        ? 'error'
                                        : ''
                                    }
                                  />
                                </Input>
                              </div>
                            </div>
                          ) : (
                            <div className="col-fm-register col-12 col-md-6">
                              <div className="item-fm">
                                <Input
                                  type="email"
                                  className="fm-control"
                                  placeholder="อีเมล์หน่วยงาน"
                                  disabled
                                >
                                  <input
                                    {...register('internal_email')}
                                    maxLength={100}
                                    data-type="email"
                                    onInput={appContext.diFormPattern}
                                    className={
                                      checkMyError('internal_email')
                                        ? 'error'
                                        : ''
                                    }
                                  />
                                </Input>
                              </div>
                            </div>
                          )
                        ) : null}
                      </>
                    ) : null}
                    {appContext.user.verify_email == 2 ? (
                      <div className="col-fm-register col-12 col-md-6">
                        <div className="item-fm">
                          <Input
                            type="email"
                            className="fm-control"
                            placeholder="อีเมล์"
                          >
                            <input
                              {...register('email')}
                              maxLength={100}
                              data-type="email"
                              onInput={appContext.diFormPattern}
                              className={checkMyError('email') ? 'error' : ''}
                            />
                          </Input>
                        </div>
                      </div>
                    ) : (
                      <div className="col-fm-register col-12 col-md-6">
                        <div className="item-fm">
                          <Input
                            type="email"
                            className="fm-control"
                            placeholder="อีเมล์"
                            disabled
                          >
                            <input
                              {...register('email')}
                              maxLength={100}
                              data-type="email"
                              onInput={appContext.diFormPattern}
                              className={checkMyError('email') ? 'error' : ''}
                            />
                          </Input>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    {medical ? (
                      <>
                        <div className="col-fm-register col-12 col-md-6">
                          <div className="item-fm">
                            {lang == 'en' ? (
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="This medical license ID has already been used."
                              >
                                <input
                                  {...register('medical_id')}
                                  maxLength={6}
                                  data-type="number"
                                  onInput={appContext.diFormPattern}
                                  className={
                                    checkMyError('medical_id') ? 'error' : ''
                                  }
                                />
                              </Input>
                            ) : (
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="เลขใบประกอบวิชาชีพ (กรอกเฉพาะตัวเลข 4-6 หลัก) *"
                                required
                              >
                                <input
                                  {...register('medical_id')}
                                  maxLength={6}
                                  data-type="number"
                                  onInput={appContext.diFormPattern}
                                  className={
                                    checkMyError('medical_id') ? 'error' : ''
                                  }
                                />
                                
                              </Input>
                            )}
                          </div>
                        </div>

                        {/* // new input pin start */}
                        {/* <div className="col-fm-register col-12 col-md-6">
                          <div className="item-fm">
                            {lang == "en" ? (
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="MD e-Service PIN Code"
                              >
                                <input
                                  {...register("medical_pincode")}
                                  maxLength={6}
                                  data-type="number"
                                  onInput={appContext.diFormPattern}
                                  className={
                                    checkMyError("medical_pincode")
                                      ? "error"
                                      : ""
                                  }
                                />
                              </Input>
                            ) : (
                              <Input
                                type="text"
                                className="fm-control"
                                placeholder="รหัส PIN ของบริการ MD e-Service"
                              >
                                <input
                                  {...register("medical_pincode")}
                                  maxLength={6}
                                  data-type="number"
                                  type={showPinPassword ? "text" : "password"}
                                  onInput={appContext.diFormPattern}
                                  className={
                                    checkMyError("medical_pincode")
                                      ? "error"
                                      : ""
                                  }
                                />
                              </Input>
                            )}
                            <div className="eye-button">
                              <i
                                id="input_eye"
                                className={
                                  showPinPassword
                                    ? "eye icon slash"
                                    : "eye icon"
                                }
                                onClick={handleShowPinPassword}
                              ></i>
                            </div>
                          </div>
                        </div>  */}
                        {/* // new input pin end */}
                      </>
                    ) : null}
                    {nurse ? (
                      <div className="col-fm-register col-12 col-md-6">
                        <div className="item-fm">
                          {lang == 'en' ? (
                            <Input
                              type="text"
                              className="fm-control"
                              placeholder="Medical license ID."
                            >
                              <input
                                {...register('n_id')}
                                maxLength={10}
                                data-type="number"
                                onInput={appContext.diFormPattern}
                                className={checkMyError('n_id') ? 'error' : ''}
                              />
                            </Input>
                          ) : (
                            <Input
                              type="text"
                              className="fm-control"
                              placeholder="เลขใบประกอบวิชาชีพ (กรอกเฉพาะตัวเลข 10 หลัก)"
                            >
                              <input
                                {...register('n_id')}
                                maxLength={10}
                                data-type="number"
                                onInput={appContext.diFormPattern}
                                className={checkMyError('n_id') ? 'error' : ''}
                              />
                            </Input>
                          )}
                        </div>
                      </div>
                    ) : null}
                    <></>
                    {showInternal ? (
                      <>
                        <div className="col-fm-register col-12 col-md-6">
                          <div className="item-fm">
                            {lang == 'en' ? (
                              <Select
                                placeholder="User Affiliation "
                                options={internal}
                                defaultValue={getValues('internal')}
                                onChange={(event, data) => {
                                  checkInternal(data.value)
                                }}
                                className={
                                  'fm-control ' +
                                  (checkMyError('internal') ? 'error' : '')
                                }
                              />
                            ) : (
                              <Select
                                placeholder="ประเภทบุคลากร *"
                                options={internal}
                                defaultValue={getValues('internal')}
                                onChange={(event, data) => {
                                  checkInternal(data.value)
                                }}
                                className={
                                  'fm-control ' +
                                  (checkMyError('internal') ? 'error' : '')
                                }
                              />
                            )}
                          </div>
                        </div>
                        {showEmail ? (
                          <div className="col-fm-register col-12 col-md-6">
                            <div className="item-fm">
                              {lang == 'en' ? (
                                <Input
                                  type="email"
                                  className="fm-control"
                                  placeholder="Institutional email"
                                >
                                  <input
                                    {...register('internal_email')}
                                    maxLength={100}
                                    data-type="email"
                                    onInput={appContext.diFormPattern}
                                    className={
                                      checkMyError('internal_email')
                                        ? 'error'
                                        : ''
                                    }
                                  />
                                </Input>
                              ) : (
                                <Input
                                  type="email"
                                  className="fm-control"
                                  placeholder="อีเมล์หน่วยงาน"
                                >
                                  <input
                                    {...register('internal_email')}
                                    maxLength={100}
                                    data-type="email"
                                    onInput={appContext.diFormPattern}
                                    className={
                                      checkMyError('internal_email')
                                        ? 'error'
                                        : ''
                                    }
                                  />
                                </Input>
                              )}
                            </div>
                          </div>
                        ) : null}
                      </>
                    ) : null}
                   <Divider />
                    {medical && (
                      <>
                        <MdeconnectPinInput
                          register={register}
                          setValue={setValue} 
                          appContext={appContext}                 
                        />
                        <Divider />
                      </>
                    )} 
                  </>
                )}
                {/* <div className="col-fm-register col-12">
                  <div className="item-fm">
                    <p className="fm-title">ที่อยู่ในการออกใบเสร็จ</p>
                  </div>
                </div>
                <div className="col-fm-register col-12">
                  <div className="item-fm">
                    <TextArea
                      placeholder="ที่อยู่"
                      defaultValue={getValues("address")}
                      data-type="textaddress"
                      onInput={appContext.diFormPattern}
                      onChange={(event) =>
                        setValue("address", event.target.value)
                      }
                      className={
                        "fm-control " + (checkMyError("address") ? "error" : "")
                      }
                    />
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Select
                      placeholder="กรุณาเลือกจังหวัด"
                      options={provinceOptions}
                      defaultValue={getValues("province")}
                      onChange={(event, data) => {
                        //   console.log(event)
                        setReloadDistrict(true);
                        setValue("province", data.value);
                        setValue("province_name", event.target.innerText);
                      }}
                      className={
                        "fm-control " +
                        (checkMyError("province") ? "error" : "")
                      }
                    />
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Select
                      placeholder="กรุณาเลือกเขต/อำเภอ"
                      options={districtOptions}
                      defaultValue={getValues("district")}
                      onChange={(event, data) => {
                        setReloadSubDistrict(true);
                        setValue("district", data.value);
                        setValue("district_name", event.target.innerText);
                      }}
                      className={
                        "fm-control " +
                        (checkMyError("district") ? "error" : "")
                      }
                    />
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Select
                      placeholder="กรุณาเลือกแขวง/ตำบล"
                      options={subdistrictOptions}
                      defaultValue={getValues("subdistrict")}
                      onChange={(event, data) => {
                        setValue("subdistrict", data.value);
                        setValue("subdistrict_name", event.target.innerText);
                      }}
                      className={
                        "fm-control " +
                        (checkMyError("subdistrict") ? "error" : "")
                      }
                    />
                  </div>
                </div>
                <div className="col-fm-register col-12 col-md-6">
                  <div className="item-fm">
                    <Input
                      type="tel"
                      className="fm-control"
                      placeholder="รหัสไปรษณีย์"
                    >
                      <input
                        {...register("postcode")}
                        maxLength={5}
                        data-type="number"
                        onInput={appContext.diFormPattern}
                        defaultValue={register["postcode"]}
                        className={checkMyError("postcode") ? "error" : ""}
                      />
                    </Input>
                  </div>
                </div> */}
                <div className="col-fm-register col-12">
                  <div className="item-fm-check">
                    <div
                      className={
                        'fm-check ' + (checkMyError('consent') ? 'error' : '')
                      }
                    >
                      <input type="checkbox" {...register('consent')} />
                      <div className="text">
                        <div
                          style={{ pointerEvents: 'none' }}
                          className="i_remark"
                        >
                          <i className="icon-ic-tick"
                          
                          />
                        </div>
                        {lang == 'en' ? (
                          <p>
                            I have read, agreed,{' '}
                            <a href="page/consent" target="_blank">
                              and consented to
                            </a>{' '}
                            <a href="page/privacy" target="_blank">
                              the Personal Data Protection Policy,
                            </a>{' '}
                            <a href="page/term" target="_blank">
                              and the Terms and Conditions
                            </a>{' '}
                            of this website.
                          </p>
                        ) : (
                          <p>
                            ฉันได้อ่านเเละยอมรับ{' '}
                            <a href="page/consent" target="_blank">
                              การให้ความยินยอม
                            </a>{' '}
                            <a href="page/privacy" target="_blank">
                              นโยบายคุ้มครองข้อมูลส่วนบุคคล
                            </a>{' '}
                            <a href="page/term" target="_blank">
                              ข้อกำหนดและเงื่อนไข
                            </a>{' '}
                            เป็นที่เรียบร้อยแล้ว
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-fm-register col-12">
                  <div className="item-fm-check">
                    <div className="fm-check register-remark">
                      {lang == 'en' ? null : (
                        <div className="text">
                          <p>
                            รับคอร์สฟรี หรือส่วนลดพิเศษ สำหรับแพทย์ นิสิต
                            นักศึกษา คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย และ
                            บุคลากรโรงพยาบาลจุฬาลงกรณ์ เพียงท่านสมัครสมาชิกด้วย
                            email ดังต่อไปนี้
                          </p>
                          <ul>
                            <li>@student.chula.ac.th (เฉพาะคณะแพทยศาสตร์)</li>
                            <li>@chulahospital.org</li>
                            <li>@chula.md</li>
                            <li>@chula.ac.th</li>
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="col-12 col-md-6 offset-md-3">
                  <button
                    className="btn-default btn-register"
                    onClick={onSubmit}
                  >
                    {lang == 'en' ? (
                      <span>Register</span>
                    ) : (
                      <span>สมัครสมาชิก</span>
                    )}
                  </button>
                </div>
              </div>
              {/* ===== */}
            </div>
          </div>
        </div>
      </div>
      <form id="FormFileData" className="hide">
        <input
          type="file"
          accept="image/jpeg,image/jpg,image/png,image/gif,image/xbm,image/xpm,image/wbmp,image/webp,image/bmp"
          onChange={(e) => {
            previewFile(e)
          }}
          ref={inputFiledata}
          className="hide"
        />
      </form>

      <Footer></Footer>
    </div>
  )
}

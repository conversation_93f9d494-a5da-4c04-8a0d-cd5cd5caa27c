async function importPublicKey(pem) {
    // Remove header, footer and whitespace
    const pemContents = pem
        .replace('-----BEGIN PUBLIC KEY-----', '')
        .replace('-----<PERSON><PERSON> PUBLIC KEY-----', '')
        .replace(/\s/g, '');

    // Convert base64 to binary
    const binaryDer = window.atob(pemContents);
    const arrayBuffer = new Uint8Array(binaryDer.length);
    for (let i = 0; i < binaryDer.length; i++) {
        arrayBuffer[i] = binaryDer.charCodeAt(i);
    }

    // Import the key
    return window.crypto.subtle.importKey(
        'spki',
        arrayBuffer,
        {
            name: 'RSA-<PERSON><PERSON><PERSON>',
            hash: { name: 'SHA-256' }
        },
        false,
        ['encrypt']
    );
}

// Function to encrypt data
async function encryptData(publicKey, data) {
    const encoder = new TextEncoder();
    const encodedData = encoder.encode(data);
    
    const encryptedData = await window.crypto.subtle.encrypt(
        {
            name: 'RSA-OAEP'
        },
        publicKey,
        encodedData
    );

    return btoa(String.fromCharCode(...new Uint8Array(encryptedData)));
}
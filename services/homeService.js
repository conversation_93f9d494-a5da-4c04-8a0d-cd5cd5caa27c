import { getUser } from "../pages/api/user";
import nookies from "nookies";
import { getSession } from "next-auth/react";

export async function fetchHomeData(context) {
  const cookies = nookies.get(context);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";
  

  const user = await getUser(utoken);
  const apiUrl = process.env.NEXT_PUBLIC_API;

const form = new URLSearchParams();
form.append("utoken", utoken);


try {
const [
    listResponse,
    bannerResponse,
    sponsorResponse,
    settingResponse,
    searchResponse,
    seoResponse,
    coursesLatestResponse
] = await Promise.all([
    fetch(
        `${apiUrl}/api/mdcu/homeContentRewrite`,
        { method: "POST", body: form }
    ),
    fetch(
        `${apiUrl}/api/mdcu/homeBanner`,
        { method: "POST", body: form }
    ),
    fetch(
        `${apiUrl}/api/mdcu/homeSponsor`,
        { method: "POST" }
    ),
    fetch(
        `${apiUrl}/api/mdcu/settingCenter/home_popup`,
        { method: "POST" }
    ),
    fetch(
        `${apiUrl}/api/mdcu/homeSearch`,
        { method: "POST" }
    ),
    fetch(
        `${apiUrl}/api/seo/home`,
        { method: "POST" }
    ),
    fetch(
        `${apiUrl}/api/ai/courses/latest`,
        { method: "POST" }
    )
]);
  

  const [
    list_arr_data,
    banner_data,
    sponsor_data,
    settingCenter,
    search_data,
    seo_data,
    coursesLatest
  ] = await Promise.all([
    listResponse.json(),
    bannerResponse.json(),
    sponsorResponse.json(),
    settingResponse.json(),
    searchResponse.json(),
    seoResponse.json(),
    coursesLatestResponse.json()
  ]);
  
  return {
    props:{
    user,
    utoken,
    list_arr_data,
    banner_data,
    sponsor_data,
    settingCenter,
    search_data,
    seo_data,
    coursesLatest,
    session: await getSession(context)
}
  };

} catch (error) {
    console.error("Error fetching home data:", error);
    return {
      props: {
        error: true,
        message: error.message
      }
    };
  }
}
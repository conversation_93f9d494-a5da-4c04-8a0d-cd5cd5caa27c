const crypto = require('crypto');

// UAT Public Key - Replace this with your actual UAT public key
const MDEconnect_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8RAczzQghSBi1SqfXzv0
TQ5V4obSlghFNsEYonDcuaktcz3RdJld+YMzTlMUJ011I1qNMzrN5Oq8a+JrBoCj
no+AA+GHMIlUBmZcAyR2iBZILjklaJA4gGlJa873t++hnvqu3oZHtQEWGPOVig1h
s41ENZvCfdBmBrCq99CB6sMXDh/dIkigYSrg6oO0TJTKrcI+ng/0Jn8T7sNENEtu
bFBwhpzfW85ZoA86wlSlGAYuhxSmCPJQHjcPxZg1CtKsSFI8mAlC+XhlRPduNAWC
fw4UxO8meKC70wcK05clnGbd071DXsOX872HhLgOpaAagWc3JnxdPIg4L2xVCHU+
GwIDAQAB
-----END PUBLIC KEY-----`;

// Encryption function
const encryptPin = (pin) => {
    try {
        const buffer = Buffer.from(pin, 'utf8');
        const encrypted = crypto.publicEncrypt(
            {
                key: MDEconnect_PUBLIC_KEY,
                padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                oaepHash: 'sha256'
            },
            buffer
        );
        return encrypted.toString('base64');
    } catch (error) {
        console.error('Encryption error:', error);
        throw error;
    }
};

// Test function
const testPinEncryption = () => {
    const pin = "111111";
    try {
        const encryptedPin = encryptPin(pin);
        console.log('Test Results:');
        console.log('-------------');
        console.log('Original PIN:', pin);
        console.log('Encrypted PIN (base64):', encryptedPin);
        console.log('-------------');
        return encryptedPin;
    } catch (error) {
        console.error('Test failed:', error);
    }
};

// Run the test
testPinEncryption();
import { NextResponse } from 'next/server';

export function middleware(request) {
  const { NEXT_PUBLIC_EXPIRE_DATE } = process.env;

  if (NEXT_PUBLIC_EXPIRE_DATE) {
    const now = new Date();
    const expiryDate = new Date(NEXT_PUBLIC_EXPIRE_DATE);
    if(now > expiryDate){
      return NextResponse.redirect(new URL('/', request.url));
    }
  }

  return NextResponse.next();
}

// Config to specify which paths the middleware should run on
export const config = {
  matcher: ['/test'],
};
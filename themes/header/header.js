import React from "react";
import { useState, useEffect, useContext } from "react";
import Router, { useRouter } from "next/router";
import AppContext from "/libs/contexts/AppContext";
import Link from "next/link";
import Image from "next/image";
import ButtonLogin from "../../themes/components/buttonLogin";
import ButtonLoginMobile from "../../themes/components/buttonLoginMobile";
import ListCart from "../../themes/components/listCart";
import NumberFormat from "react-number-format";
import styles from "/public/assets/css/component/listCart.module.css";
import {
  Menu,
  Button,
  Modal,
  Input,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";
import Swal from "sweetalert2";

const Header = React.memo(function Header(props) {
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);
  const appContext = useContext(AppContext);
  const [reloadNoti, setReloadNoti] = useState(true);
  const [notiData, setNotiData] = useState([]);
  const [notiCount, setNotiCount] = useState("");
  const [cartData, setCartData] = useState([]);
  const [cartCount, setCartCount] = useState("");
  const [cartTotalPrice, setCartTotalPrice] = useState("");
  const [cartWebPrice, setCartWebPrice] = useState("");
  const [reloadDynamic, setReloadDynamic] = useState(true);
  const [dynamicData, setDynamicData] = useState(null);
  useEffect(() => {
    // console.log("lang : " + lang);
    // console.log("reloadDynamic : " + reloadDynamic);
    if (reloadNoti) {
      setReloadNoti(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/getNotification",
        null,
        (data) => {
          setNotiData(data["data"]);
          setNotiCount(data["count"]);
        }
      );
      appContext.setReloadCart(true);
      appContext.setReloadCourse(true);
      // console.log('reload_noti');
    }
    if (appContext.reloadCart) {
      appContext.setReloadCart(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/getCart",
        null,
        (data) => {
          setCartData(data["data"]);
          setCartCount(data["count"]);
          setCartTotalPrice(data["total_price"]);
          setCartWebPrice(data["discount_web"]);
        }
      );
      // console.log('reload_cart');
    }
    if (reloadDynamic) {
      setReloadDynamic(false);
      appContext.loadApi(
        process.env.NEXT_PUBLIC_API + "/api/mdcu/getDynamicKey",
        null,
        (data) => {
          setDynamicData(data);
          // console.log(data);
        }
      );
      // console.log('reload_key');
    }
 

  }, [reloadNoti, appContext.reloadCart, reloadDynamic]);

  const navCart = React.useRef(null);
  const navMenu = React.useRef(null);
  const mainHeader = React.useRef(null);

  const onShowCart = (e) => {
    mainHeader.current.classList.add("active_cart");
    navCart.current.classList.add("on_show");
    document.body.classList.add("open_cart");
    navMenu.current.classList.remove("on_show");
  };

  const onRemoveAll = (e) => {
    mainHeader.current.classList.remove("active_manu", "active_cart");
    navCart.current.classList.remove("on_show");
    navMenu.current.classList.remove("on_show");
    document.body.classList.remove("open_cart");
    document.body.classList.remove("open_nav");
  };

  const truncate = (input, limit) => {
    if (input && input.length > limit)
      return input.substring(0, limit) + " ...";
    else return input;
  };

  const onToCheckout = (e) => {
    // const str="ขออภัย MDCU MedUMORE\n" +
    //               "ปิดปรับปรุงระบบชำระเงินชั่วคราว\n"+
    //               "ท่านจะได้รับการแจ้งเตือน\n"+
    //               "เมื่อการดำเนินการเสร็จสิ้น";
    //               Swal.fire({
    //                 html: '<pre style="font-family: \'Kanit\'">' + str + '</pre>',
    //                 icon: "warning",
    //                 confirmButtonText: "ตกลง",
    //                 confirmButtonColor: "#648d2f"
    //               });
    onRemoveAll();
    router.push('/checkout')
    window.location.href = "/checkout";
    // checkout
  };

  const onToggleMenu = (e) => {
    if (mainHeader.current.classList.contains("active_manu")) {
      mainHeader.current.classList.remove("active_manu");
      navMenu.current.classList.remove("on_show");
      document.body.classList.remove("open_nav");
    } else {
      mainHeader.current.classList.add("active_manu");
      navMenu.current.classList.add("on_show");
      document.body.classList.add("open_nav");
    }
  };

  const setMegaSubShow = (e) => {
    let itemMMega = document.getElementsByClassName("sub-list-" + e)[0];
    itemMMega.classList.toggle("active");
  };
  const addRead = (_id,_type,_link) => {
    const formData = new URLSearchParams();
    formData.append("id", _id);
    formData.append("type", _type);
    appContext.sendApi(
      process.env.NEXT_PUBLIC_API + "/api/mdcu/addRead",
      formData,
      (obj) => {
        if(_link!=null && _link!='' && _link!='null'){
          location.href=_link;
        }
      }
    );
  }

  return (
    <div>
      <div ref={mainHeader} id="header_position" className="main-header">
        <div className="inner-header">
          <div className="header-bar">
            <div className="container custom-container">
              <div className="row">
                <div className="i-header-logo">
                  {/* <Link> */}
                  <a href="/">
                    <div className="d-none d-xl-block blockLogoHeaderSize">
                      <Image
                        className="d-none d-xl-block ItemsLogoHeaderSize"
                        src="/assets/images/header-logo.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                      />
                    </div>
                    <div className="d-block d-xl-none blockLogoHeaderSize">
                      <Image
                        className="d-block d-xl-none ItemsLogoHeaderSize1"
                        src="/assets/images/header-logo-m.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                      />
                    </div>
                  </a>
                  {/* </Link> */}
                </div>

                {/* <div className="i-header-search d-none d-xl-block">
                  <Input icon={<Icon name="search" />} placeholder="ค้นหา" />
                </div> */}
                <div className="i-header-menu">
                  {lang == "en" ? (
                    <ul className="d-none d-xl-flex">
                      <li>
                        <Link href="/en/international">
                          <a
                            className={`${
                              appContext.pathname == "/international" || appContext.pathname == "/en/international"
                                ? "active"
                                : ""
                            }`}
                          >
                            <span>International Courses</span>
                          </a>
                        </Link>
                      </li>
                      <li>
                        <Link href="/infographic">
                          <a
                            className={`${
                              appContext.pathname == "/infographic" ||
                              router.asPath.indexOf("/infographic/") >-1
                                ? "active"
                                : ""
                            }`}
                          >
                            <span>Infographic</span>
                          </a>
                        </Link>
                      </li>
                      <li>
                        <Link href="/page/about">
                          <a
                            className={`${
                              router.asPath == "/page/about"||router.asPath == "/en/page/about"||router.asPath.indexOf("/article") >-1
                                ? "active"
                                : ""
                            }`}
                          >
                            <span>About Us</span>
                          </a>
                        </Link>
                        <div className="block-sub-menu">
                          <div className="inner-sub-menu">
                            <div className="item-sub-menu">
                              <Link href="/en/article">
                                <a className={`${
                                  router.asPath.indexOf("/article") >-1
                                    ? "active"
                                    : ""
                                }`}>
                                  <span>News</span>
                                </a>
                              </Link>
                            </div>
                          </div>
                        </div>
                      </li>
                    </ul>
                  ) : (
                    <ul className="d-none d-xl-flex">
                      <li>
                        <Link href="/category/course">
                          <a
                            className={`${
                              appContext.pathname == "/category/course" || appContext.pathname == "/infographic" ||
                              router.asPath.indexOf("/infographic/") >-1 || appContext.pathname == "/ebook" ||
                              router.asPath.indexOf("/ebook/") >-1
                                ? "active"
                                : ""
                            }`}
                          >
                            <span>คอร์สออนไลน์</span>
                          </a>
                        </Link>
                        <div className="block-sub-menu">
                          <div className="inner-sub-menu">
                            {/* <div className="item-sub-menu">
                              <Link href="">
                                <a>
                                  <span>Contents form partner institutions</span>
                                </a>
                              </Link>
                            </div> */}
                            <div className="item-sub-menu">
                              <Link href="/ebook">
                                <a className={`${
                                    appContext.pathname == "/ebook" ||
                                    router.asPath.indexOf("/ebook/") >-1
                                      ? "active"
                                      : ""
                                  }`}>
                                  <span>E-Book</span>
                                </a>
                              </Link>
                            </div>
                            <div className="item-sub-menu">
                              <Link href="/infographic">
                                <a className={`${
                                    appContext.pathname == "/infographic" ||
                                    router.asPath.indexOf("/infographic/") >-1
                                      ? "active"
                                      : ""
                                  }`}>
                                  <span>Infograghic</span>
                                </a>
                              </Link>
                            </div>
                          </div>
                        </div>
                      </li>
                      <li>
                        <Link href="/category/live">
                          <a
                            className={`${
                              appContext.pathname == "/category/live"
                                ? "active"
                                : ""
                            }`}
                          >
                            <span>LIVE</span>
                          </a>
                        </Link>
                      </li>
                      <li>
                        <Link href="/category/seminar">
                          <a
                            className={`${
                              appContext.pathname ==
                                "/category/main/seminar" ||
                              appContext.pathname == "/category/seminar"
                                ? "active"
                                : ""
                            }`}
                          >
                            <span>ประชุมวิชาการ</span>
                          </a>
                        </Link>
                      </li>
                      <li>
                        <Link href="/en/international">
                          <a
                            className={`${
                              appContext.pathname == "/international" || appContext.pathname == "/en/international"
                                ? "active"
                                : ""
                            }`}
                          >
                            <span>International Courses</span>
                          </a>
                        </Link>
                      </li>
                      <li>
                        <Link href="/page/about">
                          <a
                            className={`${
                              router.asPath == "/page/about"||router.asPath == "/en/page/about"||router.asPath.indexOf("/article") >-1
                                ? "active"
                                : ""
                            }`}
                          >
                            <span>About Us</span>
                          </a>
                        </Link>
                        <div className="block-sub-menu">
                          <div className="inner-sub-menu">
                            <div className="item-sub-menu">
                              <Link href="/article">
                                <a className={`${
                                  router.asPath.indexOf("/article") >-1
                                    ? "active"
                                    : ""
                                }`}>
                                  <span>ข่าวสาร</span>
                                </a>
                              </Link>
                            </div>
                          </div>
                        </div>
                      </li>
                      <li>
                        <Link href="/yearly-member">
                          <a
                            className={`${
                              appContext.pathname == "/yearly-member"
                                ? "active"
                                : ""
                            }`}
                          >
                            <span>Package</span>
                          </a>
                        </Link>
                      </li>
                      <li>
                        <a onClick={()=>appContext.creditBankPopup()} className="cursor-pointer">
                          <span className="immersive-text">Chula Lifelong Learning</span>
                        </a>
                      </li>
                      <li className="immersive-menu">
                        <a onClick={appContext.submitImmersive} className="immersive-desktop">
                          <div className="immersive-icon">
                            <Image  src="/assets/images/immersive.png" alt="" 
                              layout="fill"
                              objectFit="contain"
                            />
                          </div>
                          <span className="immersive-text">Immersive Courses</span>
                        </a>
                      </li>
                      {/* <li>
                        <Link href="/support/project">
                          <a
                            className={`${appContext.pathname ==
                              "/support/project" ||
                              appContext.pathname == "/support/foundation"
                              ? "active"
                              : ""
                            }`}
                          >
                            <span>สมทบทุน</span>
                          </a>
                        </Link>
                        <div className="block-sub-menu">
                          <div className="inner-sub-menu">
                            <div className="item-sub-menu">
                              <Link href="/support/project">
                                <a  className={`${appContext.pathname ==
                                  "/support/project"
                                  ? "active"
                                  : ""
                                }`}>
                                  <span>สนับสนุนโครงการ</span>
                                </a>
                              </Link>
                            </div>
                            <div className="item-sub-menu">
                              <Link href="/support/foundation">
                                <a  className={`${appContext.pathname == "/support/foundation"
                                  ? "active"
                                  : ""
                                }`}>
                                  <span>บริจาคให้มูลนิธิ</span>
                                </a>
                              </Link>
                            </div>
                          </div>
                        </div>
                      </li> */}
                    </ul>
                  )}
                </div>
                <div className="i-header-group-action">
                  <div className="action-noti">
                    <Menu compact className="btn-dropdown-custom">
                      <Dropdown text={notiCount} icon="bell outline">
                        <Dropdown.Menu className="inner-dropdown-custom">
                          <Dropdown.Item onClick={(e) => e.stopPropagation()}>
                            <div className="dropdown-noti-title">
                              <h3>MESSAGES</h3>
                              <div className="blockNotiTitleImgSize">
                                <Image src="/assets/images/noti-title-bg-bottom.png"
                                  layout="fill"
                                  objectFit="contain"
                                  className="ItemsNotiTitleImgSize"
                                  alt=""
                                />
                              </div>
                              {notiData.length == 0 ? (
                                <p>ไม่มีแจ้งเตือนในขณะนี้</p>
                              ) : null}
                            </div>
                            <div className="dropdown-noti-list">
                              {notiData.map((val, key) =>
                                // val.link != null && val.link != "" ? (
                                //   <Link key={key} href={val.link}>
                                //     <div className="noti-list-item">
                                //       <div className="noti-item-img">
                                //         <div className="thumb">
                                //           <div className="blockNotiImgSize">
                                //             {val && val.image && val.image!=null && val.image!='' && val.image!='null' ?(
                                //               <Image
                                //                 src={val.image}
                                //                 alt=""
                                //                 layout="fill"
                                //                 objectFit="contain"
                                //                 className="ItemsNotiImgSize"
                                //               />
                                //             ):null}
                                //           </div>
                                //         </div>
                                //       </div>
                                //       <div className="noti-item-text">
                                //         <h3>{val.title}</h3>
                                //         <h4>{val.description}</h4>
                                //         <p>{val.date}</p>
                                //       </div>
                                //       <div className="noti-item-action">
                                //         <button className="action">
                                //           <i className="icon-ic-right"></i>
                                //         </button>
                                //       </div>
                                //     </div>
                                //   </Link>
                                // ) : (
                                //   <div key={key} className="noti-list-item">
                                //     <div className="noti-item-img">
                                //     <div className="thumb">
                                //       <div className="blockNotiImgSize">
                                //         {val && val.image && val.image!=null && val.image!='' && val.image!='null' ?(
                                //           <Image
                                //             src={val.image}
                                //             alt=""
                                //             layout="fill"
                                //             objectFit="contain"
                                //             className="ItemsNotiImgSize"
                                //           />
                                //         ):null}
                                //       </div>
                                //     </div>
                                //     </div>
                                //     <div className="noti-item-text">
                                //       <h3>{val.title}</h3>
                                //       <h4>{val.description}</h4>
                                //       <p>{val.date}</p>
                                //     </div>
                                //   </div>
                                // )
                                <div key={key} className={`noti-list-item ${val.read =='true'? "read": ""}`}onClick={(e) => addRead(val.id,val.type,val.link)}>
                                  <div className="noti-item-img">
                                    <div className="thumb">
                                      <div className="blockNotiImgSize">
                                        {val && val.image && val.image!=null && val.image!='' && val.image!='null' ?(
                                          <Image
                                            src={val.image}
                                            alt=""
                                            layout="fill"
                                            objectFit="contain"
                                            className="ItemsNotiImgSize"
                                          />
                                        ):null}
                                      </div>
                                    </div>
                                  </div>
                                  <div className="noti-item-text">
                                    <h3>{val.title}</h3>
                                    <h4>{val.description}</h4>
                                    <p>{val.date}</p>
                                  </div>
                                </div>
                              )}
                            </div>
                          </Dropdown.Item>
                        </Dropdown.Menu>
                      </Dropdown>
                    </Menu>
                  </div>
                  <button onClick={onShowCart} className="btn-nav-action buy">
                    <i className="icon-ic-doctor-bag"></i>
                    <span>{cartCount}</span>
                  </button>
                  <ButtonLogin onClick={onShowCart}></ButtonLogin>
                  <button
                    onClick={onToggleMenu}
                    className="btn-nav-dot d-flex d-xl-none"
                  >
                    <i className="icon-ic-dot-nav"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div ref={navMenu} className="group-menu-f-mobile d-block d-xl-none">
          <div className="menu-f-mobile-inner">
            <div className="f-mobile-body">
              {/* <div className="i-header-search">
                <Input icon={<Icon name="search" />} placeholder="ค้นหา" />
              </div> */}
              <div className="i-header-menu">
                <ul>
                  {lang == "en" ? (
                    <li>
                      <Link href="/en/international">
                        <a
                          onClick={onRemoveAll}
                          className={`${
                            appContext.pathname == "/international" || appContext.pathname == "/en/international"
                              ? "active"
                              : ""
                          }`}
                        >
                          <span>International Courses</span>
                        </a>
                      </Link>
                    </li>
                  ):null}
                  {lang != "en" ? (
                  <li className="sub-list-1">
                    <Link href="/category/course">
                      <a
                        onClick={onRemoveAll}
                        className={`${
                          appContext.pathname == "/category/course" || appContext.pathname == "/infographic" ||
                          router.asPath.indexOf("/infographic/") >-1 || appContext.pathname == "/ebook" ||
                          router.asPath.indexOf("/ebook/") >-1
                            ? "active"
                            : ""
                        }`}
                      >
                        {lang != "en" ? (
                          <span>คอร์สออนไลน์</span>
                        ) : (
                          <span>Course</span>
                        )}
                      </a>
                    </Link>
                    <button className="btn_show" onClick={(e) => setMegaSubShow(1)}>
                      <i className="icon-ic-down"></i>
                    </button>
                    <div className="block-sub-menu-m">
                      <div className="block-inner-sub-menu-m">
                        {/* <div className="item-sub-menu-m">
                          <Link href="">
                            <a onClick={onRemoveAll}>
                              <span>
                                Contents from partner institutions
                              </span>
                            </a>
                          </Link>
                        </div> */}
                        <div className="item-sub-menu-m">
                          <Link href="/ebook">
                            <a  className={`${
                              appContext.pathname == "/ebook" ||
                              router.asPath.indexOf("/ebook/") >-1
                                ? "active"
                                : ""
                            }`} onClick={onRemoveAll}>
                              <span>
                              E-Book
                              </span>
                            </a>
                          </Link>
                        </div>
                        <div className="item-sub-menu-m">
                          <Link href="/infographic">
                            <a onClick={onRemoveAll}
                              className={`${
                              appContext.pathname == "/infographic" ||
                              router.asPath.indexOf("/infographic/") >-1
                                ? "active"
                                : ""
                            }`}>
                              <span>
                                Infograghic
                              </span>
                            </a>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </li>
                  ):null}
                  {lang != "en" ? (
                  <li>
                    <Link href="/category/live">
                      <a
                        onClick={onRemoveAll}
                        className={`${
                          appContext.pathname == "/category/live"
                            ? "active"
                            : ""
                        }`}
                      >
                        <span>LIVE</span>
                      </a>
                    </Link>
                  </li>
                  ):null}
                  {lang != "en" ? (
                  <li>
                    <Link href="/category/seminar">
                      <a
                        onClick={onRemoveAll}
                        className={`${
                          appContext.pathname == "/category/main/seminar" ||
                          appContext.pathname == "/category/seminar"
                            ? "active"
                            : ""
                        }`}
                      >
                        {lang != "en" ? (
                          <span>ประชุมวิชาการ</span>
                        ) : (
                          <span>Seminar</span>
                        )}
                      </a>
                    </Link>
                  </li>
                  ):null}
                  {lang != "en" ? (
                    <li>
                      <Link href="/en/international">
                        <a
                          onClick={onRemoveAll}
                          className={`${
                            appContext.pathname == "/international" || appContext.pathname == "/en/international"
                              ? "active"
                              : ""
                          }`}
                        >
                          <span>International Courses</span>
                        </a>
                      </Link>
                    </li>
                  ):null}
                  {lang == "en" ? (
                  <li>
                    <Link href="/infographic">
                      <a
                        onClick={onRemoveAll}
                        className={`${appContext.pathname == "/infographic" ||
                        router.asPath.indexOf("/infographic/") >-1
                          ? "active"
                          : ""
                          }`}
                      >
                        <span>Infographic</span>
                      </a>
                    </Link>
                  </li>
                  ):null}
                  <li className="sub-list-2">
                    <Link href="/page/about">
                      <a
                        onClick={onRemoveAll}
                        className={`${
                          router.asPath == "/page/about"||router.asPath == "/en/page/about"||router.asPath.indexOf("/article") >-1
                            ? "active"
                            : ""
                        }`}
                      >
                        <span>About Us</span>
                      </a>
                    </Link>
                    <button className="btn_show" onClick={(e) => setMegaSubShow(2)}>
                      <i className="icon-ic-down"></i>
                    </button>
                    <div className="block-sub-menu-m">
                      <div className="block-inner-sub-menu-m">
                        <div className="item-sub-menu-m">
                          <Link href="/article">
                            <a onClick={onRemoveAll}
                              className={`${
                              router.asPath.indexOf("/article") >-1
                                ? "active"
                                : ""
                            }`}>
                            {lang != "en" ? (
                              <span>ข่าวสาร</span>
                            ) : (
                              <span>News</span>
                            )}
                            </a>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </li>
                  {lang != "en" ? (
                  <li>
                    <Link href="/yearly-member">
                      <a
                        onClick={onRemoveAll}
                        className={`${
                          appContext.pathname == "/yearly-member"
                            ? "active"
                            : ""
                        }`}
                      >
                        <span>Package</span>
                      </a>
                    </Link>
                  </li>
                  ) : null}
                  {lang != "en" ? (
                    <li>
                      <a onClick={()=>appContext.creditBankPopup()} className="cursor-pointer">
                        <span className="immersive-text">Chula Lifelong Learning</span>
                      </a>
                    </li>
                  ) : null}
                  {lang != "en" ? (
                  <li>
                    <a onClick={appContext.submitImmersive} className="immersive-mobile">
                      <div className="immersive-icon">
                        <Image  src="/assets/images/immersive.png" alt="" 
                          layout="fill"
                          objectFit="contain"
                        />
                      </div>
                      <span>Immersive Courses</span>
                    </a>
                  </li>
                  ) : null}
                  {/* {lang != "en" ? (
                  <li className="sub-list-3">
                    <Link href="/support/project">
                      <a
                        onClick={onRemoveAll}
                        className={`${appContext.pathname ==
                          "/support/project" ||
                          appContext.pathname == "/support/foundation"
                          ? "active"
                          : ""
                          }`}
                      >
                        <span>สมทบทุน</span>
                      </a>
                    </Link>
                    <button className="btn_show" onClick={(e) => setMegaSubShow(3)}>
                      <i className="icon-ic-down"></i>
                    </button>
                    <div className="block-sub-menu-m">
                      <div className="block-inner-sub-menu-m">
                        <div className="item-sub-menu-m">
                          <Link href="/support/project">
                            <a onClick={onRemoveAll}
                              className={`${appContext.pathname ==
                              "/support/project"
                              ? "active"
                              : ""
                              }`}>
                              <span>
                                สนับสนุนโครงการ
                              </span>
                            </a>
                          </Link>
                        </div>
                        <div className="item-sub-menu-m">
                          <Link href="/support/foundation">
                            <a onClick={onRemoveAll}
                              className={`${
                              appContext.pathname == "/support/foundation"
                              ? "active"
                              : ""
                              }`}>
                              <span>
                                บริจาคให้มูลนิธิ
                              </span>
                            </a>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </li>
                  ) : null} */}
                </ul>
              </div>
            </div>
            {appContext.user ? (
              <div className="f-mobile-action">
                <Link href={"/auth/signout"}>
                  <button className="btn-action">
                    <span className="name">ออกจากระบบ</span>
                  </button>
                </Link>
              </div>
            ) : (
              <ButtonLoginMobile onClick={onShowCart}></ButtonLoginMobile>
            )}
          </div>
        </div>
      </div>
      {/* ===== */}
      <div ref={navCart} className="group-menu-cart">
        <div className="menu-cart-inner">
          <div className="menu-cart-header">
            <button onClick={onRemoveAll} className="close-menu-cart">
              <i className="icon-ic-close-cart"></i>
              <span>ปิด</span>
            </button>
            <h3>รถเข็นของท่าน</h3>
          </div>
          <div className="menu-cart-body">
            <div className="menu-scroll">
              <div className={`${styles.block_list_cart}`}>
                <div className={`${styles.list_inner_cart}`}>
                  {cartData.map((val, key) => (
                    <div key={key} className={`${styles.item_cart}`}>
                      <div className={`${styles.cart_img}`}>
                        <div className="blockCratImgSize">
                          {val && val.image && val.image!=null && val.image!='' && val.image!='null' ?(
                            <Image className="thumb ItemsCratImgSize" src={val.image} alt="" 
                              layout="fill"
                              objectFit="contain"
                            />
                          ):null}
                        </div>
                      </div>
                      <div className={`${styles.cart_content}`}>
                        <h3>{val.title}</h3>
                        <h4
                          dangerouslySetInnerHTML={{
                            __html: truncate(val.description, 80),
                          }}
                        ></h4>
                        <NumberFormat
                          value={val.price}
                          displayType={"text"}
                          thousandSeparator={true}
                          renderText={(value, props) => (
                            <p {...props}>{value}</p>
                          )}
                        />
                      </div>
                      <div className={`${styles.cart_action}`}>
                        <button
                          onClick={() => appContext.removeCart(val.id)}
                          className={`${styles.btn_cart_action}`}
                        >
                          <i className="icon-ic-close-cart"></i>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <div className="menu-cart-footer">
            {cartWebPrice != null &&
            cartWebPrice != "" &&
            cartWebPrice != "null" &&
            cartWebPrice != 0 ? (
              <div className="cart_footer_total web">
                <div className="name">ส่วนลดคูปองเงินสด</div>
                <NumberFormat
                  value={cartWebPrice}
                  displayType={"text"}
                  thousandSeparator={true}
                  renderText={(value, props) => (
                    <div className="price" {...props}>
                      -{value} บาท
                    </div>
                  )}
                />
              </div>
            ) : null}
            <div className="cart_footer_total">
              <div className="name">รวมทั้งหมด</div>
              <NumberFormat
                value={cartTotalPrice}
                displayType={"text"}
                thousandSeparator={true}
                renderText={(value, props) => (
                  <div className="price" {...props}>
                    {value} บาท
                  </div>
                )}
              />
            </div>
            <div className="cart_footer_action">
              <div className="row">
                <div className="col-12 col-md-6 text-center col_footer_action">
                  <button className="btn-default" onClick={onToCheckout}>
                    <span>ชำระเงิน</span>
                  </button>
                </div>
                {/* <div className="col-12 col-md-6 col_footer_action">
                  <button className="btn-default light">
                    <span>ดูคอร์สเรียนในรถเข็น</span>
                  </button>
                </div> */}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* ===== */}
    </div>
  );
});

export default React.memo(Header);

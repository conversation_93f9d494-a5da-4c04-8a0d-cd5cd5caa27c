import React, { Component } from "react";
import { render } from "react-dom";
import { Progress } from 'semantic-ui-react'
import Image from "next/image";

export default class index extends Component {
  render() {
    return (
      <div className="card card-lock radius">
        <div className="inner">
            <div className="card-img">
              <div className="img-thumb">
              <Image src="/assets/images/scbx-blog-list-7.jpg" alt="" 
                layout="fill"
                objectFit="contain"
              />
              </div>
              <div className="card-icon lock">
                <i className="icon-ic-lock"></i>
              </div>
            </div>
        </div>
      </div>
    );
  }
}

import React from 'react';
import Link from "next/link";
import { useState, useEffect, useContext } from "react";


import {
  Menu,
  Button,
  Modal,
  Select,
  Input,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";


const Index = () => {

  return (
    <div className='RankingViewTop10 MyRankingView'>
      <div className='MyRankingViewTitle'>
        <h1>10 อันดับที่ดีที่สุด</h1>
      </div>
      <div className='MyRankingViewTable'>
        <div className='InnerViewTableScroll'>
          <div className='InnerViewTable'>
            <table className="table">
              <thead>
                <tr>
                  <th scope="col">อันดับ</th>
                  <th scope="col">ชื่อ</th>
                  <th scope="col">วันที่ทำ</th>
                  <th scope="col">คะแนน</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>1</td>
                  <td>Mai <PERSON></td>
                  <td>10/12/2022</td>
                  <td><span>10</span> คะแนน</td>
                </tr>
                <tr>
                  <td>2</td>
                  <td>Woottipong Sirisujaritthom</td>
                  <td>10/12/2022</td>
                  <td><span>9</span> คะแนน</td>
                </tr>
                <tr>
                  <td>3</td>
                  <td>Tee Suwanchatree</td>
                  <td>10/12/2022</td>
                  <td><span>8</span> คะแนน</td>
                </tr>
                <tr>
                  <td>4</td>
                  <td>Malairak Utthayod</td>
                  <td>10/12/2022</td>
                  <td><span>7</span> คะแนน</td>
                </tr>
                <tr>
                  <td>5</td>
                  <td>Sinitta Boonprasert</td>
                  <td>10/12/2022</td>
                  <td><span>6</span> คะแนน</td>
                </tr>
                <tr>
                  <td>6</td>
                  <td>Chuthaphorn Chaingarm</td>
                  <td>10/12/2022</td>
                  <td><span>5</span> คะแนน</td>
                </tr>
                <tr>
                  <td>7</td>
                  <td>Piyaporn May Jansiriwilaikul</td>
                  <td>10/12/2022</td>
                  <td><span>4</span> คะแนน</td>
                </tr>
                <tr>
                  <td>8</td>
                  <td>Theppithak Nanthanangkul</td>
                  <td>10/12/2022</td>
                  <td><span>4</span> คะแนน</td>
                </tr>
                <tr>
                  <td>9</td>
                  <td>Prim Ninyakanon</td>
                  <td>10/12/2022</td>
                  <td><span>4</span> คะแนน</td>
                </tr>
                <tr>
                  <td>10</td>
                  <td>Nana Waranya</td>
                  <td>10/12/2022</td>
                  <td><span>3</span> คะแนน</td>
                </tr>
                <tr>
                  <td className='my'>38</td>
                  <td className='my'>Phornthip Utama</td>
                  <td className='my'>10/12/2022</td>
                  <td className='my'><span>1</span> คะแนน</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Index;

.backButtonContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  padding:10px 0;
  animation: slideInDown 0.3s ease-out;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 18px;
  background: #638d2e;
  color: white;
  border: none;
  border-radius: 12px;
  font-family: 'Kanit', sans-serif;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(99, 141, 46, 0.2);
  transition: all 0.3s ease;
  text-decoration: none;
  min-width: 200px;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* .backButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
} */

.backButton:hover::before {
  left: 100%;
}

.backButton:hover {
  /* background: #527a25; */
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(99, 141, 46, 0.3);
}

.backButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(99, 141, 46, 0.2);
}

.backButton svg {
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.backButton:hover svg {
  transform: translateX(-2px);
}

.backButton span {
  font-weight: 400;
  letter-spacing: 0.5px;
}
/* 
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} */

/* Alternative design - Card style */
.backButtonCard {
  width: 100%;
  max-width: 400px;
  margin: 0 auto 30px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  animation: slideInDown 0.3s ease-out;
}

.backButtonCard .backButton {
  width: 100%;
  background: linear-gradient(135deg, #638d2e 0%, #527a25 100%);
  border-radius: 10px;
  padding: 16px 24px;
  box-shadow: 0 3px 10px rgba(99, 141, 46, 0.2);
}

/* Minimalist design */
.backButtonMinimal {
  display: flex;
  justify-content: flex-start;
  padding: 15px 20px;
  background: transparent;
  border-bottom: 1px solid #e9ecef;
  animation: slideInDown 0.3s ease-out;
}

.backButtonMinimal .backButton {
  background: transparent;
  color: #638d2e;
  border: 2px solid #638d2e;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.backButtonMinimal .backButton:hover {
  background: #638d2e;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 141, 46, 0.2);
}

/* Mobile responsive */
@media only screen and (max-device-width: 480px) {
  .backButtonContainer {
    padding: 15px 10px;
    margin-bottom: 20px;
  }

  .backButton {
    padding: 12px 20px;
    font-size: 14px;
    min-width: 180px;
    gap: 8px;
  }

  .backButton span {
    font-size: 14px;
  }

  .backButton svg {
    width: 18px;
    height: 18px;
  }

  .backButtonCard {
    margin: 0 10px 20px;
    padding: 15px;
  }

  .backButtonMinimal {
    padding: 12px 15px;
  }
}

@media only screen and (min-device-width: 481px) and (max-device-width: 768px) {
  .backButtonContainer {
    padding: 18px 15px;
    margin-bottom: 25px;
  }

  .backButton {
    padding: 13px 22px;
    font-size: 15px;
    min-width: 190px;
  }

  .backButtonCard {
    margin: 0 15px 25px;
    padding: 18px;
  }

  .backButtonMinimal {
    padding: 14px 18px;
  }
}

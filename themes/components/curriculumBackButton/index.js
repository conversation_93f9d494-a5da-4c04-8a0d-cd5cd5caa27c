import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import styles from './curriculumBackButton.module.css'

const CurriculumBackButton = () => {
  const [showBackButton, setShowBackButton] = useState(false)
  const [curriculumUrl, setCurriculumUrl] = useState('')
  const [courseSlug, setCourseSlug] = useState('')
  const router = useRouter()

  useEffect(() => {
    const checkCurriculumReturn = () => {
      if (typeof window !== 'undefined') {
        const returnUrl = localStorage.getItem('curriculum_return_url')
        const timestamp = localStorage.getItem('curriculum_return_timestamp')
        const savedCourseSlug = localStorage.getItem('curriculum_course_slug')

        const fromCurriculum = router.query.from === 'curriculum'

        const currentPath = window.location.pathname
        const slugMatch = currentPath.match(/\/course\/([^/?]+)/)
        const currentCourseSlug = slugMatch ? decodeURIComponent(slugMatch[1]) : null

        if (returnUrl && timestamp) {
          const timeDiff = Date.now() - parseInt(timestamp)
          
          if (timeDiff < 3600000) {
            const isInCurriculumCourse = savedCourseSlug && currentCourseSlug === savedCourseSlug
            
            if (fromCurriculum || isInCurriculumCourse) {
              setShowBackButton(true)
              setCurriculumUrl(returnUrl)
              setCourseSlug(currentCourseSlug)

              if (fromCurriculum && currentCourseSlug) {
                localStorage.setItem('curriculum_course_slug', currentCourseSlug)
              }
            }
          } else {
            localStorage.removeItem('curriculum_return_url')
            localStorage.removeItem('curriculum_return_timestamp')
            localStorage.removeItem('curriculum_course_slug')
          }
        }
      }
    }

    checkCurriculumReturn()

    const handleRouteChange = (url) => {
      const newSlugMatch = url.match(/\/course\/([^/?]+)/)
      const newCourseSlug = newSlugMatch ? decodeURIComponent(newSlugMatch[1]) : null
      
      const savedCourseSlug = localStorage.getItem('curriculum_course_slug')
      const isStillInSameCourse = savedCourseSlug && newCourseSlug === savedCourseSlug
      const isGoingToCurriculum = curriculumUrl && url.includes(curriculumUrl)

      if (!isStillInSameCourse && !isGoingToCurriculum) {
        setShowBackButton(false)
        localStorage.removeItem('curriculum_return_url')
        localStorage.removeItem('curriculum_return_timestamp')
        localStorage.removeItem('curriculum_course_slug')
      }
    }

    const handleClick = (event) => {
      if (!event.target.closest(`.${styles.backButton}`)) {
        const currentUrl = window.location.pathname
 
        const slugMatch = currentUrl.match(/\/course\/([^/?]+)/)
        const currentCourseSlug = slugMatch ? decodeURIComponent(slugMatch[1]) : null
        
        const savedCourseSlug = localStorage.getItem('curriculum_course_slug')
        const isInSameCourse = savedCourseSlug && currentCourseSlug === savedCourseSlug
        const isInCurriculum = currentUrl.includes('/curriculum/')
        
        if (!isInSameCourse && !isInCurriculum) {
          setShowBackButton(false)
          localStorage.removeItem('curriculum_return_url')
          localStorage.removeItem('curriculum_return_timestamp')
          localStorage.removeItem('curriculum_course_slug')
        }
      }
    }

    router.events.on('routeChangeStart', handleRouteChange)
    document.addEventListener('click', handleClick)

    return () => {
      router.events.off('routeChangeStart', handleRouteChange)
      document.removeEventListener('click', handleClick)
    }
  }, [router, curriculumUrl, courseSlug])

  const handleBackToCurriculum = () => {
    if (curriculumUrl) {
      localStorage.removeItem('curriculum_return_url')
      localStorage.removeItem('curriculum_return_timestamp')
      localStorage.removeItem('curriculum_course_slug')
      
      window.location.href = curriculumUrl
    }
  }

  if (!showBackButton) {
    return null
  }

  return (
     <div className={styles.backButtonContainer}>
      <button 
        className={styles.backButton} 
        onClick={handleBackToCurriculum}
        title="กลับไปยังหลักสูตร"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="m12 19-7-7 7-7"/>
          <path d="M19 12H5"/>
        </svg>
        <span>กลับไปยังหลักสูตร</span>
      </button>
    </div>
  )
}

export default CurriculumBackButton
import React, { Component } from 'react'
import { useState, useEffect, useContext } from "react";
import { Button, Modal, Input, Select, TextArea,  Icon, Dropdown, Label } from "semantic-ui-react";

export default class index extends Component {
  render() {
    if (this.props.type == "deafult") {
      return (
        <div className="block-quiz">
        <div className="i-title-quiz">
          <h3>
            <span>ข้อที่ 1.</span> ข้อความต่อไปนี้กล่าวถึงทฤษฎีของอะตอม
          </h3>
        </div>
        <div className="i-choose-quiz-row row">
          {/* ***** */}
          <div className="col-12 col-md-6 col-choose-quiz">
            <div className="item-choose">
              <div className="fm-check">
                <input type="radio" name="ra1" />
                <div className="text">
                  <div className="i_remark">
                    <i className="icon-ic-circle" />
                  </div>
                  <p>
                    โกลด์สไตน์ทดลองและศึกษารังสีที่อยู่ในหลอดรังสีแคโทดที่มีประจุบวกพบว่า
                    อนุภาคมีประจุบวกและอัตราส่วนของประจุต่อมวลคงที่เช่นเดียวกับการทดลองของ
                    เจ.เจ. ทอมสัน
                  </p>
                </div>
              </div>
            </div>
          </div>
          {/* ***** */}
          <div className="col-12 col-md-6 col-choose-quiz">
            <div className="item-choose">
              <div className="fm-check">
                <input type="radio" name="ra1" />
                <div className="text">
                  <div className="i_remark">
                    <i className="icon-ic-circle" />
                  </div>
                  <p>
                    แบบจำลองอะตอมกลุ่มหมอกจะสามารถระบุตำแหน่งของอิเล็กตรอนว่าอยู่ที่ไหนได้ไม่แน่นอน
                    เนื่องจากการสังเกตนั้นต้องใช้แสง
                    แสงจึงทำให้เกิดความไม่แน่นอนในการระบุตำแหน่ง
                  </p>
                </div>
              </div>
            </div>
          </div>
          {/* ***** */}
          <div className="col-12 col-md-6 col-choose-quiz">
            <div className="item-choose">
              <div className="fm-check">
                <input type="radio" name="ra1" />
                <div className="text">
                  <div className="i_remark">
                    <i className="icon-ic-circle" />
                  </div>
                  <p>
                    เจ.เจ. ทอมสัน ได้ทดลองและสรุปว่า
                    จะตอมทุกชนิดมีอนุภาคที่มีประจุลบเป็นองค์ประกอบและหาอัตราส่วนของประจุต่อมวลของอนุภาคได้ค่าคงที่
                  </p>
                </div>
              </div>
            </div>
          </div>
          {/* ***** */}
          <div className="col-12 col-md-6 col-choose-quiz">
            <div className="item-choose">
              <div className="fm-check">
                <input type="radio" name="ra1" />
                <div className="text">
                  <div className="i_remark">
                    <i className="icon-ic-circle" />
                  </div>
                  <p>
                    รัทเทอร์ฟอร์ดได้เสนอว่า
                    อะตอมประกอบด้วยนิวเคลียสที่มีโปรตอนรวมอยู่ตรงกลาง
                    มีขนาดเล็กแต่มีมวลมากและมีประจุบวก
                    ส่วนอิเล็กตรอนมีมวลน้อยและมีประจุลบวิ่งรอบนิวเคลียสในระนาบเดียวกัน
                  </p>
                </div>
              </div>
            </div>
          </div>
          {/* ***** */}
        </div>
      </div>
      );
    }else if (this.props.type == "text") {
      return (
        <div className="block-quiz">
        <div className="i-title-quiz">
          <h3>
            <span>ข้อที่ 1.</span> กล้ามเนื้อชนิดใดสามารถพบได้ในระบบย่อยอาหาร
          </h3>
        </div>
        <div className="i-choose-quiz-row row">
          {/* ***** */}
          <div className="col-12 col-md-12 col-choose-quiz">
            <div className="item-fm">
            <TextArea
                placeholder="" className="fm-control"
              />
            </div>
          </div>
          {/* ***** */}
        </div>
      </div>
      );
    } else {
      return null;
    }
  }
}



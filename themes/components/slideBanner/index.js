import React, { Component } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination,Autoplay } from "swiper";
import Image from "next/image";

import "swiper/css";
import "swiper/css/pagination";
import styles from "/public/assets/css/component/slideBanner.module.css";

export default class index extends Component {
  render() {
    if(this.props.type=='single'){
      return (
        <div className="slide_banner">
          <Swiper
            className="Swiper_slide_banner"
            pagination={false}
            slidesPerView={1}
            loop={false}
          >
            {
              this.props.data.map((val,key) =>
              <SwiperSlide key={key}>
                <div key={key} className={`${styles.inner}`}>
                  <div className="d-none d-xl-block BlockbannerSlide1">
                    {val && val.image_th_desktop && val.image_th_desktop!=null && val.image_th_desktop!='' && val.image_th_desktop!='null' ?(
                      <Image className="d-none d-xl-block ItemBannerSlideImgSize1" src={val.image_th_desktop} alt="" 
                      layout="fill"
                      objectFit="contain"
                      />
                    ):null}
                  </div>
                  <div className="d-block d-xl-none BlockbannerSlide1">
                    {val && val.image_th_mobile && val.image_th_mobile!=null && val.image_th_mobile!='' && val.image_th_mobile!='null' ?(
                      <Image className="d-block d-xl-none ItemBannerSlideImgSize1" src={val.image_th_mobile} alt="" 
                        layout="fill"
                        objectFit="contain"
                      />
                    ):null}
                  </div>
                </div>
              </SwiperSlide>
              )
            }
           
          </Swiper>
        </div>
      );
    }else{
      return (
        <div className="slide_banner">
          <Swiper
            className="Swiper_slide_banner"
            pagination={true}
            modules={[Pagination,Autoplay]}
            slidesPerView={1}
            loop={true}
            autoplay= {{
              delay: 5000,
              disableOnInteraction: false
            }}
            // onSlideChange={() => console.log("slide change")}
            // onSwiper={(swiper) => console.log(swiper)}
          >
            {
              this.props.data.map((val,key) =>
              <SwiperSlide key={key}>
                <div key={key} className={`${styles.inner}`}>
                  <div className="d-none d-xl-block BlockbannerSlide">
                    {val && val.image_th_desktop && val.image_th_desktop!=null && val.image_th_desktop!='' && val.image_th_desktop!='null' ?(
                      val && val.link && val.link!=null && val.link!='' && val.link!='null' ?(
                        <a href={val.link} target="_blank" rel="noreferrer">
                          <Image className="d-none d-xl-block ItemBannerSlideImgSize" src={val.image_th_desktop} alt="" 
                            layout="fill"
                            objectFit="contain"
                          />
                        </a>
                      ):(
                        <Image className="d-none d-xl-block ItemBannerSlideImgSize" src={val.image_th_desktop} alt="" 
                          layout="fill"
                          objectFit="contain"
                        />
                      )
                    ):null}
                  </div>
                  <div className="d-block d-xl-none BlockbannerSlide">
                    {val && val.image_th_mobile && val.image_th_mobile!=null && val.image_th_mobile!='' && val.image_th_mobile!='null' ?(
                      val && val.link && val.link!=null && val.link!='' && val.link!='null' ?(
                        <a href={val.link} target="_blank" rel="noreferrer">
                          <Image className="d-block d-xl-none ItemBannerSlideImgSize" src={val.image_th_mobile} alt="" 
                            layout="fill"
                            objectFit="contain"
                          />
                        </a>
                      ):(
                        <Image className="d-block d-xl-none ItemBannerSlideImgSize" src={val.image_th_mobile} alt="" 
                          layout="fill"
                          objectFit="contain"
                        />
                      )
                    ):null}
                  </div>
                </div>
              </SwiperSlide>
              )
            }
           
          </Swiper>
        </div>
      );
    }
  }
}

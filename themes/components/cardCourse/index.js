import React, { Component } from "react";
import ReactStars from "react-rating-stars-component";
import { render } from "react-dom";
import Link from 'next/link'
import Image from "next/image";

const ratingThisStar = {
  size: 24,
  count: 5,
  color: "#FFC130",
  activeColor: "#FFC130",
  value: 4,
  a11y: true,
  isHalf: true,
  emptyIcon: <i className="icon-ic-star-light" />,
  halfIcon: <i className="icon-ic-star-half-light" />,
  filledIcon: <i className="icon-ic-star" />,
  onChange: newValue => {
    // console.log(newValue);
  }
};

export default class index extends Component {
  render() {
    return (
      //ให้ กางออกตลอด ลบ .card-course-hover
        <div className="card card-course card-course-hover">
          <div className="inner">
              <div className="card-img">
                <a href="/course" className={`mousePointer`}>
                <img className="img-thumb mousePointer" src={this.props.src} alt="" />
                </a>
                    {/* <div className="screenover mousePointer d-block d-xl-none"></div> */}
                <div className="img-action">
                  <div className="master-img-action">
                    <img src="/assets/images/user1.jpg" alt="" />
                    <img src="/assets/images/user2.jpg" alt="" />
                    <div className="number">+5</div>
                  </div>
                  <button className="btn-img-action add">
                    <i className="icon-ic-circle-plus"></i>
                  </button>
                  <button className="btn-img-action like">
                    <i className="icon-ic-circle-heart"></i>
                  </button>
                </div>
              </div>
              <div className="card-description">
                <div className="description-detail">
                  <div className="description-info">
                    <div className="item-info">
                      <i className="icon-ic-lessons"></i>
                      <span>32</span>
                      บทเรียน
                    </div>
                    <div className="item-info">
                      <i className="icon-ic-time"></i>
                      <span>42</span>
                      นาที
                    </div>
                  </div>
                  <div className="description-text">
                    <p>
                    Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                    Lorem Ipsum is simply dummy text of
                    </p>
                  </div>
                  <div className="description-rating">
                  <ReactStars {...ratingThisStar} />
                  <div className="text-rating">4.5 (1 rating)</div>
                  </div>
                </div>
                <div className="description-price">
                    <div className="description-sale">
                      <span span> ปกติ 5,200</span>
                    </div>
                    <div className="description-big-price">
                      ราคา
                      <span>1,200</span>
                      บาท
                    </div>
                </div>

              </div>
          </div>
        </div>
    );
  }
} 

import React, { Component } from "react";
import ReactStars from "react-rating-stars-component";
import { render } from "react-dom";
import Link from "next/link";
import Image from "next/image";
import NumberFormat from "react-number-format";
import { Popup, Card, Button } from "semantic-ui-react";

function convertSecond(second) {
  if (second != null && second != "") {
    var hours = parseInt(
      new Date(second * 1000).toISOString().substring(11, 13)
    );
    var minutes = parseInt(
      new Date(second * 1000).toISOString().substring(14, 16)
    );
    var seconds = parseInt(
      new Date(second * 1000).toISOString().substring(17, 19)
    );
    if(hours==0){
      return minutes+'m';
    }else{
      return hours + "h:" + minutes+'m';
    }
    
  } else {
    return '0m';
  }
}
const truncate = (input, limit) => {
  if (input && input.length > limit) return input.substring(0, limit) + " ...";
  else return input;
};
export default class index extends Component {
  constructor(props) {
    super(props);
    if(this.props.lang){
      this.state = {
        lang:this.props.lang
      };
    }else{
      this.state = {
        lang:'th'
      };
    }
  }
  translateEng(_value) {
    if(this.state.lang=='en'){
      if(_value=='คณะวิทยากร'){
        return "Faculty of speakers";
      }else if(_value=='วิทยากร'){
        return "Speaker";
      }else if(_value=='บทเรียน'){
        return "Lessons";
      }else if(_value=='ฟรี'){
        return "Free";
      }else if(_value=='นาที'){
        return "Minutes";
      }else if(_value=='ชั่วโมง'){
        return "Hours";
      }else if(_value=='คะแนน'){
        return "Points";
      }else if(_value=='เข้าเรียน'){
        return "Attend course";
      }else if(_value=='ปกติ'){
        return "Normal";
      }else if(_value=='ราคา'){
        return "Price";
      }else if(_value=='บาท'){
        return "Baht";
      }else if(_value=='ดูรายละเอียดเพิ่มเติม'){
        return "See more details";
      }else if(_value=='เพิ่มเติม'){
        return "More";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  render() {
    const ratingThisStar = {
      size: 24,
      count: 5,
      edit: false,
      color: "#FFC130",
      activeColor: "#FFC130",
      value: this.props.data.rate,
      a11y: false,
      isHalf: true,
      emptyIcon: <i className="icon-ic-star-light" />,
      halfIcon: <i className="icon-ic-star-half-light" />,
      filledIcon: <i className="icon-ic-star" />,
      onChange: (newValue) => {
        // console.log(newValue);
      },
    };
    return (
      //ให้ กางออกตลอด ลบ .card-course-hover
      <div className="card card-course card-course-hover">
        <div className="inner">
          <div className="card-img">
            {this.props.data.receive_point != null &&
            this.props.data.receive_point != "" &&
            this.props.data.receive_point != 0 &&
            this.props.data.receive_point != "0" &&
            this.props.data.receive_point != "null" &&
            this.props.data.receive_point > 0 ? (
              <div className="cme-point">
                <Image
                  className="cme-icon"
                  src="/assets/images/cme-icon.png"
                  alt=""
                  layout='fill'
                  objectFit="contain"
                />
                <NumberFormat
                  value={this.props.data.receive_point}
                  displayType={"text"}
                  thousandSeparator={true}
                  renderText={(value, props) => (
                    <span {...props}>
                      {value}
                    </span>
                  )}
                />
              </div>
            ) : null}
            <Link href={`/category/knowledge?series=${this.props.data.group_id}`}>
            <div className="img-thumb mousePointer d-none d-xl-block BlockImgSize">
            {this.props.data && this.props.data.image_th && this.props.data.image_th!=null && this.props.data.image_th!='' && this.props.data.image_th!='null' ?(
              <Image
                  className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                  // data-src={this.props.data.image_th}
                  src={this.props.data.image_th}
                  alt={this.props.data.title_th}
                  layout='fill'
                  objectFit="contain"
                />
                ) : (
                  <Image
                    className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                    // data-src={"/assets/images/course_10.jpg"}
                    src={"/assets/images/course_10.jpg"}
                    layout='fill'
                    objectFit='contain'
                    alt=""
                    />
                  )}
            </div>
            </Link>
            <div className="img-thumb mousePointer d-block d-xl-none BlockImgSize">
              {this.props.data && this.props.data.image_th && this.props.data.image_th!=null && this.props.data.image_th!='' && this.props.data.image_th!='null' ?(
                <Image
                  className="img-thumb mousePointer d-block d-xl-none swiper-lazy ItemsImgsize"
                  // data-src={this.props.data.image_th}
                  src={this.props.data.image_th}
                  alt={this.props.data.title_th}
                  layout='fill'
                  objectFit="contain"
                />
              ):null}
            </div>
            {this.props.data.is_subscription || this.props.data.is_soon || this.props.data.is_hot==1 || this.props.data.free_icon==1 || this.props.data.is_new==1 ? (
              <div className="card-logo-list">
                <Link href={`/category/knowledge?series=${this.props.data.group_id}`}>
                  <div className="logo">
                  <div className="BlockImgSize">
                    {this.props.data.is_subscription ? (
                        <Image src="/assets/images/card_icon/member.png" alt=""
                        layout="fill"
                        objectFit="contain"
                        className="ItemsImgsize"
                        />
                      ):null}
                  </div>
                  <div className="BlockImgSize">
                  {this.props.data.is_soon ? (
                      <Image src="/assets/images/card_icon/coming_soon.png" alt=""
                      layout="fill"
                      objectFit="contain"
                      className="ItemsImgsize"
                      />
                    ):null}
                  </div>
                  <div className="BlockImgSize">
                  {this.props.data.is_hot==1 ? (
                      <Image src="/assets/images/card_icon/hot.png" alt=""
                      layout="fill"
                      objectFit="contain"
                      className="ItemsImgsize"
                      />
                    ):null}
                  </div>
                  <div className="BlockImgSize">
                  {this.props.data.is_new==1 ? (
                      <Image src="/assets/images/card_icon/new_ep.png" alt=""
                      layout="fill"
                      objectFit="contain"
                      className="ItemsImgsize"
                      />
                    ):null}
                  </div>
                  <div className="BlockImgSize">
                    {this.props.data.free_icon==1 ? (
                      <Image src="/assets/images/card_icon/free_icon.png" alt=""
                      layout="fill"
                      objectFit="contain"
                      className="ItemsImgsize"
                      />
                    ):null}
                  </div>
                  </div>
                </Link>
              </div>
            ):null}
            {this.props.data.speaker && this.props.data.speaker.length > 0 ? (
              this.props.data.speaker.length == 1 ? (
                <div className={`card-logo-list ${this.props.data.is_subscription || this.props.data.is_soon || this.props.data.is_hot==1 || this.props.data.free_icon==1 
                || this.props.data.is_new==1 ? "have_logo" : ""}`}>
                  <a href={`/category/knowledge?speaker=${this.props.data.speaker[0].id}`}>
                    <p className="speaker-name">{this.props.data.speaker[0].title_th}</p>
                  </a>
                </div>
              ):(
                <div className={`card-logo-list ${this.props.data.is_subscription || this.props.data.is_soon || this.props.data.is_hot==1 || this.props.data.free_icon==1 
                  || this.props.data.is_new==1 ? "have_logo" : ""}`}>
                  <Popup
                    hideOnScroll
                    content={
                      <>
                        <ul>
                          {this.props.data.speaker.map((val, key) =>
                            val.avatar != null &&
                            val.avatar != "" &&
                            val.avatar != "null" ? (
                              <a href={`/category/knowledge?speaker=${val.id}`}>
                                <li>
                                  <Image src={val.avatar} alt="" 
                                   layout="fill"
                                   objectFit="contain"
                                  />{" "}
                                  <span>{val.title_th}</span>
                                </li>
                              </a>
                            ) : (
                              <a href={`/category/knowledge?speaker=${val.id}`}>
                                <li>
                                  <Image src="/assets/images/iuser.png" alt="" 
                                   layout="fill"
                                   objectFit="contain"
                                  />{" "}
                                  <span>{val.title_th}</span>
                                </li>
                              </a>
                            )
                          )}
                        </ul>
                      </>
                    }
                    on='click'
                    popper={{
                      id: "popper-container-custom-card",
                      style: { zIndex: 2000 },
                    }}
                    trigger={
                      <p className="speaker-name">{this.translateEng('คณะวิทยากร')}</p>
                    }
                  />
                </div>
              )
            ) : null}
            {this.props.data.logo_list.length>0 ? (
              <div className="card-logo-list-right">
                <div className="logo">
                  <div className="BlockImgSize">
                    {this.props.data.logo_list.map((val, key) =>
                      val && val.logo && val.logo!=null && val.logo!='' && val.logo!='null' ?(
                        val.link!=null && val.link!='' && val.link!='null'?(
                          <Link key={key} href={val.link}>
                            <Image src={val.logo} alt=""
                              layout="fill"
                              objectFit="cover"
                              objectPosition='center'
                              className="ItemsImgsize"
                            />
                          </Link>
                        ):(
                          <Image src={val.logo} alt=""
                            layout="fill"
                            objectFit="cover"
                            objectPosition='center'
                            className="ItemsImgsize"
                          />
                        )
                      ):null
                    )}
                  </div>
                </div>
              </div>
            ):null}
            {/* <Link href={`/category/knowledge?series=${this.props.data.group_id}`}><div className="screenover mousePointer d-block d-xl-none"></div></Link> */}
            {this.props.type == "zoom" ? (
              <Image className="img-logo" src="/assets/images/zoom.png" alt="" 
              layout="fill"
              objectFit="contain"
              />
            ):null}
            <div className="img-action">
              {this.props.data.speaker &&
              this.props.data.speaker.length > 0 ? (
                <div className="master-img-action">
                  <Popup
                    hideOnScroll
                    content={
                      <>
                        <ul>
                          {this.props.data.speaker.map((val, key) =>
                            val.avatar != null &&
                            val.avatar != "" &&
                            val.avatar != "null" ? (
                              <li>
                                <div className="blockImgSizePopup">
                                <Image src={val.avatar} alt="" 
                                 layout="fill"
                                 objectFit="contain"
                                 className="ItemsImgsizePopup"
                                />{" "}
                                </div>
                                <span>{val.title_th}</span>
                              </li>
                            ) : (
                              <li>
                                <div className="blockImgSizePopup">
                                <Image src="/assets/images/iuser.png" alt="" 
                                 layout="fill"
                                 objectFit="contain"
                                 className="ItemsImgsizePopup"
                                />{" "}
                                </div>
                                <span>{val.title_th}</span>
                              </li>
                            )
                          )}
                        </ul>
                      </>
                    }
                    // on='click' test
                    popper={{
                      id: "popper-container-custom",
                      style: { zIndex: 2000 },
                    }}
                    trigger={
                      this.props.data.speaker[0].avatar != null &&
                      this.props.data.speaker[0].avatar != "" &&
                      this.props.data.speaker[0].avatar != "null" ? (
                        <Image src={this.props.data.speaker[0].avatar} alt="" 
                        layout="fill"
                        objectFit="contain"
                        className="ItemsImgsize"
                        />
                      ) : (
                        <Image src="/assets/images/iuser_w.png" alt="" 
                        layout="fill"
                        objectFit="contain"
                        className="ItemsImgsize"
                        />
                      )
                    }
                  />
                  {/* {this.props.data.speaker.length > 1 ? (
                    <div className="number">
                      +{this.props.data.speaker.length - 1}
                    </div>
                  ) : null} */}
                  <div className="number">
                    {this.translateEng('วิทยากร')}
                  </div>
                </div>
              ) : null}
            </div>
          </div>
          <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
          <Link href={`/category/knowledge?series=${this.props.data.group_id}`}>
            <div className="card-description">
              <div className="description-detail">
                <div className="description-info">
                  {this.props.data.trailer_media!=2 ? (
                    <div className="item-info">
                      <i className="icon-ic-lessons"></i>
                      <span>{this.props.data.lesson}</span>
                      {this.translateEng('บทเรียน')}
                    </div>
                  ):null}
                  <div className="item-info">
                    <i className="icon-ic-time"></i>
                    <span>{convertSecond(this.props.data.duration)}</span>
                  </div>
                  {this.props.data.is_certificate ? (
                    <div className="item-info">
                      <div className="BlockImgSize">
                      <Image className="certificate-icon ItemsImgsize" src="/assets/images/certificate.png" alt="" 
                        layout="fill"
                        objectFit="contain"
                      /> Certificate
                      </div>
                    </div>
                  ):null}
                </div>
                <div className="description-text">
                  <p>{truncate(this.props.data.subtitle_th, 80)}</p>
                </div>
                <div className="description-rating">
                  <ReactStars {...ratingThisStar} />
                  <div className="text-rating">
                    {Number(this.props.data.rate).toFixed(1)} ({this.props.data.rating} rating)
                  </div>
                </div>
              </div>
              <div className="description-price cursor-pointer">
                <div className="description-big-price">{this.translateEng('เพิ่มเติม')}</div>
              </div>
              <div className="description-view-more d-flex d-xl-none">
                <div className="description-view">
                  <div className="inner-view">
                    <span>{this.translateEng('ดูรายละเอียดเพิ่มเติม')}</span>
                  </div>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </div>
    );
  }
}

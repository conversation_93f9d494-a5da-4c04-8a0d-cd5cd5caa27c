import React, { Component } from "react";
import styles from "/public/assets/css/component/itemListChapter.module.css";
import stylesLock from "/public/assets/css/component/itemListChapterLock.module.css";
const Condition = ({ condition,data }) => {
  return condition ? (
    <div className="documents-list videojs no-bg-mb">
      <div className="row document-item">
        <div className="col-8 item">
          <div className="row row-m-0">
            <p className="document-title">Chapter {data.chapter} :</p><span>{data.name}</span>
          </div>
        </div>
        <div className="col-4 item text-right">
          <div>
            <a className="document-download" href="#">ดาวโหลดไฟล์ในบทเรียน</a>
            <a href="#">
              <img
                className={`${styles.iconThumb}`}
                src="/assets/images/icon_pdf.png"
              />
            </a>
            <a href="#">
              <img
                className={`${styles.iconThumb}`}
                src="/assets/images/icon_powerpoint.png"
              />
            </a>
            <a href="#">
              <img
                className={`${styles.iconThumb}`}
                src="/assets/images/icon_excel.png"
              />
            </a>
          </div>
        </div>
      </div>
    </div>
  ) : (
    <div className="documents-list videojs no-bg-mb">
      <div className="row document-item lock">
        <div className="col-8 item">
          <div className="row row-m-0">
            <p className="document-title">Chapter {data.chapter} :</p><span>{data.name}</span>
          </div>
        </div>
        <div className="col-4 item text-right">
          <div>
            <a className="document-download" href="#">ดาวโหลดไฟล์ในบทเรียน</a>
            <a href="#">
              <img
                className={`${styles.iconThumb}`}
                src="/assets/images/icon_pdf.png"
              />
            </a>
            <a href="#">
              <img
                className={`${styles.iconThumb}`}
                src="/assets/images/icon_powerpoint.png"
              />
            </a>
            <a href="#">
              <img
                className={`${styles.iconThumb}`}
                src="/assets/images/icon_excel.png"
              />
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
export default class index extends Component {
  render() {
    return (
      this.props.data.map((val, key) => (
        <Condition key={key} data={val} condition={val.status=='1'}></Condition>
      ))
    );
  }
}

import React from 'react';
import Link from "next/link";
import { useState, useEffect, useContext } from "react";


import {
  Menu,
  Button,
  Modal,
  Select,
  Input,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";




const Index = () => {

  return (

    <div className="ui pagination menu">
      <a className="item page disable">
        <i className="icon-ic-left"></i>
      </a>
      <a className="item link active">1</a>
      <a className="item link">2</a>
      <a className="item link dot">...</a>
      <a className="item link">9</a>
      <a className="item link">10</a>
      <a className="item page">
        <i className="icon-ic-right"></i>
      </a>
    </div>

  );
}

export default Index;

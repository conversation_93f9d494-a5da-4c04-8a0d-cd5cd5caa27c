import React from 'react';
import { Chart as ChartJS, ArcElement, Too<PERSON><PERSON>, Legend, } from 'chart.js';
import { Pie } from 'react-chartjs-2';

import Link from "next/link";
import { useState, useEffect, useContext } from "react";

ChartJS.register(Arc<PERSON><PERSON>, Tooltip, Legend);

import {
  Menu,
  Button,
  Modal,
  Select,
  Input,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";

export const statisticViewData = {
  labels: ['ข้อที่ทำถูก', 'ข้อที่ยังไม่ได้ทำ', 'ข้อที่ทำผิด'],
  datasets: [
    {
      data: [3, 5, 4],
      backgroundColor: [
        'rgba(75, 192, 192, 0.5)',
        'rgba(255, 159, 64, 0.5)',
        'rgba(255, 99, 132, 0.5)',
      ],
      borderColor: [
        'rgba(75, 192, 192, 0.5)',
        'rgba(255, 159, 64, 0.5)',
        'rgba(255, 99, 132, 0.5)',
      ],
      borderWidth: 1,
      options: {
        plugins: {


        }
      }
    },
  ],
};


export const options = {
  responsive: true,
  layout: {},
  plugins: {
    legend: {
      position: 'bottom',
      labels: {
        usePointStyle: true,
        boxWidth: 10,
        padding: 15,
        font: {
            size: 14,
            family: 'Kanit'
        }
      }
    },
    title: {},
  },
};

const Index = () => {

  return (
    <div className='statisticViewAmount'>
        <div className="statisticViewAmount-description">
          <div className="item-description">ข้อสอบ 10 ข้อ</div>
          <div className="item-description"> ทำแล้ว 5 ข้อ</div>
        </div>
        <div className="statisticViewAmount-chart">
          <div className="chart-inner">
          <Pie data={statisticViewData} options={options}   />
          </div>
        </div>
  </div>
  );
}

export default Index;

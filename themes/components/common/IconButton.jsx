import React from 'react';

function IconButton({
  onClick,
  icon,
  className
}) {

    const styles = {
        backgroundColor: 'transparent',
        border: 'none',
        cursor: 'pointer',
        padding: 0,
        display: 'flex',
        alignItems: 'start',
        justifyContent: 'center'
      };
      
  return (
    <button style={styles} onClick={onClick} className={className}>
      {icon}
    </button>
  );
}

export default IconButton;
/** @jsxImportSource @emotion/react */
import { css } from "@emotion/react";
import Image from "next/image";

const Avatar = ({ src, alt = "User Avatar", size = 50, rounded = true, fallback = "/default-avatar.png", className , onClick = () => {} }) => {
  const avatarStyle = css`
    width: ${size}px;
    height: ${size}px;
    border-radius: ${rounded ? "50%" : "8px"};
    position: relative;
    border: solid 2px #B5B5B5;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  `;

  const imageStyle = css`
    object-fit: cover;
  `;

  return (
    <div css={avatarStyle} className={className} onClick={onClick}>
      <Image
        src={src || fallback}
        alt={alt}
        width={size } // Subtract border width (3px * 2)
        height={size } // Subtract border width (3px * 2)
        onError={(e) => {e.currentTarget.src = fallback}}
        css={imageStyle}
      />
    </div>
  );
};

export default Avatar;
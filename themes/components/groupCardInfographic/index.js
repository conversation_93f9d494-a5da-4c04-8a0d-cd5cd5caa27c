import React, { Component } from "react";
import Link from "next/link";
import Image from "next/image";
import moment from "moment";
import { Swiper, SwiperSlide } from "swiper/react";
import { Zoom, FreeMode, Pagination, Controller, Navigation,Lazy,Mousewheel } from "swiper";
import "swiper/css/lazy";
import CardInfographicSw from "../cardInfographicSw";
const truncate = (input,limit) => {
  if (input && input.length > limit)
      return input.substring(0, limit) + ' ...';
  else
      return input;
};
export default class index extends Component {
  render() {
    if(this.props.type=='desktop'){
      return this.props.data.map((val, key) => (
        <div key={key} className="col-6 col-md-6 col-lg-4 col-xl-4 pd-t-b">
          <div className="card card-seminar radius">
            <div className="inner cursor">
              <a href={`/infographic/${val.slug}`}>
                <div className="card-seminar-inner">
                  <div className="seminar-img">
                    {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                      <Image className="seminar-thumb ItemsImgsize" src={val.image_th} alt="" 
                        layout="fill"
                        objectFit="contain"
                      />
                    ):null}
                  </div>
                  <div className="seminar-description">
                    <div className="description-content content-top">
                      <h3>{truncate(val.title_th,35)}</h3>
                      <p>{truncate(val.subtitle_th,50)}</p>
                    </div>
                    <div className="description-content content-bottom">
                      <div className="date">
                        <i className="icon-ic-clock"></i> <span>{moment(val.started_date).format("MMMM Do YYYY")}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      ));
    }else{
      return (
        <Swiper
          freeMode={true}
          lazy={true}
          mousewheel={{
            forceToAxis: true,
            enabled: true,
          }}
          modules={[FreeMode,Lazy,Pagination,Mousewheel]}
          spaceBetween={30}
          pagination={{
            clickable: true,
          }}
          style={{ zIndex: this.props.index }}
        >
          {this.props.data.map((val, key) => (
            <SwiperSlide key={key}>
              <CardInfographicSw key={key} data={val}></CardInfographicSw>
            </SwiperSlide>
          ))}
        </Swiper>
      );
    }
  }
}

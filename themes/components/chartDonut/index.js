import React, { Component } from "react";
import { Chart as ChartJ<PERSON>, ArcElement, Tooltip, Legend } from 'chart.js';
import { Doughnut } from 'react-chartjs-2';

import styles from "/public/assets/css/component/chartDonut.module.css";

ChartJS.register(Arc<PERSON><PERSON>, Legend);

const dataDonut = {
  // labels: ['Red', 'Blue'],
  datasets: [
    {
      showTooltips: 'false',
      tooltips: 'false',
      label: '# of Votes',
      data: [40, 60,],
      backgroundColor: [
        '#9F9F9F',
        '#94C120',
      ],
      tooltips: {enabled: false},
      hover: {mode: null},
      borderColor: [
        '#9F9F9F',
        '#9F9F9F',
    ],
      borderWidth: 5,
    },
  ],
  plugins: [{

  }],
};

export default class index extends Component {
  render() {
    return (
      <div className={`${styles.block_chart_info}`}>
        <div className={`${styles.inner_chart}`}>
          <div className={`${styles.chart_info_img}`}>
            <Doughnut data={dataDonut} />
            <div className={`${styles.chart_info_number}`}>
              <div className={`${styles.number}`}>
                <span>40%</span>
                </div>
            </div>
          </div>
          <div className={`${styles.chart_info_text}`}>
              <h3>Systemic Lupus Erythematosus (SLE)</h3>
              <h4>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.</h4>
          </div>
        </div>
      </div>
    );
  }
}

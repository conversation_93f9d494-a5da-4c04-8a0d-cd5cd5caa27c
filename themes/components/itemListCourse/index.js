import React, { Component } from 'react'

import styles from '/public/assets/css/component/itemListCourse.module.css'

import Link from 'next/link'
import Image from 'next/image'

export default class index extends Component {
  render() {
    return this.props.data.map((val, key) => (
      <div key={key} className={`${styles.item_list_course}`}>
        <div className={`${styles.item_course_inner}`}>
          <div className={`${styles.item_course_img}`}>
            <div className={`${styles.blockLisCourseImgSize}`}>
              {val &&
              val.image_th &&
              val.image_th != null &&
              val.image_th != '' &&
              val.image_th != 'null' ? (
                <Image
                  className={`${styles.thumb}`}
                  key={key}
                  src={val.image_th}
                  layout="fill"
                  objectFit="cover"
                  alt=""
                />
              ) : null}
            </div>
          </div>
          <div className={`${styles.item_course_text}`}>
            <h3>{val.title_th}</h3>
            <h4>{val.subtitle_th}</h4>
            {val.expire_date && (
              <h5 className="text-danger">{val.expire_date}</h5>
            )}
          </div>
          {val.trailer_media == 6 ? (
            <div className={`${styles.item_course_action}`}>
              <a href={`/ebook/${val.slug}`}>
                <button className={`${styles.action}`}>              
                      <i className={val.is_download ? "icon-ic-download-1" : "eye icon"}></i>
                      <span>{val.is_download ? "Download" : "อ่าน"}</span>
                </button>
              </a>
            </div>
          ) : val.trailer_media == 7 ? (
            <div className={`${styles.item_live_action}`}>
              <div className={`${styles.item_action_title}`}>
                <p>เข้าชม</p>
              </div>
              {new Date(val.started_time) <= new Date() &&
              new Date(val.end_time) >= new Date() ? (
                <a href={`/live/${val.slug}`}>
                  <button className={`${styles.action} ${styles.active}`}>
                    <div className={`${styles.play_stream}`}>
                      <i className="icon-ic-shape-right"></i>
                    </div>
                    <h3>Live</h3>
                    <p>STREAMING</p>
                  </button>
                </a>
              ) : (
                <a href={`/live/${val.slug}`}>
                  <button className={`${styles.action}`}>
                    <div className={`${styles.play_stream}`}>
                      <i className="icon-ic-shape-right"></i>
                    </div>
                    <h3>Live</h3>
                    <p>STREAMING</p>
                  </button>
                </a>
              )}
              <div className={`${styles.item_action_time}`}>
                <p>
                  วันที่ Live : {val.live_date}
                  <br></br>
                  เวลา : {val.live_time}
                </p>
              </div>
            </div>
          ) : val.trailer_media == 9 || val.trailer_media == 10 ? (
            // <div className={`${styles.item_course_action}`}>
            //   <a onClick={()=>this.props.submitImmersiveCourse(val.partner_ref_id,val.trailer_media == 9?'cvlearn':'mooc')}>
            //     <button className={`${styles.action}`}>
            //       <i className="icon-ic-play"></i>
            //       <span>Play</span>
            //     </button>
            //   </a>
            // </div>
            <div className={`${styles.item_course_action}`}>
              <a
                href={`${
                  val.is_curriculum
                    ? `/curriculum/${val.slug}`
                    : `/course/${val.slug}`
                }`}
              >
                <button className={`${styles.action}`}>
                  <i className="icon-ic-play"></i>
                  <span>Play</span>
                </button>
              </a>
            </div>
          ) : val.zoom_join_url == null || val.zoom_join_url == '' ? (
            <div className={`${styles.item_course_action}`}>
              <a
                href={`${
                  val.is_curriculum
                    ? `/curriculum/${val.slug}`
                    : `/course/${val.slug}`
                }`}
              >
                <button className={`${styles.action}`}>
                  <i className="icon-ic-play"></i>
                  <span>Play</span>
                </button>
              </a>
              <p className={`${styles.percent}`}>{val.learned_percent}%</p>
            </div>
          ) : (
            <div className={`${styles.item_course_action}`}>
              <a
                href={`${
                  val.is_curriculum
                    ? `/curriculum/${val.slug}`
                    : `/course/${val.slug}`
                }`}
              >
                <button className={`${styles.action}`}>
                  <i className="icon-ic-play"></i>
                  <span>Play</span>
                </button>
              </a>
            </div>
          )}
        </div>
      </div>
    ))
  }
}

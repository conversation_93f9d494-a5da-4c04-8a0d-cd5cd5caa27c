import React, { Component } from "react";
import Link from "next/link";
import styles from "/public/assets/css/component/chooseTypeDataList.module.css";

export default class index extends Component {
  render() {
    return (
      <div className={`${styles.choose_type_data_list}`}>
        <ul>
          {this.props.data.map((val, key) => (
            val.value=='immersive'?(
              <a onClick={this.props.submitImmersive} key={key}>
                {val.value == this.props.type ? (
                  <li className={`${styles.active}`}>
                    <span>{val.text}</span>
                  </li>
                ) : (
                  <li>
                    <span>{val.text}</span>
                  </li>
                )}
              </a>
            ):val.value=='chula-lifelong-learning'?(
              <a onClick={this.props.creditBankPopup} key={key}>
                {val.value == this.props.type ? (
                  <li className={`${styles.active}`}>
                    <span>{val.text}</span>
                  </li>
                ) : (
                  <li>
                    <span>{val.text}</span>
                  </li>
                )}
              </a>
            ):(
              <a href={`${val.link}`} key={key}>
                {val.value == this.props.type ? (
                  <li className={`${styles.active}`}>
                    <span>{val.text}</span>
                  </li>
                ) : (
                  <li>
                    <span>{val.text}</span>
                  </li>
                )}
              </a>
            )
          ))}
        </ul>
      </div>
    );
  }
}

import React, { Component } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Zoom, FreeMode, Pagination } from "swiper";

import CardLock from "../cardLock";


import "swiper/css";
import "swiper/css/pagination";

import styles from "/public/assets/css/component/groupCategory.module.css";

export default class index extends Component {
  render() { 
      return (
        <div
          className={`container custom-container space-between-content`}
          style={{ "z-index": this.props.index }}
        >
          <div className="container custom-container">
            <div className="row">
              <div className="col-12">
                <h2
                  className={`${styles.title}`}
                  style={{ color: this.props.color }}
                >
                  {this.props.name}
                </h2>
              </div>
            </div>
          </div>

          {/* <Swiper
            slidesPerView={this.props.size}
            spaceBetween={10}
            freeMode={false}
            modules={[Zoom, FreeMode]}
            zoom={true}
            className={`${styles.mySlide}`}
          >
            {this.props.data.map((val, key) => (
              <SwiperSlide key={key}>
                <CardLock key={key} src={val.image}></CardLock>
              </SwiperSlide>
            ))}
          </Swiper> */}
        </div>
      ); 
  }
}

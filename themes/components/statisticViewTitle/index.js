import React from "react";
import Link from "next/link";
import { useState, useEffect, useContext } from "react";

import {
  Menu,
  Button,
  Modal,
  Select,
  Input,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";

const Index = () => {
  return (
    <div className="StatisticViewTitle">
      <h1>
        วันที่ทำข้อสอบ <span>01/12/2022</span>
      </h1>
      <div className="description-list">
        <div className="item-description">
          <i className="icon-ic-document"></i> <span>10 ข้อ</span>
        </div>
        <div className="item-description">
          <i className="icon-ic-document"></i> <span>10 คะแนน</span>
        </div>
        <div className="item-description">
          <i className="icon-ic-tick-thanks"></i> <span>เกณฑ์การผ่าน 50%</span>
        </div>
      </div>
    </div>
  );
};

export default Index;

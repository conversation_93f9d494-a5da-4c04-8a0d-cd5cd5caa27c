import React, { Component } from "react";
import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
  TextArea,
} from "semantic-ui-react";
import { render } from "react-dom";
import Image from "next/image";

import styles from "/public/assets/css/component/paymentOption.module.css";

export default class index extends Component {
  render() {
    return (
      <div className="block-payment-list">
        {/* ======== */}
        <div className="item-payment">
          <div className="item-choose-payment">
            <div className="fm-check">
            <input type="radio" name="ra1" />
              <div className="text">
                <div className="i_remark">
                  <i className="icon-ic-circle" />
                </div>
                <div className="content">
                  <p>QR Code</p>
                  <Image 
                    className="qr ItemsQRCodeImgSize" 
                    src="/assets/images/qr.png" 
                    layout="fill"
                    objectFit="contain"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* ======== */}
        <div className="item-payment">
          <div className="item-choose-payment">
            <div className="fm-check">
              <input type="radio" name="ra1" />
              <div className="text">
                <div className="i_remark">
                  <i className="icon-ic-circle" />
                </div>
                <div className="content">
                  <p>Credit/Debit</p>
                  <Image
                    className="visa ItemsQRCodeImgSize"
                    src="/assets/images/visa.jpg"
                    layout="fill"
                    objectFit="contain"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* ======== */}
        <div className="item-payment">
          <div className="item-choose-payment">
            <div className="fm-check">
            <input type="radio" name="ra1" />
              <div className="text">
                <div className="i_remark">
                  <i className="icon-ic-circle" />
                </div>
                <div className="content">
                  <p>โอนเงิน</p>
                  <div className="account-bank">
                    <Image 
                      className="bank ItemsQRCodeImgSize" 
                      src="/assets/images/bank.jpg" 
                      layout="fill"
                      objectFit="contain"
                      alt=""
                    />
                    <div className="bank-description">
                      <h3>ธนาคารไทยพาณิชย์</h3>
                      <h4>887-845-4710 </h4>
                      <h4>คุณ สุนิชา ธนการะกิจ</h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* ======== */}
      </div>
    );
  }
}

import React from 'react';
import Link from "next/link";
import { useState, useEffect, useContext } from "react";


import {
  Menu,
  Button,
  Modal,
  Select,
  Input,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";


const Index = () => {

  return (
    <div className='RankingViewHistory MyRankingView'>
            <div className='MyRankingViewTitle'>
        <h1>10 อันดับที่ดีที่สุด</h1>
      </div>
      <div className='MyRankingViewTable'>
        <div className='InnerViewTableScroll'>
          <div className='InnerViewTable'>
            <table className="table">
              <thead>
                <tr>
                  <th scope="col">ทดสอบครั้งที่</th>
                  <th scope="col">วันที่</th>
                  <th scope="col">คะแนน</th>
                  <th scope="col">สถานะ</th>
                  <th scope="col">ผลการทดสอบ</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>2</td>
                  <td>10/10/2022</td>
                  <td><span>9</span> คะแนน</td>
                  <td><span className='status complete'>ตรวจแล้ว</span></td>
                  <td><span className='result succes'>ผ่าน</span></td>
                </tr>
                <tr>
                  <td>3</td>
                  <td>1/11/2022</td>
                  <td><span>4</span> คะแนน</td>
                  <td><span className='status complete'>ตรวจแล้ว</span></td>
                  <td><span className='result fail'>ไม่ผ่าน</span></td>
                </tr>
                <tr>
                  <td>5</td>
                  <td>12/11/2022</td>
                  <td><span>7</span> คะแนน</td>
                  <td><span className='status progress'>กำลังตรวจสอบ</span></td>
                  <td><span className='result succes'>-</span></td>
                </tr>
                <tr>
                  <td>33</td>
                  <td>20/11/2022</td>
                  <td><span>8</span> คะแนน</td>
                  <td><span className='status wait'>รอการตรวจสอบ</span></td>
                  <td><span className='result succes'>-</span></td>
                </tr>
                <tr>
                  <td>35</td>
                  <td>23/11/2022</td>
                  <td><span>5</span> คะแนน</td>
                  <td><span className='status wait'>รอการตรวจสอบ</span></td>
                  <td><span className='result succes'>-</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Index;

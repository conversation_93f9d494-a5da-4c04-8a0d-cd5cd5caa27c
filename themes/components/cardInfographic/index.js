import React, { Component } from "react";
import Link from "next/link";
import Image from "next/image";

export default class index extends Component {
  render() {
    return (
      <div className="card card-seminar radius">
        <div className="inner cursor">
          <a href={`/infographic/${this.props.data.slug}`}>
            <div className="card-seminar-inner">
              <div className="seminar-img">
                {this.props.data && this.props.data.image_th && this.props.data.image_th!=null && this.props.data.image_th!='' && this.props.data.image_th!='null' ?(
                  <Image className="seminar-thumb" src={this.props.data.image_th} alt={this.props.data.title_th} 
                  layout="fill"
                  objectFit="contain"
                  />
                ):null}
              </div>
              <div className="seminar-description">
                <div className="description-content content-top">
                  <h3>{this.props.data.title_th}</h3>
                  <p>
                    {this.props.data.subtitle_th}
                  </p>
                </div>
                <div className="description-content content-bottom">
                  <div className="date">
                    <i className="icon-ic-clock"></i> <span>{this.props.data.date}</span>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    );
  }
}

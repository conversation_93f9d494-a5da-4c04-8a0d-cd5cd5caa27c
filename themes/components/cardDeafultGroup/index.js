import React, { Component } from "react";
import Link from "next/link";
import { Swiper, SwiperSlide } from "swiper/react";
import CardProgramSw from "../cardProgramSw";
import CardProgram from "../cardProgram";
import { Zoom, FreeMode, Pagination, Controller, Navigation,Lazy,Mousewheel } from "swiper";
import "swiper/css/lazy";
export default class index extends Component {
  constructor(props) {
    super(props);
    if(this.props.lang){
      this.state = {
        lang:this.props.lang
      };
    }else{
      this.state = {
        lang:'th'
      };
    }
  }
  render() {
    if(this.props.type=='desktop'){
      return (
        <div className="card card-default radius">
          <div className="inner card-list-row">
            {this.props.data.map((val, key) => (
              <div key={key} className="col-6 col-md-6 col-lg-4 col-xl-4 card-item">
                 <CardProgram
                    type="normal"
                    callback={this.props.callback}
                    data={val}
                    lang={this.props.lang}
                  ></CardProgram>
              </div>
            ))}
            
          </div>
        </div>
      );
    }else{
      return (
        <div className="category-item-slide-big">
          <Swiper
            freeMode={true}
            lazy={true}
            mousewheel={{
              forceToAxis: true,
              enabled: true,
            }}
            modules={[FreeMode,Lazy,Pagination,Mousewheel]}
            spaceBetween={30}
            pagination={{
              clickable: true,
            }}
            style={{ zIndex: this.props.index }}
          >
            {this.props.data.map((val, key) => (
              <SwiperSlide key={key}>
                <CardProgramSw
                  type="normal"
                  callback={this.props.callback}
                  data={val}
                  lang={this.props.lang}
                ></CardProgramSw>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      );
    }
  }
}

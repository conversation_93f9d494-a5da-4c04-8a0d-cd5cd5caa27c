import React, { Component } from "react";
import Image from "next/image";

const truncate = (input, limit) => {
  if (input && input.length > limit) return input.substring(0, limit) + " ...";
  else return input;
};
export default class index extends Component {
  constructor(props) {
    super(props);
    if(this.props.lang){
      this.state = {
        lang:this.props.lang
      };
    }else{
      this.state = {
        lang:'th'
      };
    }
  }
  render() {

    // ฟังก์ชันช่วยในการเลือกไอคอน
    const renderIcon = () => {
      switch (this.props.data.type) {
        case 'youtube':
          return <i className="icon-ic-youtube" />;
        case 'tiktok':
          return <i className="icon-ic-social-tiktok" />;
        default:
          return <i className="icon-ic-circle-play" />;
      }
    };
    return (
      //ให้ กางออกตลอด ลบ .card-course-hover
      <div className={`card card-course card-course-hover ${this.props.data.trailer_media==6 ? 'card-course-ebook':''}`}>
        <div className="inner">
          <div className="card-img">
            <a {...(this.props.data.video_link != null && this.props.data.video_link !== ''? { target: '_blank', rel: 'noreferrer' }: {})} href={`${this.props.data.video_link!=null&&this.props.data.video_link!=''?this.props.data.video_link:'/course/'+this.props.data.slug}`}>
                  <div className="d-none d-xl-block BlockImgSize Fixblock">
                    {this.props.data && this.props.data.img && this.props.data.img!=null && this.props.data.img!='' && this.props.data.img!='null' ?(
                      <Image
                      className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                      src={this.props.data.img}
                      alt={this.props.data.img}
                      layout='fill'
                      objectFit='contain'
                    />
                    ) : (
                      <Image
                      className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                      // data-src={"/assets/images/course_10.jpg"}
                      src={"/assets/images/course_10.jpg"}
                      layout='fill'
                      objectFit='contain'
                      alt=""
                      />
                    )}
                  </div>
                </a>
            <div className="img-action bg-danger">
              
                <div className={`text-size-white`}>
                  <span> {this.props.data.title}</span>
                </div>
              <button
                onClick={() =>
                  this.props.callback("favourite", this.props.data.id, "")
                }
                className={`btn-img-action like favourite_class_${this.props.data.id} ${
                  this.props.data.has_fav ? "active" : ""
                }`}
              >
                {renderIcon()}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

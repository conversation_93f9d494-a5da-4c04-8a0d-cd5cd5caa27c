import React, { Component } from "react";
import ReactStars from "react-rating-stars-component";
import { render } from "react-dom";
import Link from "next/link";
import Image from "next/image";
import NumberFormat from "react-number-format";
import { Popup, Card, Button, Progress } from "semantic-ui-react";

function convertSecond(second) {
  if (second != null && second != "") {
    var hours = parseInt(
      new Date(second * 1000).toISOString().substring(11, 13)
    );
    var minutes = parseInt(
      new Date(second * 1000).toISOString().substring(14, 16)
    );
    var seconds = parseInt(
      new Date(second * 1000).toISOString().substring(17, 19)
    );
    if(hours==0){
      return minutes+'m';
    }else{
      return hours + "h:" + minutes+'m';
    }
    
  } else {
    return '0m';
  }
}
const truncate = (input, limit) => {
  if (input && input.length > limit) return input.substring(0, limit) + " ...";
  else return input;
};
export default class index extends Component {
  render() {
    const ratingThisStar = {
      size: 24,
      count: 5,
      edit: false,
      color: "#FFC130",
      activeColor: "#FFC130",
      value: this.props.data.rate,
      a11y: false,
      isHalf: true,
      emptyIcon: <i className="icon-ic-star-light" />,
      halfIcon: <i className="icon-ic-star-half-light" />,
      filledIcon: <i className="icon-ic-star" />,
      onChange: (newValue) => {
        // console.log(newValue);
      },
    };
    return (
      //ให้ กางออกตลอด ลบ .card-course-hover
      <div className="card card-course card-course-hover">
        <div className="inner">
          <div className="card-img">
            {this.props.data.receive_point != null &&
            this.props.data.receive_point != "" &&
            this.props.data.receive_point != 0 &&
            this.props.data.receive_point != "0" &&
            this.props.data.receive_point != "null" &&
            this.props.data.receive_point > 0 ? (
              <div className="cme-point">
                <Image
                  className="cme-icon"
                  src="/assets/images/cme-icon.png"
                  alt=""
                  layout="fill"
                  objectFit="contain"
                />
                <NumberFormat
                  value={this.props.data.receive_point}
                  displayType={"text"}
                  thousandSeparator={true}
                  renderText={(value, props) => (
                    <span {...props}>
                      {value}
                    </span>
                  )}
                />
              </div>
            ) : null}
            <a {...(this.props.data.link_out != null && this.props.data.link_out !== ''? { target: '_blank', rel: 'noreferrer' }: {})} href={`${this.props.data.link_out!=null&&this.props.data.link_out!=''?this.props.data.link_out:'/course/'+this.props.data.slug}`}>
            <div className="d-none d-xl-block BlockImgSize Fixblock">
            {this.props.data && this.props.data.learning_cover && this.props.data.learning_cover!=null && this.props.data.learning_cover!='' && this.props.data.learning_cover!='null' ? (
            <Image
                className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                // data-src={this.props.data.learning_cover}
                alt={this.props.data.title_th}
                src={this.props.data.learning_cover}
                layout='fill'
                objectFit='contain'
              />
              ) : (
                <Image
                className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                // data-src={"/assets/images/course_10.jpg"}
                src={"/assets/images/course_10.jpg"}
                layout='fill'
                objectFit='contain'
                alt=""
                />
              )}
            </div>
            </a>
            <div className="d-block d-xl-none BlockImgSize">
            {this.props.data && this.props.data.learning_cover && this.props.data.learning_cover!=null && this.props.data.learning_cover!='' && this.props.data.learning_cover!='null' ? (
              <Image
                className="img-thumb mousePointer d-block d-xl-none swiper-lazy ItemsImgsize"
                // data-src={this.props.data.learning_cover}
                src={this.props.data.learning_cover}
                alt={this.props.data.title_th}
                layout="fill"
              />
            ):null}
            </div>
            {this.props.data.is_subscription || this.props.data.is_soon || this.props.data.is_hot==1 || this.props.data.free_icon==1 || this.props.data.is_new==1 ? (
              <div className="card-logo-list">
                <a {...(this.props.data.link_out != null && this.props.data.link_out !== ''? { target: '_blank', rel: 'noreferrer' }: {})} href={`${this.props.data.link_out!=null&&this.props.data.link_out!=''?this.props.data.link_out:'/course/'+this.props.data.slug}`}>
                  <div className="logo">
                    {this.props.data.is_subscription ? (
                      <div className="BlockImgSize">
                        <Image src="/assets/images/card_icon/member.png" alt=""
                         layout="fill"
                         objectFit="contain"
                         className="ItemsImgsize"
                        />
                      </div>
                    ):null}
                    {this.props.data.is_soon ? (
                      <div className="BlockImgSize">
                        <Image src="/assets/images/card_icon/coming_soon.png" alt=""
                         layout="fill"
                         objectFit="contain"
                         className="ItemsImgsize"
                        />
                      </div>
                    ):null}
                    {this.props.data.is_hot==1 ? (
                      <div className="BlockImgSize">
                        <Image src="/assets/images/card_icon/hot.png" alt=""
                         layout="fill"
                         objectFit="contain"
                         className="ItemsImgsize"
                        />
                      </div>
                    ):null}
                    {this.props.data.is_new==1 ? (
                      <div className="BlockImgSize">
                        <Image src="/assets/images/card_icon/new_ep.png" alt=""
                         layout="fill"
                         objectFit="contain"
                         className="ItemsImgsize"
                        />
                      </div>
                    ):null}
                    {this.props.data.free_icon==1 ? (
                      <div className="BlockImgSize">
                        <Image src="/assets/images/card_icon/free_icon.png" alt=""
                         layout="fill"
                         objectFit="contain"
                         className="ItemsImgsize"
                        />
                      </div>
                    ):null}
                  </div>
                </a>
              </div>
            ):null}
            {this.props.data.speaker && this.props.data.speaker.length > 0 ? (
              this.props.data.speaker.length == 1 ? (
                <div className={`card-logo-list ${this.props.data.is_subscription || this.props.data.is_soon || this.props.data.is_hot==1 || this.props.data.free_icon==1 
                || this.props.data.is_new==1 ? "have_logo" : ""}`}>
                  <a href={`/category/knowledge?speaker=${this.props.data.speaker[0].id}`}>
                   
                  </a>
                </div>
              ):(
                <div className={`card-logo-list ${this.props.data.is_subscription || this.props.data.is_soon || this.props.data.is_hot==1 || this.props.data.free_icon==1 
                  || this.props.data.is_new==1 ? "have_logo" : ""}`}>
                  <Popup
                    hideOnScroll
                    content={
                      <>
                        <ul>
                          {this.props.data.speaker.map((val, key) =>
                            val.avatar != null &&
                            val.avatar != "" &&
                            val.avatar != "null" ? (
                              <a href={`/category/knowledge?speaker=${val.id}`}>
                                <li>
                                  <Image src={val.avatar} alt="" 
                                    layout="fill"
                                    objectFit="contain"
                                  />{" "}
                                  <span>{val.title_th}</span>
                                </li>
                              </a>
                            ) : (
                              <a href={`/category/knowledge?speaker=${val.id}`}>
                                <li>
                                  <Image src="/assets/images/iuser.png" alt="" 
                                    layout="fill"
                                    objectFit="contain"
                                  />{" "}
                                  <span>{val.title_th}</span>
                                </li>
                              </a>
                            )
                          )}
                        </ul>
                      </>
                    }
                    on='click'
                    popper={{
                      id: "popper-container-custom-card",
                      style: { zIndex: 2000 },
                    }}
                    trigger={
                      <p className="speaker-name">คณะวิทยากร</p>
                    }
                  />
                </div>
              )
            ) : null}
            {this.props.data.logo_list.length>0 ? (
              <div className="card-logo-list-right">
                <div className="logo">
                  <div className="BlockImgSize">
                    {this.props.data.logo_list.map((val, key) =>
                      val && val.logo && val.logo!=null && val.logo!='' && val.logo!='null' ?(
                        val.link!=null && val.link!='' && val.link!='null'?(
                          <Link key={key} href={val.link}>
                            <Image src={val.logo} alt=""
                              layout="fill"
                              objectFit="cover"
                              objectPosition='center'
                              className="ItemsImgsize"
                            />
                          </Link>
                        ):(
                          <Image src={val.logo} alt=""
                            layout="fill"
                            objectFit="cover"
                            objectPosition='center'
                            className="ItemsImgsize"
                          />
                        )
                      ):null
                    )}
                  </div>
                </div>
              </div>
            ):null}
            
            {/* <Link href={`/course/${this.props.data.slug}`}><div className="screenover mousePointer d-block d-xl-none"></div></Link> */}
            {this.props.type == "zoom" ? (
              <div className="img-logo">
                <Image src="/assets/images/zoom.png" alt="" 
                layout="fill"
                objectFit="cover"
                objectPosition='center'
                />
              </div>
            ):null}
            <div className="img-action">
              {this.props.data.speaker &&
              this.props.data.speaker.length > 0 ? (
                <div className="master-img-action">
                  <Popup
                    hideOnScroll
                    content={
                      <>
                        <ul>
                          {this.props.data.speaker.map((val, key) =>
                            val.avatar != null &&
                            val.avatar != "" &&
                            val.avatar != "null" ? (
                              <li>
                                <div className="blockImgSizePopup">
                                  <Image src={val.avatar} alt="" 
                                   layout="fill"
                                   objectFit="contain"
                                   className="ItemsImgsizePopup"
                                  />{" "}
                                </div>
                                <span>{val.title_th}</span>
                              </li>
                            ) : (
                              <li>
                                <div className="blockImgSizePopup">
                                  <Image src="/assets/images/iuser.png" alt="" 
                                    layout="fill"
                                    objectFit="contain"
                                    className="ItemsImgsizePopup"
                                  />{" "}
                                </div>
                                <span>{val.title_th}</span>
                              </li>
                            )
                          )}
                        </ul>
                      </>
                    }
                    // on='click'
                    popper={{
                      id: "popper-container-custom",
                      style: { zIndex: 2000 },
                    }}
                    trigger={
                      this.props.data.speaker[0].avatar != null &&
                      this.props.data.speaker[0].avatar != "" &&
                      this.props.data.speaker[0].avatar != "null" ? (
                        <Image src={this.props.data.speaker[0].avatar} alt="" 
                          layout="fill"
                          objectFit="contain"
                        />
                      ) : (
                        <Image src="/assets/images/iuser_w.png" alt="" 
                          layout="fill"
                          objectFit="contain"
                        />
                      )
                    }
                  />
                  {/* {this.props.data.speaker.length > 1 ? (
                    <div className="number">
                      +{this.props.data.speaker.length - 1}
                    </div>
                  ) : null} */}
                  <div className="number">
                    วิทยากร
                  </div>
                </div>
              ) : null}
              {this.props.data.reward_point>0?(
                <div className={`card-reward-coin`}>
                  <Image alt="" width={32} height={32} src={'/assets/images/coin.png'} objectFit='contain' />
                  <p>{this.props.data.reward_point} คะแนน</p>
                </div>
              ):null}
              {this.props.data.allowed ||
              this.props.data.order_status == 4 ||
              this.props.data.order_status == 1 || this.props.data.is_internal || this.props.data.is_volume ? null : 
              this.props.data.is_promotion == 1 ? (
                  this.props.data.pro_price == 0 ? null : (
                    <button
                      onClick={() =>
                        this.props.callback("cart", this.props.data.id, "")
                      }
                      className={`btn-img-action add ${
                        this.props.data.has_cart ? "active" : ""
                      }`}
                    >
                      <i
                        className={`${
                          this.props.data.has_cart
                            ? "icon-ic-doctor-bag"
                            : "icon-ic-doctor-bag"
                        }`}
                      ></i>
                    </button>
                  )
              ) : (
                this.props.data.price == 0 ? null : (
                  <button
                    onClick={() =>
                      this.props.callback("cart", this.props.data.id, "")
                    }
                    className={`btn-img-action add ${
                      this.props.data.has_cart ? "active" : ""
                    }`}
                  >
                    <i
                      className={`${
                        this.props.data.has_cart
                          ? "icon-ic-doctor-bag"
                          : "icon-ic-doctor-bag"
                      }`}
                    ></i>
                  </button>
                )
              )}
              <button
                onClick={() =>
                  this.props.callback("playlist", this.props.data.id, "")
                }
                className={`btn-img-action add playlist_class_${this.props.data.id} ${
                  this.props.data.has_playlist ? "active" : ""
                }`}
              >
                <i
                  className={`playlist_icon_${this.props.data.id} ${
                    this.props.data.has_playlist
                      ? "icon-ic-tick-thanks"
                      : "icon-ic-circle-plus"
                  }`}
                ></i>
              </button>
              <button
                onClick={() =>
                  this.props.callback("favourite", this.props.data.id, "")
                }
                className={`btn-img-action like favourite_class_${this.props.data.id} ${
                  this.props.data.has_fav ? "active" : ""
                }`}
              >
                <i className="icon-ic-circle-heart"></i>
              </button>
            </div>
            <div className="video-progress">
              <Progress percent={this.props.data.learning_percent} />
            </div>
          </div>
          <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
          <a {...(this.props.data.link_out != null && this.props.data.link_out !== ''? { target: '_blank', rel: 'noreferrer' }: {})} href={`${this.props.data.link_out!=null&&this.props.data.link_out!=''?this.props.data.link_out:'/course/'+this.props.data.slug}`}>
            <div className="card-description">
              <div className="description-detail">
                <div className="description-info">
                  {this.props.data.trailer_media!=2 ? (
                    <div className="item-info">
                      <i className="icon-ic-lessons"></i>
                      <span>{this.props.data.lesson}</span>
                  
                    </div>
                  ):null}
                  <div className="item-info">
                    <i className="icon-ic-time"></i>
                    <span>{convertSecond(this.props.data.duration)}</span>
                  </div>
                  {this.props.data.is_certificate ? (
                    <div className="item-info">
                      <div className="BlockImgSize">
                        <Image className="certificate-icon ItemsImgsize" src="/assets/images/certificate.png" alt="" 
                          layout="fill"
                          objectFit="contain"
                        /> Certificate
                      </div>
                    </div>
                  ):null}
                </div>
                <div className="description-text">
                  <p>{truncate(this.props.data.subtitle_th, 80)}</p>
                </div>
                <div className="description-rating">
                  <ReactStars {...ratingThisStar} />
                  <div className="text-rating">
                    {Number(this.props.data.rate).toFixed(1)} ({this.props.data.rating} rating)
                  </div>
                </div>
              </div>
              {this.props.data.allowed ||
              this.props.data.order_status == 4 ||
              this.props.data.order_status == 1 ? (
                <div className="description-price">
                  <div className="description-big-price">เข้าเรียน</div>
                </div>
              ) : this.props.data.is_subscription ? (
                <div className="description-price">
                  <div className="description-big-price">Member</div>
                </div>
              ):(
                this.props.data.is_internal || this.props.data.is_volume ? (
                  <div className="description-price">
                    <div className="description-big-price">ฟรี</div>
                  </div>
                ):(
                  this.props.data.is_promotion == 1 ? (
                    <div className="description-price">
                      <div className="description-sale">
                        <NumberFormat
                          value={this.props.data.price}
                          displayType={"text"}
                          thousandSeparator={true}
                          renderText={(value, props) => (
                            <span {...props}>ปกติ {value}</span>
                          )}
                        />
                      </div>
                      {this.props.data.pro_price == 0 ? (
                        <div className="description-big-price">ฟรี</div>
                      ) : (
                        <div className="description-big-price">
                          ราคา
                          <NumberFormat
                            value={this.props.data.pro_price}
                            displayType={"text"}
                            thousandSeparator={true}
                            renderText={(value, props) => (
                              <span {...props}>{value}</span>
                            )}
                          />
                          บาท
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="description-price">
                      {this.props.data.price == 0 ? (
                        <div className="description-big-price">ฟรี</div>
                      ) : (
                        <div className="description-big-price">
                          ราคา
                          <NumberFormat
                            value={this.props.data.price}
                            displayType={"text"}
                            thousandSeparator={true}
                            renderText={(value, props) => (
                              <span {...props}>{value}</span>
                            )}
                          />
                          บาท
                        </div>
                      )}
                    </div>
                  )
                )
              )
              }
              <div className="description-view-more d-flex d-xl-none">
                <div className="description-view">
                  <div className="inner-view">
                    <span>ดูรายละเอียดเพิ่มเติม</span>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    );
  }
}

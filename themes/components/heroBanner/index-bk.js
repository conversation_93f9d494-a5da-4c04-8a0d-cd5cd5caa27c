import React, { Component } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination,Autoplay } from "swiper";
import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
  TextArea,
} from "semantic-ui-react";

import Link from "next/link";
import Image from 'next/image'

import "swiper/css";
import "swiper/css/pagination";
const dataOptions = [
  { value: "1", key: "1", text: "ประชาชน" },
  { value: "2", key: "2", text: "แพทย์ และนิสิตแพทย์" },
  { value: "3", key: "3", text: "พยาบาล และบุคลากรทางการแพทย์" },
];
export default class index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      categories: '',
      disease: '',
      keyword: '',
      filter: 2,
    };
  }
  setFilter(_id) {
    this.setState({
      filter: _id,
    });
  }
  setGroup(_id) {
    this.setState({
      categories: _id.value,
    });
  }
  setDisease(_id) {
    this.setState({
      disease: _id.value,
    });
  }
  submit() {
    this.setState({
      keyword: document.getElementById("keyword_box").value,
    });
    setTimeout(() => {
      let go_url = '/category/knowledge';
      if(this.state.categories!=''){
        go_url = go_url+'?categories='+this.state.categories;
      }
      if(this.state.disease!=''){
        if(this.state.categories==''){
          go_url = go_url+'?disease='+this.state.disease;
        }else{
          go_url = go_url+'&disease='+this.state.disease;
        }
      }
      if(this.state.keyword!=''){
        if(this.state.categories==''&&this.state.disease==''){
          go_url = go_url+'?keyword='+this.state.keyword;
        }else{
          go_url = go_url+'&keyword='+this.state.keyword;
        }
      }
      window.location.href=go_url;
    }, "0");
  }
  filter() {
    this.props.callback(this.state.filter);
  }
  
  render() {
    return (
      <div className={`top-banner ${!this.props.user ? '' : "no-go-section"}`}>
        <div className={`hero_banner ${!this.props.user ? 'go-section' : ""}`}>
          <Swiper
            className="Swiper_hero_banner"
            pagination={true}
            modules={[Pagination, Autoplay]}
            slidesPerView={1}
            loop={true}
            autoplay={{
              delay: 5000,
              disableOnInteraction: false,
            }}
            // onSlideChange={() => console.log("slide change")}
            // onSwiper={(swiper) => console.log(swiper)}
          >
            {this.props.data.map((val, key) => (
              <SwiperSlide key={key}>
                {val.type == 1 && val.link!=null && val.link!='' && val.link!='null' ? (
                  <a href={val.link}>
                    <div className="d-none d-xl-block d-md-block blockImgHeroBannerSize">
                      {val && val.image_th_desktop && val.image_th_desktop!=null && val.image_th_desktop!='' && val.image_th_desktop!='null' ?(
                        <Image
                          className="d-none d-xl-block d-md-block ItemsImgHeroBannerSize"
                          src={val.image_th_desktop}
                          alt=""
                          layout="fill"
                          objectFit="contain"
                        />
                      ):null}
                    </div>
                    <div className="d-block d-md-none d-xl-none img-mb blockImgHeroBannerSize">
                      {val && val.image_th_mobile && val.image_th_mobile!=null && val.image_th_mobile!='' && val.image_th_mobile!='null' ?(
                        <Image
                          className="d-block d-md-none d-xl-none img-mb ItemsImgHeroBannerSize"
                          src={val.image_th_mobile}
                          alt=""
                          layout="fill"
                          objectFit="contain"
                        />
                      ):null}
                    </div>
                  </a>
                ) : (
                  <div>
                      <div className="d-none d-xl-block d-md-block blockImgHeroBannerSize">
                        {val && val.image_th_desktop && val.image_th_desktop!=null && val.image_th_desktop!='' && val.image_th_desktop!='null' ?(
                          <Image
                            className="d-none d-xl-block d-md-block ItemsImgHeroBannerSize"
                            src={val.image_th_desktop}
                            alt=""
                            layout="fill"
                          objectFit="contain"
                          />
                        ):null}
                      </div>
                      <div className="d-block d-md-none d-xl-none img-mb blockImgHeroBannerSize">
                        {val && val.image_th_mobile && val.image_th_mobile!=null && val.image_th_mobile!='' && val.image_th_mobile!='null' ?(
                          <Image
                            className="d-block d-md-none d-xl-none img-mb ItemsImgHeroBannerSize"
                            src={val.image_th_mobile}
                            alt=""
                            layout="fill"
                          objectFit="contain"
                          />
                        ):null}
                      </div>
                  </div>
                )}
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
        {
          <div className={`hero_option ${!this.props.user ? 'go-section' : ""}`}>
            <div className="container custom-container">
              <div className={`inner-hero_fm ${!this.props.user ? 'with-go-section' : ""}`}>
                {!this.props.user ? (
                  <div className="go-section">
                    <div className="top-section">
                      <h3>คอร์สสำหรับ</h3>
                      {/* <div className="item-fm">
                        <Select
                          className="fm-control"
                          placeholder="คอร์สสำหรับ"
                          options={dataOptions}
                          defaultValue={dataOptions[0].value}
                          onChange={(event, data) => {
                            this.setFilter(data)
                          }}
                        />
                      </div> */}
                      <p className={`go-section-choice ${this.state.filter==1 ? "active" : ""}`} onClick={() => this.setFilter(1)}>ประชาชน</p>
                      <p className={`go-section-choice ${this.state.filter==2 ? "active" : ""}`} onClick={() => this.setFilter(2)}>แพทย์ นิสิต นักศึกษา<br></br>และบุคลากรทางการแพทย์</p>
                    </div>
                    <div className="bottom-section">
                      <div className="item-hero_fm action">
                        {this.state.filter!='' ? (
                          <button onClick={() => this.filter()} className="btn-hero_fm">
                            {this.state.filter==1?(
                              <span>Free !</span>
                            ):(
                              <span>Go !</span>
                            )}
                          </button>
                        ):null}
                      </div>
                    </div>
                  </div>
                ):null}
                <div className="row">
                  {/* ***** */}
                  <div className="item-hero_fm dropdown">
                    <div className="item-fm home-filter">
                      <Select
                        className="fm-control"
                        placeholder="หมวดหมู่"
                        options={this.props.widget.group}
                        onChange={(event, data) => {
                          this.setGroup(data)
                        }}
                      />
                    </div>
                  </div>
                  {/* ***** */}
                  <div className="item-hero_fm dropdown">
                    <div className="item-fm home-filter">
                      <Select
                        className="fm-control"
                        placeholder="กลุ่มโรค"
                        options={this.props.widget.disease}
                        onChange={(event, data) => {
                          this.setDisease(data)
                        }}
                      />
                    </div>
                  </div>
                  {/* ***** */}
                  <div className="item-hero_fm">
                    <div className="item-fm home-filter">
                      <Input
                        id="keyword_box"
                        type="text"
                        className="fm-control"
                        placeholder="ค้นหา.... ชื่อเรื่อง ชื่อวิทยากร คำสำคัญ"
                      ></Input>
                    </div>
                  </div>
                  {/* ***** */}
                  <div className="item-hero_fm action">
                    <button onClick={() => this.submit()} className="btn-hero_fm home-filter">
                      <span>ค้นหา</span>
                    </button>
                  </div>
                  {/* ***** */}
                </div>
              </div>

              {/* <div className="inner-hero_option">
              <Swiper
                className="Swiper_hero_option"
                // slidesPerView={3}
                spaceBetween={0}
                pagination={{
                  clickable: true,
                }}
                breakpoints={{
                  640: {
                    slidesPerView: 1,
                  },
                  768: {
                    slidesPerView: 2,
                  },
                  1024: {
                    slidesPerView: 3,
                  },
                }}
                modules={[Pagination]}
              >
                {this.props.widget.map((val,key) =>
                  <SwiperSlide key={key}>
                    {val.link!=null&&val.link!=''?(
                      <Link href={val.link}>
                        <div className="card-hero_option cursor">
                          <div className="inner">
                            <div className="hero_option-img">
                              <i className={val.icon}></i>
                            </div>
                            <div className="hero_option-content">
                              <h3>{val.title}</h3>
                              <p>{val.description}</p>
                            </div>
                          </div>
                        </div>
                      </Link>
                    ):(
                      <div className="card-hero_option">
                        <div className="inner">
                          <div className="hero_option-img">
                            <i className={val.icon}></i>
                          </div>
                          <div className="hero_option-content">
                            <h3>{val.title}</h3>
                            <p>{val.description}</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </SwiperSlide>
                )}
              </Swiper>
            </div> */}
            </div>
          </div>
        }
      </div>
    );
  }
}

import React, { Component } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper";
import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
  TextArea,
} from "semantic-ui-react";

import Link from "next/link";
import Image from "next/image";
import "swiper/css";
import "swiper/css/pagination";
import axios from "axios";
import { BsArrowsAngleExpand, BsArrowsAngleContract } from "react-icons/bs";
import IconButton from "../common/IconButton";
import Avatar from "../common/Avatar";
import ButtomTag from "./BottomTag";
import ReactMarkdown from "react-markdown";
import FloatingImage from "./FloatingImage";
import { IoMdSend } from "react-icons/io";
import SendIcon from "../icons/SendIcon";
import CourseCard from "./CourseCard";
import remarkBreaks from "remark-breaks";
import AvatarMoblie from './AvartarMobile'
const dataOptions = [
  { value: "1", key: "1", text: "ประชาชน" },
  { value: "2", key: "2", text: "แพทย์ และนิสิตแพทย์" },
  { value: "3", key: "3", text: "พยาบาล และบุคลากรทางการแพทย์" },
];
import LoadingAIChat from "./LoadingAiChat";
// import { AssistantStream } from "openai/lib/AssistantStream";
// import dynamic from "next/dynamic";
// const AssistantStream = dynamic(() => import('openai/lib/AssistantStream'), {
//   ssr: false,
// });

export default class index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      categories: "",
      disease: "",
      keyword: "",
      filter: 2,
      activeTab: 1, // กำหนดค่าเริ่มต้นให้แท็บ 1 ถูกเปิด
      isExpandedChat: false,
      isCollapseChat: false,
      isStartChat: false,
      isStartChatText: "",
      userInput: "",
      messages: [],
      inputDisabled: false,
      limitMessage: false,
      threadId: "",
      tags: [],
      devModeCount: 0
    };
  }
  setFilter(_id) {
    this.setState({
      filter: _id,
    });
  }
  setGroup(_id) {
    this.setState({
      categories: _id.value,
    });
  }
  setDisease(_id) {
    this.setState({
      disease: _id.value,
    });
  }

  // ฟังก์ชันเปลี่ยนแท็บ
  handleTabClick = (tabIndex) => {
    this.setState({ activeTab: tabIndex });
  };

  handleExpandClick = () => {
    this.setState((prevState) => ({
      isExpandedChat: !prevState.isExpandedChat,
    }));
  };

  handleCollapseClick = () => {
    this.setState((prevState) => ({
      isCollapseChat: !prevState.isCollapseChat,
    }));
  };

  submit() {
    this.setState({
      keyword: document.getElementById("keyword_box").value,
    });
    setTimeout(() => {
      let go_url = "/category/knowledge";
      if (this.state.categories != "") {
        go_url = go_url + "?categories=" + this.state.categories;
      }
      if (this.state.disease != "") {
        if (this.state.categories == "") {
          go_url = go_url + "?disease=" + this.state.disease;
        } else {
          go_url = go_url + "&disease=" + this.state.disease;
        }
      }
      if (this.state.keyword != "") {
        if (this.state.categories == "" && this.state.disease == "") {
          go_url = go_url + "?keyword=" + this.state.keyword;
        } else {
          go_url = go_url + "&keyword=" + this.state.keyword;
        }
      }
      window.location.href = go_url;
    }, "0");
  }
  filter() {
    this.props.callback(this.state.filter);
  }

  ///////// CHAT WITH AI /////////
  handleKeyDown = async (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      await this.handleSubmit(e);
    }
  };

  handleSubmitMobile = async (e) => {
    this.setState((prevState) => ({
      isExpandedChat: true,
    }));
    this.handleSubmit(e);

  };


  componentDidMount() {
    this.createThread();
  }

  createThread = async () => {
    const res = await fetch(`/api/assistants/threads`, {
      method: "POST",
    });
    const data = await res.json();
    const defaultMessage =
      "สวัสดีครับ 😊 ยินดีต้อนรับสู่ MedUMORE E-Learning Platform!\nมีหัวข้อหรือคอร์สไหนที่สนใจเรียนรู้เป็นพิเศษไหมครับ?";
    const userMessage = this.props.user
      ? `สวัสดีครับ คุณ ${this.props.user.name}! 😊 ยินดีต้อนรับสู่ MedUMORE E-Learning Platform!\n ช่วงนี้สนใจคอร์สไหนเป็นพิเศษไหมครับ? หรืออยากให้แนะนำคอร์สดีๆ ให้ไหมครับ?`
      : defaultMessage;
    this.setState({
      threadId: data.threadId,
      messages: [
        {
          role: "assistant",
          text: userMessage,
        },
      ],
    });

    // if (this.props.user) {
    //   const userTypes = {
    //     1: "แพทย์",
    //     2: "ประชาชนทั่วไป",
    //     3: "บุคลากรทางการแพทย์",
    //     4: "อื่นๆ",
    //     5: "นิสิต นักศึกษาคณะวิทยาศาสตร์สุขภาพ",
    //     14: "International Partner",
    //     15: "กลุ่มโครงการพิเศษ",
    //   };

    //   const userInternals = {
    //     1: "บุคลากรโรงพยาบาลจุฬาลงกรณ์",
    //     2: "บุคลากรโรงพยาบาลอื่นๆ",
    //     3: "บุคลากรคณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย",
    //     4: "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย",
    //     5: "จุฬาลงกรณ์มหาวิทยาลัย",
    //     6: "มหาวิทยาลัยอื่นๆ",
    //   };

    //   const userStatuses = { 1: "ยืนยันแล้ว", 2: "ยังไม่ได้ยืนยัน" };

    //   const getValue = (obj, key, defaultValue) => obj?.[key] ?? defaultValue;

    //   this.setState({ threadId: data.threadId, isStartChat: true }, () => {
    //     const { user = {}, coursesLatest = [] } = this.props;
    //     const {
    //       name = "Unknown",
    //       id = "N/A",
    //       status,
    //       user_type,
    //       internal,
    //       created_at,
    //       updated_at,
    //       last_access,
    //       last_learning
    //     } = user;

    //     // ฟอร์แมตข้อมูลคอร์ส
    //     const allCourses =
    //       coursesLatest?.courses?.length > 0
    //         ? coursesLatest.courses
    //           .map(
    //             (course) =>
    //               `[Course ID: ${course.course_id}, Course Name:${course.course_name}, Course Created At: ${new Date(
    //                 course.created_at
    //               ).toLocaleString()}]`
    //           )
    //         : "No courses available";

    //     const formattedCreatedAt = created_at
    //       ? new Date(created_at).toLocaleString()
    //       : new Date(updated_at).toLocaleString();

    //     const formattedLastAccess = last_access
    //       ? new Date(last_access).toLocaleString()
    //       : "N/A";

    //     const formattedLastLearning = last_learning
    //       ? last_learning
    //       : "N/A";

    //     const userStatus = getValue(userStatuses, status, "ยังไม่ได้ยืนยัน");
    //     const memberType = getValue(userTypes, user_type, "ไม่ทราบประเภทผู้ใช้");
    //     const userGroup = getValue(userInternals, internal, "มหาวิทยาลัยอื่นๆ");

    //     this.handleSubmit(
    //       { preventDefault: () => { } },
    //       `<<User Name: ${name}, User ID: ${id}, User Status: ${userStatus}, 
    //         Member Type: ${memberType}, User Group: ${userGroup}, 
    //         Registration Date: ${formattedCreatedAt}, Date Now: ${new Date().toLocaleString()}, 
    //         Latest Access: ${formattedLastAccess}, 
    //         Latest Learning Course: ${formattedLastLearning}, 
    //         Latest Release Course: 
    //         ${allCourses}>>`
    //     );
    //   });
    // } else {
    //   this.setState({
    //     threadId: data.threadId,
    //     messages: [
    //       {
    //         role: "assistant",
    //         text: "สวัสดีครับ 😊 ยินดีต้อนรับสู่ MedUMORE E-Learning Platform!\nมีหัวข้อหรือคอร์สไหนที่สนใจเรียนรู้เป็นพิเศษไหมครับ?",
    //       },
    //     ],
    //   });
    // }
  };

  regexNewLine = (text) => {
    return text.replace(/\n\n(\d+)\./g, '\n\n$1').replace(/\n\n\n/g, "/\n\n\\")
  };

  componentDidUpdate(_, prevState) {
    if (prevState.messages !== this.state.messages) {
      this.scrollToBottom();
    }
  }

  scrollToBottom = async () => {
    const lastAssistantIndex = [...this.state.messages]
      .reverse()
      .findIndex((msg) => msg.role === "assistant");

    if (lastAssistantIndex !== -1) {
      const lastAssistant =
        this.state.messages[
        this.state.messages.length - 1 - lastAssistantIndex
        ];

      const jsonMatch = lastAssistant.text.match(/```json\n([\s\S]*?)\n```/);
      const text = lastAssistant.text
        .replace(/```json\n[\s\S]*?\n```/, "")
        .trim();
      const formattedText = text.replace(/\n/g, "  \n");

      const json = jsonMatch ? JSON.parse(jsonMatch[1]) : null;
      if (json && json.length > 0) {
        const mappedCourses = json.map(
          ({ Course_ID, course_id, coursename, course_name, Course_name }) => ({
            course_id: Course_ID || course_id,
            course_name: coursename || course_name || Course_name,
          })
        );
        await axios({
          url: `${process.env.NEXT_PUBLIC_API}/api/ai/courses`,
          method: "POST",
          data: {
            courses: mappedCourses,
          },
          headers: {
            "Content-Type": "application/json",
          },
        })
          .then((apiResponse) => {
            this.setState((prevState) => ({
              messages: prevState.messages.map((msg, index) =>
                index === prevState.messages.length - 1 - lastAssistantIndex
                  ? {
                    role: msg.role,
                    text: formattedText,
                    json: apiResponse.data,
                  }
                  : msg
              ),
              tags: apiResponse.data.tags,
            }));
          })
          .catch((error) => {
            this.setState((prevState) => ({
              messages: prevState.messages.map((msg, index) =>
                index === prevState.messages.length - 1 - lastAssistantIndex
                  ? {
                    role: msg.role,
                    text: formattedText,
                  }
                  : msg
              ),
            }));
          });
      }
    }

    const conversationDiv = document.querySelector(".conversation_history");

    if (conversationDiv) {
      conversationDiv.scrollTo({
        top: conversationDiv.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  sendMessage = async (text) => {
    const { threadId } = this.state;
    try {
      console.log("sendMessage");
      console.log(threadId);
      const response = await fetch(
        `/api/assistants/threads/${threadId}/messages`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ content: text }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const { AssistantStream } = require("openai/lib/AssistantStream");
      const stream = AssistantStream.fromReadableStream(response.body);
      this.handleReadableStream(stream);
    } catch (error) {
      console.error("Error sending message:", error);
      this.setState({
        inputDisabled: false,
        messages: [...this.state.messages, {
          role: "assistant",
          text: "ขออภัยครับ เกิดข้อผิดพลาดในการส่งข้อความ กรุณาลองใหม่อีกครั้งครับ 🙏\n\nError: " + error.message
        }]
      });
    }
  };

  handleSubmit = (e) => {
    e?.preventDefault();
    const { userInput, messages, isStartChat } = this.state;
    if (!userInput.trim()) return;
  
    if (messages.length >= 20) {
      this.setState((prevState) => ({
        messages: [
          ...prevState.messages,
          {
            role: "assistant",
            text: "ขอบคุณที่ร่วมพูดคุยกันนะครับ! 💬 ตอนนี้เราขอจบการสนทนาไว้ตรงนี้ก่อน แล้วพบกันใหม่ในครั้งถัดไปนะครับ ถ้ามีคำถามเพิ่มเติมหรืออยากเรียนรู้หัวข้อไหน อย่าลืมมาหาเรานะ! 😊",
          },
        ],
        userInput: "",
        limitMessage: true,
      }));
      return;
    }
  
    const { user = null, coursesLatest = [] } = this.props;
    const getValue = (obj, key, defaultValue) => obj?.[key] ?? defaultValue;
  
    const userTypes = {
      1: "แพทย์",
      2: "ประชาชนทั่วไป",
      3: "บุคลากรทางการแพทย์",
      4: "อื่นๆ",
      5: "นิสิต นักศึกษาคณะวิทยาศาสตร์สุขภาพ",
      14: "International Partner",
      15: "กลุ่มโครงการพิเศษ",
    };
  
    const userInternals = {
      1: "บุคลากรโรงพยาบาลจุฬาลงกรณ์",
      2: "บุคลากรโรงพยาบาลอื่นๆ",
      3: "บุคลากรคณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย",
      4: "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย",
      5: "จุฬาลงกรณ์มหาวิทยาลัย",
      6: "มหาวิทยาลัยอื่นๆ",
    };
  
    const userStatuses = { 1: "ยืนยันแล้ว", 2: "ยังไม่ได้ยืนยัน" };
  
    let preInput = "";
  
    if (!isStartChat && messages.length === 1) {
      if (user) {
        const {
          name = "Unknown",
          id = "N/A",
          status,
          user_type,
          internal,
          created_at,
          updated_at,
          last_access,
          last_learning,
        } = user;
  
        const formattedCreatedAt = new Date(created_at || updated_at).toLocaleString();
        const formattedLastAccess = last_access ? new Date(last_access).toLocaleString() : "N/A";
        const formattedLastLearning = last_learning || "N/A";
        const userStatus = getValue(userStatuses, status, "ยังไม่ได้ยืนยัน");
        const memberType = getValue(userTypes, user_type, "ไม่ทราบประเภทผู้ใช้");
        const userGroup = getValue(userInternals, internal, "มหาวิทยาลัยอื่นๆ");
  
        const allCourses = coursesLatest?.courses?.map(
          (course) =>
            `[Course ID: ${course.course_id}, Course Name: ${course.course_name}, Course Created At: ${new Date(
              course.created_at
            ).toLocaleString()}]`
        ) || "No courses available";
  
        preInput = `<<User Name: ${name}, User ID: ${id}, User Status: ${userStatus}, Member Type: ${memberType}, User Group: ${userGroup}, Registration Date: ${formattedCreatedAt}, Date Now: ${new Date().toLocaleString()}, Latest Access: ${formattedLastAccess}, Latest Learning Course: ${formattedLastLearning}, Latest Release Course: ${allCourses}>> `;
      } else {
        preInput = `<<User Not Logged In, Date Now: ${new Date().toLocaleString()}>> `;
      }
    }
  
    this.sendMessage(`${userInput} ${preInput}`);
    this.setState((prevState) => ({
      messages: [...prevState.messages, { role: "user", text: userInput }],
      userInput: "",
      isStartChat: true,
      inputDisabled: true,
    }));
  };  

  handleReadableStream = (stream) => {
    stream.on("textCreated", this.handleTextCreated);
    stream.on("textDelta", this.handleTextDelta);
    stream.on("event", (event) => {
      if (event.event === "thread.run.completed") this.handleRunCompleted();
    });
    stream.on("error", (error) => {
      console.error("Stream error:", error);
      this.setState({
        inputDisabled: false,
        messages: [...this.state.messages, {
          role: "assistant",
          text: "ขออภัยครับ เกิดข้อผิดพลาดในการเชื่อมต่อ กรุณาลองใหม่อีกครั้งครับ 🙏"
        }]
      });
    });
    stream.on("end", () => {
      this.setState({ inputDisabled: false });
    });
  };

  handleTextCreated = () => {
    if (this.state.isStartChat) {
      this.appendMessage("assistant", "");
    }
  };

  handleTextDelta = (delta) => {
    if (delta.value) this.appendToLastMessage(delta.value);
  };

  handleRunCompleted = () => {
    this.setState({ inputDisabled: false });
  };

  handleTextAreaResize = (e) => {
    const textarea = e.target;
    textarea.style.height = "auto"; // Reset height to recalculate
    textarea.style.height = textarea.scrollHeight + "px";
  };

  appendToLastMessage = (text) => {
    this.setState((prevState) => {
      if (!prevState.isStartChat) {
        return { isStartChatText: prevState.isStartChatText + text };
      }
      const messages = [...prevState.messages];
      messages[messages.length - 1].text += text;
      return { messages };
    });
  };
  appendMessage = (role, text) => {
    this.setState((prevState) => ({
      messages: [...prevState.messages, { role, text }],
    }));
  };

  //dev mode
  handleResetThreadClick = () => {
    this.setState(prevState => {
      const newCount = prevState.devModeCount + 1;

      if (prevState.devModeCount === 5) {
        this.setState(() => ({
          messages: [],
          isStartChat: false
        }));
        this.createThread();
        return { devModeCount: 0 };
      }

      return {
        devModeCount: newCount >= 6 ? 0 : newCount
      };
    });
  };



  ///////// CLOSE CHAT WITH AI /////////


  render() {
    const {
      activeTab,
      isExpandedChat,
      userInput,
      messages,
      inputDisabled,
      tags,
      isCollapseChat,
      limitMessage
    } = this.state;

    return (
      <div className={`top-banner ${!this.props.user ? "" : "no-go-section"}`}>
        <div className={`hero_banner ${!this.props.user ? "go-section" : ""}`}>
          <Swiper
            className="Swiper_hero_banner"
            pagination={true}
            modules={[Pagination, Autoplay]}
            slidesPerView={1}
            loop={true}
            autoplay={{
              delay: 5000,
              disableOnInteraction: false,
            }}
          // onSlideChange={() => console.log("slide change")}
          // onSwiper={(swiper) => console.log(swiper)}
          >
            {this.props.data.map((val, key) => (
              <SwiperSlide key={key}>
                {val.type == 1 &&
                  val.link != null &&
                  val.link != "" &&
                  val.link != "null" ? (
                  <a href={val.link}>
                    <div className="d-none d-xl-block d-md-block blockImgHeroBannerSize">
                      {val &&
                        val.image_th_desktop &&
                        val.image_th_desktop != null &&
                        val.image_th_desktop != "" &&
                        val.image_th_desktop != "null" ? (
                        <Image
                          className="d-none d-xl-block d-md-block ItemsImgHeroBannerSize"
                          src={val.image_th_desktop}
                          alt=""
                          layout="fill"
                          objectFit="contain"
                        />
                      ) : null}
                    </div>
                    <div className="d-block d-md-none d-xl-none img-mb blockImgHeroBannerSize">
                      {val &&
                        val.image_th_mobile &&
                        val.image_th_mobile != null &&
                        val.image_th_mobile != "" &&
                        val.image_th_mobile != "null" ? (
                        <Image
                          className="d-block d-md-none d-xl-none img-mb ItemsImgHeroBannerSize"
                          src={val.image_th_mobile}
                          alt=""
                          layout="fill"
                          objectFit="contain"
                        />
                      ) : null}
                    </div>
                  </a>
                ) : (
                  <div>
                    <div className="d-none d-xl-block d-md-block blockImgHeroBannerSize">
                      {val &&
                        val.image_th_desktop &&
                        val.image_th_desktop != null &&
                        val.image_th_desktop != "" &&
                        val.image_th_desktop != "null" ? (
                        <Image
                          className="d-none d-xl-block d-md-block ItemsImgHeroBannerSize"
                          src={val.image_th_desktop}
                          alt=""
                          layout="fill"
                          objectFit="contain"
                        />
                      ) : null}
                    </div>
                    <div className="d-block d-md-none d-xl-none img-mb blockImgHeroBannerSize">
                      {val &&
                        val.image_th_mobile &&
                        val.image_th_mobile != null &&
                        val.image_th_mobile != "" &&
                        val.image_th_mobile != "null" ? (
                        <Image
                          className="d-block d-md-none d-xl-none img-mb ItemsImgHeroBannerSize"
                          src={val.image_th_mobile}
                          alt=""
                          layout="fill"
                          objectFit="contain"
                        />
                      ) : null}
                    </div>
                  </div>
                )}
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
        {
          <div
            className={`hero_option ${!this.props.user ? "go-section" : ""}`}
          >
            <div className="container custom-container">
              <div>
                <div className="tabs pl-3 sm-relative-top">
                  <button
                    className={`tab fm-title-ai font-weight-light ${this.state.activeTab === 1 ? "active" : ""
                      }`}
                    onClick={() => this.handleTabClick(1)}
                  >
                    Chat with AI
                  </button>
                  <button
                    className={`tab fm-title-ai font-weight-light ${this.state.activeTab === 2 ? "active" : ""
                      }`}
                    onClick={() => this.handleTabClick(2)}
                  >
                    Search Menu
                  </button>
                  <AvatarMoblie loading={inputDisabled} activeTab={activeTab} handleResetThreadClick={this.handleResetThreadClick} />
                </div>

                <div className="tab-content">



                  {activeTab === 1 && (

                    <div
                      className={`row inner-hero_fm p-md-3 p-lg-3 p-sm-1 m-0 `}
                    // style={{ height: isExpandedChat ? "100%" : "100%" }}
                    >

                      <div className="col my-0 p-0 p-md-2 align-middle">
                        <div className="col border-div-ai p-2 p-md-4 ">
                          {this.state.isStartChat && (
                            <div
                              className="p-2 d-flex d-md-none flex-row-reverse"
                              style={{ position: "absolute", right: 15, top: 10, zIndex: 10 }}
                            >
                              <IconButton
                                icon={
                                  isCollapseChat ?
                                    < BsArrowsAngleExpand
                                      style={{ fontSize: "18px", color: "#637c12" }}
                                    /> :
                                    <BsArrowsAngleContract
                                      style={{ fontSize: "18px", color: "#637c12" }}
                                    />
                                }
                                onClick={this.handleCollapseClick}
                              />
                            </div>
                          )
                          }
                          <div>
                            {/* <IconButton icon={<BsArrowsAngleExpand />} />                              */}
                            {/* {!this.state.isStartChat &&
                              this.state.isStartChatText && (
                                <div className="row">
                                  <div className="col-2 col-md-1 d-flex-all-start align-items-start ">
                                    <Avatar src="/assets/images/avatar/u-robot-avatar.png" />
                                  </div>
                                  <div className="col-10 col-md-10">
                                    <div className="fm-greeting-ai pt-2 font-weight-light mt-2 ">
                                      <ReactMarkdown>
                                        {this.state.isStartChatText}
                                      </ReactMarkdown>
                                    </div>
                                  </div>
                                </div>
                              )}
                            <div className="row pb-3">
                              <div className="col-0 col-md-1 col-sm-2"></div>
                              {!this.state.isStartChat &&
                                this.state.isStartChatText && (
                                  <div className="col d-flex mt-3 ">
                                    <a
                                      href={"การลงทุนสำหรับแพทย์"}
                                      target="_blank"
                                      className="mt-3 mr-3 ui button btn-suggess-ai p-2 px-3 m-2"
                                    >
                                      <span className="">
                                        การลงทุนสำหรับแพทย์
                                      </span>
                                    </a>

                                    <a
                                      href={"category/course"}
                                      target="_blank"
                                      className="mr-3 ui button btn-suggess-ai py-2 px-3 m-3"
                                    >
                                      <span>คอร์สแนะนำ</span>
                                    </a>
                                    <a
                                      href={"Trending"}
                                      target="_blank"
                                      className="mr-3 ui button btn-suggess-ai p-2 px-3 m-3"
                                    >
                                      <span>Trending ในกลุ่มหมอ</span>
                                    </a>
                                     <a
                                  href={"history"}
                                  target="_blank"
                                  className="mr-3 ui button btn-suggess-ai p-2 px-3 m-3"
                                >
                                  <span>เรียนต่อให้จบ</span>
                                </a> 
                                  </div>
                                )}
                            </div> */}
                            {messages && messages.length > 0 ? (
                              <div
                                className="row conversation_history"
                                style={{
                                  height: isCollapseChat ? "20vh" : isExpandedChat ? "80vh" : "100%"
                                }}
                              >
                                <div className="col">
                                  {messages.map((msg, index) => (
                                    <div
                                      key={index}
                                      className="message mb-1 px-1 py-2 mt-1"
                                      style={{ whiteSpace: "pre-wrap" }}
                                    >
                                      <div className="row align-items-start">
                                        <div className="col-2 col-md-1  d-flex-align-start pl-1  ">
                                          {msg.role == "assistant" ? (

                                            <Avatar src="/assets/images/avatar/u-robot-avatar.png" />

                                          ) : (
                                            <Avatar
                                              src={
                                                this.props.user?.avatar ||
                                                "/assets/images/user-key.png"
                                              }
                                            />
                                          )}
                                        </div>
                                        <div
                                          className={`col-10 col-md-10 fm-greeting-ai font-weight-light  chat-text-box d-block h-100 align-self-center`}
                                        >
                                          <ReactMarkdown
                                            remarkPlugins={[remarkBreaks]}
                                            components={{
                                              // ol: ({ children }) => <ol style={{ paddingLeft: "10px" , paddingTop : 0 , marginTop : 0}}>{children}</ol>
                                              p: ({ children }) => (
                                                <p style={{ lineHeight: 1.2 }}>
                                                  {children}
                                                </p>
                                              ),
                                            }}
                                          >
                                            {this.regexNewLine(msg.text)}                                    
                                          </ReactMarkdown>

                                          {msg.role === "assistant" &&
                                            msg.json &&
                                            msg.json.courses.length > 0 && (
                                              <div className="row d-flex-justify-center mt-2">
                                                {msg.json.courses.map(
                                                  (course, courseIndex) => (
                                                    <div
                                                      key={courseIndex}
                                                      className="col-12 col-sm-6 col-md-6 col-lg-4 mb-4"
                                                    >
                                                      <CourseCard
                                                        course={course}
                                                      />
                                                    </div>
                                                  )
                                                )}
                                              </div>
                                            )}
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ) : (
                              <div className="row conversation_history"></div>
                            )}

                            <div className="row d-flex align-items-center px-sx-2 py-2 ">
                              <div className="col-sm-2 col-md-1 ps-sm-2 d-none d-md-block">
                                <Avatar
                                  src={
                                    this.props.user?.avatar ||
                                    "/assets/images/user-key.png"
                                  }
                                />
                              </div>
                              <div className="col col-xs-10 p-xs-0 pl-4 pr-1">
                                <textarea
                                  className="form-control auto-resize "
                                  placeholder={!inputDisabled ? `Type Here ...` : `Let me think about that ...`}
                                  rows="1"
                                  value={userInput}
                                  onChange={(e) => {
                                    this.setState({
                                      userInput: e.target.value,
                                    });
                                    this.handleTextAreaResize(e);
                                  }}
                                  onKeyDown={this.handleKeyDown}
                                  style={{
                                    minHeight: "20px",
                                    maxHeight: "120px",
                                    resize: "none",
                                    overflowY: "hidden",
                                  }}
                                  disabled={limitMessage}
                                />
                              </div>
                              <div className="col-xs-1 d-flex-align-center d-none d-md-block px-2 ">
                                {inputDisabled ? (
                                  <LoadingAIChat />
                                ) : (
                                  <button
                                    className="ui button btn-send-chat-with-ai py-0 mx-2"
                                    onClick={this.handleSubmit}
                                    disabled={inputDisabled || limitMessage} // Disable button when sending
                                  >
                                    <div className="btn-send-text">Send</div>
                                  </button>
                                )}
                              </div>
                              <div className="col-xs-1 d-flex-align-center  d-xs-block d-md-none px-2 ">
                                {inputDisabled ? (
                                  <LoadingAIChat />
                                ) : (
                                  <IconButton
                                    icon={<SendIcon />}
                                    onClick={this.handleSubmitMobile}
                                  />
                                )}
                              </div>
                              {/* </div> */}
                            </div>
                          </div>
                        </div>
                        {tags.length > 0 && (
                          <div className="col mt-3 d-flex flex-wrap">
                            {tags.map((tag, tagIndex) => (
                              <p key={tagIndex}>
                                <ButtomTag tag={tag} />
                              </p>
                            ))}
                          </div>
                        )}
                      </div>

                      <div className="col-1.5 d-none d-lg-block">
                        <div className="row text-center">
                          <div className="robot-avatar-banner" onClick={this.handleResetThreadClick}>
                            {inputDisabled ? (
                              <FloatingImage
                                src="/assets/images/u-doctor-hero-banner-2.gif"
                                alt="Robot Doc"
                              />
                            ) : (
                              <FloatingImage
                                src="/assets/images/u-doctor-hero-banner-1.gif"
                                alt="Robot Doc"
                              />
                            )}

                          </div>
                        </div>
                        <div className="row d-none d-md-flex align-center">
                          <h3
                            className="text-center"
                            style={{
                              fontSize: "16px",
                              fontFamily: "Comfortaa",
                              fontWeight: 600,
                              color: "#637c12",
                            }}
                          >
                            Aim D
                          </h3>
                        </div>
                      </div>
                    </div>
                  )}
                  {activeTab === 2 && (
                    <div
                      className={`inner-hero_fm ${!this.props.user ? "with-go-section" : ""
                        }`}
                    >
                      {!this.props.user ? (
                        <div className="go-section">
                          <div className="top-section">
                            <h3>คอร์สสำหรับ</h3>
                            <p
                              className={`go-section-choice ${this.state.filter == 1 ? "active" : ""
                                }`}
                              onClick={() => this.setFilter(1)}
                            >
                              ประชาชน
                            </p>
                            <p
                              className={`go-section-choice ${this.state.filter == 2 ? "active" : ""
                                }`}
                              onClick={() => this.setFilter(2)}
                            >
                              แพทย์ นิสิต นักศึกษา<br></br>และบุคลากรทางการแพทย์
                            </p>
                          </div>
                          <div className="bottom-section">
                            <div className="item-hero_fm action">
                              {this.state.filter != "" ? (
                                <button
                                  onClick={() => this.filter()}
                                  className="btn-hero_fm"
                                >
                                  {this.state.filter == 1 ? (
                                    <span>Free !</span>
                                  ) : (
                                    <span>Go !</span>
                                  )}
                                </button>
                              ) : null}
                            </div>
                          </div>
                        </div>
                      ) : null}
                      <div className="row">
                        {/* Row 1 */}
                        <div className="col-12 d-flex mb-3">
                          {/* Dropdown 1 */}
                          <div
                            className="item-hero_fm dropdown me-3"
                            style={{ flex: 1 }}
                          >
                            <div className="item-fm home-filter">
                              <Select
                                className="fm-control"
                                placeholder="หมวดหมู่"
                                options={this.props.widget.group}
                                onChange={(event, data) => {
                                  this.setGroup(data);
                                }}
                              />
                            </div>
                          </div>
                          {/* Dropdown 2 */}
                          <div
                            className="item-hero_fm dropdown"
                            style={{ flex: 1 }}
                          >
                            <div className="item-fm home-filter">
                              <Select
                                className="fm-control"
                                placeholder="กลุ่มโรค"
                                options={this.props.widget.disease}
                                onChange={(event, data) => {
                                  this.setDisease(data);
                                }}
                              />
                            </div>
                          </div>
                        </div>

                        {/* Row 2 */}
                        <div className="col-12 d-flex">
                          {/* Input Text */}
                          <div
                            className="item-hero_fm col me-3"
                            style={{ flex: 10 }}
                          >
                            <div className="item-fm home-filter">
                              <Input
                                id="keyword_box"
                                type="text"
                                className="fm-control"
                                placeholder="ค้นหา.... ชื่อเรื่อง ชื่อวิทยากร คำสำคัญ"
                              />
                            </div>
                          </div>
                          {/* Button */}
                          <div
                            className="item-hero_fm action"
                            style={{ flex: 1 }}
                          >
                            <button
                              onClick={() => this.submit()}
                              className="btn-hero_fm home-filter"
                            >
                              <span>ค้นหา</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        }
      </div>
    );
  }
}

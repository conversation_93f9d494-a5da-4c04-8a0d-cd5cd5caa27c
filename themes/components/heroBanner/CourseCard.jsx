/** @jsxImportSource @emotion/react */
import Image from 'next/image';
import styled from '@emotion/styled';
import ReactStars from "react-rating-stars-component";

const ImageContainer = styled.div`
  position: relative;
  width: 100%;
  padding-top:  75.00%; 
  overflow: hidden;
`;

const StyledImage = styled(Image)`
  border-radius: 18px;
  padding: 18px;
  background: #fafafa;
`;

const TitleCourse = styled.div`
  font-size: 18px;
  margin: 0 0 16px 0;
  color: #303030;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 38px;
 
`


const MetaContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: start;
  gap : 10px;
  margin: 8px 0;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
`;

const Rating = styled.div`
  display: flex;
  align-items: center;
  gap: 2px;
  
  .text-rating {
    font-size: 12px;
    color: #666;
  }
`;

const Icons = styled.div`
  display: flex;
  gap: 2px;
  color: #666;
  
  i {
    font-size: 16px;
  }
   span { 
  font-family: "Kanit";

font-size: 12px;
  margin-left: 3px;
  
  }
`;

const Description = styled.div`
  padding: 8px 0;
  font-size: 14px;
  color: #666;
  
  .speaker-label {
    font-weight: 500;
    color: #333;
  }
`;



const CourseCard = ({ course }) => {
  const ratingThisStar = {
    size: 12,
    count: 5,
    color: "#FFC130",
    activeColor: "#FFC130",
    value: course.rate,
    a11y: true,
    isHalf: true,
    edit: false,
    emptyIcon: <i className="icon-ic-star-light" />,
    halfIcon: <i className="icon-ic-star-half-light" />,
    filledIcon: <i className="icon-ic-star" />,
    onChange: newValue => {
      // console.log(newValue);
    }
  };
  return (
    <a
      href={course.link}
      target="_blank"
      rel="noopener noreferrer"
      className="card-link"
    >
      <div className="card h-100">
        <ImageContainer>
        <StyledImage
            src={course.image_th}
            alt={course.title_th}
            layout="fill"
            objectFit="cover"
            quality={85}
            loading="lazy"
            priority={false}
          />
        </ImageContainer>


        <TitleCourse>{course.title_th}</TitleCourse>
        <MetaContainer>
          <Rating>
            <ReactStars {...ratingThisStar} />
            <div className="text-rating">{course.rating}</div>
          </Rating>
          <Icons>
            <i className="icon-ic-lessons" title="Lessons">
              <span>{course.lesson}</span>
            </i>
          </Icons>
          <Icons>
            <i className="icon-ic-time" title="Duration">
              <span>{course.duration}</span>
            </i>
          </Icons>
        </MetaContainer>

        {course.speaker && (
          <Description>
            <span className="speaker-label">วิทยากร: </span>
            {course.speaker}
          </Description>)}

      </div>
    </a>
  );
}

export default CourseCard;


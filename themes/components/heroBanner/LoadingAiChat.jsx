/** @jsxImportSource @emotion/react */
import React from 'react';
import styled from '@emotion/styled';

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  padding: 5px;
`;

const Dot = styled.div`
  width: 7px;
  height: 7px;
  background-color: #4CAF50; // Green color
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out;
  animation-delay: ${props => props.delay}s;

  @keyframes bounce {
    0%, 80%, 100% { 
      transform: scale(0);
      opacity: 0.3;
    }
    40% { 
      transform: scale(1);
      opacity: 1;
    }
  }
`;

const LoadingDots = () => {
  return (
    <LoadingContainer>
      <Dot delay={0} />
      <Dot delay={0.2} />
      <Dot delay={0.4} />
    </LoadingContainer>
  );
};

// Alternative SVG version
const SVGLoadingDots = () => {
  return (
    <svg width="70" height="30" viewBox="0 0 120 30">
      <circle 
        cx="20" 
        cy="15" 
        r="8" 
        fill="#4CAF50">
        <animate
          attributeName="opacity"
          from="1"
          to="0.3"
          dur="1s"
          repeatCount="indefinite"
          begin="0s"
        />
        <animate
          attributeName="r"
          from="8"
          to="4"
          dur="1s"
          repeatCount="indefinite"
          begin="0s"
        />
      </circle>
      <circle 
        cx="60" 
        cy="15" 
        r="8" 
        fill="#4CAF50">
        <animate
          attributeName="opacity"
          from="1"
          to="0.3"
          dur="1s"
          repeatCount="indefinite"
          begin="0.2s"
        />
        <animate
          attributeName="r"
          from="8"
          to="4"
          dur="1s"
          repeatCount="indefinite"
          begin="0.2s"
        />
      </circle>
      <circle 
        cx="100" 
        cy="15" 
        r="8" 
        fill="#4CAF50">
        <animate
          attributeName="opacity"
          from="1"
          to="0.3"
          dur="1s"
          repeatCount="indefinite"
          begin="0.4s"
        />
        <animate
          attributeName="r"
          from="8"
          to="4"
          dur="1s"
          repeatCount="indefinite"
          begin="0.4s"
        />
      </circle>
    </svg>
  );
};

// You can export both versions
export { LoadingDots, SVGLoadingDots };

// Usage example with a wrapper component
const LoadingWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 30px;

`;


export default LoadingDots;
/** @jsxImportSource @emotion/react */
import { css, keyframes } from "@emotion/react";
import styled from "@emotion/styled";
import Image from "next/image";

// Define floating animation
const floatUpDown = keyframes`
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); } /* Move up */
  100% { transform: translateY(0px); }
`;

// Wrapper for Image with animation
const FloatingWrapper = styled.div`
  position: relative;
  width: 210px;  
  height: 210px;
  // animation: ${floatUpDown} 2s ease-in-out infinite;

  @media (max-width: 768px) {
    display : none;
  }
`;


const FloatingImage = ({ src, alt = "Floating Image" }, handleClick) => {
  return (
    <FloatingWrapper >
      <Image
        alt={alt}
        src={src}
        layout="fill"
        objectFit="contain"
        onClick={() => handleClick}
      />
    </FloatingWrapper>
  );
};

export default FloatingImage;
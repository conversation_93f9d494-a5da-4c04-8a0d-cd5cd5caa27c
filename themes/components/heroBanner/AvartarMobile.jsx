import React from 'react'
import Image from 'next/image'



function AvartarMobile({ loading, activeTab, handleResetThreadClick }) {
    return (
        activeTab === 1 && (
            <div style={{ position: "absolute", right: 0, top: -70, zIndex: 10 }} className="d-flex d-md-none" onClick={handleResetThreadClick}>
                {loading ? (
                    <Image
                        src="/assets/images/u-doctor-hero-banner-2.gif"
                        alt="Robot Doc"
                        width={120}
                        height={120}
                    />
                ) : (
                    <Image
                        src="/assets/images/u-doctor-hero-banner-1.gif"
                        alt="Robot Doc"
                        width={120}
                        height={120}
                    />
                )}
            </div>
        )
    )
}

export default AvartarMobile
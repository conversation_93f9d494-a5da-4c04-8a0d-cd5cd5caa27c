import React, { Component } from "react";
import Image from "next/image";
import {
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";
import { render } from "react-dom";

import Swal from 'sweetalert2'
const sortOptions = [
  { value: "cme_desc", text: "by CME & Points (High to Low)" },
  { value: "cme_asc", text: "by CME & Points (Low to High)" },
  { value: "date", text: "by Upload date" },
  { value: "price", text: "by Price" },
  { value: "popular", text: "Popular" },
  { value: "a_to_z", text: "by A to Z" },
];

function btn_show() {
  document.getElementById("block_filter").classList.add('on_show');
  document.body.classList.add("open_nav");
}

function btn_search() {
  document.getElementById("search_fm").classList.toggle('on_show');
}

export default class index extends Component {
  constructor(props) {
    super(props);
    if(this.props.lang){
      this.state = {
        comment: '',
        lang:this.props.lang
      };
    }else{
      this.state = {
        comment: '',
        lang:'th'
      };
    }
  }
  translateEng(_value) {
    if(this.state.lang=='en'){
      if(_value=='ค้นหา.... ชื่อเรื่อง ชื่อวิทยากร คำสำคัญ'){
        return "Search for titles, speaker's names, keywords.";
      }else if(_value=='ค้นหา.... ชื่อเรื่อง ชื่อผู้เขียน คำสำคัญ'){
        return "Search for titles, writer's names, keywords.";
      }else if(_value=='ค้นหา'){
        return "Search";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  submit(){
    
    // if(this.state.comment!=''){
      // if(this.state.comment!=null&&this.state.comment!=''&&this.state.comment!='null'){
        this.props.keywordSort(this.state.comment)
      // }else{
      //   this.props.keywordSort(this.props.default)
      // }
    // }else{
    //   Swal.fire({
    //     text: 'กรุณากรอกคำที่ต้องการค้นหา',
    //     icon: 'error',
    //     confirmButtonText: 'ปิด',
    //     confirmButtonColor: "#648d2f"
    //   })
    // }
  }
  render() {
    return (
      <div className="block-course-search">
        <div className="inner">
          <div className="course-filter" onClick={() => btn_show()}>
            <div className="blockImgSearchSize">
            <Image src="/assets/images/icon-filter.png" alt="" 
              layout="fill"
              objectFit="contain"
              className="ItemsImgSearchSize"
            />
            </div>
          </div>
          <div className="course-sort">
            <div className="item-fm">
              <Select
                onChange={(event, data) =>
                  this.props.selectSort(data.value)
                }
                className="fm-control green"
                placeholder="Sort"
                options={sortOptions}
              />
            </div>
          </div>
          <div className="course-search">
            <div className="item-fm category-search" id="search_fm">
              {this.props.type=='course'?(
                <Input
                  id="search_box"
                  className="fm-control search gray"
                  icon={<Icon name='search' inverted circular link />}
                  iconPosition='left'
                  defaultValue={this.props.default}
                  onChange={(event) => this.state.comment = event.target.value}
                  placeholder={this.translateEng('ค้นหา.... ชื่อเรื่อง ชื่อวิทยากร คำสำคัญ')}
                  onKeyDown={(e)=>{
                    if (e.key === 'Enter') {
                      this.submit()
                    }
                  }}
                />
              ):(
                <Input
                  id="search_box"
                  className="fm-control search gray"
                  icon={<Icon name='search' inverted circular link />}
                  iconPosition='left'
                  defaultValue={this.props.default}
                  onChange={(event) => this.state.comment = event.target.value}
                  placeholder={this.translateEng('ค้นหา.... ชื่อเรื่อง ชื่อผู้เขียน คำสำคัญ')}
                  onKeyDown={(e)=>{
                    if (e.key === 'Enter') {
                      this.submit()
                    }
                  }}
                />
              )}
              <div className="category-search-btn">
                <button onClick={() => this.submit()} className="btn-default">
                  <span>{this.translateEng('ค้นหา')}</span>
                </button>
              </div>
            </div>
            <div className="item-seacrh-mobile" onClick={() => btn_search()}>
              <i aria-hidden="true" className="search icon"></i>
            </div>
          </div>
        </div>
      </div>
      
    );
  }
}

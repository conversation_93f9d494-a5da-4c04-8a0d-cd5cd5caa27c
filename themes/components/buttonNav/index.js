import React from 'react';
import Link from "next/link";
import Image from "next/image";
import FormPopLogin from "../formPopLogin";


import {
  Menu,
  Button,
  Modal,
  Input,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";

const Index = () => {

  const [open, setOpen] = useState(false)
  return (
    <div>
    <Button className="btn-to-login" onClick={() => setOpen(true)}>
      <Image
        className="avatar"
        src="/assets/images/user-key.png"
        alt=""
        layout='fill'
      />
      <span className="name">Login / Register</span>
    </Button>
    <Modal
      className='modalLogin'
      onClose={() => setOpen(false)}
      onOpen={() => setOpen(true)}
      open={open}>
      <Modal.Content className="modalLoginContent">
        <div className="bock-modal-login">
            <div className="inner">
              <div className="modal-login-bg">
                <Image className="bg" src="/assets/images/bg-login.jpg"
                layout='fill'
                alt=""
                />
                <div className="group-login-tabs">
                  <button className='login-tab active'>
                    <span>เข้าระบบ</span>
                  </button>
                  <button className='login-tab'>
                    <span>สมัครสมาชิก</span>
                  </button>
                </div>
              </div>
              <div className="modal-login-fm">
              <FormPopLogin></FormPopLogin>
              </div>
            </div>
        </div>
      </Modal.Content>
    </Modal>
</div>
  );
}

export default Index;

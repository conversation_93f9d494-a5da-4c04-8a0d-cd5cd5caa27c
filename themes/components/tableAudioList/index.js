import React, { Component } from "react";
import Link from "next/link";

import styles from "/public/assets/css/component/tableAudioList.module.css";


export default class index extends Component {
  render() {
    return (
      <div className={`${styles.block_table_audio}`}>
        <div className={`${styles.inner_table_audio}`}>
          <div className={`${styles.scroll}`}>
          <table>
            <thead>
              <tr>
                <th scope="col" >ชื่อ</th>
                <th scope="col">เวลา</th>
                <th scope="col">ราคา</th>
              </tr>
            </thead>
            <tbody>
              <tr className={`${styles.audio_active}`}>
                <td className={`${styles.status}`}>
                  <span>1. Lorem Ipsum is simply</span>
                  <i className="icon-ic-audio-active"></i>
                </td>
                <td className={`${styles.time}`}>
                  3:31
                </td>
                <td className={`${styles.action}`}>
                  <i className="icon-ic-circle-play"></i>
                </td>
              </tr>
              {/* ===== */}
              <tr>
                <td className={`${styles.status}`}>
                  <span>2. Lorem Ipsum is simply</span>
                  <i className="icon-ic-audio-active"></i>
                </td>
                <td className={`${styles.time}`}>
                  3:31
                </td>
                <td className={`${styles.action}`}>
                  <span>1,300 บาท</span>
                </td>
              </tr>
              {/* ===== */}
              <tr>
                <td className={`${styles.status}`}>
                  <span>3. Lorem Ipsum is simply</span>
                  <i className="icon-ic-audio-active"></i>
                </td>
                <td className={`${styles.time}`}>
                  3:31
                </td>
                <td className={`${styles.action}`}>
                <span>1,300 บาท</span>
                </td>
              </tr>
              {/* ===== */}
            </tbody>
          </table>
          </div>
        </div>
      </div>
    );
  }
}

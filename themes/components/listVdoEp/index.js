import React, { Component, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import styles from "/public/assets/css/component/listVdoEp.module.css";

import { IoMdDownload } from "react-icons/io";
import {
  Menu,
  Button,
  Modal,
  Input,
  Icon,
  Dropdown,
  Label,
  List,
  Progress
} from "semantic-ui-react";
import "semantic-ui-css/semantic.min.css";
import Swal from "sweetalert2";
function convertSecond(second) {
  if (second != null && second != "") {
    var hours = parseInt(
      new Date(second * 1000).toISOString().substring(11, 13)
    );
    var minutes = parseInt(
      new Date(second * 1000).toISOString().substring(14, 16)
    );
    var seconds = parseInt(
      new Date(second * 1000).toISOString().substring(17, 19)
    );
    if(hours==0){
      return minutes+'m';
    }else{
      return hours + "h:" + minutes+'m';
    }
    
  } else {
    return '0m';
  }
}
const truncate = (input,limit) => {
  if (input && input.length > limit)
      return input.substring(0, limit) + ' ...';
  else
      return input;
};

export default class index extends Component {
  constructor(props) {
    super(props);
    if(this.props.lang){
      this.state = {
        keySelect: this.props.keySelect,
        vdoList: this.props.vdoList,
        vdoSection: this.props.vdoSection,
        keyQuiz: this.props.keyQuiz,
        keyPreTest: this.props.keyPreTest,
        lang:this.props.lang
      };
    }else{
      this.state = {
        keySelect: this.props.keySelect,
        vdoList: this.props.vdoList,
        vdoSection: this.props.vdoSection,
        keyQuiz: this.props.keyQuiz,
        keyPreTest: this.props.keyPreTest,
        lang:'th'
      };
    }
  }
  translateEng(_value) {
    if(this.state.lang=='en'){
      if(_value=='แบบทดสอบหลังเรียน'){
        return "Examination after class";
      }else if(_value=='แบบทดสอบก่อนเรียน'){
        return "Examination before class";
      }else if(_value=='แบบประเมินความพึงพอใจ'){
        return "Assessment";
      }else if(_value=='กรุณาดูวิดีโอบทเรียนให้จบก่อนค่ะ'){
        return "Please watch the video lesson to the end";
      }else if(_value=='แบบทดสอบของคุณอยู่ระหว่างตรวจสอบค่ะ'){
        return "Your test is under review";
      }else if(_value=='คุณทำแบบทดสอบได้'){
        return "You can take the test for";
      }else if(_value=='คะแนน'){
        return "Points";
      }else if(_value=='ปิด'){
        return "Close";
      }else if(_value=='ไม่'){
        return "No";
      }else if(_value=='ใช่'){
        return "Yes";
      }else if(_value=='ต้องการเริ่มใหม่ใช่หรือไม่?'){
        return "Want to restart?";
      }else if(_value=='กรุณาเข้าสู่ระบบค่ะ'){
        return "Please login";
      }else if(_value=='ตกลง'){
        return "OK";
      }else if(_value=='กรุณาซื้อคอร์สนี้ก่อนค่ะ'){
        return "Please buy this course";
      }else if(_value=='คุณต้องการเริ่มเรียนใช่หรือไม่?'){
        return "Do you want to start studying?";
      }else if(_value=='กรุณาเรียนบทเรียนก่อนหน้าให้จบก่อนค่ะ'){
        return "Please finish the previous lesson";
      }else if(_value=='คุณเคยทำแบบทดสอบไปแล้ว'){
        return "You have already taken the test";
      }else if(_value=='คุณทำแบบทดสอบครบแล้ว'){
        return "You have limited the test";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }

  selectVdo(_keySelect) {
    // console.log('selectVdo : '+_keySelect);
    this.setState({
      keySelect: _keySelect,
    });
    this.props.callback(_keySelect)
  }

  checkFree() {
    if(!this.props.vdoListAll.course_free){
      return false;
    }
    return true;
  }

  selectLockDoc() {
    if (this.props.user) {
      if(this.props.isSoon){
        Swal.fire({
          text: "Coming soon",
          icon: "info",
          confirmButtonText: this.translateEng('ปิด'),
          confirmButtonColor: "#648d2f"
        });
      }else{
        if (this.props.checkFree) {
          Swal.fire({
            text: this.translateEng('คุณต้องการเริ่มเรียนใช่หรือไม่?'),
            icon: 'info',
            showCancelButton: true,
            confirmButtonColor: '#648d2f',
            cancelButtonColor: '#d33',
            confirmButtonText: this.translateEng('ใช่'),
            cancelButtonText: this.translateEng('ไม่')
          }).then((result) => {
            if (result.isConfirmed) {
              this.props.cbStart(
                "free",
                this.props.vdoListAll.course_id,
                ""
              );
            }
          })
        }else{
          Swal.fire({
            text: this.translateEng('กรุณาซื้อคอร์สนี้ก่อนค่ะ'),
            icon: "info",
            confirmButtonText: this.translateEng('ปิด'),
            confirmButtonColor: "#648d2f"
          });
        }
      }
    }else{
      Swal.fire({
        text: this.translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: this.translateEng('ตกลง'),
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        this.props.setOpen(true);
      });
    }
  }

  checkBuy() {
    if(!this.props.vdoListAll.course_allow){
      return false;
    }
    return true;
  }

  selectLockVdo(_keySelect) {
    if (this.props.user) {
      if(this.checkBuy()){
        Swal.fire({
          text: this.translateEng('กรุณาเรียนบทเรียนก่อนหน้าให้จบก่อนค่ะ'),
          icon: "info",
          confirmButtonText: this.translateEng('ปิด'),
          confirmButtonColor: "#648d2f"
        });
      }else{
        if(this.props.isSoon){
          Swal.fire({
            text: "Coming soon",
            icon: "info",
            confirmButtonText: this.translateEng('ปิด'),
            confirmButtonColor: "#648d2f"
          });
        }else{
          if (this.checkFree()) {
            Swal.fire({
              text: this.translateEng('คุณต้องการเริ่มเรียนใช่หรือไม่?'),
              icon: 'info',
              showCancelButton: true,
              confirmButtonColor: '#648d2f',
              cancelButtonColor: '#d33',
              confirmButtonText: this.translateEng('ใช่'),
              cancelButtonText: this.translateEng('ไม่')
            }).then((result) => {
              if (result.isConfirmed) {
                this.props.cbStart(
                  "free",
                  this.props.vdoListAll.course_id,
                  ""
                );
              }
            })
          }else{
            Swal.fire({
              text: this.translateEng('กรุณาซื้อคอร์สนี้ก่อนค่ะ'),
              icon: "info",
              confirmButtonText: this.translateEng('ปิด'),
              confirmButtonColor: "#648d2f"
            });
          }
        }
      }
    }else{
      Swal.fire({
        text: this.translateEng('กรุณาเข้าสู่ระบบค่ะ'),
        icon: "info",
        confirmButtonText: this.translateEng('ตกลง'),
        confirmButtonColor: "#648d2f"
      }).then((result) => {
        this.props.setOpen(true);
      });
    }
  }

  selectQuiz(_keySelect) {
    this.setState({
      keyQuiz: _keySelect,
    });
    this.props.cbQuiz(_keySelect)
  }
  selectQuiz2(_keySelect,_last_point) {
    Swal.fire({
      title: this.translateEng('คุณทำแบบทดสอบได้')+" "+_last_point+" "+this.translateEng('คะแนน'),
      text: this.translateEng('ต้องการเริ่มใหม่ใช่หรือไม่?'),
      icon: 'info',
      showCancelButton: true,
      confirmButtonColor: '#648d2f',
      cancelButtonColor: '#d33',
      confirmButtonText: this.translateEng('ใช่'),
      cancelButtonText: this.translateEng('ไม่')
    }).then((result) => {
      if (result.isConfirmed) {
        this.selectQuiz(_keySelect)
      }
    })
  }
  selectQuiz3(_keySelect,_last_point) {
    Swal.fire({
      text: this.translateEng('คุณทำแบบทดสอบได้')+" "+_last_point+" "+this.translateEng('คะแนน'),
      icon: "info",
      confirmButtonText: this.translateEng('ปิด'),
      confirmButtonColor: "#648d2f"
    });
  }
  selectQuiz4(_keySelect) {
    Swal.fire({
      text: this.translateEng('แบบทดสอบของคุณอยู่ระหว่างตรวจสอบค่ะ'),
      icon: "info",
      confirmButtonText: this.translateEng('ปิด'),
      confirmButtonColor: "#648d2f"
    });
  }
  selectQuiz5(_keySelect) {
    Swal.fire({
      text: this.translateEng('กรุณาดูวิดีโอบทเรียนให้จบก่อนค่ะ'),
      icon: "error",
      confirmButtonText: this.translateEng('ปิด'),
      confirmButtonColor: "#648d2f"
    });
  }
  selectPreTest(_keySelect) {
    this.setState({
      keyPreTest: _keySelect,
    });
    this.props.cbPreTest(_keySelect)
  }
  selectPreTest2(_keySelect,_last_point) {
    Swal.fire({
      title: this.translateEng('คุณเคยทำแบบทดสอบไปแล้ว'),
      text: this.translateEng('ต้องการเริ่มใหม่ใช่หรือไม่?'),
      icon: 'info',
      showCancelButton: true,
      confirmButtonColor: '#648d2f',
      cancelButtonColor: '#d33',
      confirmButtonText: this.translateEng('ใช่'),
      cancelButtonText: this.translateEng('ไม่')
    }).then((result) => {
      if (result.isConfirmed) {
        this.selectPreTest(_keySelect)
      }
    })
  }
  selectPreTest3(_keySelect,_last_point) {
    Swal.fire({
      text: this.translateEng('คุณทำแบบทดสอบครบแล้ว'),
      icon: "info",
      confirmButtonText: this.translateEng('ปิด'),
      confirmButtonColor: "#648d2f"
    });
  }
  selectPreTest4(_keySelect) {
    Swal.fire({
      text: this.translateEng('แบบทดสอบของคุณอยู่ระหว่างตรวจสอบค่ะ'),
      icon: "info",
      confirmButtonText: this.translateEng('ปิด'),
      confirmButtonColor: "#648d2f"
    });
  }

  render() {
    return (
      <div className={`container-fluid  space-between-content course-vid`}>
        {!this.state.vdoSection || this.state.vdoSection.length==0?(
          this.state.vdoList && this.state.vdoList.length>0?(
            this.state.vdoList.map((val, key) =>
              val.lock ? (
                <div
                  className={`row ${styles.list} 
                ${this.state.keySelect == key ? styles.active : ""}`}
                  key={key}
                  onClick={() => this.selectLockVdo(key)}
                >
                  <div className="col-12 col-md-4 col-lg-2">
                    <div className="card card-lock radius">
                      <div className="inner">
                          <div className="card-img">
                            <div className={styles.videoThumbDiv}>
                              {val && val.thumb && val.thumb!=null && val.thumb!='' && val.thumb!='null' ?(
                                <Image layout="fill" className="img-thumb" src={val.thumb} alt="" />
                              ):null}
                            </div>
                            <div className="card-icon lock">
                              <i className={`icon-ic-lock`}></i>
                            </div>
                          </div>
                      </div>
                    </div>

                  </div>
                  <div className="col-12 col-md-6 col-lg-8">
                    <p className={`${styles.title}`}>
                      {val.title}
                      <p className={`${styles.description}`}>
                        {truncate(val.lesson_key,80)}
                      </p>
                    </p>
                  </div>
                  <div className={`col-12 col-md-2 col-lg-2 ${styles.text_right}`}>
                    {val.ep_oculus == 1 ?(
                      <p className={`this_time ${styles.calendar}`}><i className="icon-ic-time_2 d-block d-md-none"></i> {convertSecond(val.duration)}</p>
                    ):(
                      val.played_percent>0?(
                        <p className={`this_time ${styles.calendar}`}>{val.played_percent}%</p>
                      ):(
                        <p className={`this_time ${styles.calendar}`}>0%</p>
                      )
                    )}
                  </div>
                  {(val.quiz.question && val.quiz.question.length>0)||(val.pre_test.question && val.pre_test.question.length>0) ? (
                    <div className="card-quiz">
                      <>
                        {val.pre_test.question && val.pre_test.question.length>0 ? (
                          val.pre_test.allowed && val.pre_test.first_time && !val.pre_test.check ? (
                            <div className="item-quiz pre-test-button">
                              <div className={styles.iconTestDiv}>
                                <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                              </div>
                              {this.translateEng('แบบทดสอบก่อนเรียน')}
                            </div>
                          ):(
                            val.pre_test.allowed && !val.pre_test.first_time && !val.pre_test.check ? (
                              <div className="item-quiz pre-test-button">
                                <div className={styles.iconTestDiv}>
                                  <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                </div>
                                {this.translateEng('แบบทดสอบก่อนเรียน')}
                              </div>
                            ):(
                              !val.pre_test.allowed && !val.pre_test.first_time && !val.pre_test.check ? (
                                <div className="item-quiz pre-test-button">
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                  </div>
                                  {this.translateEng('แบบทดสอบก่อนเรียน')}
                                </div>
                              ):(
                                !val.pre_test.allowed && !val.pre_test.first_time && val.pre_test.check ? (
                                  <div className="item-quiz pre-test-button">
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบก่อนเรียน')}
                                  </div>
                                ):(
                                  <div className="item-quiz pre-test-button">
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบก่อนเรียน')}
                                  </div>
                                )
                              )
                            )
                          )
                        ):(null)}
                        {val.quiz.question && val.quiz.question.length>0 ? (
                          val.quiz.allowed && val.quiz.first_time && !val.quiz.check ? (
                            <div className="item-quiz">
                            <div className={styles.iconTestDiv}>
                              <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                            </div>
                              {this.translateEng('แบบทดสอบหลังเรียน')}
                            </div>
                          ):(
                            val.quiz.allowed && !val.quiz.first_time && !val.quiz.check ? (
                              <div className="item-quiz">
                                <div className={styles.iconTestDiv}>
                                  <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                </div>
                                {this.translateEng('แบบทดสอบหลังเรียน')}
                              </div>
                            ):(
                              !val.quiz.allowed && !val.quiz.first_time && !val.quiz.check ? (
                                <div className="item-quiz">
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                  </div>
                                  {this.translateEng('แบบทดสอบหลังเรียน')}
                                </div>
                              ):(
                                !val.quiz.allowed && !val.quiz.first_time && val.quiz.check ? (
                                  <div className="item-quiz">
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบหลังเรียน')}
                                  </div>
                                ):(
                                  <div className="item-quiz">
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบหลังเรียน')}
                                  </div>
                                )
                              )
                            )
                          )
                        ):(null)}
                      </>
                    </div>
                  ):null}
                </div>
              ) : (
                <div
                  className={`row ${styles.list} 
                ${this.state.keySelect == key ? styles.active : ""}`}
                  key={key}
                >
                  <div className="col-12 col-md-4 col-lg-2" onClick={() => this.selectVdo(key)}>
                    <div className="card radius">
                      <div className="inner">
                          <div className="card-img">
                              <div className={styles.videoThumbDiv}>
                                {val && val.thumb && val.thumb!=null && val.thumb!='' && val.thumb!='null' ?(
                                  <Image layout="fill" className="img-thumb" src={val.thumb} alt="" />
                                ):null}
                              </div>
                              {val.ep_oculus == 1 ?(
                                <i className={`icon-ic-play play-mobile`}></i>
                              ):(
                                <i className={`icon-ic-qrcode play-mobile`}></i>
                              )}
                              <div className="video-progress">
                                <Progress percent={val.played_percent} />
                              </div>
                          </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-12 col-md-6 col-lg-8" onClick={() => this.selectVdo(key)}>
                    <p className={`${styles.title}`}>
                      {val.title}
                      <p className={`${styles.description}`}>
                        {truncate(val.lesson_key,80)}
                      </p>
                    </p>
                  </div>
                  {val.ep_oculus == 1 ?(
                    <div className={`col-12 col-md-2 col-lg-2 ${styles.text_right}`} onClick={() => this.selectVdo(key)}>
                      <p className={`this_time ${styles.calendar}`}><i className="icon-ic-time_2 d-block d-md-none"></i> {convertSecond(val.duration)}</p>
                        <i className={`icon-ic-play play-desktop`}></i>
                    </div>
                  ):(
                    <div className={`col-12 col-md-2 col-lg-2 ${styles.text_right}`} onClick={() => this.selectVdo(key)}>
                      {val.played_percent>0?(
                        <p className={`this_time ${styles.calendar}`}>{val.played_percent}%</p>
                      ):(
                        <p className={`this_time ${styles.calendar}`}>0%</p>
                      )}
                        <i className={`icon-ic-qrcode play-desktop`}></i>
                    </div>
                  )}
                  {(val.quiz.question && val.quiz.question.length>0)||(val.pre_test.question && val.pre_test.question.length>0) ? (
                    <div className="card-quiz">
                      <>
                        {val.pre_test.question && val.pre_test.question.length>0 ? (
                          val.pre_test.allowed && val.pre_test.first_time && !val.pre_test.check ? (
                            <div className="item-quiz pre-test-button" onClick={() => this.selectPreTest(key)}>
                              <div className={styles.iconTestDiv}>
                                <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                              </div>
                              {this.translateEng('แบบทดสอบก่อนเรียน')}
                            </div>
                          ):(
                            val.pre_test.allowed && !val.pre_test.first_time && !val.pre_test.check ? (
                              <div className="item-quiz pre-test-button" onClick={() => this.selectPreTest2(key,val.pre_test.last_point)}>
                                <div className={styles.iconTestDiv}>
                                  <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                </div>
                                {this.translateEng('แบบทดสอบก่อนเรียน')}
                              </div>
                            ):(
                              !val.pre_test.allowed && !val.pre_test.first_time && !val.pre_test.check ? (
                                <div className="item-quiz pre-test-button" onClick={() => this.selectPreTest3(key,val.pre_test.last_point)}>
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                  </div>
                                  {this.translateEng('แบบทดสอบก่อนเรียน')}
                                </div>
                              ):(
                                !val.pre_test.first_time && val.pre_test.check ? (
                                  <div className="item-quiz pre-test-button" onClick={() => this.selectPreTest4(key)}>
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบก่อนเรียน')}
                                  </div>
                                ):(
                                  <div className="item-quiz pre-test-button" onClick={() => this.selectPreTest(key)}>
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบก่อนเรียน')}
                                  </div>
                                )
                              )
                            )
                          )
                        ):(null)}
                        {val.quiz.question && val.quiz.question.length>0 ? (
                          val.quiz.allowed && val.quiz.first_time && !val.quiz.check ? (
                            <div className="item-quiz" onClick={() => this.selectQuiz(key)}>
                              <div className={styles.iconTestDiv}>
                                <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                              </div> 
                              {this.translateEng('แบบทดสอบหลังเรียน')}
                            </div>
                          ):(
                            val.quiz.allowed && !val.quiz.first_time && !val.quiz.check ? (
                              <div className="item-quiz" onClick={() => this.selectQuiz2(key,val.quiz.last_point)}>
                                <div className={styles.iconTestDiv}>
                                  <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                </div>
                                {this.translateEng('แบบทดสอบหลังเรียน')}
                              </div>
                            ):(
                              !val.quiz.allowed && !val.quiz.first_time && !val.quiz.check ? (
                                <div className="item-quiz" onClick={() => this.selectQuiz3(key,val.quiz.last_point)}>
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                  </div>
                                  {this.translateEng('แบบทดสอบหลังเรียน')}
                                </div>
                              ):(
                                !val.quiz.allowed && !val.quiz.first_time && val.quiz.check ? (
                                  <div className="item-quiz" onClick={() => this.selectQuiz4(key)}>
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบหลังเรียน')}
                                  </div>
                                ):(
                                  <div className="item-quiz" onClick={() => this.selectQuiz5(key)}>
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบหลังเรียน')}
                                  </div>
                                )
                              )
                            )
                          )
                        ):(null)}
                      </>
                    </div>
                  ):null}
                </div>
              )
            )
          ):null
        ):(
          this.state.vdoSection.map((val, key) =>
            val.sec_type=='lesson'?(
              val.lock ? (
                <div
                  className={`row ${styles.list} 
                ${this.state.keySelect == val.index ? styles.active : ""}`}
                  key={val.index}
                  onClick={() => this.selectLockVdo(val.index)}
                >
                  <div className="col-12 col-md-4 col-lg-2">
                    <div className="card card-lock radius">
                      <div className="inner">
                          <div className="card-img">
                            <div className={styles.videoThumbDiv}>
                              {val && val.thumb && val.thumb!=null && val.thumb!='' && val.thumb!='null' ?(
                                <Image layout="fill" className="img-thumb" src={val.thumb} alt="" />
                              ):null}
                            </div>
                            <div className="card-icon lock">
                              <i className={`icon-ic-lock`}></i>
                            </div>
                          </div>
                      </div>
                    </div>

                  </div>
                  <div className="col-12 col-md-6 col-lg-8">
                    <p className={`${styles.title}`}>
                      {val.title}
                      <p className={`${styles.description}`}>
                        {truncate(val.lesson_key,80)}
                      </p>
                    </p>
                  </div>
                  <div className={`col-12 col-md-2 col-lg-2 ${styles.text_right}`}>
                    {val.ep_oculus == 1 ?(
                      <p className={`this_time ${styles.calendar}`}><i className="icon-ic-time_2 d-block d-md-none"></i> {convertSecond(val.duration)}</p>
                    ):(
                      val.played_percent>0?(
                        <p className={`this_time ${styles.calendar}`}>{val.played_percent}%</p>
                      ):(
                        <p className={`this_time ${styles.calendar}`}>0%</p>
                      )
                    )}
                  </div>
                  {(val.quiz.question && val.quiz.question.length>0)||(val.pre_test.question && val.pre_test.question.length>0) ? (
                    <div className="card-quiz">
                      <>
                        {val.pre_test.question && val.pre_test.question.length>0 ? (
                          val.pre_test.allowed && val.pre_test.first_time && !val.pre_test.check ? (
                            <div className="item-quiz pre-test-button">
                              <div className={styles.iconTestDiv}>
                                <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                              </div>
                              {this.translateEng('แบบทดสอบก่อนเรียน')}
                            </div>
                          ):(
                            val.pre_test.allowed && !val.pre_test.first_time && !val.pre_test.check ? (
                              <div className="item-quiz pre-test-button">
                                <div className={styles.iconTestDiv}>
                                  <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                </div>
                                {this.translateEng('แบบทดสอบก่อนเรียน')}
                              </div>
                            ):(
                              !val.pre_test.allowed && !val.pre_test.first_time && !val.pre_test.check ? (
                                <div className="item-quiz pre-test-button">
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                  </div>
                                  {this.translateEng('แบบทดสอบก่อนเรียน')}
                                </div>
                              ):(
                                !val.pre_test.allowed && !val.pre_test.first_time && val.pre_test.check ? (
                                  <div className="item-quiz pre-test-button">
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบก่อนเรียน')}
                                  </div>
                                ):(
                                  <div className="item-quiz pre-test-button">
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบก่อนเรียน')}
                                  </div>
                                )
                              )
                            )
                          )
                        ):(null)}
                        {val.quiz.question && val.quiz.question.length>0 ? (
                          val.quiz.allowed && val.quiz.first_time && !val.quiz.check ? (
                            <div className="item-quiz">
                            <div className={styles.iconTestDiv}>
                              <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                            </div>
                              {this.translateEng('แบบทดสอบหลังเรียน')}
                            </div>
                          ):(
                            val.quiz.allowed && !val.quiz.first_time && !val.quiz.check ? (
                              <div className="item-quiz">
                                <div className={styles.iconTestDiv}>
                                  <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                </div>
                                {this.translateEng('แบบทดสอบหลังเรียน')}
                              </div>
                            ):(
                              !val.quiz.allowed && !val.quiz.first_time && !val.quiz.check ? (
                                <div className="item-quiz">
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                  </div>
                                  {this.translateEng('แบบทดสอบหลังเรียน')}
                                </div>
                              ):(
                                !val.quiz.allowed && !val.quiz.first_time && val.quiz.check ? (
                                  <div className="item-quiz">
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบหลังเรียน')}
                                  </div>
                                ):(
                                  <div className="item-quiz">
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบหลังเรียน')}
                                  </div>
                                )
                              )
                            )
                          )
                        ):(null)}
                      </>
                    </div>
                  ):null}
                </div>
              ) : (
                <div
                  className={`row ${styles.list} 
                ${this.state.keySelect == val.index ? styles.active : ""}`}
                  key={val.index}
                >
                  <div className="col-12 col-md-4 col-lg-2" onClick={() => this.selectVdo(val.index)}>
                    <div className="card radius">
                      <div className="inner">
                          <div className="card-img">
                              <div className={styles.videoThumbDiv}>
                                {val && val.thumb && val.thumb!=null && val.thumb!='' && val.thumb!='null' ?(
                                  <Image layout="fill" className="img-thumb" src={val.thumb} alt="" />
                                ):null}
                              </div>
                              {val.ep_oculus == 1 ?(
                                <i className={`icon-ic-play play-mobile`}></i>
                              ):(
                                <i className={`icon-ic-qrcode play-mobile`}></i>
                              )}
                              <div className="video-progress">
                                <Progress percent={val.played_percent} />
                              </div>
                          </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-12 col-md-6 col-lg-8" onClick={() => this.selectVdo(val.index)}>
                    <p className={`${styles.title}`}>
                      {val.title}
                      <p className={`${styles.description}`}>
                        {truncate(val.lesson_key,80)}
                      </p>
                    </p>
                  </div>
                  {val.ep_oculus == 1 ?(
                    <div className={`col-12 col-md-2 col-lg-2 ${styles.text_right}`} onClick={() => this.selectVdo(val.index)}>
                      <p className={`this_time ${styles.calendar}`}><i className="icon-ic-time_2 d-block d-md-none"></i> {convertSecond(val.duration)}</p>
                        <i className={`icon-ic-play play-desktop`}></i>
                    </div>
                  ):(
                    <div className={`col-12 col-md-2 col-lg-2 ${styles.text_right}`} onClick={() => this.selectVdo(val.index)}>
                      {val.played_percent>0?(
                        <p className={`this_time ${styles.calendar}`}>{val.played_percent}%</p>
                      ):(
                        <p className={`this_time ${styles.calendar}`}>0%</p>
                      )}
                        <i className={`icon-ic-qrcode play-desktop`}></i>
                    </div>
                  )}
                  {(val.quiz.question && val.quiz.question.length>0)||(val.pre_test.question && val.pre_test.question.length>0) ? (
                    <div className="card-quiz">
                      <>
                        {val.pre_test.question && val.pre_test.question.length>0 ? (
                          val.pre_test.allowed && val.pre_test.first_time && !val.pre_test.check ? (
                            <div className="item-quiz pre-test-button" onClick={() => this.selectPreTest(val.index)}>
                              <div className={styles.iconTestDiv}>
                                <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                              </div>
                              {this.translateEng('แบบทดสอบก่อนเรียน')}
                            </div>
                          ):(
                            val.pre_test.allowed && !val.pre_test.first_time && !val.pre_test.check ? (
                              <div className="item-quiz pre-test-button" onClick={() => this.selectPreTest2(val.index,val.pre_test.last_point)}>
                                <div className={styles.iconTestDiv}>
                                  <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                </div>
                                {this.translateEng('แบบทดสอบก่อนเรียน')}
                              </div>
                            ):(
                              !val.pre_test.allowed && !val.pre_test.first_time && !val.pre_test.check ? (
                                <div className="item-quiz pre-test-button" onClick={() => this.selectPreTest3(val.index,val.pre_test.last_point)}>
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                  </div>
                                  {this.translateEng('แบบทดสอบก่อนเรียน')}
                                </div>
                              ):(
                                !val.pre_test.first_time && val.pre_test.check ? (
                                  <div className="item-quiz pre-test-button" onClick={() => this.selectPreTest4(val.index)}>
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบก่อนเรียน')}
                                  </div>
                                ):(
                                  <div className="item-quiz pre-test-button" onClick={() => this.selectPreTest(val.index)}>
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบก่อนเรียน')}
                                  </div>
                                )
                              )
                            )
                          )
                        ):(null)}
                        {val.quiz.question && val.quiz.question.length>0 ? (
                          val.quiz.allowed && val.quiz.first_time && !val.quiz.check ? (
                            <div className="item-quiz" onClick={() => this.selectQuiz(val.index)}>
                              <div className={styles.iconTestDiv}>
                                <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                              </div> 
                              {this.translateEng('แบบทดสอบหลังเรียน')}
                            </div>
                          ):(
                            val.quiz.allowed && !val.quiz.first_time && !val.quiz.check ? (
                              <div className="item-quiz" onClick={() => this.selectQuiz2(val.index,val.quiz.last_point)}>
                                <div className={styles.iconTestDiv}>
                                  <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                </div>
                                {this.translateEng('แบบทดสอบหลังเรียน')}
                              </div>
                            ):(
                              !val.quiz.allowed && !val.quiz.first_time && !val.quiz.check ? (
                                <div className="item-quiz" onClick={() => this.selectQuiz3(val.index,val.quiz.last_point)}>
                                  <div className={styles.iconTestDiv}>
                                    <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                  </div>
                                  {this.translateEng('แบบทดสอบหลังเรียน')}
                                </div>
                              ):(
                                !val.quiz.allowed && !val.quiz.first_time && val.quiz.check ? (
                                  <div className="item-quiz" onClick={() => this.selectQuiz4(val.index)}>
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบหลังเรียน')}
                                  </div>
                                ):(
                                  <div className="item-quiz" onClick={() => this.selectQuiz5(val.index)}>
                                    <div className={styles.iconTestDiv}>
                                      <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
                                    </div>
                                    {this.translateEng('แบบทดสอบหลังเรียน')}
                                  </div>
                                )
                              )
                            )
                          )
                        ):(null)}
                      </>
                    </div>
                  ):null}
                </div>
              )
            ):(
              <div
                className={`row ${styles.list}`}
                key={key}
              >
                <div className="col-12 col-md-4 col-lg-2">
                  <div className="card card-lock radius">
                    <div className="inner">
                        <div className="card-img">
                          <div className={styles.videoThumbDiv}>
                            {val && val.thumb && val.thumb!=null && val.thumb!='' && val.thumb!='null' ?(
                              <Image layout="fill" className="img-thumb" src={val.thumb} alt="" />
                            ):null}
                          </div>
                          {this.props.courseAllow ?(
                            <a className="card-icon download-file-mb" href={`${val.file}`} target="_blank" rel="noreferrer">
                              <IoMdDownload />
                            </a>
                          ):(
                            <>
                              <div className="card-icon lock" onClick={() => this.selectLockDoc()}>
                                <i className={`icon-ic-lock`}></i>
                              </div>
                            </>
                          )}
                        </div>
                    </div>
                  </div>
                </div>
                <div className="col-12 col-md-5 col-lg-7">
                  <p className={`${styles.title}`}>
                    {val.title}
                  </p>
                </div>
                {this.props.courseAllow ?(
                  <div className={`col-12 col-md-3 col-lg-3 download-file-ds ${styles.text_right}`}>
                    <a className="card-icon download-file-ds" href={`${val.file}`} target="_blank" rel="noreferrer">
                      <p>ดาวน์โหลด</p>
                      <IoMdDownload />
                    </a>
                  </div>
                ):(
                  <div className={`col-12 col-md-3 col-lg-3 download-file-ds ${styles.text_right}`}>
                    <p onClick={() => this.selectLockDoc()}>ดาวน์โหลด</p>
                    <i className={`icon-ic-lock`} onClick={() => this.selectLockDoc()}></i>
                  </div>
                )}
              </div>
            )
          )
        )
        }
       {/* {this.props.isAssessment ? ( */}
          <div className={`row ${styles.list}`}>
            <div className="card-quiz" onClick={() => this.props.cbAssessment()}>
              <div className={styles.iconTestDiv}>
                <Image alt="" layout="intrinsic" className="icon-test" src="/assets/images/icontest.png" width={50} height={50} />
              </div>
              {this.translateEng('แบบประเมินความพึงพอใจ')}
            </div>
          </div>
        {/* ):null} */}
      </div>
    );
  }
}

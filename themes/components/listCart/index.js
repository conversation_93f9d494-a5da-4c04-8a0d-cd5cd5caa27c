import React, { Component } from "react";
import Link from "next/link";
import Image from "next/image";

import styles from "/public/assets/css/component/listCart.module.css";


export default class index extends Component {
  render() {
    return (
      <div className={`${styles.block_list_cart}`}>
        <div className={`${styles.list_inner_cart}`}>
          {/* ===== */}
          <div className={`${styles.item_cart}`}>
            <div className={`${styles.cart_img}`}>
              <div className={`${styles.blockCratImgSize}`}>
              <Image className="thumb ItemsCratImgSize" src="/assets/images/scbx-blog-list-13.jpg" alt="" 
              layout="fill"
              objectFit="contain"
              />
              </div>
            </div>
            <div className={`${styles.cart_content}`}>
              <h3>What is Lorem Ipsum?</h3>
              <h4>Lorem Ipsum is simply dummy text of the printing and typesetting industry.</h4>
              <p>1,250 บาท</p>
            </div>
            <div className={`${styles.cart_action}`}>
              <button className={`${styles.btn_cart_action}`}>
                <i className="icon-ic-close-cart"></i>
              </button>
            </div>
          </div>
          {/* ===== */}
          <div className={`${styles.item_cart}`}>
            <div className={`${styles.cart_img}`}>
              <div className={`${styles.blockCratImgSize}`}>
                <Image className="thumb ItemsCratImgSize" src="/assets/images/scbx-blog-list-12.jpg" alt="" 
                 layout="fill"
                 objectFit="contain"
                />
              </div>
            </div>
            <div className={`${styles.cart_content}`}>
              <h3>What is Lorem Ipsum?</h3>
              <h4>Lorem Ipsum is simply dummy text of the printing and typesetting industry.</h4>
              <p>1,250 บาท</p>
            </div>
            <div className={`${styles.cart_action}`}>
              <button className={`${styles.btn_cart_action}`}>
                <i className="icon-ic-close-cart"></i>
              </button>
            </div>
          </div>
          {/* ===== */}
        </div>
      </div>
    );
  }
}

import React, { useState, useRef, useEffect } from 'react'
import styled from '@emotion/styled'
import { theme } from './theme'
import { useController } from 'react-hook-form'
import { usePinInput } from '@/themes/components/mdeconnectPinInput/hooks/usePinInput'
const PIN_LENGTH = 6

// Styled Components
const Container = styled.div`
  background-color: ${theme.colors.primaryLight};
  padding: ${theme.spacing.lg};
  border-radius: ${theme.borderRadius.regular};
`

const Title = styled.h4`
  font-family: Kanit;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.8rem;
`

const VerificationLink = styled.a`
  color: ${theme.colors.primary};
  text-underline-offset: 3px;
  text-decoration: underline;
`

const RadioGroup = styled.div`
  margin-top: ${theme.spacing.sm};
  display: flex;
  height: 30px;
`

const RadioLabel = styled.label`
  margin-left: 3vw;
  flex: 1 1 auto;
  display: flex;
  align-items: center;
  cursor: pointer;

  @media (max-width: ${theme.breakpoints.tablet}) {
    margin-left: 2vw;
  }

  @media (max-width: ${theme.breakpoints.mobile}) {
    margin-left: 1vw;
  }
`

const CheckboxWrapper = styled.div`
  position: relative;
`

export const Checkbox = styled.div`
  width: 20px;
  height: 20px;
  border: 1px solid ${theme.colors.borderColor};
  border-radius: ${theme.borderRadius.round};
  background-color: ${(props) =>
    props.checked ? theme.colors.primary : theme.colors.white};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease, border-color 0.3s ease;

  &:after {
    content: '';
    width: 11px;
    height: 6px;
    border: 2px solid ${theme.colors.white};
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    position: absolute;
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    opacity: ${(props) => (props.checked ? 1 : 0)};
    transition: opacity 0.2s ease;
  }
`

const RadioText = styled.span`
  margin-left: ${theme.spacing.xs};
  font-size: 16px;
`

const PinTextSection = styled.p`
  font-size: 16px;
  color: ${theme.colors.grayText};
  margin-top: ${theme.spacing.sm};
`

const PinSection = styled.div`
  margin-top: ${theme.spacing.md};
  display: flex;
  flex-direction: column;
  align-items: center;
`

const PinInputContainer = styled.div`
  display: flex;
  gap: ${theme.spacing.xs};
`

const PinInput = styled.input`
  width: 58px;
  height: 58px;
  text-align: center;
  font-size: 24px;
  border: 1px solid ${theme.colors.borderColor};
  border-radius: ${theme.borderRadius.round};

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(99, 141, 46, 0.8);
  }

  @media (max-width: ${theme.breakpoints.tablet}) {
    width: 48px;
    height: 48px;
    font-size: 14px;
  }

  @media (max-width: ${theme.breakpoints.mobile}) {
    width: 38px;
    height: 38px;
    font-size: 12px;
  }
`

const EyeButton = styled.div`
  align-self: center;
  cursor: pointer;
  font-size: 20px;
  color: ${theme.colors.primary};
`

// Component for radio option
export const RadioOption = ({ name, label, checked, onChange }) => (
  <RadioLabel>
    <CheckboxWrapper>
      <Checkbox checked={checked} onClick={() => onChange(name)} />
    </CheckboxWrapper>
    <RadioText>{label}</RadioText>
  </RadioLabel>
)

// Component for pin input
const PinInputField = ({
  index,
  value,
  showPinPassword,
  inputRef,
  onChange,
  onKeyDown,
}) => (
  <PinInput
    ref={inputRef}
    type={showPinPassword ? 'text' : 'password'}
    inputMode="numeric"
    maxLength="1"
    value={value}
    onChange={(e) => onChange(index, e.target.value)}
    onKeyDown={(e) => onKeyDown(index, e)}
  />
)

const MdeconnectPinInput = ({ register, setValue, appContext, verify }) => {
  const [verificationOption, setVerificationOption] = useState('verifyLater')
  const [showPinPassword, setShowPinPassword] = useState(false)
  const {
    pinValues,
    setPinValues,
    inputRefs,
    handlePinChange,
    handleKeyDown,
    handlePaste,
  } = usePinInput(PIN_LENGTH)

  /**
   * Updates the form with the PIN values
   * @param {string} combinedPin - The combined PIN value
   */
  const updateFormWithPin = (combinedPin) => {
    // Try to use setValue first (recommended approach)
    if (setValue) {
      setValue('medical_pincode', combinedPin, { shouldValidate: true });
      return;
    }

    // Fallback to register method
    if (!register) return;

    const event = {
      target: {
        value: combinedPin,
        name: 'medical_pincode',
        type: 'text',
      },
    }

    try {
      // Update the form with the combined PIN value
      register('medical_pincode').onChange(event);
    } catch (error) {
      if (appContext?.diFormPattern) {
        appContext.diFormPattern(event);
      }
    }
  }

  /**
   * Updates the form with verification option
   * @param {string} option - The verification option value
   */
  const updateVerificationOption = (option) => {
    // Try to use setValue first (recommended approach)
    if (setValue) {
      setValue('md_verify', option, { shouldValidate: true });
      return;
    }

    // Fallback to register method
    if (!register) return;

    const event = {
      target: {
        value: option,
        name: 'md_verify',
        type: 'text',
      },
    }

    try {
      register('md_verify').onChange(event);
    } catch (error) {
      if (appContext?.diFormPattern) {
        appContext.diFormPattern(event);
      }
    }
  }

  // Initialize form
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, PIN_LENGTH);

    // Set initial form values
    updateVerificationOption(verificationOption);

    if (verificationOption === 'verifyNow') {
      updateFormWithPin(pinValues.join(''));
    }
  }, []);

  // Update form when PIN values change
  useEffect(() => {
    if (verificationOption === 'verifyNow') {
      updateFormWithPin(pinValues.join(''));
    }
  }, [pinValues]);

  /**
   * Handles verification option change
   * @param {string} option - The selected verification option
   */
  const handleVerificationOptionChange = (option) => {
    setVerificationOption(option);
    updateVerificationOption(option);
    
    // If user chooses to verify later, clear the PIN values and update form
    if (option === 'verifyLater') {
      setPinValues(Array(PIN_LENGTH).fill(''));
      updateFormWithPin('');
    }
  }

  /**
   * Custom handler for PIN changes that ensures form updates
   */
  const handlePinInputChange = (index, value) => {
    // Use the original handler
    handlePinChange(index, value);
    
    // Form update will be handled by the useEffect that watches pinValues
  }

  const togglePinVisibility = () => {
    setShowPinPassword(!showPinPassword);
  }
  
  return (
    <Container>
      <Title>
        ยืนยันตัวตนกับแพทยสภา ผ่าน App MD eConnect เพื่อสิทธิประโยชน์ของแพทย์
        และเก็บคะแนน CME{' '}
        <VerificationLink target='_blank' href="https://youtube.com/shorts/i5bql-PU5-A"> (วิธีการยืนยันตัวตน)</VerificationLink>
        <VerificationLink target='_blank' href="https://youtube.com/shorts/PRn9F5kNHLs">(วิธีการ Download App MD Econnect)</VerificationLink>
      </Title>

      <RadioGroup>
        <RadioOption
          name="verifyNow"
          label="ยืนยันตัวตนตอนนี้"
          checked={verificationOption === 'verifyNow'}
          onChange={() => handleVerificationOptionChange('verifyNow')}
        />
        <RadioOption
          name="verifyLater"
          label="ยืนยันภายหลัง"
          checked={verificationOption === 'verifyLater'}
          onChange={() => handleVerificationOptionChange('verifyLater')}
        />
      </RadioGroup>

      {verificationOption === 'verifyNow' && (
        <>
          <PinTextSection>กรอกรหัส PIN จาก App MD eConnect</PinTextSection>
          <PinSection>
            <PinInputContainer onPaste={handlePaste}>
              {pinValues.map((value, index) => (
                <PinInputField
                  key={index}
                  index={index}
                  value={value}
                  showPinPassword={showPinPassword}
                  inputRef={(el) => (inputRefs.current[index] = el)}
                  onChange={handlePinInputChange}
                  onKeyDown={handleKeyDown}
                />
              ))}
              <EyeButton onClick={togglePinVisibility}>
                <i className={`eye icon ${showPinPassword ? '' : 'slash'}`} />
              </EyeButton>
            </PinInputContainer>
          </PinSection>
        </>
      )}
    </Container>
  )
}

export default MdeconnectPinInput

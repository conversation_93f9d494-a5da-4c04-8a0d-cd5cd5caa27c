import { useState, useRef } from 'react';

/**
 * Custom hook for PIN input functionality
 * @param {number} pinLength - Length of the PIN
 * @returns {Object} PIN input utilities
 */
export function usePinInput(pinLength) {
  const [pinValues, setPinValues] = useState(Array(pinLength).fill(''));
  const inputRefs = useRef([]);
  
  // Ensure refs array is initialized correctly
  if (inputRefs.current.length !== pinLength) {
    inputRefs.current = Array(pinLength).fill(null);
  }

  /**
   * Handles PIN input changes
   * @param {number} index - The index of the PIN input
   * @param {string} value - The input value
   */
  const handlePinChange = (index, value) => {
    // Only allow numbers
    if (!/^\d*$/.test(value)) return;

    const newPinValues = [...pinValues];
    newPinValues[index] = value;
    setPinValues(newPinValues);

    // Move to the next input if a value was entered
    if (value && index < pinLength - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };
  
  /**
   * Handles backspace key for PIN inputs
   * @param {number} index - The index of the PIN input
   * @param {Event} e - The keyboard event
   */
  const handleKeyDown = (index, e) => {
    if (e.key === 'Backspace' && !pinValues[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  /**
   * Handles pasting into PIN inputs
   * @param {Event} e - The paste event
   */
  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, pinLength);

    if (pastedData) {
      const newPinValues = [...pinValues];
      
      pastedData.split('').forEach((digit, i) => {
        if (i < pinLength) newPinValues[i] = digit;
      });
      
      setPinValues(newPinValues);

      // Focus the appropriate input based on pasted data length
      const focusIndex = Math.min(pastedData.length, pinLength - 1);
      inputRefs.current[focusIndex]?.focus();
    }
  };

  return {
    pinValues,
    setPinValues,
    inputRefs,
    handlePinChange,
    handleKeyDown,
    handlePaste
  };
}
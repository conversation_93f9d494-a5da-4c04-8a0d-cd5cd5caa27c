import React, { Component } from "react";

import styles from "/public/assets/css/component/itemListCourse.module.css";

import Link from "next/link";
import Image from "next/image";

export default class index extends Component {
  render() {
    return (
      this.props.data.map((val, key) => (
        <div key={key} className={`${styles.item_list_course}`}>
          <div className={`${styles.item_course_inner}`}>
            <div className={`${styles.item_course_img}`}>
              <div className={`${styles.blockLisCourseImgSize}`}>
              {val && val.cover && val.cover!=null && val.cover!='' && val.cover!='null' ?(
                <Image className={`${styles.thumb}`} key={key} src={val.cover} 
                  layout="fill"
                  objectFit="cover"
                  alt=""
                />
              ):null}
              </div>
            </div>
            <div className={`${styles.item_course_text}`}>
              <h3>{val.course_title}</h3>
              <h4>{val.instructor_string}</h4>
            </div>
            <div className={`${styles.item_course_action}`}>
              <a onClick={()=>this.props.submitImmersiveCourse(val.cid,this.props.type)}>
                <button className={`${styles.action}`}>
                  <i className="icon-ic-play"></i>
                  <span>Play</span>
                </button>
              </a>
              <p className={`${styles.percent}`}>{val.progress}%</p>
            </div>
          </div>
        </div>
      ))
    );
  }
}

import React from 'react';
import Link from "next/link";
import { useState, useEffect, useContext } from "react";


import {
  <PERSON>u,
  Button,
  Modal,
  Select,
  Input,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";

const playListOptions = [
  { key: 'af', value: 'af', text: 'Afghanistan' },
  { key: 'ax', value: 'ax', text: 'Aland Islands' },
  { key: 'al', value: 'al', text: 'Albania' },
  { key: 'dz', value: 'dz', text: 'Algeria' },
  { key: 'as', value: 'as', text: 'American Samoa' },
  { key: 'ad', value: 'ad', text: 'Andorra' },
  { key: 'ao', value: 'ao', text: 'Angola' },
  { key: 'ai', value: 'ai', text: 'Anguilla' },
  { key: 'ag', value: 'ag', text: 'Antigua' },
  { key: 'ar', value: 'ar', text: 'Argentina' },
  { key: 'am', value: 'am', text: 'Armenia' },
  { key: 'aw', value: 'aw', text: 'Aruba' },
  { key: 'au', value: 'au', text: 'Australia' },
  { key: 'at', value: 'at', text: 'Austria' },
  { key: 'az', value: 'az', text: 'Azerbaijan' },
  { key: 'bs', value: 'bs', text: 'Bahamas' },
  { key: 'bh', value: 'bh', text: 'Bahrain' },
  { key: 'bd', value: 'bd', text: 'Bangladesh' },
  { key: 'bb', value: 'bb', text: 'Barbados' },
  { key: 'by', value: 'by', text: 'Belarus' },
  { key: 'be', value: 'be', text: 'Belgium' },
  { key: 'bz', value: 'bz', text: 'Belize' },
  { key: 'bj', value: 'bj', text: 'Benin' },
]

function show_add_list() {
  document.getElementById("fmCreateListAdd").classList.toggle('active');
}

const Index = () => {

  const [open, setOpen] = useState(false)
  return (
    <div>
    <Button className="btn-create-list" onClick={() => setOpen(true)}>
      <span> create-list </span>
    <i className='icon-ic-circle-plus'></i>
    </Button>
    <Modal
      className='modalCreateList'
      onClose={() => setOpen(false)}
      onOpen={() => setOpen(true)}
      open={open}>
      <Modal.Content className="modalCreateListContent">
        <div className="block-modal-CreateList">
            <div className="inner">
              <h3>เพิ่มไปยังเพลย์ลิสต์ของคุณ</h3>
            </div>
            <div className="fm-CreateList-select">
              <div className="item-fm">
                <Select
                  className="fm-control"
                  placeholder="เพลย์ลิสต์ของคุณ"
                  options={playListOptions}
                  onChange={(event, data) => {}}
                />
              </div>
              <button className='btn-add-list' onClick={() => show_add_list()}>
                <span>สร้างเพลย์ลิสต์</span>
              </button>
            </div>
            <div id='fmCreateListAdd' className='fm-CreateList-add'>
              <div className="item-fm">
              <Input
                id="create_list"
                type="text"
                className="fm-control"
                placeholder="ป้อนชื่อเพลย์ลิสต์"
              ></Input>
              </div>
            </div>
            <div className='fm-CreateList-action'>
              <button className='btn-add-list'>
                <span>ตกลง</span>
              </button>
            </div>
        </div>
      </Modal.Content>
    </Modal>
</div>
  );
}

export default Index;

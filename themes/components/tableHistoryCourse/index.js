import React, { Component } from "react";
import Link from "next/link";

import styles from "/public/assets/css/component/tableHistoryCourse.module.css";
import { IoMdDownload } from "react-icons/io";

import NumberFormat from "react-number-format";
import Swal from 'sweetalert2'
export default class index extends Component {
  render() {
    return (
      <div className={`${styles.block_table_history_course}`}>
        <div className={`${styles.inner_table_history_course}`}>
          <div className={`${styles.scroll}`}>
          <table>
            <thead>
              <tr>
                <th scope="col">หมายเลขสั่งซื้อ</th>
                <th scope="col">สินค้า</th>
                <th scope="col">วันที่ซื้อ</th>
                <th scope="col">สถานะ</th>
                <th scope="col">ราคา</th>
                <th scope="col">ใบเสร็จ</th>
                {/* <th scope="col">เริ่มบทเรียน</th> */}
              </tr>
            </thead>
            <tbody>
              {this.props.data.map((val, key) => (
                <tr key={key}>
                  <td>{val.order_number}</td>
                  <td dangerouslySetInnerHTML={{__html: val.order_item,}}></td>
                  <td>{val.order_date}</td>
                  {val.order_status=='ยืนยันชำระเงิน' ? (
                    <td>
                      <Link href={`/payment/${val.order_no}`}>
                        <a className="order-status-btn">ยืนยันชำระเงิน</a>
                      </Link>
                      <span className="order-status-line">|</span>
                      <a className="order-status-btn red" onClick={() =>this.props.callback(val.order_id)}>ยกเลิก</a>
                    </td>
                  ):(
                    val.order_status=='ชำระเงิน' ? (
                      <td>
                        <a className="order-status-btn" onClick={() =>this.props.payment(val.order_no,val.price,val.payment_type,val.qr_id)}>ชำระเงิน</a>
                        <span className="order-status-line">|</span>
                        <a className="order-status-btn red" onClick={() =>this.props.callback(val.order_id)}>ยกเลิก</a>
                      </td>
                    ):(
                      <td>{val.order_status}</td>
                    )
                  )} 
                  {
                    val.price>0
                    ?
                    <NumberFormat
                    decimalScale={2}
                    value={val.price}
                    displayType={"text"}
                    thousandSeparator={true}
                    renderText={(value, props) => ( 
                      <td {...props}>
                        {value}
                      </td>
                    )}
                      />
                    :
                      <td>
                        ฟรี
                      </td> 
                  }
                  
                  <td>
                    {val.receipt!=null && val.receipt!='' && val.price>0 ? (
                      <a href={`${val.receipt}`} target="_blank" rel="noreferrer"><IoMdDownload /></a>
                      // <a className="cursor-pointer" onClick={() => Swal.fire({
                      //   html: 'ขณะนี้ระบบยังไม่สามารถออกใบเสร็จได้<br>ท่านสามารถดาวน์โหลดใบเสร็จได้เร็วๆ นี้',
                      //   icon: 'info',
                      //   confirmButtonText: 'ตกลง',
                      //   confirmButtonColor: "#648d2f"
                      // }).then((result) => {
                      // })}><IoMdDownload /></a>
                    ):(
                        <p>-</p>
                    )}
                  </td>
                  {/* {val.order_status=='ชำระเงินสำเร็จ' ? (
                    <td>
                      <Link href={`/course/${val.slug}`}>
                        <a><i className={`icon-ic-play`}></i></a>
                      </Link>
                    </td>
                  ):(
                    <td><p>-</p></td>
                  )} */}
                </tr>
              ))}
            </tbody>
          </table>
          </div>
        </div>
      </div>
    );
  }
}

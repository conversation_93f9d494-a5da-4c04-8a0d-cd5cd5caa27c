import React, { Component } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Zoom, FreeMode, Pagination, Controller, Navigation, Lazy, Mousewheel } from "swiper";

import CardSocialShare from "../cardSocialShare";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/lazy";
import styles from "/public/assets/css/component/groupCategory.module.css";

export default class GroupSocialShare extends Component {
  render() {
    const { bg, color, name, index, isLoop, size, data, callback } = this.props;

    return (
      <div
        className={`container custom-container space-between-content ${
          bg && bg !== "null" ? "is_banner" : ""
        }`}
        style={{ zIndex: index, backgroundImage: `url(${bg})` }}
      >
        <div className="row">
          <div className="col-12">
            <h2 className={styles.title} style={{ color }}>
              {name}
            </h2>
          </div>
        </div>

        <Swiper
          freeMode={true}
          loop={isLoop}
          lazy={{
            enabled: true,
            loadPrevNext: true,
            loadPrevNextAmount: 6,
          }}
          mousewheel={{
            forceToAxis: true,
            enabled: true,
          }}
          modules={[FreeMode, Lazy, Zoom, Mousewheel]}
          spaceBetween={0}
          zoom={true}
          className={styles.mySlide}
          breakpoints={{
            0: {
              slidesPerView: 1.5,
            },
            768: {
              slidesPerView: 3,
            },
            1200: {
              slidesPerView: size,
            },
          }}
        >
          {data.map((val, key) => (
            <SwiperSlide key={key}>
                  <CardSocialShare
                    type="normal"
                    callback={this.props.callback}
                    key={key}
                    data={val}
                  ></CardSocialShare>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    );
  }
}

import React, { Component } from "react";
import Link from "next/link";

export default class index extends Component {
  render() {
    return (
      <div className="block-list-tags">
        <div className="container custom-container">
          <div className="row">
            <div className="inner">
              <div className="list-tags-title">
                <h3>Tag : </h3>
              </div>
              <div className="list-tags-item">
                <div className="tags">
                  {
                    this.props.data.map((val,key) =>
                      <a key={key} href={`/category/knowledge?tag_id=${val.id}&tag=${val.title}`}>
                        <span className="tag">{val.title}</span>
                      </a>
                      // <span className="tag">{val.title}</span>
                    )
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

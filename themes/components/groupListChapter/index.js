import React, { Component } from "react";
import styles from "/public/assets/css/component/itemListChapter.module.css";
import stylesLock from "/public/assets/css/component/itemListChapterLock.module.css";
const Condition = ({ condition,data }) => {
  return condition ? (
    <div className={`${styles.item_list_chapter}`}>
      <div className={`${styles.icon_chapter}`}>
        <i className="icon-ic-tick-thanks"></i>
      </div>
      <div className={`${styles.number_chapter}`}>
        Chapter {data.chapter} :
      </div>
      <div className={`${styles.title_chapter}`}>
        {data.name}
      </div>
      <div  className={`${styles.time_chapter}`}>
        {data.time} นาที
      </div>
    </div>
  ) : (
    <div className={`${stylesLock.item_list_chapter}`}>
        <div className={`${stylesLock.icon_chapter}`}>
          <i className="icon-ic-circle-lock"></i>
        </div>
        <div className={`${stylesLock.number_chapter}`}>
          Chapter {data.chapter} :
        </div>
        <div className={`${stylesLock.title_chapter}`}>
          {data.name}
        </div>
        <div  className={`${stylesLock.time_chapter}`}>
          {data.time} นาที
        </div>
    </div>
  )
}
export default class index extends Component {
  render() {
    return (
      this.props.data.map((val, key) => (
        <Condition key={key} data={val} condition={val.status=='1'}></Condition>
      ))
    );
  }
}

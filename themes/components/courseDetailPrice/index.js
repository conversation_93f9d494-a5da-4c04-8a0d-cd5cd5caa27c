import React, { Component } from "react";
import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
} from "semantic-ui-react";
import { render } from "react-dom";
import NumberFormat from 'react-number-format';



export default class index extends Component {
  render() {
    return (
      this.props.data.is_promotion==1 ? (
        <div className="block-course-detail-price text-center">
          <div className="description-sale">
          <NumberFormat
            value={this.props.data.price}
            displayType={'text'}
            thousandSeparator={true}
            renderText={(value, props) => <span {...props}>ปกติ {value}</span>}
          />
          </div>
          <div className="description-big-price">
          ราคา
          <NumberFormat
            value={this.props.data.pro_price}
            displayType={'text'}
            thousandSeparator={true}
            renderText={(value, props) => <span {...props}>{value}</span>}
          />
          บาท
          </div>
        </div>
      ) : (
        <div className="block-course-detail-price text-center">
          <div className="description-big-price">
          ราคา
          <NumberFormat
            value={this.props.data.price}
            displayType={'text'}
            thousandSeparator={true}
            renderText={(value, props) => <span {...props}>{value}</span>}
          />
          บาท
          </div>
        </div>
      )
    );
  }
}

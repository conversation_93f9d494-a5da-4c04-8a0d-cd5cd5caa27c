import React, { Component } from "react";
import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
  TextArea,
} from "semantic-ui-react";
import { render } from "react-dom";

import styles from "/public/assets/css/component/purchaseReport.module.css";

export default class index extends Component {
  render() {
    return (
      <div className="item-purchase-report">
        {/* ===== */}
        <div className="item-report-price">
          <div className="purchase-report-name">
              <h3>ยอดรวมย่อย</h3>
          </div>
          <div className="purchase-report-price">1,250 บาท</div>
        </div>
        {/* ===== */}
        <div className="item-report-price">
          <div className="purchase-report-name">
            <h4>สวนลด <span><i className="icon-ic-tikket"></i> CODEE</span></h4>
          </div>
          <div className="purchase-report-price">-150 บาท</div>
        </div>
        {/* ===== */}
        <div className="item-report-price">
          <div className="purchase-report-name">
              <h4>หักภาษี ณ ที่จ่าย 3%</h4>
          </div>
          <div className="purchase-report-price">44.31  บาท</div>
        </div>
        {/* ===== */}
      </div>
    );
  }
}

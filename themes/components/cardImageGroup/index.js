import React, { Component } from "react";
import Link from "next/link";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Zoom, FreeMode, Pagination, Controller, Navigation,Lazy,Mousewheel } from "swiper";
import "swiper/css/lazy";

export default class index extends Component {
  render() {
    if(this.props.type=='desktop'){
      return (
        <div className="card card-default radius">
          <div className="inner card-list-row">
            {this.props.data.map((val, key) => (
              <div key={key} className="col-6 col-md-6 col-lg-3 col-xl-3 card-item">
                {val.trailer_media==6 ? (
                  <a href={`/ebook/${val.slug}`}>
                    <div className="card-img">
                      {this.props.display=='zoom' ? (
                        <div className="img-logo cursor certificate-img-list">
                          <Image src="/assets/images/zoom.png" alt="" 
                          layout="fill"
                          objectFit="cover"
                          objectPosition="center"
                          />
                        </div>
                      ):(null)}
                        <div className="img-thumb cursor-pointer certificate-img-list">
                          {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                            <Image src={val.image_th} alt={val.title_th} 
                              layout="fill"
                              objectFit="cover"
                              objectPosition="center"
                            />
                          ):null}
                        </div>
                    </div>
                  </a>
                ):(
                  <a href={`/course/${val.slug}`}>
                    <div className="card-img">
                      {this.props.display=='zoom' ? (
                        <div className="img-logo cursor certificate-img-list">
                          <Image src="/assets/images/zoom.png" alt="" 
                          layout="fill"
                          objectFit="cover"
                          objectPosition="center"
                          />
                        </div>
                      ):(null)}
                        <div className="img-thumb cursor-pointer certificate-img-list">
                          {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                            <Image src={val.image_th} alt={val.title_th} 
                              layout="fill"
                              objectFit="cover"
                              objectPosition="center"
                            />
                          ):null}
                        </div>
                    </div>
                  </a>
                )}
              </div>
            ))}
            
          </div>
        </div>
      );
    }else{
      return (
        <div>
          <div className="category-item-slide-big">
            <Swiper
              freeMode={true}
              lazy={true}
              mousewheel={{
                forceToAxis: true,
                enabled: true,
              }}
              modules={[FreeMode,Lazy,Pagination,Mousewheel]}
              spaceBetween={30}
              pagination={{
                clickable: true,
              }}
            >
              {this.props.data.map((val, key) => (
                key<3?(
                  <SwiperSlide key={key}>
                    {val.trailer_media==6 ? (
                      <a href={`/ebook/${val.slug}`}>
                        <div className="card-img">
                          {this.props.display=='zoom' ? (
                            <div className="img-logo-zoom cursor certificate-img-list">
                              <Image src="/assets/images/zoom.png" alt="" 
                                layout="fill"
                                objectFit="cover"
                                objectPosition="center"
                              />
                            </div>
                          ):(null)}
                          <div className="img-thumb cursor-pointer swiper-lazy certificate-img-list">
                            {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                              <Image data-src={val.image_th} src={val.image_th} alt={val.title_th} 
                                layout="fill"
                                objectFit="cover"
                                objectPosition="center"
                              />
                            ):null}
                          </div>
                          <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                        </div>
                      </a>
                    ):(
                      <a href={`/course/${val.slug}`}>
                        <div className="card-img">
                          {this.props.display=='zoom' ? (
                            <div className="img-logo-zoom cursor certificate-img-list">
                              <Image src="/assets/images/zoom.png" alt="" 
                                layout="fill"
                                objectFit="cover"
                                objectPosition="center"
                              />
                            </div>
                          ):(null)}
                          <div className="img-thumb cursor-pointer swiper-lazy certificate-img-list">
                            {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                              <Image data-src={val.image_th} src={val.image_th} alt={val.title_th} 
                                layout="fill"
                                objectFit="cover"
                                objectPosition="center"
                              />
                            ):null}
                          </div>
                          <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                        </div>
                      </a>
                    )}
                  </SwiperSlide>
                ):(
                  null
                )
              ))}
            </Swiper>
          </div>
          <div className="category-item-slide">
            <Swiper
              freeMode={true}
              lazy={true}
              mousewheel={{
                forceToAxis: true,
                enabled: true,
              }}
              modules={[FreeMode,Lazy,Mousewheel]}
              spaceBetween={10}
              breakpoints={{
                0: {
                  slidesPerView: 2.5,

                }
              }}
            >
              {this.props.data.map((val, key) => (
                key>=3?(
                  <SwiperSlide key={key}>
                    {val.trailer_media==6 ? (
                      <a href={`/ebook/${val.slug}`}>
                        <div className="card-img">
                          {this.props.display=='zoom' ? (
                            <div className="img-logo-zoom small cursor certificate-img-list">
                              <Image src="/assets/images/zoom.png" alt="" 
                              layout="fill"
                              objectFit="cover"
                              objectPosition="center"
                              />
                            </div>
                          ):(null)}
                          <div className="img-thumb cursor-pointer swiper-lazy certificate-img-list">
                            {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                              <Image data-src={val.image_th} src={val.image_th} alt={val.title_th} 
                                layout="fill"
                                objectFit="cover"
                                objectPosition="center"
                              />
                            ):null}
                          </div>
                          <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                        </div>
                      </a>
                    ):(
                      <a href={`/course/${val.slug}`}>
                        <div className="card-img">
                          {this.props.display=='zoom' ? (
                            <div className="img-logo-zoom small cursor certificate-img-list">
                              <Image src="/assets/images/zoom.png" alt="" 
                              layout="fill"
                              objectFit="cover"
                              objectPosition="center"
                              />
                            </div>
                          ):(null)}
                          <div className="img-thumb cursor-pointer swiper-lazy certificate-img-list">
                            {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                              <Image data-src={val.image_th} src={val.image_th} alt={val.title_th} 
                                layout="fill"
                                objectFit="cover"
                                objectPosition="center"
                              />
                            ):null}
                          </div>
                          <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                        </div>
                      </a>
                    )}
                  </SwiperSlide>
                ):(
                  null
                )
              ))}
            </Swiper>
          </div>
        </div>
      );
    }
  }
}

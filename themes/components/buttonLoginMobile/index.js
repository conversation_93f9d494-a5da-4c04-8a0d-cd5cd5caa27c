import React, { useState, useContext } from "react";
import Link from "next/link";
import Image from "next/image";
import FormPopLogin from "../formPopLogin";
import FormPopLoginEn from "../formPopLoginEn";
import FormPopPass from "../formPopPass";
import Router, { useRouter } from "next/router";
import AppContext from "/libs/contexts/AppContext";

import {
  Menu,
  Button,
  Modal,
  Input,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";

const Index = () => {
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const [lang, setLang] = useState(locale);
  const appContext = useContext(AppContext);
  const [openForgot, setOpenForgot] = useState(false);

  function setPopup(_type) {
    if (_type == "forgot") {
      setOpenForgot(true);
      appContext.setOpen(false);
    } else if (_type == "login") {
      setOpenForgot(false);
      appContext.setOpen(true);
    } else {
      setOpenForgot(false);
      appContext.setOpen(false);
    }
  }

  return (
    <div>
      <div className="f-mobile-action">
        <button className="btn-action" onClick={() => appContext.setOpen(true)}>
          <Image className="avatar ItemsImgsize" src="/assets/images/user-key.png" alt="" 
          layout="fill"
          objectFit="contain"
          />
          <span className="name">Login / Register</span>
        </button>
      </div>

      <Modal
        className="modalLogin"
        onClose={() => appContext.setOpen(false)}
        onOpen={() => appContext.setOpen(true)}
        open={appContext.open}
      >
        <Modal.Content className="modalLoginContent">
          <div className="bock-modal-login">
            <div className="inner">
              <div className="modal-login-bg">
                <Image className="bg" src="/assets/images/bg-login.jpg" 
                layout="fill"
                alt=""
                />
                <div className="group-login-tabs">
                  <button className="login-tab active">
                    {lang == "en" ? <span>Singin</span> : <span>เข้าระบบ</span>}
                  </button>

                  {lang == "en" ? (
                    <a href={"/en/register"}>
                      <button className="login-tab">
                        <span>Register</span>
                      </button>
                    </a>
                  ) : (
                    <a href={"/register"}>
                      <button className="login-tab">
                        <span>สมัครสมาชิก</span>
                      </button>
                    </a>
                  )}
                  {lang=='th'?(
                    <button className="login-tab inter-login-btn" onClick={()=>setLang('en')}>
                      <span>International<br></br>Visitor</span>
                    </button>
                  ):null}
                </div>
              </div>
              <div className="modal-login-fm">
                {lang=='th'?(
                  <FormPopLogin
                    callback={setPopup}
                    name="input_password_pop_m"
                  ></FormPopLogin>
                ):(
                  <FormPopLoginEn
                    callback={setPopup}
                    name="input_password_pop_m"
                  ></FormPopLoginEn>
                )}
              </div>
            </div>
          </div>
        </Modal.Content>
      </Modal>
      <Modal
        className="modalLogin"
        onClose={() => setOpenForgot(false)}
        onOpen={() => setOpenForgot(true)}
        open={openForgot}
      >
        <Modal.Content className="modalLoginContent">
          <div className="bock-modal-login">
            <div className="inner">
              <div className="modal-login-bg">
                <Image className="bg" src="/assets/images/bg-login.jpg" 
                  layout="fill"
                  alt=""
                />
                <div className="group-login-tabs">
                  <button
                    className="login-tab active"
                    onClick={() => {
                      setPopup("login");
                    }}
                  >
                    {lang == "en" ? <span>Singin</span> : <span>เข้าระบบ</span>}
                  </button>
                  {lang == "en" ? (
                    <a href={"/en/register"}>
                      <button className="login-tab">
                        <span>Register</span>
                      </button>
                    </a>
                  ) : (
                    <a href={"/register"}>
                      <button className="login-tab">
                        <span>สมัครสมาชิก</span>
                      </button>
                    </a>
                  )}
                </div>
              </div>
              <div className="modal-login-fm">
                <FormPopPass callback={setPopup}></FormPopPass>
              </div>
            </div>
          </div>
        </Modal.Content>
      </Modal>
    </div>
  );
};

export default Index;

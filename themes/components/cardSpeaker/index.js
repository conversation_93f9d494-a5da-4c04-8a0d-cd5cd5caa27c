import React, { Component } from "react";
import Link from "next/link";
import Image from "next/image";

export default class index extends Component {
  render() {
    return (
      <div className="card card-news">
        <div className="inner cursor">
          <a href={`/category/knowledge?speaker=${this.props.data.id}`}>
            <div className="card-news-inner">
                <div className="card-news-img">
                  {this.props.data && this.props.data.image && this.props.data.image!=null && this.props.data.image!='' && this.props.data.image!='null' ?(
                    <div className="blockImgSpeakerSize">
                      <Image
                        className="news-thumb swiper-lazy ItemsImgSpeakerSize"
                        src={this.props.data.image}
                        alt={this.props.data.image}
                        layout='fill'
                        objectFit='contain'
                      />
                    </div>
                  ):(
                    <div className="blockImgSpeakerSize">
                      <Image className="news-thumb swiper-lazy ItemsImgSpeakerSize" data-src="/assets/images/scbx-blog-list-11.jpg" alt={this.props.data.title_th} 
                      layout="fill"
                      objectFit="contain"
                      />
                    </div>
                  )}
                  <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                </div>
                <div className="card-news-description">
                  <h3>
                    {this.props.data.title_th}
                  </h3>
                </div>
            </div>
          </a>
        </div>
      </div>
    );
  }
}

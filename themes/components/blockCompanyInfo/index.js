import React, { Component } from "react";
import NumberFormat from "react-number-format";
import Link from "next/link";
import Image from "next/image";

export default class index extends Component {
  render() {
    return (
      <div className="block-profile-my-info company">
        <div className="my-info">
          <div className="info-avatar">
            <div className="inner">
              {this.props.data.company_image && this.props.data.company_image!=null && this.props.data.company_image != "" && this.props.data.company_image!='null' ? (
                <div className="blockProfileInfo">
                  <Image src={this.props.data.company_image} alt="" 
                    layout="fill"
                    objectFit="contain"
                    className="ItemsProfileInfo"
                  />
                </div>
              ) : (null
                // <div className="blockProfileInfo">
                //   <Image src="/assets/images/user-key.png" alt="" 
                //     layout="fill"
                //     objectFit="contain"
                //     className="ItemsProfileInfo"
                //   />
                // </div>
              )}
            </div>
          </div>
          <div className="info-detail">
            <div className="title-name">
              <h3>
                <span>
                  {this.props.data.company_name}
                </span>
              </h3>
            </div>
            <div className="title-list">
              {/* ****************** */}
              <div className="list-item green">
                <p className="name">
                  <span>ข้อมูลบริษัท</span>
                </p>
                <p className="mail">{this.props.data.company_title}</p>
              </div>
              <div className="list-item green">
                <p className="name">
                  <span>ข้อมูลเพิ่มเติมบริษัท</span>
                </p>
                <p className="mail">{this.props.data.company_subtitle}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

import React, { Component } from "react";
import NumberFormat from "react-number-format";
import Link from "next/link";
import Image from "next/image";

export default class index extends Component {
  render() {
    return (
      <div className="block-profile-my-info">
        <div className="my-info">
          <div className="info-avatar">
            <div className="inner">
              {this.props.user.avatar && this.props.user.avatar != "" && this.props.user.avatar != null && this.props.user.avatar != "null" ? (
                <div className="blockProfileInfo">
                  <Image src={this.props.user.avatar} alt="" 
                    layout="fill"
                    objectFit="contain"
                    className="ItemsProfileInfo"
                  />
                </div>
              ) : (
                <div className="blockProfileInfo">
                  <Image src="/assets/images/user-key.png" alt="" 
                    layout="fill"
                    objectFit="contain"
                    className="ItemsProfileInfo"
                  />
                </div>
              )}
              <Link href={"/profile/edit"}>
                <button className="btn-edit-img">
                  <i className="icon-ic-image"></i>
                </button>
              </Link>
            </div>
          </div>
          <div className="info-detail">
            <div className="title-name">
              <h3>
                <span>
                  {this.props.user.name} {this.props.user.lastname}
                </span>
              </h3>
              <Link href={"/profile/edit"}>
                <button className="btn-edit-info">
                  <i className="icon-ic-edit"></i>
                </button>
              </Link>
              <div className="verify-icon">
                {this.props.user.medical_status!=2?(
                  <div className="icon-box">
                    <i className="user md icon"></i><span className="icon-ic-tick"></span>
                  </div>
                ):null}
                {this.props.user.internal_status==1||this.props.user.internal_status==2?(
                  <div className="icon-box">
                    <i className="hospital icon"></i><span className="icon-ic-tick"></span>
                  </div>
                ):null}
              </div>
            </div>
            <div className="title-list">
              {/* ****************** */}
              <div className="list-item green">
                <p className="name">
                  <i className="icon-ic-mail"></i>
                  <span>อีเมล์</span>
                </p>
                <p className="mail">{this.props.user.email}</p>
              </div>
              {/* ****************** */}
              <div className="list-item green">
                <p className="name2">
                  <i className="icon-ic-pc-course"></i>
                  <span>จำนวนคอร์สที่มี</span>
                </p>
                <p className="course">
                  <span>
                    <NumberFormat
                      value={this.props.count}
                      displayType={"text"}
                      thousandSeparator={true}
                    />
                  </span>{" "}
                  คอร์ส
                </p>
              </div>
              {/* ****************** */}
              {this.props.user.medical_status!=2?(
              <div className="list-item pink">
                <p className="name2">
                  <i className="icon-ic-coin-point"></i>
                  <span>คะแนน CME</span>
                </p>
                <p className="course">
                  <span>
                    <NumberFormat
                      value={this.props.user.point}
                      displayType={"text"}
                      thousandSeparator={true}
                    />
                  </span>{" "}
                  คะแนน
                </p>
              </div>
              ):null}
              <div className="list-item pink">
                <p className="name2">
                  <i className="icon-ic-tick-thanks"></i>
                  <span>คะแนนสะสม</span>
                </p>
                <p className="course">
                  <span>
                    <NumberFormat
                      value={this.props.user.reward_point}
                      displayType={"text"}
                      thousandSeparator={true}
                    />
                  </span>{" "}
                  คะแนน
                </p>
              </div>
              {/* ****************** */}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

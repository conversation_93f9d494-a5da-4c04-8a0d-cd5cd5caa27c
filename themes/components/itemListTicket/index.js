import React, { Component } from "react";

import styles from "/public/assets/css/component/itemListLive.module.css";

import Link from "next/link";
import Image from "next/image";

export default class index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0
    };
  }
  componentDidMount() {
    // Set the target date for the countdown
    const targetDate = new Date(this.props.data.started_time).getTime();

    this.interval = setInterval(() => {
      const now = new Date().getTime();
      const timeDifference = targetDate - now;

      if (timeDifference <= 0) {
        // Countdown has ended
        clearInterval(this.interval);
        this.setState({
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0
        });
      } else {
        // Calculate days, hours, minutes, and seconds remaining
        const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
        const hours = Math.floor(
          (timeDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
        );
        const minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDifference % (1000 * 60)) / 1000);

        this.setState({ days, hours, minutes, seconds });
      }
    }, 1000); // Update every second
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }
  render() {
    const { days, hours, minutes, seconds } = this.state;
    return (
      <div className={`${styles.item_list_course}`}>
        <div className={`${styles.item_course_inner}`}>
          <div className={`${styles.item_course_img}`}>
            <div className={`${styles.blockLisCourseImgSize}`}>
            {this.props.data && this.props.data.image_th && this.props.data.image_th!=null && this.props.data.image_th!='' && this.props.data.image_th!='null' ?(
              <Image className={`${styles.thumb}`} src={this.props.data.image_th} 
                layout="fill"
                objectFit="cover"
                alt=""
              />
            ):null}
            </div>
          </div>
          <div className={`${styles.item_course_text}`}>
            <h3>{this.props.data.title_th}</h3>
            <h4>{this.props.data.subtitle_th}</h4>
          </div>
          <div className={`${styles.item_live_action}`}>
            <div className={`${styles.item_action_time}`}>
              <p>
                {this.props.data.buy_date}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

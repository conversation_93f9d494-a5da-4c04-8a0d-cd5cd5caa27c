import React, { Component, useState} from "react";
import Link from "next/link";
import Image from "next/image";
import { findDOMNode } from 'react-dom'
import ReactPlayer from "react-player";
import styles from "/public/assets/css/component/listVdoModal.module.css";
import {
  Menu,
  Button,
  Modal,
  Input,
  Icon,
  Dropdown,
  Label,
  List,
} from "semantic-ui-react";
import "semantic-ui-css/semantic.min.css";

import CardCourse from "../cardCourse";
import CardLock from "../cardLock";
import Swal from "sweetalert2";
import screenfull from 'screenfull';

function convertSecond(second) {
  if (second != null && second != "") {
    var hours = parseInt(
      new Date(second * 1000).toISOString().substring(11, 13)
    );
    var minutes = parseInt(
      new Date(second * 1000).toISOString().substring(14, 16)
    );
    var seconds = parseInt(
      new Date(second * 1000).toISOString().substring(17, 19)
    );
    if(hours==0){
      return minutes+'m';
    }else{
      return hours + "h:" + minutes+'m';
    }
    
  } else {
    return '0m';
  }
}
export default class index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      keySelect: this.props.keySelect,
      vdoList: this.props.vdoList,
      reloadVdo: false,
      check: 0,
      current_sec: 9999999,
    };
  }

  selectVdo(_keySelect) {
    if(this.state.vdoList[_keySelect]["ep_oculus"]==2){
      this.props.genOculus(this.state.vdoList[_keySelect]["id"]);
    }else{
      location.href="/course/"+this.props.slug+'?ep='+_keySelect;
      // this.setState({
      //   reloadVdo: true,
      // });
      // this.setState({
      //   keySelect: _keySelect,
      // });

      // setTimeout(() => {
      //   this.setState({
      //     reloadVdo: false,
      //   });
      // }, "500");
    }
  }

  checkLock(){
    if(this.state.vdoList[this.state.keySelect]["lock"]&&this.state.keySelect!=0){
      if(this.state.check==0){
        Swal.fire({
          text: "กรุณาทำแบบทดสอบก่อนเรียนบทถัดไป",
          icon: "error",
          confirmButtonText: "ปิด",
          confirmButtonColor: "#648d2f"
        }).then((result) => {
          
        });
        this.state.check=1;
      }
    }
  }

  skipVdo(_keySelect,_id) {
    this.props.skipVdo(_id);

    if(this.state.vdoList[_keySelect]["ep_oculus"]==2){
      this.props.genOculus(this.state.vdoList[_keySelect]["id"]);
    }else{
      this.setState({
        reloadVdo: true,
      });
      this.setState({
        keySelect: _keySelect,
      });
  
      setTimeout(() => {
        this.setState({
          reloadVdo: false,
        });
      }, "500");
    }
  }
  render() {
    setTimeout(() => {
      this.checkLock()
    }, "0");
    return (
      <div className={`${styles.modalBackground}`}>
        <div className={`${styles.vdo_left}`} onClick={() => {this.props.callback(false)}}></div>
        <div
          className={`${styles.modalVdo}`}
        >
          <div className={`${styles.vdo_top}`} onClick={() => {this.props.callback(false)}}></div>
          <div className={`${styles.modaVdoContent}`}>
            <div className={`${styles.groupTitle}`}>
              <span></span>
              <p>{this.state.vdoList[this.state.keySelect]["title"]}</p>
            </div>

            <div className={`${styles.blockPlayer}`}>
              {!this.state.reloadVdo ? (
                <div className="modal-video-block">
                  {this.state.vdoList[this.state.keySelect]["lock"] ? (
                    <div className="video-lock">
                      <ReactPlayer
                        playsinline
                        className="video-player"
                        width={680}
                        height={Number(680 / (16 / 9))}
                      />
                      <i className={`icon-ic-lock`}></i>
                    </div>
                  ):(
                    <ReactPlayer
                      playsinline
                      ref={player => { this.player = player }}
                      playing
                      className="video-player"
                      url={this.state.vdoList[this.state.keySelect]["vdo"]}
                      controls={true}
                      width={680}
                      height={Number(680 / (16 / 9))}
                      onDuration={() => {
                        this.player.seekTo(this.state.vdoList[this.state.keySelect]["watching"],'seconds');
                        this.setState({
                          current_sec: this.state.vdoList[this.state.keySelect]["watching"],
                        });
                      }}
                      progressInterval={3000}
                      onProgress={(progress) => {
                        if(this.state.keySelect == this.state.vdoList.length - 1){
                          this.props.cbProgress(progress,this.state.vdoList[this.state.keySelect]["id"],'last');
                        }else{
                          this.props.cbProgress(progress,this.state.vdoList[this.state.keySelect]["id"],'normal');
                        }
                        this.setState({
                          current_sec: progress.playedSeconds,
                        });
                      }}
                      onEnded={() => {
                        if(this.state.keySelect == this.state.vdoList.length - 1){
                          this.props.cbProgressEnd(this.state.vdoList[this.state.keySelect]["id"],'last');
                        }else{
                          this.props.cbProgressEnd(this.state.vdoList[this.state.keySelect]["id"],'normal');
                        }
                      }}
                    />
                  )}
                  {this.state.vdoList[this.state.keySelect]["skip_intro"] == 1 && this.state.vdoList[this.state.keySelect]["skip_sec"] != 0 
                  && this.state.current_sec < this.state.vdoList[this.state.keySelect]["skip_sec"] ?(
                    <div className="video-skip-intro">
                        <Button onClick={() => {
                          this.player.seekTo(this.state.vdoList[this.state.keySelect]["skip_sec"],'seconds');
                          this.setState({
                            current_sec: this.state.vdoList[this.state.keySelect]["skip_sec"],
                          });
                        }}>
                          ข้าม intro
                        </Button>
                    </div>
                  ):null}
                  <div className="video-nav-control">
                    {this.state.keySelect == 0?(
                      null
                    ):(
                      this.state.vdoList[this.state.keySelect]["is_skip"] == 1 ? (
                        <div className="nav-left">
                          <Button onClick={() => this.selectVdo(this.state.keySelect-1)}>
                            <Icon name='chevron left' />
                          </Button>
                        </div>
                      ):(
                        null
                      )
                    )}
                    {this.state.keySelect == this.state.vdoList.length - 1 ?(
                      null  
                    ):(
                      this.state.vdoList[this.state.keySelect]["is_skip"] == 1 ? (
                        <div className="nav-right">
                          <Button onClick={() => this.skipVdo(this.state.keySelect+1,this.state.vdoList[this.state.keySelect]["id"])}>
                            <Icon name='chevron right' />
                          </Button>
                        </div>
                      ):(
                        null
                      )
                    )}
                  </div>
                </div>
                // <div className="video-fullscreen-btn">
                //   <Button onClick={() => {
                //     console.log(this.player)
                //     screenfull.request(findDOMNode(this.player))
                //   }}>
                //   </Button>
                // </div>
              ) : null}
            </div>
            <div className={`container-fluid  space-between-content`}>
              {this.state.vdoList.map((val, key) =>
                val.lock ? (
                  <div
                    className={`row ${styles.list} 
                  ${this.state.keySelect == key ? styles.active : ""}`}
                    key={key}
                  >
                    <div className="col-4 col-lg-2">
                      <div className="card card-lock radius">
                        <div className="inner">
                            <div className="card-img">
                              <div className="blockImgSize">
                                {val && val.thumb && val.thumb!=null && val.thumb!='' && val.thumb!='null' ?(
                                  <Image className="img-thumb" src={val.thumb} alt="" 
                                    layout="fill"
                                    objectFit="contain"
                                  />
                                ):null}
                              </div>
                              <div className="card-icon lock">
                                <i className={`icon-ic-lock`}></i>
                              </div>
                            </div>
                        </div>
                      </div>
  
                    </div>
                    <div className="col-6 col-lg-8">
                      <p className={`${styles.title}`}>
                        {val.title}
                        <span className={`${styles.description}`}>
                          {val.description}
                        </span>
                      </p>
                    </div>
                    <div className={`col-2 col-lg-2 ${styles.text_right}`}>
                      {val.ep_oculus == 1 ?(
                        <p className={`${styles.calendar}`}>{convertSecond(val.duration)}</p>
                      ):null}
                    </div>
                  </div>
                ) : (
                  <div
                    className={`row ${styles.list} 
                  ${this.state.keySelect == key ? styles.active : ""}`}
                    key={key}
                    onClick={() => this.selectVdo(key)}
                  >
                    <div className="col-4 col-lg-2">
                      <div className="card radius">
                        <div className="inner">
                            <div className="card-img">
                              <div className="blockImgSize">
                                {val && val.thumb && val.thumb!=null && val.thumb!='' && val.thumb!='null' ?(
                                  <Image className="img-thumb ItemsImgSize" src={val.thumb} alt="" 
                                    layout="fill"
                                    objectFit="contain"
                                  />
                                ):null}
                              </div>
                            </div>
                        </div>
                      </div>
                      
                    </div>
                    <div className="col-6 col-lg-8">
                      <p className={`${styles.title}`}>
                        {val.title}
                        <span className={`${styles.description}`}>
                          {val.description}
                        </span>
                      </p>
                    </div>
                    <div className={`col-2 col-lg-2 ${styles.text_right}`}>
                      {val.ep_oculus == 1 ?(
                        <p className={`${styles.calendar}`}>{convertSecond(val.duration)}</p>
                      ):null}
                    </div>
                  </div>
                )
              )}
            </div>
          </div>
          <div className={`${styles.vdo_bottom}`} onClick={() => {this.props.callback(false)}}></div>
        </div>
        <div className={`${styles.vdo_right}`} onClick={() => {this.props.callback(false)}}></div>
      </div>
      
    );
  }
}

import React, { Component } from "react"; 
import Link from "next/link";
import Image from "next/image";
export default class index extends Component {
  render() {
    return (
      this.props.data.map((val, key) => (
        <div key={key} className="col-12 col-md-6 col-lg-3 col-xl-3">
          <div className="card card-news">
            <div className="inner cursor">
              {this.props.lang=='en'?(
                <a href={`/en/article/${val.slug}`}>
                  <div className="card-news-inner">
                      <div className="card-news-img">
                        <div className="BlockImgSize">
                          {val && val.image && val.image!=null && val.image!='' && val.image!='null' ?(
                            <Image className="news-thumb ItemsImgSize" src={val.image} alt={val.title_th} 
                              layout="fill"
                              objectFit="contain"
                            />
                          ):null}
                        </div>
                      </div>
                      <div className="card-news-description">
                        <h3>
                          {val.title_th}
                        </h3>
                        <p>
                          {val.subtitle_th}
                        </p>
                        <div className="detail">
                          <div className="date"><i className="icon-ic-clock"></i> <span>{val.date}</span></div>
                          <a className="more">
                            <span>อ่านเพิ่มเติม</span>
                            <i className="icon-ic-right"></i>
                            <i className="icon-ic-right"></i>
                          </a>
                        </div>
                      </div>
                  </div>
                </a>
              ):(
                <a href={`/article/${val.slug}`}>
                  <div className="card-news-inner">
                      <div className="card-news-img">
                        <div className="BlockImgSize">
                          {val && val.image && val.image!=null && val.image!='' && val.image!='null' ?(
                            <Image className="news-thumb ItemsImgSize" src={val.image} alt={val.title_th} 
                              layout="fill"
                              objectFit="contain"
                            />
                          ):null}
                        </div>
                      </div>
                      <div className="card-news-description">
                        <h3>
                          {val.title_th}
                        </h3>
                        <p>
                          {val.subtitle_th}
                        </p>
                        <div className="detail">
                          <div className="date"><i className="icon-ic-clock"></i> <span>{val.date}</span></div>
                          <a className="more">
                            <span>อ่านเพิ่มเติม</span>
                            <i className="icon-ic-right"></i>
                            <i className="icon-ic-right"></i>
                          </a>
                        </div>
                      </div>
                  </div>
                </a>
              )}
            </div>
          </div>
        </div>
      ))
    );
  }
}

import React, { Component } from "react";
import { render } from "react-dom";
import { Progress } from 'semantic-ui-react'
import styles from "/public/assets/css/component/cardVideo.module.css";
import Image from "next/image";

export default class index extends Component {
  render() {
    return (
      <div className="card card-video ebook-banner">
        <div className="inner">
            <div className="card-img">
              <div className={styles.blockVideoImgSize}>
                {this.props.data && this.props.data.cover && this.props.data.cover!=null && this.props.data.cover!='' && this.props.data.cover!='null' ?(
                  <Image layout="fill" className="img-thumb ItemsVideoImgSize" src={this.props.data.cover} alt="" />
                ):null}
              </div>
            </div>
        </div>
      </div>
    );
  }
}

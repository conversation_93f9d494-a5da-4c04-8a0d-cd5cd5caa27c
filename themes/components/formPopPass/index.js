import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import React from "react";
import { useState, useEffect, useContext } from "react";

import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
  TextArea,
} from "semantic-ui-react";

import Link from "next/link";

import styles from "/public/assets/css/component/formPop.module.css";

import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import AppContext from "/libs/contexts/AppContext";
import Swal from "sweetalert2";

export default function formPopPass(props) {
  const router = useRouter();
  const appContext = useContext(AppContext);
  const { register, setValue, getValues, handleSubmit } = useForm({
    defaultValues: {},
  });
  const [errorArray, setErrorArray] = useState([]);
  const cookies = parseCookies();

  return (
    <div className={`${styles.block_pop_fm}`}>
      <div className={`${styles.inner_pop_fm}`}>
        <div className={`${styles.logo}`}>
          <div className={`${styles.icon}`}>
            <i className="icon-ic-circle-lock-full"></i>
          </div>
        </div>
        {/* ====== */}
        <div className={`${styles.col_fm}`}>
          <div className="item-fm fm-w-icon">
            <i className="icon-ic-f-user"></i>
            <Input
              type="text"
              className="fm-control"
              placeholder="อีเมล์"
            >
              <input
                {...register("email")}
                maxLength={100}
                data-type="email"
                onInput={appContext.diFormPattern}
              />
            </Input>
          </div>
        </div>
        {/* ====== */}
        <div className={`${styles.col_fm_btn}`}>
          <button
            className={`${styles.btn_submit}`}
            onClick={() => {
              setValue("login_type", "email");
              let obj = getValues();
              let bol = true;
              var errarr = [];
              if (!appContext.isEmail(obj["email"])) {
                bol = false;
                errarr.push("email");
              }
              setErrorArray(errarr);

              if (bol) {
                appContext.loadApi(
                  process.env.NEXT_PUBLIC_API + "/api/user/forgot",
                  obj,
                  (data) => {
                    if (data["status"] == "success") {
                      Swal.fire({
                        text: "ระบบได้ส่งลิงค์ไปยังอีเมลแล้ว",
                        icon: "info",
                        confirmButtonText: "ปิด",
                        confirmButtonColor: "#648d2f"
                      });
                      props.callback("")
                    } else {
                      if (data["status"] == "dup") {
                        Swal.fire({
                          text: "ไม่สามารถส่งลิงค์ได้ภายใน 3 นาที",
                          icon: "error",
                          confirmButtonText: "ปิด",
                          confirmButtonColor: "#648d2f"
                        });
                      } else {
                        Swal.fire({
                          text: "ไม่พบอีเมลในระบบ",
                          icon: "error",
                          confirmButtonText: "ปิด",
                          confirmButtonColor: "#648d2f"
                        });
                      }
                    }
                  }
                );
              } else {
                Swal.fire({
                  text: "กรุณาตรวจสอบข้อมูล",
                  icon: "error",
                  confirmButtonText: "ปิด",
                  confirmButtonColor: "#648d2f"
                });
              }
            }}
          >
            <span>ส่ง</span>
          </button>
        </div>
      </div>
    </div>
  );
}

import React, { Component } from "react";
import { render } from "react-dom";
import { Progress } from 'semantic-ui-react'
import styles from "/public/assets/css/component/cardVideo.module.css";
import Image from "next/image";

export default class index extends Component {
  render() {
    return (
      <div className="card card-video radius">
        <div className="inner">
            <div className="card-img flex-center-img">
              <div className={`card-block-img ${styles.blockVideoImgSize}`}>
                {this.props.data && this.props.data.image && this.props.data.image!=null && this.props.data.image!='' && this.props.data.image!='null' ?(
                  <Image layout="fill" className="img-thumb ItemsVideoImgSize" src={this.props.data.image} alt="" />
                ):null}
              </div>
            </div>
        </div>
      </div>
    );
  }
}

import React, { Component } from "react";

import styles from "/public/assets/css/component/itemListLive.module.css";

import Link from "next/link";
import Image from "next/image";

export default class index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0
    };
  }
  componentDidMount() {
    // Set the target date for the countdown
    const targetDate = new Date(this.props.data.started_time).getTime();

    this.interval = setInterval(() => {
      const now = new Date().getTime();
      const timeDifference = targetDate - now;

      if (timeDifference <= 0) {
        // Countdown has ended
        clearInterval(this.interval);
        this.setState({
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0
        });
      } else {
        // Calculate days, hours, minutes, and seconds remaining
        const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
        const hours = Math.floor(
          (timeDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
        );
        const minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDifference % (1000 * 60)) / 1000);

        this.setState({ days, hours, minutes, seconds });
      }
    }, 1000); // Update every second
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }
  render() {
    const { days, hours, minutes, seconds } = this.state;
    return (
      <div className={`${styles.item_list_course}`}>
        <div className={`${styles.item_course_inner}`}>
          <div className={`${styles.item_course_img}`}>
            <div className={`${styles.blockLisCourseImgSize}`}>
            {this.props.data && this.props.data.image_th && this.props.data.image_th!=null && this.props.data.image_th!='' && this.props.data.image_th!='null' ?(
              <Image className={`${styles.thumb}`} src={this.props.data.image_th} 
                layout="fill"
                objectFit="cover"
                alt=""
              />
            ):null}
            </div>
          </div>
          <div className={`${styles.item_course_text}`}>
            <h3>{this.props.data.title_th}</h3>
            <h4>{this.props.data.subtitle_th}</h4>
          </div>
            {this.props.data.trailer_media == 6 ?(
              <div className={`${styles.item_course_action}`}>
                <a href={`/ebook/${this.props.data.slug}`}>
                  <button className={`${styles.action}`}>
                    <i className="icon-ic-download-1"></i>
                    <span>Download</span>
                  </button>
                </a>
              </div>
            ):(
              this.props.data.trailer_media == 7 ?(
                <div className={`${styles.item_live_action}`}>
                  <div className={`${styles.item_action_title}`}>
                    <p>เข้าชม</p>
                  </div>
                  {new Date (this.props.data.started_time) <= new Date () && new Date (this.props.data.end_time) >= new Date () ?(
                    <a href={`/live/${this.props.data.slug}`}>
                      <button className={`${styles.action} ${styles.active}`}>
                        <div className={`${styles.play_stream}`}>
                          <i className="icon-ic-shape-right"></i>
                        </div>
                        <h3>Live</h3>
                        <p>STREAMING</p>
                      </button>
                    </a>
                  ):(
                    <a href={`/live/${this.props.data.slug}`}>
                      <button className={`${styles.action}`}>
                        <div className={`${styles.play_stream}`}>
                          <i className="icon-ic-shape-right"></i>
                        </div>
                        <h3>Live</h3>
                        <p>STREAMING</p>
                      </button>
                    </a>
                  )}
                  <div className={`${styles.item_action_time}`}>
                    <p>
                      วันที่ Live : {this.props.data.live_date} เวลา {this.props.data.live_time}
                    </p>
                  </div>
                  {new Date (this.props.data.started_time) >= new Date () ?(
                    <div className="live-soon-profile">
                        {/* <div className="countdown-title">
                          <h3>Live In</h3>
                        </div> */}
                        <div className="countdown-time">
                          <div className="time-item">
                            <div className="time-bg">
                              <span>{days.toString().padStart(2, '0')}</span>
                            </div>
                            <h3>DAYS</h3>
                          </div>
                          <div className="time-dot">
                            <span>:</span>
                          </div>
                          <div className="time-item">
                            <div className="time-bg">
                              <span>{hours.toString().padStart(2, '0')}</span>
                            </div>
                            <h3>HOURS</h3>
                          </div>
                          <div className="time-dot">
                            <span>:</span>
                          </div>
                          <div className="time-item">
                            <div className="time-bg">
                              <span>{minutes.toString().padStart(2, '0')}</span>
                            </div>
                            <h3>MINUTES</h3>
                          </div>
                          <div className="time-dot">
                            <span>:</span>
                          </div>
                          <div className="time-item">
                            <div className="time-bg">
                              <span>{seconds.toString().padStart(2, '0')}</span>
                            </div>
                            <h3>SECONDS</h3>
                          </div>
                        </div>
                    </div>
                  ):null}
                </div>
              ):(
                this.props.data.zoom_join_url==null||this.props.data.zoom_join_url=='' ? (
                  <div className={`${styles.item_course_action}`}>
                    <a href={`/course/${this.props.data.slug}`}>
                      <button className={`${styles.action}`}>
                        <i className="icon-ic-play"></i>
                        <span>Play</span>
                      </button>
                    </a>
                    <p className={`${styles.percent}`}>{this.props.data.learned_percent}%</p>
                  </div>
                ):(
                  <div className={`${styles.item_course_action}`}>
                    <a href={`/course/${this.props.data.slug}`}>
                      <button className={`${styles.action}`}>
                        <i className="icon-ic-play"></i>
                        <span>Play</span>
                      </button>
                    </a>
                  </div>
                )
              )
            )}
        </div>
      </div>
    );
  }
}

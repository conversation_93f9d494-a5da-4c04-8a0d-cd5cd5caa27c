import React, { Component } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Zoom, FreeMode, Pagination } from "swiper";

import "swiper/css";
import "swiper/css/pagination";

import styles from "/public/assets/css/component/groupRadio.module.css";

export default class index extends Component {
  render() {
    return (
      <div className={`row ${styles.group_margin}`}>
        <div className="col-12">
          <h2
            className={`${styles.radio_title}`}>
            {this.props.title}
            <div className="item-choose row">
              <div className="fm-check" onClick={() => this.props.callback(1)}>
                <input id={this.props.name} type="radio" name={this.props.name}/>
                <div className="text">
                  <div className="i_remark">
                    <i className="icon-ic-circle" />
                  </div>
                  <p>{this.props.value_1}</p>
                </div>
              </div>
              <div className="fm-check" onClick={() => this.props.callback(2)}>
                <input id={`${this.props.name}2`} type="radio" name={this.props.name}/>
                <div className="text">
                  <div className="i_remark">
                    <i className="icon-ic-circle" />
                  </div>
                  <p>{this.props.value_2}</p>
                </div>
              </div>
            </div>
          </h2>
        </div>
      </div>
    );
  }
}

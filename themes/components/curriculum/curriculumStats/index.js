import React from 'react';
import styles from './curriculumStats.module.css';

function convertSecond(second) {
  if (second != null && second != '') {
    var hours = parseInt(
      new Date(second * 1000).toISOString().substring(11, 13)
    );
    var minutes = parseInt(
      new Date(second * 1000).toISOString().substring(14, 16)
    );

    if (hours == 0) {
      return minutes + ' นาที ';
    } else {
      return hours + ' ชั่วโมง ' + minutes + ' นาที ';
    }
  } else {
    return '0 นาที';
  }
}

const CurriculumStats = ({ curriculumData = null, courseData = null }) => {
  const calculateStats = () => {
    if (!curriculumData || !curriculumData.courses || curriculumData.courses.length === 0) {
      return {
        courseCount: 0,
        totalDuration: '0 นาที',
        completionPercent: 0,
        cme: 0
      };
    }

    const courses = curriculumData.courses;
    const courseCount = courses.length;

    const totalSeconds = courses.reduce((total, course) => {
      return total + (course.course_duration || 0);
    }, 0);
    const totalDuration = convertSecond(totalSeconds);
    
    const completionPercent = curriculumData.data.progress_percent || 0;

    const cme = curriculumData.data.receive_point || 0;

    return {
      courseCount,
      totalDuration,
      completionPercent,
      cme
    };
  };

  const stats = calculateStats();

  return (
    <div className={styles.statsContainer}>
      <div className="row">
        <div className="col-md-3">
          <div className={styles.statsCard}>
            <svg
              className={styles.statsIcon}
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 32 33"
              fill="none"
            >
              <path
                d="M4.66673 11.1668C4.667 9.90265 4.99403 8.66007 5.61609 7.55961C6.23815 6.45914 7.13412 5.53817 8.21705 4.88605C9.29998 4.23394 10.5331 3.87283 11.7967 3.83778C13.0603 3.80273 14.3116 4.09492 15.429 4.68599C16.5464 5.27707 17.4921 6.14696 18.1742 7.21124C18.8563 8.27553 19.2517 9.49807 19.322 10.7602C19.3924 12.0224 19.1353 13.2813 18.5756 14.4148C18.016 15.5483 17.1729 16.5178 16.1281 17.2294C18.3781 18.0547 20.3296 19.5344 21.7316 21.4781C23.1335 23.4218 23.9217 25.7406 23.9947 28.1361C23.9916 28.3944 23.8888 28.6415 23.7079 28.8257C23.5269 29.01 23.2817 29.1172 23.0235 29.1249C22.7653 29.1327 22.5141 29.0404 22.3224 28.8673C22.1307 28.6942 22.0133 28.4537 21.9947 28.1961C21.9153 25.598 20.8275 23.133 18.9616 21.3233C17.0958 19.5136 14.5987 18.5016 11.9994 18.5016C9.40013 18.5016 6.90299 19.5136 5.03717 21.3233C3.17135 23.133 2.08347 25.598 2.00407 28.1961C1.99072 28.4572 1.87564 28.7027 1.68348 28.88C1.49132 29.0573 1.23737 29.1523 0.976031 29.1446C0.714691 29.1369 0.466749 29.0272 0.285321 28.839C0.103894 28.6507 0.00341122 28.3989 0.00540006 28.1374C0.0781352 25.7417 0.866209 23.4226 2.26818 21.4787C3.67015 19.5347 5.62186 18.0548 7.87207 17.2294C6.88417 16.5566 6.0757 15.6525 5.51705 14.5959C4.95839 13.5392 4.66648 12.362 4.66673 11.1668ZM12.0001 5.83343C10.5856 5.83343 9.22902 6.39534 8.22883 7.39553C7.22864 8.39572 6.66673 9.75228 6.66673 11.1668C6.66673 12.5813 7.22864 13.9378 8.22883 14.938C9.22902 15.9382 10.5856 16.5001 12.0001 16.5001C13.4146 16.5001 14.7711 15.9382 15.7713 14.938C16.7715 13.9378 17.3334 12.5813 17.3334 11.1668C17.3334 9.75228 16.7715 8.39572 15.7713 7.39553C14.7711 6.39534 13.4146 5.83343 12.0001 5.83343ZM23.0534 11.1668C22.857 11.1668 22.6641 11.1801 22.4747 11.2068C22.3425 11.2304 22.2069 11.2272 22.076 11.1974C21.9451 11.1675 21.8215 11.1115 21.7127 11.0329C21.6038 10.9542 21.512 10.8544 21.4425 10.7395C21.3731 10.6245 21.3275 10.4968 21.3085 10.3638C21.2895 10.2309 21.2975 10.0955 21.332 9.9657C21.3665 9.83591 21.4267 9.7144 21.5092 9.6084C21.5916 9.50241 21.6946 9.41411 21.8119 9.34877C21.9293 9.28343 22.0585 9.24239 22.1921 9.2281C23.5182 9.03637 24.8704 9.29103 26.0359 9.952C27.2014 10.613 28.114 11.6427 28.6302 12.8792C29.1464 14.1157 29.2367 15.4887 28.8871 16.7821C28.5374 18.0756 27.7676 19.2161 26.6987 20.0241C28.2699 20.7276 29.604 21.8708 30.5399 23.3156C31.4758 24.7605 31.9737 26.4453 31.9734 28.1668C31.9734 28.432 31.868 28.6863 31.6805 28.8739C31.493 29.0614 31.2386 29.1668 30.9734 29.1668C30.7082 29.1668 30.4538 29.0614 30.2663 28.8739C30.0788 28.6863 29.9734 28.432 29.9734 28.1668C29.9739 26.6788 29.4947 25.2303 28.607 24.0362C27.7193 22.842 26.4704 21.9658 25.0454 21.5374L24.3334 21.3241V19.0894L24.8801 18.8108C25.6904 18.4003 26.3388 17.7287 26.7205 16.9044C27.1022 16.0801 27.195 15.1513 26.9838 14.2678C26.7727 13.3843 26.27 12.5977 25.5569 12.0351C24.8437 11.4724 23.9618 11.1665 23.0534 11.1668Z"
                fill="#335991"
              />
            </svg>
            <span className={styles.statsText}>{stats.courseCount} Course</span>
          </div>
        </div>
        <div className="col-md-3">
          <div className={styles.statsCard}>
            <svg
              className={styles.statsIcon}
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 32 33"
              fill="none"
            >
              <g clipPath="url(#clip0_121_2481)">
                <path
                  d="M16 0.5C12.8355 0.5 9.74207 1.43838 7.11088 3.19649C4.4797 4.95459 2.42894 7.45345 1.21793 10.3771C0.00693258 13.3007 -0.309921 16.5177 0.307443 19.6214C0.924806 22.7251 2.44866 25.5761 4.6863 27.8137C6.92394 30.0514 9.77487 31.5752 12.8786 32.1926C15.9823 32.8099 19.1993 32.4931 22.1229 31.2821C25.0466 30.0711 27.5454 28.0203 29.3035 25.3891C31.0616 22.7579 32 19.6645 32 16.5C31.9954 12.2579 30.3082 8.19095 27.3087 5.19136C24.3091 2.19177 20.2421 0.504588 16 0.5V0.5ZM16 29.8333C13.3629 29.8333 10.7851 29.0513 8.5924 27.5863C6.39975 26.1212 4.69078 24.0388 3.68161 21.6024C2.67245 19.1661 2.4084 16.4852 2.92287 13.8988C3.43734 11.3124 4.70722 8.93661 6.57192 7.07191C8.43662 5.20721 10.8124 3.93733 13.3988 3.42286C15.9852 2.90839 18.6661 3.17244 21.1025 4.18161C23.5388 5.19078 25.6212 6.89974 27.0863 9.0924C28.5514 11.2851 29.3333 13.8629 29.3333 16.5C29.3295 20.035 27.9235 23.4242 25.4238 25.9238C22.9242 28.4235 19.535 29.8295 16 29.8333ZM18.6667 16.5C18.6688 16.9686 18.5474 17.4295 18.3147 17.8362C18.082 18.243 17.7462 18.5812 17.3412 18.8169C16.9362 19.0526 16.4762 19.1774 16.0076 19.1787C15.539 19.18 15.0783 19.0579 14.6719 18.8245C14.2656 18.5911 13.9279 18.2547 13.6929 17.8493C13.4579 17.4439 13.3339 16.9837 13.3334 16.5151C13.3328 16.0465 13.4558 15.586 13.6899 15.18C13.9239 14.7741 14.2609 14.437 14.6667 14.2027V9.83333C14.6667 9.47971 14.8072 9.14057 15.0572 8.89053C15.3072 8.64048 15.6464 8.5 16 8.5C16.3536 8.5 16.6928 8.64048 16.9428 8.89053C17.1929 9.14057 17.3333 9.47971 17.3333 9.83333V14.2027C17.7375 14.4351 18.0734 14.7697 18.3075 15.1729C18.5415 15.5761 18.6654 16.0338 18.6667 16.5Z"
                  fill="#638D2E"
                />
              </g>
              <defs>
                <clipPath id="clip0_121_2481">
                  <rect
                    width="32"
                    height="32"
                    fill="white"
                    transform="translate(0 0.5)"
                  />
                </clipPath>
              </defs>
            </svg>
            <span className={styles.statsText}>รวม : {stats.totalDuration}</span>
          </div>
        </div>
        <div className="col-md-3">
          <div className={styles.statsCard}>
            <svg
              className={styles.statsIcon}
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 32 33"
              fill="none"
            >
              <g clipPath="url(#clip0_121_2484)">
                <path
                  d="M16 0.5C7.1635 0.5 0 7.6635 0 16.5C0 25.337 7.1635 32.5 16 32.5C24.837 32.5 32 25.337 32 16.5C32 7.6635 24.837 0.5 16 0.5ZM16 30.5315C8.2805 30.5315 2 24.2195 2 16.4999C2 8.78044 8.2805 2.49994 16 2.49994C23.7195 2.49994 30 8.78047 30 16.4999C30 24.2194 23.7195 30.5315 16 30.5315ZM22.3855 10.6455L12.998 20.092L8.77047 15.8645C8.37997 15.474 7.74697 15.474 7.35597 15.8645C6.96547 16.255 6.96547 16.888 7.35597 17.2785L12.3055 22.2285C12.696 22.6185 13.329 22.6185 13.72 22.2285C13.765 22.1835 13.8035 22.1345 13.8385 22.0835L23.8005 12.06C24.1905 11.6695 24.1905 11.0365 23.8005 10.6455C23.4095 10.255 22.7765 10.255 22.3855 10.6455Z"
                  fill="#638C1C"
                />
              </g>
              <defs>
                <clipPath id="clip0_121_2484">
                  <rect
                    width="32"
                    height="32"
                    fill="white"
                    transform="translate(0 0.5)"
                  />
                </clipPath>
              </defs>
            </svg>
            <span className={styles.statsText}>{stats.completionPercent} % เสร็จสิ้น</span>
          </div>
        </div>
        <div className="col-md-3">
          <div className={styles.statsCard}>
            <span className={styles.statsText}>CME : {stats.cme}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CurriculumStats;
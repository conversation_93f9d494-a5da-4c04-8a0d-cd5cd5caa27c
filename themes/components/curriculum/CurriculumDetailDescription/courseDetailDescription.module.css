.curriculumDetailLabel {
  color: #737473;
  font-family: Kanit;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.curriculumDetailContent {
  color: #737473;
  font-family: Kanit;
  font-size: 16px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
}

.curriculumDetailItem {
  color: #737473;
  font-family: Kanit;
  font-size: 16px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  display: block;
  margin-top: 10px;
  width: 100%;
}

.curriculumDetailItem .label {
  font-weight: 500;
}

.curriculumDetailItem .content {
  font-weight: 300;
}

.curriculumDescription {
  overflow: hidden;
  color: #737473;
  text-overflow: ellipsis;
  white-space: normal;
  font-family: Kanit;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-top: 15px;
  padding-top: 15px;
  display: block;
  width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  line-height: 1.4;
}

.curriculumDetailItem,
.curriculumDescription {
  clear: both;
  float: none;
}

.curriculumRatingInfo {
  color: #737473;
  font-family: Kanit;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  padding-left: 15px;
}

.curriculumLevel {
  color: #737473;
  font-family: Kanit;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  padding-left: 15px;
}

/* =================================================================
   MOBILE RESPONSIVE DESIGN
   ================================================================= */
@media only screen and (max-device-width: 480px) {
  .curriculumLevel {
    color: #737473;
    font-family: Kanit;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding-left: 0px;
  }
}

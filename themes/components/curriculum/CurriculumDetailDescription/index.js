import React, { Component } from 'react'
import Image from 'next/image'
import ReactStars from 'react-rating-stars-component'
import styles from '/public/assets/css/component/courseDetailDescription.module.css'
import curriculumStyles from './courseDetailDescription.module.css'

function convertSecond(second) {
  if (second != null && second != '') {
    var hours = parseInt(
      new Date(second * 1000).toISOString().substring(11, 13)
    )
    var minutes = parseInt(
      new Date(second * 1000).toISOString().substring(14, 16)
    )
    var seconds = parseInt(
      new Date(second * 1000).toISOString().substring(17, 19)
    )
    if (hours == 0) {
      return minutes + 'm'
    } else {
      return hours + 'h:' + minutes + 'm'
    }
  } else {
    return '0m'
  }
}

export default class CurriculumDetailDescription extends Component {
  constructor(props) {
    super(props)
    if (this.props.lang) {
      this.state = {
        lang: this.props.lang,
      }
    } else {
      this.state = {
        lang: 'th',
      }
    }
  }

  translateEng(_value) {
    if (this.state.lang == 'en') {
      if (_value == 'นาที') {
        return 'Minutes'
      } else if (_value == 'ชั่วโมง') {
        return 'Hours'
      } else if (_value == 'วิทยากร') {
        return 'Speaker'
      } else if (_value == 'ผู้เขียน') {
        return 'Writer'
      } else if (_value == 'ภาควิชา') {
        return 'Department'
      } else if (_value == 'วันที่บรรยาย') {
        return 'Lecture date'
      } else if (_value == 'วันที่เผยแพร่') {
        return 'Release date'
      } else if (_value == 'วันที่เรียน') {
        return 'Class date'
      } else {
        return _value
      }
    } else {
      return _value
    }
  }
  
  getLevelText(level) {
    const levelNumber = parseInt(level)
    switch (levelNumber) {
      case 1:
        return 'Beginner'
      case 2:
        return 'Advance'
      case 3:
        return 'Intermediate'
      default:
        return level
    }
  }

  getTagsText(tags) {
    if (!tags || !Array.isArray(tags) || tags.length === 0) {
      return null
    }

    return tags
      .filter((tag) => tag && tag.title)
      .map((tag) => tag.title)
      .join(', ')
  }

  render() {
    const curriculumData = this.props.data
    const courseData = this.props.courseData || {}

    const getData = (curriculumValue, courseValue, defaultValue = null) => {
      if (
        curriculumValue &&
        curriculumValue !== null &&
        curriculumValue !== '' &&
        curriculumValue !== 'null'
      ) {
        return curriculumValue
      }
      if (
        courseValue &&
        courseValue !== null &&
        courseValue !== '' &&
        courseValue !== 'null'
      ) {
        return courseValue
      }
      return defaultValue
    }

    const hasData = (curriculumValue, courseValue) => {
      return getData(curriculumValue, courseValue) !== null
    }

    const hasTags = (curriculumTags, courseTags) => {
      const tags = getData(curriculumTags, courseTags)
      return tags && Array.isArray(tags) && tags.length > 0
    }

    const ratingThisStar = {
      size: 24,
      count: 5,
      color: '#FFC130',
      activeColor: '#FFC130',
      value: getData(curriculumData.rate, courseData.rate, 4.5),
      a11y: false,
      isHalf: true,
      emptyIcon: <i className="icon-ic-star-light" />,
      halfIcon: <i className="icon-ic-star-half-light" />,
      filledIcon: <i className="icon-ic-star" />,
      onChange: (newValue) => {
        if (this.props.callback) {
          this.props.callback(newValue)
        }
      },
    }

    return (
      <div className="block-course-detail-description">
        <div className="item-description-title">
          <h1 className="curriculum">{getData(curriculumData.title, courseData.title, 'หลักสูตร')}</h1>
        </div>

        <div className="item-description curriculum">
          <ReactStars {...ratingThisStar} />
          <div className={curriculumStyles.curriculumRatingInfo}>
            {Number(getData(curriculumData.rate, courseData.rate, 4.5)).toFixed(
              1
            )}{' '}
            ({getData(curriculumData.rating, courseData.rating, 150)} rating)
          </div>
        </div>

        {hasData(curriculumData.level, courseData.level) && (
          <div className={curriculumStyles.curriculumLevel}>
            Level :{' '}
            {this.getLevelText(getData(curriculumData.level, courseData.level))}
          </div>
        )}

        {hasData(curriculumData.speaker_name, courseData.speaker_name) && (
          <div className={curriculumStyles.curriculumDetailItem}>
            <span className={curriculumStyles.label}>
              {this.translateEng('วิทยากร')} :{' '}
            </span>
            <span className={curriculumStyles.content}>
              {getData(curriculumData.speaker_name, courseData.speaker_name)}
            </span>
          </div>
        )}

        {/* {hasData(
          curriculumData.department_name,
          courseData.department_name
        ) && (
          <div className={curriculumStyles.curriculumDetailItem}>
            <span className={curriculumStyles.label}>
              {this.translateEng('ภาควิชา')} :{' '}
            </span>
            <span className={curriculumStyles.content}>
              {getData(
                curriculumData.department_name,
                courseData.department_name
              )}
            </span>
          </div>
        )} */}

        {hasTags(curriculumData.tag, courseData.tag) && (
          <div className={curriculumStyles.curriculumDetailItem}>
            <span className={curriculumStyles.label}>
              {this.translateEng('ภาควิชา')} :{' '}
            </span>
            <span className={curriculumStyles.content}>
              {this.getTagsText(getData(curriculumData.tag, courseData.tag))}
            </span>
          </div>
        )}

        {hasData(
          curriculumData.publish_date_th,
          courseData.publish_date_th
        ) && (
          <div className={curriculumStyles.curriculumDetailItem}>
            <span className={curriculumStyles.label}>
              {this.translateEng('วันที่เผยแพร่')} :{' '}
            </span>
            <span className={curriculumStyles.content}>
              {getData(
                curriculumData.publish_date_th,
                courseData.publish_date_th
              )}
            </span>
          </div>
        )}

        {hasData(curriculumData.description, courseData.description) && (
          <div
            className={curriculumStyles.curriculumDescription}
            dangerouslySetInnerHTML={{
              __html: getData(
                curriculumData.description,
                courseData.description
              ),
            }}
          ></div>
        )}
      </div>
    )
  }
}

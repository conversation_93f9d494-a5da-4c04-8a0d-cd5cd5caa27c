/* Curriculum Action Button Styles */
.curriculumActionButton {
  padding: 9px 9px;
  margin-top: 10px;
  border-radius: 8px;
  text-align: center;
  font-family: 'Kanit', sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  border: 2px solid;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 50px;
}

/* เริ่มเรียน button */
.startButton {
  border-color: #638D2E;
  color: #fff;
  background-color: #638D2E;
}

.startButton:hover {
  background-color: #638D2E;
  color: white;
}

/* ทบทวน button */
.reviewButton {
  border-color: #E3E3E4;
  color: #555;
  background-color: #E3E3E4;
}

.reviewButton:hover {
  border-color: #E3E3E4;
  background-color: #E3E3E4;
}

/* ล็อค button */
.lockedButton {
  border-color: #B0B6BE;
  color: #fff;
  background-color: #B0B6BE;
  cursor: not-allowed;
  pointer-events: none;
   min-width: 70px;
}

.lockedButton:hover {
  border-color: #B0B6BE;
  background-color: #B0B6BE;
  color: #fff;
}

/* Responsive design */
@media (max-width: 768px) {
  .curriculumActionButton {
    font-size: 12px;
    min-width: 30px;
  }
}
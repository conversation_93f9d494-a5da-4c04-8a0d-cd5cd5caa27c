import React, { Component } from 'react'
import ReactStars from 'react-rating-stars-component'
import { render } from 'react-dom'
import Link from 'next/link'
import Image from 'next/image'
import NumberFormat from 'react-number-format'
import { Pop<PERSON>, <PERSON>, But<PERSON> } from 'semantic-ui-react'
import styles from './cardCurriculum.module.css'

function convertSecond(second) {
  if (second != null && second != '') {
    var hours = parseInt(
      new Date(second * 1000).toISOString().substring(11, 13)
    )
    var minutes = parseInt(
      new Date(second * 1000).toISOString().substring(14, 16)
    )
    var seconds = parseInt(
      new Date(second * 1000).toISOString().substring(17, 19)
    )
    if (hours == 0) {
      return minutes + 'm'
    } else {
      return hours + 'h:' + minutes + 'm'
    }
  } else {
    return '0m'
  }
}

const truncate = (input, limit) => {
  if (input && input.length > limit) return input.substring(0, limit) + ' ...'
  else return input
}

export default class CardCurriculum extends Component {
  constructor(props) {
    super(props)
    if (this.props.lang) {
      this.state = {
        lang: this.props.lang,
      }
    } else {
      this.state = {
        lang: 'th',
      }
    }
  }

  translateEng(_value) {
    if (this.state.lang == 'en') {
      if (_value == 'คณะวิทยากร') {
        return 'Faculty of speakers'
      } else if (_value == 'วิทยากร') {
        return 'Speaker'
      } else if (_value == 'คณะผู้เขียน') {
        return 'Faculty of writers'
      } else if (_value == 'ผู้เขียน') {
        return 'Writers'
      } else if (_value == 'บทเรียน') {
        return 'Lessons'
      } else if (_value == 'ฟรี') {
        return 'Free'
      } else if (_value == 'นาที') {
        return 'Minutes'
      } else if (_value == 'ชั่วโมง') {
        return 'Hours'
      } else if (_value == 'คะแนน') {
        return 'Points'
      } else if (_value == 'เข้าเรียน') {
        return 'Attend course'
      } else if (_value == 'ดาวน์โหลด') {
        return 'Download'
      } else if (_value == 'ปกติ') {
        return 'Normal'
      } else if (_value == 'ราคา') {
        return 'Price'
      } else if (_value == 'บาท') {
        return 'Baht'
      } else if (_value == 'ดูรายละเอียดเพิ่มเติม') {
        return 'See more details'
      } else {
        return _value
      }
    } else {
      return _value
    }
  }

  render() {
    const ratingThisStar = {
      size: 24,
      count: 5,
      edit: false,
      color: '#FFC130',
      activeColor: '#FFC130',
      value: this.props.data.rate,
      a11y: false,
      isHalf: true,
      emptyIcon: <i className="icon-ic-star-light" />,
      halfIcon: <i className="icon-ic-star-half-light" />,
      filledIcon: <i className="icon-ic-star" />,
    }

    const isCurriculum = this.props.isCurriculum || false
    const isAccessible =
      this.props.isAccessible !== undefined ? this.props.isAccessible : true
    const progressPercent =
      this.props.progressPercent || this.props.data.progress_percent || 0

    const isCompleted = this.props.data.progress_percent >= 100
    const isLocked =
      this.props.data.allowed === false ||
      this.props.data.allowed === undefined ||
      this.props.data.allowed === null

    const buttonIcon = isLocked ? (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="18"
        height="19"
        viewBox="0 0 18 19"
        fill="none"
      >
        <path
          d="M14.25 8.375H13.5V6.875C13.5 4.25888 11.366 2.125 8.75 2.125C6.13388 2.125 4 4.25888 4 6.875V8.375H3.25C2.42188 8.375 1.75 9.04688 1.75 9.875V15.125C1.75 15.9531 2.42188 16.625 3.25 16.625H14.25C15.0781 16.625 15.75 15.9531 15.75 15.125V9.875C15.75 9.04688 15.0781 8.375 14.25 8.375ZM5.5 6.875C5.5 5.08725 6.96225 3.625 8.75 3.625C10.5377 3.625 12 5.08725 12 6.875V8.375H5.5V6.875Z"
          fill="white"
        />
      </svg>
    ) : isCompleted ? (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="18"
        height="19"
        viewBox="0 0 18 19"
        fill="none"
      >
        <mask
          id="mask0_340_1525"
          style={{ maskType: 'luminance' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="1"
          width="18"
          height="17"
        >
          <path
            d="M9.00001 17C9.9851 17.0012 10.9607 16.8078 11.8708 16.4308C12.7809 16.0538 13.6076 15.5007 14.3033 14.8033C15.0007 14.1076 15.5538 13.2809 15.9308 12.3708C16.3078 11.4607 16.5012 10.4851 16.5 9.50001C16.5012 8.51491 16.3078 7.53929 15.9308 6.62919C15.5538 5.71909 15.0007 4.89245 14.3033 4.19676C13.6076 3.49932 12.7809 2.94621 11.8708 2.56922C10.9607 2.19223 9.9851 1.99879 9.00001 2.00001C8.01491 1.99879 7.03929 2.19223 6.12919 2.56922C5.21909 2.94621 4.39245 3.49932 3.69676 4.19676C2.99932 4.89245 2.44621 5.71909 2.06922 6.62919C1.69223 7.53929 1.49879 8.51491 1.50001 9.50001C1.49879 10.4851 1.69223 11.4607 2.06922 12.3708C2.44621 13.2809 2.99932 14.1076 3.69676 14.8033C4.39245 15.5007 5.21909 16.0538 6.12919 16.4308C7.03929 16.8078 8.01491 17.0012 9.00001 17Z"
            fill="white"
            stroke="white"
            strokeWidth="2"
            strokeLinejoin="round"
          />
          <path
            d="M6 9.5L8.25 11.75L12.75 7.25"
            stroke="black"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </mask>
        <g mask="url(#mask0_340_1525)">
          <path d="M0 0.5H18V18.5H0V0.5Z" fill="#555555" />
        </g>
      </svg>
    ) : (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="21"
        viewBox="0 0 20 21"
        fill="none"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10 18.5C12.1217 18.5 14.1566 17.6571 15.6569 16.1569C17.1571 14.6566 18 12.6217 18 10.5C18 8.37827 17.1571 6.34344 15.6569 4.84315C14.1566 3.34285 12.1217 2.5 10 2.5C7.87827 2.5 5.84344 3.34285 4.34315 4.84315C2.84285 6.34344 2 8.37827 2 10.5C2 12.6217 2.84285 14.6566 4.34315 16.1569C5.84344 17.6571 7.87827 18.5 10 18.5ZM9.555 7.668C9.4044 7.56752 9.22935 7.50981 9.04852 7.50103C8.86769 7.49224 8.68786 7.53272 8.52823 7.61813C8.3686 7.70354 8.23516 7.83068 8.14213 7.98599C8.04909 8.1413 7.99997 8.31896 8 8.5V12.5C7.99997 12.681 8.04909 12.8587 8.14213 13.014C8.23516 13.1693 8.3686 13.2965 8.52823 13.3819C8.68786 13.4673 8.86769 13.5078 9.04852 13.499C9.22935 13.4902 9.4044 13.4325 9.555 13.332L12.555 11.332C12.692 11.2407 12.8043 11.117 12.8819 10.9718C12.9596 10.8267 13.0002 10.6646 13.0002 10.5C13.0002 10.3354 12.9596 10.1733 12.8819 10.0282C12.8043 9.88304 12.692 9.75932 12.555 9.668L9.555 7.668Z"
          fill="white"
        />
      </svg>
    )

    const buttonText = isLocked
      ? 'ล็อค'
      : isCompleted
      ? this.translateEng('ทบทวน')
      : this.translateEng('เริ่มเรียน')
    const buttonClass = isCompleted ? 'review-button' : 'start-button'

    return (
      //ให้ กางออกตลอด ลบ .card-course-hover
      <div
        className={`card card-course card-course-hover ${
          this.props.data.trailer_media == 6 ? 'card-course-ebook' : ''
        }`}
      >
        <div className="inner">
          <div className="card-img">
            {this.props.data.receive_point != null &&
            this.props.data.receive_point != '' &&
            this.props.data.receive_point != 0 &&
            this.props.data.receive_point != '0' &&
            this.props.data.receive_point != 'null' &&
            this.props.data.receive_point > 0 ? (
              <div className="cme-point">
                <Image
                  className="cme-icon"
                  src="/assets/images/cme-icon.png"
                  alt=""
                  layout="fill"
                  objectFit="contain"
                />
                <NumberFormat
                  value={this.props.data.receive_point}
                  displayType={'text'}
                  thousandSeparator={true}
                  renderText={(value, props) => <span {...props}>{value}</span>}
                />
              </div>
            ) : null}

            {this.props.data.trailer_media == 6 ? (
              !isLocked ? (
                <a
                  {...(this.props.data.link_out != null &&
                  this.props.data.link_out !== ''
                    ? { target: '_blank', rel: 'noreferrer' }
                    : {})}
                  href={`${
                    this.props.data.link_out != null &&
                    this.props.data.link_out != ''
                      ? this.props.data.link_out
                      : '/ebook/' + this.props.data.slug
                  }`}
                  onClick={(e) => {
                if (this.props.isCurriculum) {
                  e.preventDefault()

                  if (typeof window !== 'undefined') {
                    const currentUrl = window.location.pathname
                    localStorage.setItem('curriculum_return_url', currentUrl)
                    localStorage.setItem(
                      'curriculum_return_timestamp',
                      Date.now().toString()
                    )
                  }

                  if (this.props.callback) {
                    this.props.callback(
                      'course',
                      this.props.data.id,
                      this.props.data
                    )
                  }
                }
              }}
                >
                  <div className="d-none d-xl-block BlockImgSize Fixblock">
                    {this.props.data &&
                    this.props.data.image_th &&
                    this.props.data.image_th != null &&
                    this.props.data.image_th != '' &&
                    this.props.data.image_th != 'null' ? (
                      <Image
                        className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                        src={this.props.data.image_th}
                        alt={this.props.data.image_th}
                        layout="fill"
                        objectFit="contain"
                      />
                    ) : (
                      <Image
                        className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                        src={'/assets/images/course_10.jpg'}
                        layout="fill"
                        objectFit="contain"
                        alt=""
                      />
                    )}
                  </div>
                </a>
              ) : (
                <div className="d-none d-xl-block BlockImgSize Fixblock">
                  {this.props.data &&
                  this.props.data.image_th &&
                  this.props.data.image_th != null &&
                  this.props.data.image_th != '' &&
                  this.props.data.image_th != 'null' ? (
                    <Image
                      className="img-thumb d-none d-xl-block swiper-lazy ItemsImgsize"
                      src={this.props.data.image_th}
                      alt={this.props.data.image_th}
                      layout="fill"
                      objectFit="contain"
                    />
                  ) : (
                    <Image
                      className="img-thumb d-none d-xl-block swiper-lazy ItemsImgsize"
                      src={'/assets/images/course_10.jpg'}
                      layout="fill"
                      objectFit="contain"
                      alt=""
                    />
                  )}
                </div>
              )
            ) : this.props.data.trailer_media == 7 ? (
              !isLocked ? (
                <a
                  {...(this.props.data.link_out != null &&
                  this.props.data.link_out !== ''
                    ? { target: '_blank', rel: 'noreferrer' }
                    : {})}
                  href={`${
                    this.props.data.link_out != null &&
                    this.props.data.link_out != ''
                      ? this.props.data.link_out
                      : '/live/' + this.props.data.slug
                  }`}
                  onClick={(e) => {
                if (this.props.isCurriculum) {
                  e.preventDefault()

                  if (typeof window !== 'undefined') {
                    const currentUrl = window.location.pathname
                    localStorage.setItem('curriculum_return_url', currentUrl)
                    localStorage.setItem(
                      'curriculum_return_timestamp',
                      Date.now().toString()
                    )
                  }

                  if (this.props.callback) {
                    this.props.callback(
                      'course',
                      this.props.data.id,
                      this.props.data
                    )
                  }
                }
              }}
                >
                  <div className="d-none d-xl-block BlockImgSize Fixblock">
                    {this.props.data &&
                    this.props.data.image_th &&
                    this.props.data.image_th != null &&
                    this.props.data.image_th != '' &&
                    this.props.data.image_th != 'null' ? (
                      <Image
                        className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                        src={this.props.data.image_th}
                        alt={this.props.data.image_th}
                        layout="fill"
                        objectFit="contain"
                      />
                    ) : (
                      <Image
                        className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                        src={'/assets/images/course_10.jpg'}
                        layout="fill"
                        objectFit="contain"
                        alt=""
                      />
                    )}
                  </div>
                </a>
              ) : (
                <div className="d-none d-xl-block BlockImgSize Fixblock">
                  {this.props.data &&
                  this.props.data.image_th &&
                  this.props.data.image_th != null &&
                  this.props.data.image_th != '' &&
                  this.props.data.image_th != 'null' ? (
                    <Image
                      className="img-thumb d-none d-xl-block swiper-lazy ItemsImgsize"
                      src={this.props.data.image_th}
                      alt={this.props.data.image_th}
                      layout="fill"
                      objectFit="contain"
                    />
                  ) : (
                    <Image
                      className="img-thumb d-none d-xl-block swiper-lazy ItemsImgsize"
                      src={'/assets/images/course_10.jpg'}
                      layout="fill"
                      objectFit="contain"
                      alt=""
                    />
                  )}
                </div>
              )
            ) : !isLocked ? (
              <a
                {...(this.props.data.link_out != null &&
                this.props.data.link_out !== ''
                  ? { target: '_blank', rel: 'noreferrer' }
                  : {})}
                href={`${
                  this.props.data.link_out != null &&
                  this.props.data.link_out != ''
                    ? this.props.data.link_out
                    : '/course/' + this.props.data.slug
                }`}
                onClick={(e) => {
                if (this.props.isCurriculum) {
                  e.preventDefault()

                  if (typeof window !== 'undefined') {
                    const currentUrl = window.location.pathname
                    localStorage.setItem('curriculum_return_url', currentUrl)
                    localStorage.setItem(
                      'curriculum_return_timestamp',
                      Date.now().toString()
                    )
                  }

                  if (this.props.callback) {
                    this.props.callback(
                      'course',
                      this.props.data.id,
                      this.props.data
                    )
                  }
                }
              }}
              >
                <div className="d-none d-xl-block BlockImgSize Fixblock">
                  {this.props.data &&
                  this.props.data.image_th &&
                  this.props.data.image_th != null &&
                  this.props.data.image_th != '' &&
                  this.props.data.image_th != 'null' ? (
                    <Image
                      className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                      src={this.props.data.image_th}
                      alt={this.props.data.image_th}
                      layout="fill"
                      objectFit="contain"
                    />
                  ) : (
                    <Image
                      className="img-thumb mousePointer d-none d-xl-block swiper-lazy ItemsImgsize"
                      src={'/assets/images/course_10.jpg'}
                      layout="fill"
                      objectFit="contain"
                      alt=""
                    />
                  )}
                </div>
              </a>
            ) : (
              <div className="d-none d-xl-block BlockImgSize Fixblock">
                {this.props.data &&
                this.props.data.image_th &&
                this.props.data.image_th != null &&
                this.props.data.image_th != '' &&
                this.props.data.image_th != 'null' ? (
                  <Image
                    className="img-thumb d-none d-xl-block swiper-lazy ItemsImgsize"
                    src={this.props.data.image_th}
                    alt={this.props.data.image_th}
                    layout="fill"
                    objectFit="contain"
                  />
                ) : (
                  <Image
                    className="img-thumb d-none d-xl-block swiper-lazy ItemsImgsize"
                    src={'/assets/images/course_10.jpg'}
                    layout="fill"
                    objectFit="contain"
                    alt=""
                  />
                )}
              </div>
            )}

            <div className="d-block d-xl-none BlockImgSize">
              {this.props.data &&
              this.props.data.image_th &&
              this.props.data.image_th != null &&
              this.props.data.image_th != '' &&
              this.props.data.image_th != 'null' ? (
                <Image
                  className="img-thumb mousePointer d-block d-xl-none swiper-lazy ItemsImgsize"
                  src={this.props.data.image_th}
                  alt={this.props.data.title_th}
                  layout="fill"
                />
              ) : null}
            </div>

            {!(isCurriculum && !isAccessible) && (
              <div className="img-action">
                {this.props.data.speaker &&
                this.props.data.speaker.length > 0 ? (
                  <div className="master-img-action">
                    <Popup
                      hideOnScroll
                      content={
                        <>
                          <ul>
                            {this.props.data.speaker.map((val, key) =>
                              val.avatar != null &&
                              val.avatar != '' &&
                              val.avatar != 'null' ? (
                                <li key={key}>
                                  <div className="blockImgSizePopup">
                                    <Image
                                      src={val.avatar}
                                      alt=""
                                      layout="fill"
                                      objectFit="contain"
                                      className="ItemsImgsizePopup"
                                    />{' '}
                                  </div>
                                  <span>{val.title_th}</span>
                                </li>
                              ) : (
                                <li key={key}>
                                  <div className="blockImgSizePopup">
                                    <Image
                                      src="/assets/images/iuser.png"
                                      alt=""
                                      layout="fill"
                                      objectFit="contain"
                                      className="ItemsImgsizePopup"
                                    />{' '}
                                  </div>
                                  <span>{val.title_th}</span>
                                </li>
                              )
                            )}
                          </ul>
                        </>
                      }
                      popper={{
                        id: 'popper-container-custom',
                        style: { zIndex: 2000 },
                      }}
                      trigger={
                        this.props.data.speaker[0].avatar != null &&
                        this.props.data.speaker[0].avatar != '' &&
                        this.props.data.speaker[0].avatar != 'null' ? (
                          <Image
                            src={this.props.data.speaker[0].avatar}
                            alt=""
                            layout="fill"
                            objectFit="contain"
                          />
                        ) : (
                          <Image
                            src="/assets/images/iuser_w.png"
                            alt=""
                            layout="fill"
                            objectFit="contain"
                          />
                        )
                      }
                    />
                    <div className="number">
                      {this.props.data.trailer_media == 6
                        ? this.translateEng('ผู้เขียน')
                        : this.translateEng('วิทยากร')}
                    </div>
                  </div>
                ) : null}

                {this.props.data.reward_point > 0 ? (
                  <div className={`card-reward-coin`}>
                    <Image
                      alt=""
                      width={32}
                      height={32}
                      src={'/assets/images/coin.png'}
                      objectFit="contain"
                    />
                    <p>{this.props.data.reward_point} คะแนน</p>
                  </div>
                ) : null}

                {this.props.data.trailer_media != 6 ? (
                  <button
                    onClick={() =>
                      this.props.callback('playlist', this.props.data.id, '')
                    }
                    className={`btn-img-action add playlist_class_${
                      this.props.data.id
                    } ${this.props.data.has_playlist ? 'active' : ''}`}
                  >
                    <i
                      className={`playlist_icon_${this.props.data.id} ${
                        this.props.data.has_playlist
                          ? 'icon-ic-tick-thanks'
                          : 'icon-ic-circle-plus'
                      }`}
                    ></i>
                  </button>
                ) : null}

                <button
                  onClick={() =>
                    this.props.callback('favourite', this.props.data.id, '')
                  }
                  className={`btn-img-action like favourite_class_${
                    this.props.data.id
                  } ${this.props.data.has_fav ? 'active' : ''}`}
                >
                  <i className="icon-ic-circle-heart"></i>
                </button>
              </div>
            )}
          </div>

          <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>

          {!(isCurriculum && !isAccessible) && !isLocked && (
            <a
              {...(this.props.data.link_out != null &&
              this.props.data.link_out !== ''
                ? { target: '_blank', rel: 'noreferrer' }
                : {})}
              href={`${
                this.props.data.link_out != null &&
                this.props.data.link_out != ''
                  ? this.props.data.link_out
                  : '/course/' + this.props.data.slug
              }`}
              onClick={(e) => {
                if (this.props.isCurriculum) {
                  e.preventDefault()

                  if (typeof window !== 'undefined') {
                    const currentUrl = window.location.pathname
                    localStorage.setItem('curriculum_return_url', currentUrl)
                    localStorage.setItem(
                      'curriculum_return_timestamp',
                      Date.now().toString()
                    )
                  }

                  if (this.props.callback) {
                    this.props.callback(
                      'course',
                      this.props.data.id,
                      this.props.data
                    )
                  }
                }
              }}
            >
              <div className="card-description">
                <div className="description-detail">
                  {this.props.data.trailer_media != 6 ? (
                    <div className="description-info">
                      {this.props.data.trailer_media != 2 ? (
                        <div className="item-info">
                          <i className="icon-ic-lessons"></i>
                          <span>{this.props.data.lesson}</span>
                          {this.translateEng('บทเรียน')}
                        </div>
                      ) : null}
                      <div className="item-info">
                        <i className="icon-ic-time"></i>
                        <span>{convertSecond(this.props.data.duration)}</span>
                      </div>
                      {this.props.data.is_certificate ? (
                        <div className="item-info">
                          <div className="BlockImgSize">
                            <Image
                              className="certificate-icon ItemsImgsize"
                              src="/assets/images/certificate.png"
                              alt=""
                              layout="fill"
                              objectFit="contain"
                            />{' '}
                            Certificate
                          </div>
                        </div>
                      ) : null}
                    </div>
                  ) : null}
                  <div className="description-text">
                    <p>{truncate(this.props.data.subtitle_th, 80)}</p>
                  </div>
                  <div className="description-rating">
                    <ReactStars {...ratingThisStar} />
                    <div className="text-rating">
                      {Number(this.props.data.rate).toFixed(1)} (
                      {this.props.data.rating} rating)
                    </div>
                  </div>
                </div>

                <div className="description-price">
                  <div
                    className={`${styles.curriculumActionButton} ${
                      isCompleted ? styles.reviewButton : styles.startButton
                    }`}
                  >
                    {buttonIcon}
                    {buttonText}
                  </div>
                </div>

                <div className="description-view-more d-flex d-xl-none">
                  <div className="description-view">
                    <div className="inner-view">
                      <span>{this.translateEng('ดูรายละเอียดเพิ่มเติม')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          )}

          {isLocked && (
            <div className="card-description">
              <div className="description-detail">
                {this.props.data.trailer_media != 6 ? (
                  <div className="description-info">
                    {this.props.data.trailer_media != 2 ? (
                      <div className="item-info">
                        <i className="icon-ic-lessons"></i>
                        <span>{this.props.data.lesson}</span>
                        {this.translateEng('บทเรียน')}
                      </div>
                    ) : null}
                    <div className="item-info">
                      <i className="icon-ic-time"></i>
                      <span>{convertSecond(this.props.data.duration)}</span>
                    </div>
                    {this.props.data.is_certificate ? (
                      <div className="item-info">
                        <div className="BlockImgSize">
                          <Image
                            className="certificate-icon ItemsImgsize"
                            src="/assets/images/certificate.png"
                            alt=""
                            layout="fill"
                            objectFit="contain"
                          />{' '}
                          Certificate
                        </div>
                      </div>
                    ) : null}
                  </div>
                ) : null}
                <div className="description-text">
                  <p>{truncate(this.props.data.subtitle_th, 80)}</p>
                </div>
                <div className="description-rating">
                  <ReactStars {...ratingThisStar} />
                  <div className="text-rating">
                    {Number(this.props.data.rate).toFixed(1)} (
                    {this.props.data.rating} rating)
                  </div>
                </div>
              </div>

              <div className="description-price">
                <div
                  className={`${styles.curriculumActionButton} ${styles.lockedButton}`}
                >
                  {buttonIcon}
                  {buttonText}
                </div>
              </div>

              <div className="description-view-more d-flex d-xl-none">
                <div className="description-view">
                  <div className="inner-view">
                    <span>{this.translateEng('ดูรายละเอียดเพิ่มเติม')}</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }
}

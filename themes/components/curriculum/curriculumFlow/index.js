import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import CardCurriculum from '/themes/components/curriculum/cardCurriculum'
import styles from './curriculumFlow.module.css'

const CurriculumFlow = ({
  curriculumData = null,
  courseData = null,
  currentProgress = 0,
  onCourseAction = null,
}) => {
  const [isMobile, setIsMobile] = useState(false)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)

    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 480)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => {
      window.removeEventListener('resize', checkMobile)
    }
  }, [])

  const convertCourseToCardFormat = (course, index) => {
    return {
      id: course.id,
      slug: course.slug,
      title_th: course.title,
      subtitle_th: course.subtitle_th,
      image_th: course.image,
      allowed: curriculumData?.data?.allowed || false,
      order_status: course.progress_percent > 0 ? 1 : 0,
      lesson: course.lesson || 1,
      duration: course.course_duration || 0,
      rate: course.rate || 4.5,
      rating: course.rating || 0,
      price: course.price || 0,
      pro_price: course.pro_price || 0,
      progress_percent: course.progress_percent || 0,
      speaker: [
        {
          id: index + 1,
          title_th: course.speaker_name || `วิทยากร ${index + 1}`,
          avatar: '/assets/images/iuser.png',
        },
      ],
      logo_list: [],
      has_fav: course.has_fav || false,
      has_playlist: false,
      reward_point: 10,
      is_certificate: true,
      trailer_media: course.trailer_media || 1,
      receive_point: null,
      is_subscription: false,
      is_soon: false,
      is_hot: 0,
      free_icon: course.is_free === 1 ? 1 : 0,
      is_new: 0,
      is_internal: false,
      user_internal: false,
      is_volume: false,
      expire_date: null,
      is_promotion: course.pro_price < course.price ? 1 : 0,
      link_out: null,
      forceLoadImage: true,
    }
  }

  const convertEpisodeToCardFormat = (episode, index) => {
    return {
      id: episode.id,
      slug: episode.id.toLowerCase(),
      title_th: episode.title,
      subtitle_th: episode.subtitle,
      image_th: episode.image,
      allowed: episode.completed,
      order_status: episode.completed ? 1 : 0,
      lesson: 1,
      duration: 3600,
      rate: 4.5,
      rating: 0,
      price: 0,
      pro_price: 0,
      speaker: [
        {
          id: index + 1,
          title_th: `วิทยากร ${index + 1}`,
          avatar: '/assets/images/iuser.png',
        },
      ],
      logo_list: [],
      has_fav: false,
      has_playlist: false,
      reward_point: 10,
      is_certificate: true,
      trailer_media: 1,
      receive_point: null,
      is_subscription: false,
      is_soon: false,
      is_hot: 0,
      free_icon: 1,
      is_new: 0,
      is_internal: false,
      user_internal: false,
      is_volume: false,
      expire_date: null,
      is_promotion: 0,
      link_out: null,
      forceLoadImage: true,
    }
  }

  const createEpisodesFromAPI = () => {
    if (
      !curriculumData ||
      !curriculumData.courses ||
      curriculumData.courses.length === 0
    ) {
      return null
    }

    return curriculumData.courses.map((course, index) => {
      const isRequired = course.is_required === 1
      let isAccessible = false
      let isCompleted = course.progress_percent >= 100

      if (!isRequired) {
        isAccessible = true
      } else {
        if (index === 0) {
          isAccessible = true
        } else {
          isAccessible = checkPreviousRequiredCoursesCompleted(
            curriculumData.courses,
            index
          )
        }
      }

      return {
        id: `EP${index + 1}`,
        title: course.title,
        subtitle: course.subtitle_th,
        image: course.image,
        completed: isCompleted,
        accessible: isAccessible,
        progress: course.progress_percent || 0,
        courseData: course,
        position: course.position || index + 1,
        isRequired: isRequired,
      }
    })
  }

  const checkPreviousRequiredCoursesCompleted = (courses, currentIndex) => {
    for (let i = 0; i < currentIndex; i++) {
      const previousCourse = courses[i]
      if (
        previousCourse.is_required === 1 &&
        previousCourse.progress_percent < 100
      ) {
        return false
      }
    }
    return true
  }

  const buildCurriculumData = () => {
    const episodes = createEpisodesFromAPI()

    if (!episodes || episodes.length === 0) {
      return null
    }

    const totalEpisodes = episodes.length
    const completedEpisodes = episodes.filter((ep) => ep.completed).length

    return {
      startCard: {
        title: 'เริ่มต้นหลักสูตร',
        subtitle:
          curriculumData && curriculumData.data
            ? curriculumData.data.title
            : 'หลักสูตรการศึกษา',
      },
      episodes: episodes,
      endCard: {
        title: 'จบบทเรียนหลักสูตร',
        subtitle: 'Emergency & Critical Care',
      },
      currentProgress: completedEpisodes,
      totalEpisodes: totalEpisodes,
    }
  }

  const data = buildCurriculumData()

  if (!data) {
    return (
      <div className={styles.flowContainer}>
        <div className={styles.noDataContainer}>
          <div className={styles.noDataIcon}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="80"
              height="80"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#9ca3af"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10" />
              <path d="m9 9 6 6" />
              <path d="m15 9-6 6" />
            </svg>
          </div>
          <h3 className={styles.noDataTitle}>ไม่มีข้อมูลหลักสูตร</h3>
          <p className={styles.noDataDescription}>
            ขณะนี้ยังไม่มีข้อมูลหลักสูตรที่จะแสดง
          </p>
        </div>
      </div>
    )
  }

  const episodeCount = data.episodes.length

  const getLayoutFormat = () => {
    if (!isClient) {
      if (episodeCount <= 5) return 'format1'
      if (episodeCount <= 10) return 'format2'
      return 'format3'
    }

    if (isMobile) {
      return 'mobile'
    }

    if (episodeCount <= 5) return 'format1'
    if (episodeCount <= 10) return 'format2'
    return 'format3'
  }

  const getEpisodeDistribution = () => {
    const layoutFormat = getLayoutFormat()

    if (layoutFormat === 'mobile') {
      return {
        startColumn: [],
        centerColumn: data.episodes,
        endColumn: [],
      }
    }

    switch (layoutFormat) {
      case 'format1':
        return {
          startColumn: [],
          centerColumn: data.episodes,
          endColumn: [],
        }
      case 'format2':
        return {
          startColumn: data.episodes.slice(0, 5),
          centerColumn: data.episodes.slice(5, 10).reverse(),
          endColumn: [],
        }
      case 'format3':
        return {
          startColumn: data.episodes.slice(0, 5),
          centerColumn: data.episodes.slice(5, 10).reverse(),
          endColumn: data.episodes.slice(10, 15),
        }
      default:
        return { startColumn: [], centerColumn: data.episodes, endColumn: [] }
    }
  }

  const calculateProgress = () => {
    if (currentProgress !== 0) return currentProgress
    if (data.currentProgress) return data.currentProgress

    const requiredCompletedEpisodes = data.episodes.filter(
      (ep) => ep.isRequired && ep.completed
    ).length

    return requiredCompletedEpisodes
  }

  const calculateCompletionPercent = () => {
    if (
      !curriculumData ||
      !curriculumData.courses ||
      curriculumData.courses.length === 0
    ) {
      return 0
    }

    const courses = curriculumData.courses
    const courseCount = courses.length

    const totalProgress = courses.reduce((total, course) => {
      return total + (course.progress_percent || 0)
    }, 0)

    const completionPercent =
      courseCount > 0 ? Math.round(totalProgress / courseCount) : 0
    return completionPercent
  }

  const completionPercent = calculateCompletionPercent()
  const allEpisodesCompleted = completionPercent === 100

  // const allRequiredEpisodesCompleted = data.episodes
  //   .filter((episode) => episode.isRequired)
  //   .every((episode) => episode.completed)

  // const allEpisodesCompleted = allRequiredEpisodesCompleted

  const isConnectionCompleted = (fromIndex, toIndex) => {
    const progress = calculateProgress()

    if (fromIndex === -1) return progress > 0
    if (toIndex === data.episodes.length) {
      return completionPercent === 100 || progress >= data.episodes.length
    }
    return progress > fromIndex
  }

  const getEpisodeNumberPosition = (
    columnType,
    index,
    episodesLength,
    layoutFormat
  ) => {
    if (columnType === 'start' && index === 0) return 'first'

    if (columnType === 'center') {
      if (layoutFormat === 'format2' || layoutFormat === 'format3') {
        if (index === episodesLength - 1) return 'first'
        return 'bottom'
      }
      return 'middle'
    }

    if (columnType === 'end' && layoutFormat === 'format3') {
      if (index === 0) return 'first'
      if (index === episodesLength - 1) return 'last'
      return 'top'
    }

    if (index === episodesLength - 1) return 'last'
    return 'middle'
  }

  const layoutFormat = getLayoutFormat()
  const { startColumn, centerColumn, endColumn } = getEpisodeDistribution()
  const progress = calculateProgress()

  if (!isClient && isMobile) {
    return null
  }

  const renderEpisodeColumn = (episodes, columnType) => {
    if (episodes.length === 0) return null

    return episodes.map((episode, index) => {
      const globalIndex = data.episodes.findIndex((ep) => ep.id === episode.id)
      const numberPosition = getEpisodeNumberPosition(
        columnType,
        index,
        episodes.length,
        layoutFormat
      )

      const isLastInStartColumn =
        layoutFormat === 'format2' &&
        columnType === 'start' &&
        index === episodes.length - 1
      const nextEpisodeCompleted =
        isLastInStartColumn && data.episodes[5] && data.episodes[5].completed

      const isLastInStartColumnFormat3 =
        layoutFormat === 'format3' &&
        columnType === 'start' &&
        index === episodes.length - 1

      const nextEpisodeCompletedFormat3 =
        isLastInStartColumnFormat3 &&
        data.episodes[5] &&
        data.episodes[5].completed

      const isEP10InFormat3 =
        layoutFormat === 'format3' &&
        columnType === 'center' &&
        index === 0 &&
        globalIndex === 9

      const currentProgressValue = calculateProgress()
      const horizontalConnectionCompleted =
        isEP10InFormat3 && currentProgressValue >= 10

      const isLastEpisodeInEndColumn =
        layoutFormat === 'format3' &&
        columnType === 'end' &&
        index === episodes.length - 1
      const lastEpisodeCompleted = isLastEpisodeInEndColumn && episode.completed

      const cardData = episode.courseData
        ? convertCourseToCardFormat(episode.courseData, globalIndex)
        : convertEpisodeToCardFormat(episode, globalIndex)

      const getWrapperClasses = () => {
        let classes = [styles.episodeWrapper]

        if (episode.completed) {
          classes.push(styles.completed)
        } else if (episode.accessible) {
          classes.push(styles.accessible)
        } else {
          classes.push(styles.pending)
        }

        if (episode.isRequired) {
          classes.push(styles.required)
        } else {
          classes.push(styles.optional)
        }

        if (isLastInStartColumn && !nextEpisodeCompleted) {
          classes.push(styles.pending)
        }

        if (isLastInStartColumnFormat3 && !nextEpisodeCompletedFormat3) {
          classes.push(styles.pending)
        }

        if (lastEpisodeCompleted) {
          classes.push(styles.completed)
        }

        return classes.join(' ')
      }

      let horizontalConnectionColor = '#d1d5db'

      if (isLastInStartColumn && nextEpisodeCompleted) {
        horizontalConnectionColor = '#638d2e'
      } else if (isLastInStartColumnFormat3 && nextEpisodeCompletedFormat3) {
        horizontalConnectionColor = '#638d2e'
      } else if (isEP10InFormat3 && horizontalConnectionCompleted) {
        horizontalConnectionColor = '#638d2e'
      }

      return (
        <div
          key={episode.id}
          className={getWrapperClasses()}
         style={{
            '--horizontal-connection-color': horizontalConnectionColor,
          }}
        >
          <div
            className={`${styles.episodeNumber} ${styles[numberPosition]} ${
              !episode.accessible ? styles.pending : ''
            } ${episode.isRequired ? styles.required : styles.optional}`}
          >
            {episode.position || globalIndex + 1}
          </div>

          <div className={styles.curriculumCardContainer}>
            <div className={styles.imagePreloader}>
              <Image
                src={episode.image}
                alt={episode.title}
                width={400}
                height={240}
                style={{ display: 'none' }}
                priority={true}
                onLoad={() => {
                  if (isClient) {
                    const cardElement = document.querySelector(
                      `#card-${episode.id}`
                    )
                    if (cardElement) {
                      cardElement.style.opacity = '1'
                    }
                  }
                }}
              />
            </div>

            <div id={`card-${episode.id}`}>
              <CardCurriculum
                type="normal"
                isCurriculum={true}
                isAccessible={episode.accessible}
                progressPercent={episode.progress}
                callback={(action, id, data) => {
                  if (
                    action === 'course' ||
                    action === 'navigation' ||
                    action === 'start'
                  ) {
                    if (!episode.accessible) {
                      return
                    }

                    if (episode.courseData && episode.courseData.slug) {
                      if (episode.progress === 0) {
                        window.location.href = `/course/${episode.courseData.slug}?from=curriculum`
                      } else {
                        window.location.href = `/course/${episode.courseData.slug}?from=curriculum`
                      }
                    }
                  } else if (
                    action === 'favourite' ||
                    action === 'playlist' ||
                    action === 'cart' ||
                    action === 'free' ||
                    action === 'vip' ||
                    action === 'volume' ||
                    action === 'internal'
                  ) {
                    if (onCourseAction && episode.courseData) {
                      onCourseAction(
                        action,
                        episode.courseData.id,
                        episode.courseData.title
                      )
                    }
                  }
                }}
                data={cardData}
                lang="th"
              />
            </div>

            {!episode.accessible && (
              <div className={styles.curriculumLockOverlay}>
                <div className={styles.curriculumLockIcon}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="48"
                    height="48"
                    viewBox="0 0 48 48"
                    fill="none"
                  >
                    <path
                      d="M23.9888 4.37695C26.591 4.37695 29.0865 5.41065 30.9265 7.25065C32.7665 9.09065 33.8002 11.5862 33.8002 14.1884V20.0752C35.3615 20.0752 36.8589 20.6954 37.9629 21.7994C39.0669 22.9034 39.6871 24.4008 39.6871 25.9621V37.7358C39.6871 39.2971 39.0669 40.7944 37.9629 41.8984C36.8589 43.0024 35.3615 43.6226 33.8002 43.6226H14.1774C12.6161 43.6226 11.1187 43.0024 10.0147 41.8984C8.91075 40.7944 8.29053 39.2971 8.29053 37.7358V25.9621C8.29053 24.4008 8.91075 22.9034 10.0147 21.7994C11.1187 20.6954 12.6161 20.0752 14.1774 20.0752V14.1884C14.1774 11.5862 15.2111 9.09065 17.0511 7.25065C18.8911 5.41065 21.3867 4.37695 23.9888 4.37695ZM23.9888 27.9244C22.9987 27.9241 22.045 28.298 21.319 28.9712C20.593 29.6445 20.1483 30.5673 20.074 31.5546L20.0642 31.8489C20.0642 32.6251 20.2944 33.3839 20.7256 34.0293C21.1569 34.6747 21.7698 35.1777 22.4869 35.4748C23.2041 35.7718 23.9932 35.8495 24.7544 35.6981C25.5157 35.5467 26.215 35.1729 26.7639 34.624C27.3128 34.0752 27.6865 33.3759 27.838 32.6146C27.9894 31.8533 27.9117 31.0642 27.6146 30.3471C27.3176 29.6299 26.8146 29.017 26.1692 28.5858C25.5238 28.1545 24.765 27.9244 23.9888 27.9244ZM23.9888 8.30152C22.4275 8.30152 20.9302 8.92174 19.8262 10.0257C18.7222 11.1297 18.102 12.6271 18.102 14.1884V20.0752H29.8757V14.1884C29.8757 12.6271 29.2554 11.1297 28.1514 10.0257C27.0474 8.92174 25.5501 8.30152 23.9888 8.30152Z"
                      fill="white"
                    />
                  </svg>
                </div>
              </div>
            )}

            {/* {episode.accessible &&
              !episode.completed &&
              episode.progress > 0 && (
                <div className={styles.curriculumContinueButton}>
                  <button
                    onClick={() => {
                      if (episode.courseData && episode.courseData.slug) {
                        window.location.href = `/course/${episode.courseData.slug}`
                      }
                    }}
                    className={styles.continueBtn}
                  >
                    ดำเนินการต่อ
                  </button>
                </div>
              )} */}

            {episode.accessible &&
              !episode.completed &&
              episode.progress > 0 && (
                <div className={styles.curriculumProgressBadge}>
                  <span>{episode.progress}%</span>
                </div>
              )}

            {episode.completed && (
              <div className={styles.curriculumFinishedBadge}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <mask
                    id={`mask_${globalIndex}_3861`}
                    maskUnits="userSpaceOnUse"
                    x="1"
                    y="1"
                    width="22"
                    height="22"
                  >
                    <path
                      d="M12 22C13.3135 22.0016 14.6143 21.7437 15.8278 21.2411C17.0412 20.7384 18.1434 20.0009 19.071 19.071C20.0009 18.1434 20.7384 17.0412 21.2411 15.8278C21.7437 14.6143 22.0016 13.3135 22 12C22.0016 10.6866 21.7437 9.38572 21.2411 8.17225C20.7384 6.95878 20.0009 5.85659 19.071 4.92901C18.1434 3.99909 17.0412 3.26162 15.8278 2.75897C14.6143 2.25631 13.3135 1.99839 12 2.00001C10.6866 1.99839 9.38572 2.25631 8.17225 2.75897C6.95878 3.26162 5.85659 3.99909 4.92901 4.92901C3.99909 5.85659 3.26162 6.95878 2.75897 8.17225C2.25631 9.38572 1.99839 10.6866 2.00001 12C1.99839 13.3135 2.25631 14.6143 2.75897 15.8278C3.26162 17.0412 3.99909 18.1434 4.92901 19.071C5.85659 20.0009 6.95878 20.7384 8.17225 21.2411C9.38572 21.7437 10.6866 22.0016 12 22Z"
                      fill="white"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M8 12L11 15L17 9"
                      stroke="black"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </mask>
                  <g mask={`url(#mask_${globalIndex}_3861)`}>
                    <path d="M0 0H24V24H0V0Z" fill="#92BF1F" />
                  </g>
                </svg>
                <span>Finished</span>
              </div>
            )}
          </div>

          {index < episodes.length - 1 && (
            <div
              className={`${styles.verticalLine} ${
                (layoutFormat === 'format2' || layoutFormat === 'format3') &&
                columnType === 'center'
                  ? isConnectionCompleted(
                      data.episodes.findIndex(
                        (ep) => ep.id === episodes[index + 1].id
                      ),
                      globalIndex
                    )
                    ? styles.completed
                    : styles.pending
                  : isConnectionCompleted(globalIndex, globalIndex + 1)
                  ? styles.completed
                  : styles.pending
              }`}
            ></div>
          )}
        </div>
      )
    })
  }

  const renderStartCardIcon = () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100"
      height="100"
      viewBox="0 0 114 112"
      fill="none"
    >
      <g clipPath="url(#clip0_163_4807)">
        <path
          d="M41.4997 109.152L36.5627 100.006C36.0433 99.0439 34.9017 98.5793 33.8417 98.8989L23.7683 101.936C21.8986 102.5 20.2053 100.67 20.9537 98.8947L38.7187 56.7598L63.4434 66.8216L45.6783 108.957C44.9302 110.732 42.4159 110.849 41.4997 109.152Z"
          fill="#D9D9D9"
        />
        <path
          d="M50.4421 61.5312L34.7246 98.81C35.4852 98.8767 36.1872 99.3102 36.5631 100.007L41.5 109.152C42.4163 110.85 44.9305 110.732 45.6789 108.957L63.4439 66.8222L50.4421 61.5312Z"
          fill="#D9D9D9"
        />
        <path
          d="M72.5003 109.152L77.4373 100.006C77.9567 99.0439 79.0983 98.5793 80.1583 98.8989L90.2318 101.936C92.1014 102.5 93.7947 100.67 93.0464 98.8947L75.2813 56.7598L50.5566 66.8216L68.3217 108.957C69.0701 110.732 71.5841 110.849 72.5003 109.152Z"
          fill="#D9D9D9"
        />
        <path
          d="M93.0466 98.8947L75.2816 56.7598L62.3252 62.0325L78.0369 99.2977C78.6194 98.8405 79.4087 98.6727 80.1584 98.8987L90.2318 101.936C92.1017 102.5 93.795 100.67 93.0466 98.8947Z"
          fill="#D9D9D9"
        />
        <path
          d="M61.4614 3.17781C63.3195 4.62616 65.7856 5.07897 68.0507 4.38794C71.2358 3.41625 74.6825 4.72809 76.3698 7.55434C77.5699 9.56422 79.703 10.8618 82.0692 11.0213C85.3963 11.2455 88.1281 13.692 88.6622 16.9263C89.0423 19.2262 90.5541 21.1934 92.7019 22.1819C95.722 23.5719 97.3697 26.8227 96.6785 30.0281C96.1871 32.3077 96.8738 34.6785 98.513 36.3624C100.818 38.7304 101.159 42.3463 99.3362 45.0901C98.0398 47.0414 97.8083 49.4957 98.7176 51.6478C99.9963 54.674 98.9848 58.1668 96.2762 60.0782C94.35 61.4375 93.2316 63.644 93.2881 65.9735C93.3679 69.2491 91.1404 72.1471 87.9117 72.9683C85.6156 73.5521 83.7613 75.2128 82.9578 77.4052C81.8276 80.4878 78.685 82.3997 75.3725 82.0195C73.0168 81.7491 70.6772 82.6396 69.1217 84.3986C66.9343 86.872 63.301 87.5394 60.3519 86.0093C58.2547 84.9212 55.7456 84.9212 53.6484 86.0093C50.6993 87.5394 47.066 86.8722 44.8787 84.3986C43.3232 82.6396 40.9835 81.7491 38.6278 82.0195C35.3154 82.3997 32.1726 80.488 31.0426 77.4052C30.2388 75.2128 28.3847 73.5523 26.0887 72.9683C22.86 72.1471 20.6325 69.2493 20.7122 65.9735C20.769 63.644 19.6506 61.4375 17.7242 60.0782C15.0156 58.1668 14.004 54.6738 15.2828 51.6478C16.1921 49.4957 15.9605 47.0414 14.6642 45.0901C12.8413 42.3463 13.1822 38.7304 15.4874 36.3624C17.1266 34.6785 17.8132 32.3074 17.3218 30.0281C16.6309 26.8225 18.2784 23.5719 21.2985 22.1819C23.4462 21.1934 24.9583 19.2264 25.3381 16.9263C25.8725 13.692 28.6041 11.2455 31.9312 11.0213C34.2974 10.8618 36.4306 9.56422 37.6305 7.55434C39.318 4.72809 42.7648 3.41625 45.9496 4.38794C48.2147 5.07897 50.6809 4.62594 52.5389 3.17781C55.1525 1.14125 58.8488 1.14125 61.4614 3.17781Z"
          fill="#D9D9D9"
        />
        <path
          d="M57.0007 75.3456C74.4876 75.3456 88.6635 61.4183 88.6635 44.2382C88.6635 27.0581 74.4876 13.1309 57.0007 13.1309C39.5138 13.1309 25.3379 27.0581 25.3379 44.2382C25.3379 61.4183 39.5138 75.3456 57.0007 75.3456Z"
          fill="#D9D9D9"
        />
        <path
          d="M57.001 13.1309C54.5769 13.1309 52.217 13.3995 49.9492 13.9063C64.0416 17.0557 74.5603 29.4395 74.5603 44.2382C74.5603 59.0369 64.0416 71.421 49.9492 74.5701C52.217 75.0769 54.5771 75.3456 57.001 75.3456C74.4879 75.3456 88.6638 61.4184 88.6638 44.2382C88.6638 27.058 74.4879 13.1309 57.001 13.1309Z"
          fill="#D9D9D9"
        />
        <path
          d="M58.4679 23.2858L62.9852 35.4433C63.2031 36.03 63.7613 36.4283 64.3966 36.4509L77.5614 36.9148C79.0175 36.9662 79.6156 38.7749 78.4682 39.6573L68.0953 47.6351C67.5948 48.0201 67.3815 48.6648 67.5563 49.2652L71.1754 61.7095C71.5757 63.0859 70.0095 64.2037 68.8009 63.4044L57.8728 56.1775C57.3453 55.8288 56.6555 55.8288 56.128 56.1775L45.1998 63.4044C43.991 64.2037 42.4251 63.0859 42.8254 61.7095L46.4445 49.2652C46.619 48.6648 46.406 48.0201 45.9054 47.6351L35.5325 39.6573C34.3852 38.7749 34.9835 36.9662 36.4394 36.9148L49.6042 36.4509C50.2394 36.4286 50.7976 36.03 51.0156 35.4433L55.5329 23.2858C56.0325 21.9412 57.9683 21.9412 58.4679 23.2858Z"
          fill="#D9D9D9"
        />
        <path
          d="M68.8014 63.4044C70.0102 64.2037 71.5761 63.0859 71.1758 61.7095L70.5196 59.4531C69.8154 60.6845 69.0274 61.8633 68.165 62.9835L68.8014 63.4044Z"
          fill="#D9D9D9"
        />
        <path
          d="M77.562 36.9156L73.6426 36.7773C74.1211 38.6864 74.4194 40.665 74.5203 42.6947L78.4689 39.6578C79.6162 38.7756 79.0182 36.9668 77.562 36.9156Z"
          fill="#D9D9D9"
        />
        <path
          d="M100.734 45.9867C102.993 42.5867 102.576 38.1629 99.7197 35.2286C98.4608 33.9353 97.9346 32.1184 98.3118 30.368C99.1684 26.3957 97.1527 22.4186 93.41 20.6964C91.7606 19.9373 90.6019 18.4297 90.31 16.6635C89.648 12.6556 86.306 9.66241 82.183 9.38459C80.366 9.26209 78.731 8.26766 77.8094 6.72416C75.7182 3.22219 71.5025 1.61722 67.5548 2.82122C65.8154 3.35191 63.9255 3.00453 62.4985 1.89263C59.2608 -0.630875 54.7389 -0.630875 51.5015 1.89263C50.0747 3.00497 48.1843 3.35191 46.4452 2.82122C42.4984 1.617 38.2815 3.22197 36.1905 6.72416C35.2689 8.26766 33.6342 9.26209 31.8171 9.38459C27.6939 9.66241 24.3521 12.6558 23.6899 16.6635C23.3982 18.4297 22.2393 19.9373 20.5901 20.6964C16.8477 22.4188 14.832 26.3959 15.6881 30.368C16.0655 32.1184 15.5391 33.9356 14.2805 35.2288C11.4242 38.1631 11.0067 42.5871 13.2658 45.9872C14.2613 47.4854 14.4388 49.3666 13.7403 51.0193C12.1556 54.7691 13.3934 59.0424 16.7497 61.411C18.229 62.4551 19.086 64.1463 19.0424 65.935C18.9438 69.9939 21.6691 73.5394 25.6698 74.557C26.8507 74.8573 27.8767 75.5287 28.6119 76.4444L19.4109 98.2675C18.1215 101.326 21.0399 104.475 24.2579 103.505L34.3313 100.468C34.6257 100.379 34.9432 100.509 35.0872 100.775L40.0242 109.921C41.6035 112.846 45.933 112.64 47.2209 109.585L56.8109 86.8392C56.9367 86.835 57.0625 86.835 57.1883 86.8392L66.7783 109.585C68.0686 112.645 72.398 112.842 73.9753 109.921L78.9122 100.775C79.0561 100.508 79.3738 100.379 79.6681 100.468L89.7413 103.505C92.9612 104.476 95.8773 101.324 94.5886 98.2678L85.3873 76.4446C86.1225 75.5289 87.1485 74.8576 88.3295 74.5572C92.3302 73.5398 95.0553 69.9943 94.9568 65.9352C94.9132 64.1465 95.7704 62.4553 97.2497 61.4114C100.606 59.0426 101.844 54.7693 100.259 51.0195C99.5616 49.3662 99.7391 47.4849 100.734 45.9867ZM44.136 108.329C43.9265 108.826 43.2304 108.856 42.9751 108.383L38.0381 99.2377C37.145 97.5833 35.1743 96.7818 33.3523 97.3308L23.2789 100.368C22.7563 100.525 22.2876 100.019 22.4969 99.5229L30.6599 80.1607C32.5018 82.6394 35.5806 84.0208 38.8218 83.6489C40.6307 83.4417 42.4238 84.1238 43.6183 85.4744C45.3512 87.4344 47.7931 88.4748 50.2844 88.4745C51.0724 88.4745 51.8651 88.3663 52.6421 88.1543L44.136 108.329ZM90.7217 100.368L80.6485 97.3306C78.8265 96.7816 76.8553 97.5833 75.9625 99.2375L71.0255 108.383C70.7706 108.856 70.0737 108.825 69.8644 108.329L61.36 88.158C64.5928 89.0396 68.1016 88.053 70.3823 85.4739C71.5766 84.1232 73.369 83.4407 75.1787 83.6485C78.4231 84.021 81.4999 82.6392 83.3406 80.1605L91.5041 99.5221C91.7139 100.02 91.2425 100.524 90.7217 100.368ZM97.938 44.1934C96.3389 46.6003 96.0539 49.6221 97.1756 52.2769C98.162 54.6112 97.3918 57.2712 95.3022 58.746C92.926 60.4229 91.5491 63.1396 91.619 66.0128C91.6805 68.5398 89.9838 70.747 87.4932 71.3803C85.1635 71.9729 83.2073 73.4626 82.0341 75.4771C82.031 75.483 82.0276 75.4887 82.0245 75.4943C81.7758 75.9238 81.5603 76.3757 81.3868 76.8493C80.5151 79.2273 78.1231 80.6846 75.5664 80.3902C72.6609 80.0564 69.78 81.153 67.8614 83.3228C66.1743 85.2307 63.4069 85.7388 61.1318 84.5587C60.3581 84.1571 59.5387 83.8784 58.7019 83.7172C58.695 83.7156 58.6881 83.7141 58.6812 83.7128C57.5697 83.5013 56.4275 83.5015 55.316 83.7137C55.312 83.7145 55.3082 83.7154 55.3042 83.7161C54.4655 83.8773 53.6445 84.1564 52.869 84.5587C50.5939 85.7391 47.8267 85.2307 46.1397 83.3228C44.4479 81.4098 42.0092 80.3314 39.464 80.3314C39.1224 80.3314 38.7784 80.3508 38.4344 80.3904C35.8801 80.6842 33.4854 79.2278 32.6138 76.8495C32.4403 76.3763 32.2252 75.9251 31.9767 75.4959C31.9729 75.4889 31.9692 75.4823 31.9654 75.4753C30.792 73.4617 28.8364 71.9727 26.5074 71.3803C24.0168 70.7468 22.3203 68.5398 22.3818 66.0131C22.4517 63.1396 21.0748 60.4231 18.6986 58.746C16.6092 57.2714 15.8386 54.6112 16.8252 52.2767C17.9469 49.6219 17.6619 46.6001 16.0628 44.1932C14.6567 42.0768 14.9164 39.3227 16.6947 37.4962C18.7167 35.4189 19.5621 32.4999 18.956 29.6881C18.423 27.2153 19.6779 24.7395 22.0075 23.6672C24.6569 22.4479 26.5183 20.0261 26.9872 17.1889C27.3994 14.6939 29.4799 12.8306 32.0464 12.6578C34.9652 12.4611 37.5917 10.8636 39.0719 8.38403C40.3738 6.20397 42.9993 5.20516 45.4554 5.95459C48.2496 6.80706 51.2859 6.24947 53.578 4.46294C55.593 2.89188 58.4083 2.89188 60.424 4.46294C62.7158 6.24925 65.7517 6.80728 68.5462 5.95459C71.0033 5.20494 73.6284 6.20419 74.9298 8.38403C76.41 10.8633 79.0365 12.4609 81.9553 12.6578C84.5218 12.8308 86.6023 14.6941 87.0145 17.1889C87.4834 20.0261 89.345 22.4479 91.9942 23.6674C94.324 24.7395 95.5787 27.2153 95.0457 29.6881C94.4396 32.4999 95.2848 35.4189 97.307 37.4962C99.0844 39.3227 99.3441 42.0768 97.938 44.1934Z"
          fill="#D9D9D9"
        />
        <path
          d="M57.0007 75.3456C74.4876 75.3456 88.6635 61.4183 88.6635 44.2382C88.6635 27.0581 74.4876 13.1309 57.0007 13.1309C39.5138 13.1309 25.3379 27.0581 25.3379 44.2382C25.3379 61.4183 39.5138 75.3456 57.0007 75.3456Z"
          fill="#D9D9D9"
        />
        <path
          d="M57.001 13.1309C54.5769 13.1309 52.217 13.3995 49.9492 13.9063C64.0416 17.0557 74.5603 29.4395 74.5603 44.2382C74.5603 59.0369 64.0416 71.421 49.9492 74.5701C52.217 75.0769 54.5771 75.3456 57.001 75.3456C74.4879 75.3456 88.6638 61.4184 88.6638 44.2382C88.6638 27.058 74.4879 13.1309 57.001 13.1309Z"
          fill="#D9D9D9"
        />
        <path
          d="M58.4679 23.2858L62.9852 35.4433C63.2031 36.03 63.7613 36.4283 64.3966 36.4509L77.5614 36.9148C79.0175 36.9662 79.6156 38.7749 78.4682 39.6573L68.0953 47.6351C67.5948 48.0201 67.3815 48.6648 67.5563 49.2652L71.1754 61.7095C71.5757 63.0859 70.0095 64.2037 68.8009 63.4044L57.8728 56.1775C57.3453 55.8288 56.6555 55.8288 56.128 56.1775L45.1998 63.4044C43.991 64.2037 42.4251 63.0859 42.8254 61.7095L46.4445 49.2652C46.619 48.6648 46.406 48.0201 45.9054 47.6351L35.5325 39.6573C34.3852 38.7749 34.9835 36.9662 36.4394 36.9148L49.6042 36.4509C50.2394 36.4286 50.7976 36.03 51.0156 35.4433L55.5329 23.2858C56.0325 21.9412 57.9683 21.9412 58.4679 23.2858Z"
          fill="#D9D9D9"
        />
        <path
          d="M68.8014 63.4044C70.0102 64.2037 71.5761 63.0859 71.1758 61.7095L70.5196 59.4531C69.8154 60.6845 69.0274 61.8633 68.165 62.9835L68.8014 63.4044Z"
          fill="#D9D9D9"
        />
        <path
          d="M77.562 36.9156L73.6426 36.7773C74.1211 38.6864 74.4194 40.665 74.5203 42.6947L78.4689 39.6578C79.6162 38.7756 79.0182 36.9668 77.562 36.9156Z"
          fill="#D9D9D9"
        />
        <path
          d="M100.734 45.9867C102.993 42.5867 102.576 38.1629 99.7197 35.2286C98.4608 33.9353 97.9346 32.1184 98.3118 30.368C99.1684 26.3957 97.1527 22.4186 93.41 20.6964C91.7606 19.9373 90.6019 18.4297 90.31 16.6635C89.648 12.6556 86.306 9.66241 82.183 9.38459C80.366 9.26209 78.731 8.26766 77.8094 6.72416C75.7182 3.22219 71.5025 1.61722 67.5548 2.82122C65.8154 3.35191 63.9255 3.00453 62.4985 1.89263C59.2608 -0.630875 54.7389 -0.630875 51.5015 1.89263C50.0747 3.00497 48.1843 3.35191 46.4452 2.82122C42.4984 1.617 38.2815 3.22197 36.1905 6.72416C35.2689 8.26766 33.6342 9.26209 31.8171 9.38459C27.6939 9.66241 24.3521 12.6558 23.6899 16.6635C23.3982 18.4297 22.2393 19.9373 20.5901 20.6964C16.8477 22.4188 14.832 26.3959 15.6881 30.368C16.0655 32.1184 15.5391 33.9356 14.2805 35.2288C11.4242 38.1631 11.0067 42.5871 13.2658 45.9872C14.2613 47.4854 14.4388 49.3666 13.7403 51.0193C12.1556 54.7691 13.3934 59.0424 16.7497 61.411C18.229 62.4551 19.086 64.1463 19.0424 65.935C18.9438 69.9939 21.6691 73.5394 25.6698 74.557C26.8507 74.8573 27.8767 75.5287 28.6119 76.4444L19.4109 98.2675C18.1215 101.326 21.0399 104.475 24.2579 103.505L34.3313 100.468C34.6257 100.379 34.9432 100.509 35.0872 100.775L40.0242 109.921C41.6035 112.846 45.933 112.64 47.2209 109.585L56.8109 86.8392C56.9367 86.835 57.0625 86.835 57.1883 86.8392L66.7783 109.585C68.0686 112.645 72.398 112.842 73.9753 109.921L78.9122 100.775C79.0561 100.508 79.3738 100.379 79.6681 100.468L89.7413 103.505C92.9612 104.476 95.8773 101.324 94.5886 98.2678L85.3873 76.4446C86.1225 75.5289 87.1485 74.8576 88.3295 74.5572C92.3302 73.5398 95.0553 69.9943 94.9568 65.9352C94.9132 64.1465 95.7704 62.4553 97.2497 61.4114C100.606 59.0426 101.844 54.7693 100.259 51.0195C99.5616 49.3662 99.7391 47.4849 100.734 45.9867ZM44.136 108.329C43.9265 108.826 43.2304 108.856 42.9751 108.383L38.0381 99.2377C37.145 97.5833 35.1743 96.7818 33.3523 97.3308L23.2789 100.368C22.7563 100.525 22.2876 100.019 22.4969 99.5229L30.6599 80.1607C32.5018 82.6394 35.5806 84.0208 38.8218 83.6489C40.6307 83.4417 42.4238 84.1238 43.6183 85.4744C45.3512 87.4344 47.7931 88.4748 50.2844 88.4745C51.0724 88.4745 51.8651 88.3663 52.6421 88.1543L44.136 108.329ZM90.7217 100.368L80.6485 97.3306C78.8265 96.7816 76.8553 97.5833 75.9625 99.2375L71.0255 108.383C70.7706 108.856 70.0737 108.825 69.8644 108.329L61.36 88.158C64.5928 89.0396 68.1016 88.053 70.3823 85.4739C71.5766 84.1232 73.369 83.4407 75.1787 83.6485C78.4231 84.021 81.4999 82.6392 83.3406 80.1605L91.5041 99.5221C91.7139 100.02 91.2425 100.524 90.7217 100.368ZM97.938 44.1934C96.3389 46.6003 96.0539 49.6221 97.1756 52.2769C98.162 54.6112 97.3918 57.2712 95.3022 58.746C92.926 60.4229 91.5491 63.1396 91.619 66.0128C91.6805 68.5398 89.9838 70.747 87.4932 71.3803C85.1635 71.9729 83.2073 73.4626 82.0341 75.4771C82.031 75.483 82.0276 75.4887 82.0245 75.4943C81.7758 75.9238 81.5603 76.3757 81.3868 76.8493C80.5151 79.2273 78.1231 80.6846 75.5664 80.3902C72.6609 80.0564 69.78 81.153 67.8614 83.3228C66.1743 85.2307 63.4069 85.7388 61.1318 84.5587C60.3581 84.1571 59.5387 83.8784 58.7019 83.7172C58.695 83.7156 58.6881 83.7141 58.6812 83.7128C57.5697 83.5013 56.4275 83.5015 55.316 83.7137C55.312 83.7145 55.3082 83.7154 55.3042 83.7161C54.4655 83.8773 53.6445 84.1564 52.869 84.5587C50.5939 85.7391 47.8267 85.2307 46.1397 83.3228C44.4479 81.4098 42.0092 80.3314 39.464 80.3314C39.1224 80.3314 38.7784 80.3508 38.4344 80.3904C35.8801 80.6842 33.4854 79.2278 32.6138 76.8495C32.4403 76.3763 32.2252 75.9251 31.9767 75.4959C31.9729 75.4889 31.9692 75.4823 31.9654 75.4753C30.792 73.4617 28.8364 71.9727 26.5074 71.3803C24.0168 70.7468 22.3203 68.5398 22.3818 66.0131C22.4517 63.1396 21.0748 60.4231 18.6986 58.746C16.6092 57.2714 15.8386 54.6112 16.8252 52.2767C17.9469 49.6219 17.6619 46.6001 16.0628 44.1932C14.6567 42.0768 14.9164 39.3227 16.6947 37.4962C18.7167 35.4189 19.5621 32.4999 18.956 29.6881C18.423 27.2153 19.6779 24.7395 22.0075 23.6672C24.6569 22.4479 26.5183 20.0261 26.9872 17.1889C27.3994 14.6939 29.4799 12.8306 32.0464 12.6578C34.9652 12.4611 37.5917 10.8636 39.0719 8.38403C40.3738 6.20397 42.9993 5.20516 45.4554 5.95459C48.2496 6.80706 51.2859 6.24947 53.578 4.46294C55.593 2.89188 58.4083 2.89188 60.424 4.46294C62.7158 6.24925 65.7517 6.80728 68.5462 5.95459C71.0033 5.20494 73.6284 6.20419 74.9298 8.38403C76.41 10.8633 79.0365 12.4609 81.9553 12.6578C84.5218 12.8308 86.6023 14.6941 87.0145 17.1889C87.4834 20.0261 89.345 22.4479 91.9942 23.6674C94.324 24.7395 95.5787 27.2153 95.0457 29.6881C94.4396 32.4999 95.2848 35.4189 97.307 37.4962C99.0844 39.3227 99.3441 42.0768 97.938 44.1934Z"
          fill="black"
        />
        <path
          d="M25.3724 42.0282C26.2868 42.1386 27.1211 41.4995 27.2336 40.6004C29.0776 25.8752 41.8748 14.7713 57.0005 14.7713C72.1335 14.7713 84.9312 25.8811 86.7692 40.6142C86.8729 41.4456 87.593 42.0553 88.4244 42.0553C88.4923 42.0553 88.5607 42.0511 88.6297 42.0428C89.545 41.9326 90.1961 41.1142 90.0841 40.215C88.041 23.8391 73.8182 11.4902 57.0005 11.4902C40.1906 11.4902 25.9687 23.8326 23.9189 40.1996C23.8064 41.0989 24.457 41.9175 25.3724 42.0282Z"
          fill="black"
        />
        <path
          d="M88.6294 46.4321C87.7168 46.3227 86.8811 46.9617 86.7689 47.861C84.9315 62.5947 72.1337 73.7052 57 73.7052C41.8732 73.7052 29.076 62.6001 27.2326 47.8739C27.1202 46.9748 26.2875 46.3359 25.3714 46.4459C24.4563 46.5564 23.8053 47.3751 23.9179 48.2744C25.9668 64.6428 40.1888 76.986 56.9998 76.986C73.8183 76.986 88.0412 64.6365 90.0834 48.2598C90.1958 47.3607 89.5448 46.5424 88.6294 46.4321Z"
          fill="black"
        />
        <path
          d="M80.5788 37.4673C80.1544 36.1842 78.9935 35.3236 77.6208 35.2752L64.5284 34.8139L60.0358 22.7232C59.5649 21.4557 58.3733 20.6367 56.9999 20.6367C55.6266 20.6367 54.4349 21.4557 53.964 22.7232L49.4715 34.8139L36.3791 35.2752C35.0066 35.3236 33.8454 36.1842 33.4211 37.4673C32.9967 38.7505 33.4211 40.1173 34.5025 40.949L44.8184 48.8828L41.2194 61.2588C40.842 62.5562 41.3162 63.907 42.4275 64.7002C43.5374 65.4925 44.9916 65.5196 46.1318 64.7654L56.9997 57.5779L67.8678 64.7649C69.0067 65.5183 70.4608 65.4929 71.5721 64.6998C72.6834 63.9068 73.1574 62.556 72.78 61.2584L69.181 48.8826L79.4969 40.9487C80.5785 40.1171 81.0032 38.7505 80.5788 37.4673ZM67.0658 46.3429C66.0248 47.1435 65.5869 48.4676 65.9503 49.7158L69.4883 61.8816L58.8048 54.8166C58.2564 54.4539 57.628 54.2726 56.9997 54.2726C56.3714 54.2726 55.743 54.4539 55.1948 54.8164L44.5111 61.8814L48.0491 49.7158C48.4123 48.4672 47.9745 47.1435 46.9339 46.3429L36.7927 38.5436L49.6634 38.0899C50.9842 38.0433 52.1304 37.225 52.5835 36.0054L56.9999 24.1199L61.4163 36.0054C61.8696 37.225 63.0156 38.0433 64.3364 38.0897L77.2066 38.5434L67.0658 46.3429Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_163_4807">
          <rect width="114" height="112" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )

  const renderEndCardIcon = (isCompleted) => {
    if (isCompleted) {
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="100"
          height="100"
          viewBox="0 0 114 112"
          fill="none"
        >
          <g clipPath="url(#clip0_128_2587)">
            <path
              d="M41.4997 109.152L36.5627 100.006C36.0433 99.0439 34.9017 98.5793 33.8417 98.8989L23.7683 101.936C21.8986 102.5 20.2053 100.67 20.9537 98.8947L38.7187 56.7598L63.4434 66.8216L45.6783 108.957C44.9302 110.732 42.4159 110.849 41.4997 109.152Z"
              fill="#FFE07D"
            />
            <path
              d="M50.4421 61.5312L34.7246 98.81C35.4852 98.8767 36.1872 99.3102 36.5631 100.007L41.5 109.152C42.4163 110.85 44.9305 110.732 45.6789 108.957L63.4439 66.8222L50.4421 61.5312Z"
              fill="#FFD064"
            />
            <path
              d="M72.5003 109.152L77.4373 100.006C77.9567 99.0439 79.0983 98.5793 80.1583 98.8989L90.2318 101.936C92.1014 102.5 93.7947 100.67 93.0464 98.8947L75.2813 56.7598L50.5566 66.8216L68.3217 108.957C69.0701 110.732 71.5841 110.849 72.5003 109.152Z"
              fill="#FFE07D"
            />
            <path
              d="M93.0466 98.8947L75.2816 56.7598L62.3252 62.0325L78.0369 99.2977C78.6194 98.8405 79.4087 98.6727 80.1584 98.8987L90.2318 101.936C92.1017 102.5 93.795 100.67 93.0466 98.8947Z"
              fill="#FFD064"
            />
            <path
              d="M61.4614 3.17781C63.3195 4.62616 65.7856 5.07897 68.0507 4.38794C71.2358 3.41625 74.6825 4.72809 76.3698 7.55434C77.5699 9.56422 79.703 10.8618 82.0692 11.0213C85.3963 11.2455 88.1281 13.692 88.6622 16.9263C89.0423 19.2262 90.5541 21.1934 92.7019 22.1819C95.722 23.5719 97.3697 26.8227 96.6785 30.0281C96.1871 32.3077 96.8738 34.6785 98.513 36.3624C100.818 38.7304 101.159 42.3463 99.3362 45.0901C98.0398 47.0414 97.8083 49.4957 98.7176 51.6478C99.9963 54.674 98.9848 58.1668 96.2762 60.0782C94.35 61.4375 93.2316 63.644 93.2881 65.9735C93.3679 69.2491 91.1404 72.1471 87.9117 72.9683C85.6156 73.5521 83.7613 75.2128 82.9578 77.4052C81.8276 80.4878 78.685 82.3997 75.3725 82.0195C73.0168 81.7491 70.6772 82.6396 69.1217 84.3986C66.9343 86.872 63.301 87.5394 60.3519 86.0093C58.2547 84.9212 55.7456 84.9212 53.6484 86.0093C50.6993 87.5394 47.066 86.8722 44.8787 84.3986C43.3232 82.6396 40.9835 81.7491 38.6278 82.0195C35.3154 82.3997 32.1726 80.488 31.0426 77.4052C30.2388 75.2128 28.3847 73.5523 26.0887 72.9683C22.86 72.1471 20.6325 69.2493 20.7122 65.9735C20.769 63.644 19.6506 61.4375 17.7242 60.0782C15.0156 58.1668 14.004 54.6738 15.2828 51.6478C16.1921 49.4957 15.9605 47.0414 14.6642 45.0901C12.8413 42.3463 13.1822 38.7304 15.4874 36.3624C17.1266 34.6785 17.8132 32.3074 17.3218 30.0281C16.6309 26.8225 18.2784 23.5719 21.2985 22.1819C23.4462 21.1934 24.9583 19.2264 25.3381 16.9263C25.8725 13.692 28.6041 11.2455 31.9312 11.0213C34.2974 10.8618 36.4306 9.56422 37.6305 7.55434C39.318 4.72809 42.7648 3.41625 45.9496 4.38794C48.2147 5.07897 50.6809 4.62594 52.5389 3.17781C55.1525 1.14125 58.8488 1.14125 61.4614 3.17781Z"
              fill="#E8AE4D"
            />
            <path
              d="M57.0007 75.3456C74.4876 75.3456 88.6635 61.4183 88.6635 44.2382C88.6635 27.0581 74.4876 13.1309 57.0007 13.1309C39.5138 13.1309 25.3379 27.0581 25.3379 44.2382C25.3379 61.4183 39.5138 75.3456 57.0007 75.3456Z"
              fill="#FFE07D"
            />
            <path
              d="M57.001 13.1309C54.5769 13.1309 52.217 13.3995 49.9492 13.9063C64.0416 17.0557 74.5603 29.4395 74.5603 44.2382C74.5603 59.0369 64.0416 71.421 49.9492 74.5701C52.217 75.0769 54.5771 75.3456 57.001 75.3456C74.4879 75.3456 88.6638 61.4184 88.6638 44.2382C88.6638 27.058 74.4879 13.1309 57.001 13.1309Z"
              fill="#FFD064"
            />
            <path
              d="M58.4679 23.2858L62.9852 35.4433C63.2031 36.03 63.7613 36.4283 64.3966 36.4509L77.5614 36.9148C79.0175 36.9662 79.6156 38.7749 78.4682 39.6573L68.0953 47.6351C67.5948 48.0201 67.3815 48.6648 67.5563 49.2652L71.1754 61.7095C71.5757 63.0859 70.0095 64.2037 68.8009 63.4044L57.8728 56.1775C57.3453 55.8288 56.6555 55.8288 56.128 56.1775L45.1998 63.4044C43.991 64.2037 42.4251 63.0859 42.8254 61.7095L46.4445 49.2652C46.619 48.6648 46.406 48.0201 45.9054 47.6351L35.5325 39.6573C34.3852 38.7749 34.9835 36.9662 36.4394 36.9148L49.6042 36.4509C50.2394 36.4286 50.7976 36.03 51.0156 35.4433L55.5329 23.2858C56.0325 21.9412 57.9683 21.9412 58.4679 23.2858Z"
              fill="#E8AE4D"
            />
            <path
              d="M68.8014 63.4044C70.0102 64.2037 71.5761 63.0859 71.1758 61.7095L70.5196 59.4531C69.8154 60.6845 69.0274 61.8633 68.165 62.9835L68.8014 63.4044Z"
              fill="#E5A32E"
            />
            <path
              d="M77.562 36.9156L73.6426 36.7773C74.1211 38.6864 74.4194 40.665 74.5203 42.6947L78.4689 39.6578C79.6162 38.7756 79.0182 36.9668 77.562 36.9156Z"
              fill="#E5A32E"
            />
            <path
              d="M100.734 45.9867C102.993 42.5867 102.576 38.1629 99.7197 35.2286C98.4608 33.9353 97.9346 32.1184 98.3118 30.368C99.1684 26.3957 97.1527 22.4186 93.41 20.6964C91.7606 19.9373 90.6019 18.4297 90.31 16.6635C89.648 12.6556 86.306 9.66241 82.183 9.38459C80.366 9.26209 78.731 8.26766 77.8094 6.72416C75.7182 3.22219 71.5025 1.61722 67.5548 2.82122C65.8154 3.35191 63.9255 3.00453 62.4985 1.89263C59.2608 -0.630875 54.7389 -0.630875 51.5015 1.89263C50.0747 3.00497 48.1843 3.35191 46.4452 2.82122C42.4984 1.617 38.2815 3.22197 36.1905 6.72416C35.2689 8.26766 33.6342 9.26209 31.8171 9.38459C27.6939 9.66241 24.3521 12.6558 23.6899 16.6635C23.3982 18.4297 22.2393 19.9373 20.5901 20.6964C16.8477 22.4188 14.832 26.3959 15.6881 30.368C16.0655 32.1184 15.5391 33.9356 14.2805 35.2288C11.4242 38.1631 11.0067 42.5871 13.2658 45.9872C14.2613 47.4854 14.4388 49.3666 13.7403 51.0193C12.1556 54.7691 13.3934 59.0424 16.7497 61.411C18.229 62.4551 19.086 64.1463 19.0424 65.935C18.9438 69.9939 21.6691 73.5394 25.6698 74.557C26.8507 74.8573 27.8767 75.5287 28.6119 76.4444L19.4109 98.2675C18.1215 101.326 21.0399 104.475 24.2579 103.505L34.3313 100.468C34.6257 100.379 34.9432 100.509 35.0872 100.775L40.0242 109.921C41.6035 112.846 45.933 112.64 47.2209 109.585L56.8109 86.8392C56.9367 86.835 57.0625 86.835 57.1883 86.8392L66.7783 109.585C68.0686 112.645 72.398 112.842 73.9753 109.921L78.9122 100.775C79.0561 100.508 79.3738 100.379 79.6681 100.468L89.7413 103.505C92.9612 104.476 95.8773 101.324 94.5886 98.2678L85.3873 76.4446C86.1225 75.5289 87.1485 74.8576 88.3295 74.5572C92.3302 73.5398 95.0553 69.9943 94.9568 65.9352C94.9132 64.1465 95.7704 62.4553 97.2497 61.4114C100.606 59.0426 101.844 54.7693 100.259 51.0195C99.5616 49.3662 99.7391 47.4849 100.734 45.9867ZM44.136 108.329C43.9265 108.826 43.2304 108.856 42.9751 108.383L38.0381 99.2377C37.145 97.5833 35.1743 96.7818 33.3523 97.3308L23.2789 100.368C22.7563 100.525 22.2876 100.019 22.4969 99.5229L30.6599 80.1607C32.5018 82.6394 35.5806 84.0208 38.8218 83.6489C40.6307 83.4417 42.4238 84.1238 43.6183 85.4744C45.3512 87.4344 47.7931 88.4748 50.2844 88.4745C51.0724 88.4745 51.8651 88.3663 52.6421 88.1543L44.136 108.329ZM90.7217 100.368L80.6485 97.3306C78.8265 96.7816 76.8553 97.5833 75.9625 99.2375L71.0255 108.383C70.7706 108.856 70.0737 108.825 69.8644 108.329L61.36 88.158C64.5928 89.0396 68.1016 88.053 70.3823 85.4739C71.5766 84.1232 73.369 83.4407 75.1787 83.6485C78.4231 84.021 81.4999 82.6392 83.3406 80.1605L91.5041 99.5221C91.7139 100.02 91.2425 100.524 90.7217 100.368ZM97.938 44.1934C96.3389 46.6003 96.0539 49.6221 97.1756 52.2769C98.162 54.6112 97.3918 57.2712 95.3022 58.746C92.926 60.4229 91.5491 63.1396 91.619 66.0128C91.6805 68.5398 89.9838 70.747 87.4932 71.3803C85.1635 71.9729 83.2073 73.4626 82.0341 75.4771C82.031 75.483 82.0276 75.4887 82.0245 75.4943C81.7758 75.9238 81.5603 76.3757 81.3868 76.8493C80.5151 79.2273 78.1231 80.6846 75.5664 80.3902C72.6609 80.0564 69.78 81.153 67.8614 83.3228C66.1743 85.2307 63.4069 85.7388 61.1318 84.5587C60.3581 84.1571 59.5387 83.8784 58.7019 83.7172C58.695 83.7156 58.6881 83.7141 58.6812 83.7128C57.5697 83.5013 56.4275 83.5015 55.316 83.7137C55.312 83.7145 55.3082 83.7154 55.3042 83.7161C54.4655 83.8773 53.6445 84.1564 52.869 84.5587C50.5939 85.7391 47.8267 85.2307 46.1397 83.3228C44.4479 81.4098 42.0092 80.3314 39.464 80.3314C39.1224 80.3314 38.7784 80.3508 38.4344 80.3904C35.8801 80.6842 33.4854 79.2278 32.6138 76.8495C32.4403 76.3763 32.2252 75.9251 31.9767 75.4959C31.9729 75.4889 31.9692 75.4823 31.9654 75.4753C30.792 73.4617 28.8364 71.9727 26.5074 71.3803C24.0168 70.7468 22.3203 68.5398 22.3818 66.0131C22.4517 63.1396 21.0748 60.4231 18.6986 58.746C16.6092 57.2714 15.8386 54.6112 16.8252 52.2767C17.9469 49.6219 17.6619 46.6001 16.0628 44.1932C14.6567 42.0768 14.9164 39.3227 16.6947 37.4962C18.7167 35.4189 19.5621 32.4999 18.956 29.6881C18.423 27.2153 19.6779 24.7395 22.0075 23.6672C24.6569 22.4479 26.5183 20.0261 26.9872 17.1889C27.3994 14.6939 29.4799 12.8306 32.0464 12.6578C34.9652 12.4611 37.5917 10.8636 39.0719 8.38403C40.3738 6.20397 42.9993 5.20516 45.4554 5.95459C48.2496 6.80706 51.2859 6.24947 53.578 4.46294C55.593 2.89188 58.4083 2.89188 60.424 4.46294C62.7158 6.24925 65.7517 6.80728 68.5462 5.95459C71.0033 5.20494 73.6284 6.20419 74.9298 8.38403C76.41 10.8633 79.0365 12.4609 81.9553 12.6578C84.5218 12.8308 86.6023 14.6941 87.0145 17.1889C87.4834 20.0261 89.345 22.4479 91.9942 23.6674C94.324 24.7395 95.5787 27.2153 95.0457 29.6881C94.4396 32.4999 95.2848 35.4189 97.307 37.4962C99.0844 39.3227 99.3431 42.0768 97.937 44.1934Z"
              fill="black"
            />
            <path
              d="M25.3724 42.0282C26.2868 42.1386 27.1211 41.4995 27.2336 40.6004C29.0776 25.8752 41.8748 14.7713 57.0005 14.7713C72.1335 14.7713 84.9312 25.8811 86.7692 40.6142C86.8729 41.4456 87.593 42.0553 88.4244 42.0553C88.4923 42.0553 88.5607 42.0511 88.6297 42.0428C89.545 41.9326 90.1961 41.1142 90.0841 40.215C88.041 23.8391 73.8182 11.4902 57.0005 11.4902C40.1906 11.4902 25.9687 23.8326 23.9189 40.1996C23.8064 41.0989 24.457 41.9175 25.3724 42.0282Z"
              fill="black"
            />
            <path
              d="M88.6294 46.4321C87.7168 46.3227 86.8811 46.9617 86.7689 47.861C84.9315 62.5947 72.1337 73.7052 57 73.7052C41.8732 73.7052 29.076 62.6001 27.2326 47.8739C27.1202 46.9748 26.2875 46.3359 25.3714 46.4459C24.4563 46.5564 23.8053 47.3751 23.9179 48.2744C25.9668 64.6428 40.1888 76.986 56.9998 76.986C73.8183 76.986 88.0412 64.6365 90.0834 48.2598C90.1958 47.3607 89.5448 46.5424 88.6294 46.4321Z"
              fill="black"
            />
            <path
              d="M80.5788 37.4673C80.1544 36.1842 78.9935 35.3236 77.6208 35.2752L64.5284 34.8139L60.0358 22.7232C59.5649 21.4557 58.3733 20.6367 56.9999 20.6367C55.6266 20.6367 54.4349 21.4557 53.964 22.7232L49.4715 34.8139L36.3791 35.2752C35.0066 35.3236 33.8454 36.1842 33.4211 37.4673C32.9967 38.7505 33.4211 40.1173 34.5025 40.949L44.8184 48.8828L41.2194 61.2588C40.842 62.5562 41.3162 63.907 42.4275 64.7002C43.5374 65.4925 44.9916 65.5196 46.1318 64.7654L56.9997 57.5779L67.8678 64.7649C69.0067 65.5183 70.4608 65.4929 71.5721 64.6998C72.6834 63.9068 73.1574 62.556 72.78 61.2584L69.181 48.8826L79.4969 40.9487C80.5785 40.1171 81.0032 38.7505 80.5788 37.4673ZM67.0658 46.3429C66.0248 47.1435 65.5869 48.4676 65.9503 49.7158L69.4883 61.8816L58.8048 54.8166C58.2564 54.4539 57.628 54.2726 56.9997 54.2726C56.3714 54.2726 55.743 54.4539 55.1948 54.8164L44.5111 61.8814L48.0491 49.7158C48.4123 48.4672 47.9745 47.1435 46.9339 46.3429L36.7927 38.5436L49.6634 38.0899C50.9842 38.0433 52.1304 37.225 52.5835 36.0054L56.9999 24.1199L61.4163 36.0054C61.8696 37.225 63.0156 38.0433 64.3364 38.0897L77.2066 38.5434L67.0658 46.3429Z"
              fill="black"
            />
          </g>
          <defs>
            <clipPath id="clip0_128_2587">
              <rect width="114" height="112" fill="white" />
            </clipPath>
          </defs>
        </svg>
      )
    }
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="100"
        height="100"
        viewBox="0 0 114 112"
        fill="none"
      >
        <g clipPath="url(#clip0_229_2701)">
          <path
            d="M41.4987 109.152L36.5618 100.006C36.0423 99.0439 34.9008 98.5793 33.8407 98.8989L23.7673 101.936C21.8976 102.5 20.2043 100.67 20.9527 98.8947L38.7177 56.7598L63.4424 66.8216L45.6773 108.957C44.9292 110.732 42.415 110.849 41.4987 109.152Z"
            fill="#D9D9D9"
          />
          <path
            d="M50.4402 61.5308L34.7227 98.8095C35.4832 98.8762 36.1853 99.3098 36.5611 100.006L41.4981 109.152C42.4143 110.849 44.9286 110.732 45.6769 108.957L63.442 66.8217L50.4402 61.5308Z"
            fill="#D9D9D9"
          />
          <path
            d="M72.5003 109.152L77.4373 100.006C77.9567 99.0439 79.0983 98.5793 80.1583 98.8989L90.2318 101.936C92.1014 102.5 93.7947 100.67 93.0464 98.8947L75.2813 56.7598L50.5566 66.8216L68.3217 108.957C69.0701 110.732 71.5841 110.849 72.5003 109.152Z"
            fill="#D9D9D9"
          />
          <path
            d="M93.0457 98.8947L75.2806 56.7598L62.3242 62.0325L78.036 99.2977C78.6184 98.8405 79.4077 98.6727 80.1574 98.8987L90.2308 101.936C92.1007 102.5 93.794 100.67 93.0457 98.8947Z"
            fill="#D9D9D9"
          />
          <path
            d="M61.4605 3.17732C63.3185 4.62567 65.7847 5.07848 68.0497 4.38745C71.2348 3.41576 74.6816 4.72761 76.3689 7.55386C77.569 9.56373 79.702 10.8614 82.0682 11.0208C85.3953 11.245 88.1271 13.6915 88.6613 16.9258C89.0413 19.2257 90.5532 21.1929 92.7009 22.1814C95.721 23.5714 97.3687 26.8222 96.6776 30.0276C96.1861 32.3072 96.8728 34.678 98.512 36.3619C100.817 38.7299 101.158 42.3458 99.3352 45.0896C98.0389 47.0409 97.8073 49.4952 98.7166 51.6473C99.9953 54.6735 98.9838 58.1663 96.2752 60.0777C94.349 61.437 93.2306 63.6435 93.2872 65.973C93.3669 69.2486 91.1394 72.1466 87.9107 72.9678C85.6147 73.5516 83.7604 75.2124 82.9568 77.4047C81.8266 80.4873 78.684 82.3992 75.3716 82.019C73.0159 81.7486 70.6762 82.6391 69.1207 84.3981C66.9333 86.8715 63.3 87.5389 60.351 86.0088C58.2538 84.9207 55.7447 84.9207 53.6474 86.0088C50.6984 87.5389 47.0651 86.8717 44.8777 84.3981C43.3222 82.6391 40.9825 81.7486 38.6268 82.019C35.3144 82.3992 32.1716 80.4875 31.0416 77.4047C30.2378 75.2124 28.3838 73.5518 26.0877 72.9678C22.859 72.1466 20.6315 69.2488 20.7112 65.973C20.768 63.6435 19.6496 61.437 17.7232 60.0777C15.0146 58.1663 14.0031 54.6733 15.2818 51.6473C16.1911 49.4952 15.9595 47.0409 14.6632 45.0896C12.8403 42.3458 13.1812 38.7299 15.4864 36.3619C17.1256 34.678 17.8123 32.307 17.3209 30.0276C16.63 26.822 18.2774 23.5714 21.2975 22.1814C23.4452 21.1929 24.9573 19.2259 25.3372 16.9258C25.8715 13.6915 28.6031 11.245 31.9302 11.0208C34.2964 10.8614 36.4297 9.56373 37.6296 7.55386C39.3171 4.72761 42.7638 3.41576 45.9487 4.38745C48.2137 5.07848 50.6799 4.62545 52.538 3.17732C55.1515 1.14076 58.8478 1.14076 61.4605 3.17732Z"
            fill="#D9D9D9"
          />
          <path
            d="M56.9988 75.3451C74.4857 75.3451 88.6616 61.4178 88.6616 44.2377C88.6616 27.0576 74.4857 13.1304 56.9988 13.1304C39.5119 13.1304 25.3359 27.0576 25.3359 44.2377C25.3359 61.4178 39.5119 75.3451 56.9988 75.3451Z"
            fill="#D9D9D9"
          />
          <path
            d="M56.999 13.1304C54.575 13.1304 52.215 13.399 49.9473 13.9058C64.0396 17.0552 74.5584 29.4391 74.5584 44.2377C74.5584 59.0364 64.0396 71.4205 49.9473 74.5696C52.215 75.0764 54.5752 75.3451 56.999 75.3451C74.486 75.3451 88.6618 61.4179 88.6618 44.2377C88.6618 27.0575 74.486 13.1304 56.999 13.1304Z"
            fill="#D9D9D9"
          />
          <path
            d="M58.4669 23.2858L62.9842 35.4433C63.2022 36.03 63.7604 36.4283 64.3956 36.4509L77.5604 36.9148C79.0166 36.9662 79.6146 38.7749 78.4673 39.6573L68.0944 47.6351C67.5938 48.0201 67.3805 48.6648 67.5553 49.2652L71.1744 61.7095C71.5747 63.0859 70.0085 64.2037 68.8 63.4044L57.8718 56.1775C57.3443 55.8288 56.6545 55.8288 56.127 56.1775L45.1989 63.4044C43.9901 64.2037 42.4241 63.0859 42.8245 61.7095L46.4435 49.2652C46.6181 48.6648 46.405 48.0201 45.9045 47.6351L35.5316 39.6573C34.3842 38.7749 34.9825 36.9662 36.4384 36.9148L49.6032 36.4509C50.2385 36.4286 50.7967 36.03 51.0146 35.4433L55.5319 23.2858C56.0315 21.9412 57.9673 21.9412 58.4669 23.2858Z"
            fill="#D9D9D9"
          />
          <path
            d="M68.7985 63.4039C70.0073 64.2032 71.5732 63.0854 71.1729 61.709L70.5167 59.4526C69.8124 60.684 69.0245 61.8628 68.1621 62.983L68.7985 63.4039Z"
            fill="#D9D9D9"
          />
          <path
            d="M77.56 36.9146L73.6406 36.7764C74.1191 38.6854 74.4175 40.664 74.5183 42.6938L78.4669 39.6569C79.6143 38.7746 79.0162 36.9658 77.56 36.9146Z"
            fill="#D9D9D9"
          />
          <path
            d="M100.733 45.9867C102.992 42.5867 102.575 38.1629 99.7187 35.2286C98.4598 33.9353 97.9337 32.1184 98.3109 30.368C99.1674 26.3957 97.1517 22.4186 93.4091 20.6964C91.7596 19.9373 90.6009 18.4297 90.309 16.6635C89.6471 12.6556 86.305 9.66241 82.1821 9.38459C80.365 9.26209 78.73 8.26766 77.8084 6.72416C75.7173 3.22219 71.5015 1.61722 67.5538 2.82122C65.8144 3.35191 63.9245 3.00453 62.4975 1.89263C59.2598 -0.630875 54.7379 -0.630875 51.5005 1.89263C50.0737 3.00497 48.1833 3.35191 46.4442 2.82122C42.4974 1.617 38.2805 3.22197 36.1895 6.72416C35.2679 8.26766 33.6332 9.26209 31.8161 9.38459C27.693 9.66241 24.3511 12.6558 23.6889 16.6635C23.3973 18.4297 22.2383 19.9373 20.5891 20.6964C16.8467 22.4188 14.831 26.3959 15.6871 30.368C16.0645 32.1184 15.5382 33.9356 14.2795 35.2288C11.4232 38.1631 11.0058 42.5871 13.2648 45.9872C14.2603 47.4854 14.4388 49.3666 13.7393 51.0193C12.1547 54.7691 13.3924 59.0424 16.7487 61.411C18.2281 62.4551 19.0851 64.1463 19.0414 65.935C18.9428 69.9939 21.6681 73.5394 25.6688 74.557C26.8498 74.8573 27.8758 75.5287 28.611 76.4444L19.4099 98.2675C18.1205 101.326 21.0389 104.475 24.2569 103.505L34.3303 100.468C34.6247 100.379 34.9422 100.509 35.0863 100.775L40.0232 109.921C41.6025 112.846 45.9321 112.64 47.2199 109.585L56.8099 86.8392C56.9357 86.835 57.0615 86.835 57.1873 86.8392L66.7774 109.585C68.0677 112.645 72.397 112.842 73.9743 109.921L78.9112 100.775C79.0551 100.508 79.3728 100.379 79.6672 100.468L89.7404 103.505C92.9602 104.476 95.8763 101.324 94.5876 98.2678L85.3863 76.4446C86.1215 75.5289 87.1475 74.8576 88.3285 74.5572C92.3292 73.5398 95.0543 69.9943 94.9559 65.9352C94.9122 64.1465 95.7694 62.4553 97.2488 61.4114C100.605 59.0426 101.843 54.7693 100.258 51.0195C99.5606 49.3662 99.7381 47.4849 100.733 45.9867ZM44.135 108.329C43.9255 108.826 43.2295 108.856 42.9741 108.383L38.0371 99.2377C37.1441 97.5833 35.1733 96.7818 33.3513 97.3308L23.2779 100.368C22.7553 100.525 22.2866 100.019 22.4959 99.5229L30.659 80.1607C32.5008 82.6394 35.5797 84.0208 38.8209 83.6489C40.6297 83.4417 42.4228 84.1238 43.6173 85.4744C45.3503 87.4344 47.7921 88.4748 50.2834 88.4745C51.0714 88.4745 51.8641 88.3663 52.6412 88.1543L44.135 108.329ZM90.7207 100.368L80.6475 97.3306C78.8255 96.7816 76.8544 97.5833 75.9615 99.2375L71.0245 108.383C70.7696 108.856 70.0727 108.825 69.8634 108.329L61.359 88.158C64.5918 89.0396 68.1006 88.053 70.3813 85.4739C71.5756 84.1232 73.368 83.4407 75.1778 83.6485C78.4221 84.021 81.499 82.6392 83.3397 80.1605L91.5031 99.5221C91.7129 100.02 91.2415 100.524 90.7207 100.368ZM97.937 44.1934C96.3379 46.6003 96.0529 49.6221 97.1746 52.2769C98.161 54.6112 97.3908 57.2712 95.3012 58.746C92.925 60.4229 91.5481 63.1396 91.618 66.0128C91.6795 68.5398 89.9828 70.747 87.4922 71.3803C85.1625 71.9729 83.2063 73.4626 82.0331 75.4771C82.03 75.483 82.0267 75.4887 82.0235 75.4943C81.7748 75.9238 81.5593 76.3757 81.3859 76.8493C80.5142 79.2273 78.1222 80.6846 75.5654 80.3902C72.66 80.0564 69.779 81.153 67.8604 83.3228C66.1733 85.2307 63.4059 85.7388 61.1308 84.5587C60.3571 84.1571 59.5377 83.8784 58.701 83.7172C58.6941 83.7156 58.6872 83.7141 58.6803 83.7128C57.5688 83.5013 56.4265 83.5015 55.315 83.7137C55.311 83.7145 55.3072 83.7154 55.3032 83.7161C54.4645 83.8773 53.6435 84.1564 52.868 84.5587C50.5929 85.7391 47.8258 85.2307 46.1387 83.3228C44.447 81.4098 42.0082 80.3314 39.463 80.3314C39.1215 80.3314 38.7775 80.3508 38.4335 80.3904C35.8791 80.6842 33.4845 79.2278 32.6128 76.8495C32.4393 76.3763 32.2242 75.9251 31.9758 75.4959C31.972 75.4889 31.9682 75.4823 31.9644 75.4753C30.791 73.4617 28.8354 71.9727 26.5064 71.3803C24.0158 70.7468 22.3194 68.5398 22.3808 66.0131C22.4507 63.1396 21.0738 60.4231 18.6976 58.746C16.6082 57.2714 15.8376 54.6112 16.8242 52.2767C17.946 49.6219 17.661 46.6001 16.0618 44.1932C14.6558 42.0768 14.9154 39.3227 16.6937 37.4962C18.7157 35.4189 19.5611 32.4999 18.955 29.6881C18.423 27.2153 19.6779 24.7395 22.0075 23.6672C24.6569 22.4479 26.5183 20.0261 26.9872 17.1889C27.3994 14.6939 29.4799 12.8306 32.0464 12.6578C34.9652 12.4611 37.5917 10.8636 39.0719 8.38403C40.3738 6.20397 42.9993 5.20516 45.4554 5.95459C48.2496 6.80706 51.2859 6.24947 53.577 4.46294C55.592 2.89188 58.4073 2.89188 60.423 4.46294C62.7148 6.24925 65.7507 6.80728 68.5453 5.95459C71.0023 5.20494 73.6274 6.20419 74.9288 8.38403C76.409 10.8633 79.0355 12.4609 81.9543 12.6578C84.5209 12.8308 86.6013 14.6941 87.0135 17.1889C87.4824 20.0261 89.344 22.4479 91.9932 23.6674C94.3231 24.7395 95.5777 27.2153 95.0447 29.6881C94.4386 32.4999 95.2838 35.4189 97.306 37.4962C99.0835 39.3227 99.3431 42.0768 97.937 44.1934Z"
            fill="black"
          />
          <path
            d="M25.3704 42.0282C26.2849 42.1386 27.1192 41.4995 27.2316 40.6004C29.0757 25.8752 41.8728 14.7713 56.9985 14.7713C72.1316 14.7713 84.9292 25.8811 86.7672 40.6142C86.871 41.4456 87.5911 42.0553 88.4225 42.0553C88.4904 42.0553 88.5587 42.0511 88.6277 42.0428C89.5431 41.9326 90.1941 41.1142 90.0821 40.215C88.039 23.8391 73.8162 11.4902 56.9985 11.4902C40.1887 11.4902 25.9667 23.8326 23.9169 40.1996C23.8045 41.0989 24.4551 41.9175 25.3704 42.0282Z"
            fill="black"
          />
          <path
            d="M88.6284 46.4316C87.7158 46.3222 86.8802 46.9612 86.7679 47.8605C84.9306 62.5942 72.1327 73.7047 56.999 73.7047C41.8722 73.7047 29.075 62.5997 27.2317 47.8734C27.1192 46.9743 26.2865 46.3354 25.3705 46.4454C24.4554 46.5559 23.8043 47.3747 23.917 48.2739C25.9659 64.6423 40.1878 76.9855 56.9988 76.9855C73.8174 76.9855 88.0402 64.636 90.0824 48.2593C90.1948 47.3602 89.5438 46.5419 88.6284 46.4316Z"
            fill="black"
          />
          <path
            d="M80.5778 37.4673C80.1534 36.1842 78.9925 35.3236 77.6198 35.2752L64.5274 34.8139L60.0349 22.7232C59.5639 21.4557 58.3723 20.6367 56.9989 20.6367C55.6256 20.6367 54.4339 21.4557 53.963 22.7232L49.4705 34.8139L36.3781 35.2752C35.0056 35.3236 33.8445 36.1842 33.4201 37.4673C32.9957 38.7505 33.4201 40.1173 34.5015 40.949L44.8174 48.8828L41.2184 61.2588C40.841 62.5562 41.3153 63.907 42.4265 64.7002C43.5365 65.4925 44.9906 65.5196 46.1309 64.7654L56.9987 57.5779L67.8668 64.7649C69.0057 65.5183 70.4598 65.4929 71.5711 64.6998C72.6824 63.9068 73.1564 62.556 72.779 61.2584L69.18 48.8826L79.4959 40.9487C80.5776 40.1171 81.0032 38.7505 80.5778 37.4673ZM67.0648 46.3429C66.0239 47.1435 65.5859 48.4676 65.9493 49.7158L69.4873 61.8816L58.8038 54.8166C58.2554 54.4539 57.6271 54.2726 56.9987 54.2726C56.3704 54.2726 55.742 54.4539 55.1939 54.8164L44.5101 61.8814L48.0482 49.7158C48.4113 48.4672 47.9736 47.1435 46.9329 46.3429L36.7918 38.5436L49.6624 38.0899C50.9832 38.0433 52.1294 37.225 52.5826 36.0054L56.9989 24.1199L61.4153 36.0054C61.8687 37.225 63.0147 38.0433 64.3355 38.0897L77.2057 38.5434L67.0648 46.3429Z"
            fill="black"
          />
        </g>
        <defs>
          <clipPath id="clip0_229_2701">
            <rect width="114" height="112" fill="white" />
          </clipPath>
        </defs>
      </svg>
    )
  }

  return (
    <div className={styles.flowContainer}>
      <div className={`${styles.gridContainer} ${styles[layoutFormat]}`}>
        {layoutFormat === 'mobile' ? (
          <div className={styles.columnContent}>
            <div className={styles.startCard}>
              <div className={styles.cardIcon}>{renderStartCardIcon()}</div>
              <h3 className={styles.cardTitle}>{data.startCard.title}</h3>
            </div>

            <div
              className={`${styles.mobileVerticalConnection} ${styles.completed}`}
            ></div>

            <div className={styles.episodeList}>
              {centerColumn.map((episode, index) => {
                const globalIndex = data.episodes.findIndex(
                  (ep) => ep.id === episode.id
                )

                const cardData = episode.courseData
                  ? convertCourseToCardFormat(episode.courseData, globalIndex)
                  : convertEpisodeToCardFormat(episode, globalIndex)

                const getWrapperClasses = () => {
                  let classes = [styles.episodeWrapper]

                  if (episode.completed) {
                    classes.push(styles.completed)
                  } else if (episode.accessible) {
                    classes.push(styles.accessible)
                  } else {
                    classes.push(styles.pending)
                  }
                  return classes.join(' ')
                }

                return (
                  <div key={episode.id} className={getWrapperClasses()}>
                    <div
                      className={`${styles.episodeNumber} ${styles.top} ${
                        !episode.accessible ? styles.pending : ''
                      }`}
                    >
                      {episode.position || globalIndex + 1}
                    </div>

                    <div className={styles.curriculumCardContainer}>
                      <div id={`card-${episode.id}`}>
                        <CardCurriculum
                          type="normal"
                          isCurriculum={true}
                          isAccessible={episode.accessible}
                          progressPercent={episode.progress}
                          callback={(action, id, data) => {
                            if (
                              action === 'course' ||
                              action === 'navigation' ||
                              action === 'start'
                            ) {
                              if (!episode.accessible) return
                              if (
                                episode.courseData &&
                                episode.courseData.slug
                              ) {
                                if (episode.progress === 0) {
                                  window.location.href = `/course/${episode.courseData.slug}?from=curriculum`
                                } else {
                                  window.location.href = `/course/${episode.courseData.slug}?from=curriculum`
                                }
                              }
                            } else if (onCourseAction && episode.courseData) {
                              onCourseAction(
                                action,
                                episode.courseData.id,
                                episode.courseData.title
                              )
                            }
                          }}
                          data={cardData}
                          lang="th"
                        />
                      </div>

                      {!episode.accessible && (
                        <div className={styles.curriculumLockOverlay}>
                          <div className={styles.curriculumLockIcon}>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="48"
                              height="48"
                              viewBox="0 0 48 48"
                              fill="none"
                            >
                              <path
                                d="M23.9888 4.37695C26.591 4.37695 29.0865 5.41065 30.9265 7.25065C32.7665 9.09065 33.8002 11.5862 33.8002 14.1884V20.0752C35.3615 20.0752 36.8589 20.6954 37.9629 21.7994C39.0669 22.9034 39.6871 24.4008 39.6871 25.9621V37.7358C39.6871 39.2971 39.0669 40.7944 37.9629 41.8984C36.8589 43.0024 35.3615 43.6226 33.8002 43.6226H14.1774C12.6161 43.6226 11.1187 43.0024 10.0147 41.8984C8.91075 40.7944 8.29053 39.2971 8.29053 37.7358V25.9621C8.29053 24.4008 8.91075 22.9034 10.0147 21.7994C11.1187 20.6954 12.6161 20.0752 14.1774 20.0752V14.1884C14.1774 11.5862 15.2111 9.09065 17.0511 7.25065C18.8911 5.41065 21.3867 4.37695 23.9888 4.37695ZM23.9888 27.9244C22.9987 27.9241 22.045 28.298 21.319 28.9712C20.593 29.6445 20.1483 30.5673 20.074 31.5546L20.0642 31.8489C20.0642 32.6251 20.2944 33.3839 20.7256 34.0293C21.1569 34.6747 21.7698 35.1777 22.4869 35.4748C23.2041 35.7718 23.9932 35.8495 24.7544 35.6981C25.5157 35.5467 26.215 35.1729 26.7639 34.624C27.3128 34.0752 27.6865 33.3759 27.838 32.6146C27.9894 31.8533 27.9117 31.0642 27.6146 30.3471C27.3176 29.6299 26.8146 29.017 26.1692 28.5858C25.5238 28.1545 24.765 27.9244 23.9888 27.9244ZM23.9888 8.30152C22.4275 8.30152 20.9302 8.92174 19.8262 10.0257C18.7222 11.1297 18.102 12.6271 18.102 14.1884V20.0752H29.8757V14.1884C29.8757 12.6271 29.2554 11.1297 28.1514 10.0257C27.0474 8.92174 25.5501 8.30152 23.9888 8.30152Z"
                                fill="white"
                              />
                            </svg>
                          </div>
                        </div>
                      )}

                      {episode.accessible &&
                        !episode.completed &&
                        episode.progress > 0 && (
                          <div className={styles.curriculumContinueButton}>
                            <button
                              onClick={() => {
                                if (
                                  episode.courseData &&
                                  episode.courseData.slug
                                ) {
                                  window.location.href = `/course/${episode.courseData.slug}`
                                }
                              }}
                              className={styles.continueBtn}
                            >
                              ดำเนินการต่อ
                            </button>
                          </div>
                        )}

                      {episode.accessible &&
                        !episode.completed &&
                        episode.progress > 0 && (
                          <div className={styles.curriculumProgressBadge}>
                            <span>{episode.progress}%</span>
                          </div>
                        )}

                      {episode.completed && (
                        <div className={styles.curriculumFinishedBadge}>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <mask
                              id={`mask_${globalIndex}_3861`}
                              maskUnits="userSpaceOnUse"
                              x="1"
                              y="1"
                              width="22"
                              height="22"
                            >
                              <path
                                d="M12 22C13.3135 22.0016 14.6143 21.7437 15.8278 21.2411C17.0412 20.7384 18.1434 20.0009 19.071 19.071C20.0009 18.1434 20.7384 17.0412 21.2411 15.8278C21.7437 14.6143 22.0016 13.3135 22 12C22.0016 10.6866 21.7437 9.38572 21.2411 8.17225C20.7384 6.95878 20.0009 5.85659 19.071 4.92901C18.1434 3.99909 17.0412 3.26162 15.8278 2.75897C14.6143 2.25631 13.3135 1.99839 12 2.00001C10.6866 1.99839 9.38572 2.25631 8.17225 2.75897C6.95878 3.26162 5.85659 3.99909 4.92901 4.92901C3.99909 5.85659 3.26162 6.95878 2.75897 8.17225C2.25631 9.38572 1.99839 10.6866 2.00001 12C1.99839 13.3135 2.25631 14.6143 2.75897 15.8278C3.26162 17.0412 3.99909 18.1434 4.92901 19.071C5.85659 20.0009 6.95878 20.7384 8.17225 21.2411C9.38572 21.7437 10.6866 22.0016 12 22Z"
                                fill="white"
                                stroke="white"
                                strokeWidth="2"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M8 12L11 15L17 9"
                                stroke="black"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </mask>
                            <g mask={`url(#mask_${globalIndex}_3861)`}>
                              <path d="M0 0H24V24H0V0Z" fill="#92BF1F" />
                            </g>
                          </svg>
                          <span>Finished</span>
                        </div>
                      )}
                    </div>

                    {index < centerColumn.length - 1 && (
                      <div
                        className={`${styles.verticalLine} ${
                          isConnectionCompleted(globalIndex, globalIndex + 1)
                            ? styles.completed
                            : styles.pending
                        }`}
                      ></div>
                    )}
                  </div>
                )
              })}
            </div>

            <div
              className={`${styles.mobileLastEpisodeConnection} ${
                completionPercent === 100 ? styles.completed : styles.pending
              }`}
            ></div>

            <div
              className={`${styles.endCard} ${
                completionPercent === 100 ? styles.completed : ''
              }`}
            >
              <div className={styles.cardIcon}>
                {renderEndCardIcon(completionPercent === 100)}
              </div>
              <div className={styles.flexEndCard}>
                <h3 className={styles.cardTitle}>{data.endCard.title}</h3>
                <p className={styles.cardDescription}>
                  {data.endCard.subtitle}
                </p>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className={styles.columnStart}>
              {(layoutFormat === 'format1' || startColumn.length > 0) && (
                <div
                  className={styles.startCard}
                  style={{
                    marginBottom:
                      layoutFormat === 'format3' && startColumn.length > 0
                        ? '2.5rem'
                        : undefined,
                  }}
                >
                  <div className={styles.cardIcon}>{renderStartCardIcon()}</div>
                  <h3 className={styles.cardTitle}>{data.startCard.title}</h3>

                  {(centerColumn.length > 0 || startColumn.length > 0) && (
                    <div
                      className={`${styles.connectionLine} ${
                        styles.connectionRight
                      } ${
                        isConnectionCompleted(-1, 0)
                          ? styles.completed
                          : styles.pending
                      }`}
                    ></div>
                  )}
                </div>
              )}

              {startColumn.length > 0 && (
                <div className={styles.episodeList}>
                  {renderEpisodeColumn(startColumn, 'start')}
                </div>
              )}
            </div>

            <div className={styles.columnContent}>
              {layoutFormat === 'format2' && (
                <div
                  className={`${styles.endCard} ${
                    completionPercent === 100 ? styles.completed : ''
                  }`}
                >
                  <div className={styles.cardIcon}>
                    {renderEndCardIcon(completionPercent === 100)}
                  </div>
                  <div className={styles.flexEndCard}>
                    <h3 className={styles.cardTitle}>{data.endCard.title}</h3>
                    <p className={styles.cardDescription}>
                      {data.endCard.subtitle}
                    </p>
                  </div>

                  <div
                    className={`${styles.connectionLine} ${
                      styles.connectionDown
                    } ${
                      isConnectionCompleted(9, 10)
                        ? styles.completed
                        : styles.pending
                    }`}
                  ></div>
                </div>
              )}

              {renderEpisodeColumn(centerColumn, 'center')}
            </div>

            <div className={styles.columnEnd}>
              {endColumn.length > 0 && (
                <div className={styles.episodeList}>
                  {renderEpisodeColumn(endColumn, 'end')}
                </div>
              )}

              {(layoutFormat === 'format1' || layoutFormat === 'format3') && (
                <Link href="/profile/certificate">
                <div
                  className={`${styles.endCard} ${
                    completionPercent === 100 ? styles.completed : ''
                  }`}
                  style={{
                    marginTop:
                      layoutFormat === 'format3' && endColumn.length > 0
                        ? '2.5rem'
                        : undefined,
                  }}
                >
                  <div className={styles.cardIcon}>
                    {renderEndCardIcon(completionPercent === 100)}
                  </div>
                  <div className={styles.flexEndCard}>
                    <h3 className={styles.cardTitle}>{data.endCard.title}</h3>
                    <p className={styles.cardDescription}>
                      {data.endCard.subtitle}
                    </p>
                  </div>

                  {layoutFormat === 'format1' && centerColumn.length > 0 && (
                    <div
                      className={`${styles.connectionLine} ${
                        styles.connectionLeft
                      } ${
                        completionPercent === 100
                          ? styles.completed
                          : styles.pending
                      }`}
                    ></div>
                  )}
                </div>
                </Link>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default CurriculumFlow

/* =================================================================
   LAYOUT CONTAINERS
   ================================================================= */
.flowContainer {
  position: relative;
  min-height: 100vh;
  width: 100%;
  margin-bottom: 30px;
}

.gridContainer {
  display: grid;
  grid-template-columns: 33.33% 33.33% 33.33%;
  gap: 0rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.gridContainer.format2,
.gridContainer.format3 {
  grid-template-columns: 33.33% 33.33% 33.33%;
}

/* =================================================================
   COLUMN LAYOUTS
   ================================================================= */
.columnStart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  position: relative;
  padding-top: 2rem;
  z-index: 5;
  gap: 2rem;
}

.columnContent {
  z-index: 15;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 0rem;
  padding: 2rem 0;
  position: relative;
}

.columnEnd {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: end;
  position: relative;
  height: 100%;
  padding-bottom: 2rem;
  gap: 2rem;
}

.episodeList {
  display: flex;
  flex-direction: column;
  gap: 0;
  width: 100%;
}

/* =================================================================
   START CARD STYLES
   ================================================================= */
.startCard {
  z-index: 8;
  background: linear-gradient(108deg, #e8ae4d -7.01%, #ffe07d 98.08%);
  border-radius: 22px;
  border: 1px solid rgba(245, 158, 11, 0.3);
  padding: 1rem;
  text-align: center;
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 80%;
  min-height: 260px;
  box-shadow: 0 10px 25px rgba(245, 158, 11, 0.25);
  transition: all 0.3s ease;
  position: relative;
}

.startCard:hover {
  box-shadow: 0 15px 35px rgba(245, 158, 11, 0.35);
}

/* =================================================================
   END CARD STYLES
   ================================================================= */
.endCard {
  z-index: 8;
  background: #c2c2c3;
  border-radius: 22px;
  border: 1px solid rgba(209, 213, 219, 0.3);
  padding: 1rem;
  text-align: start;
  display: flex;
  align-items: center;
  gap: 0;
  width: 80%;
  max-width: 400px;
  min-height: 260px;
  box-shadow: -31px 18px 53.2px 0px rgba(217, 217, 217, 0.3);
  transition: all 0.3s ease;
  position: relative;
}

.endCard.completed {
  background: linear-gradient(99deg, #638d2e 0%, #92bf1f 106.62%);
  cursor: pointer;
}

.endCard.completed .cardTitle,
.endCard.completed .cardDescription {
  color: white;
}

.endCard:hover {
  box-shadow: 0 15px 35px rgba(156, 163, 175, 0.35);
}

.flexEndCard {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
}

.flexEndCard .cardTitle {
  color: #fff;
}

/* =================================================================
   COMMON CARD STYLES
   ================================================================= */
.cardIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  border-radius: 50%;
  backdrop-filter: blur(5px);
}

.cardTitle {
  font-family: 'Kanit', sans-serif;
  font-size: 20px;
  font-weight: 500;
  line-height: 29px;
  margin: 0;
}

.endCard .cardTitle {
  text-shadow: none;
}

.cardDescription {
  font-family: 'Kanit', sans-serif;
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  line-height: 29px;
  text-align: center;
}

.endCard .cardDescription {
  color: #000;
}

/* =================================================================
   EPISODE CARD STYLES
   ================================================================= */
.episodeWrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: visible;
}

.episodeCard {
  z-index: 10;
  position: relative;
  border-radius: 22px;
  overflow: visible;
  width: 80%;
  max-width: 400px;
  min-height: 240px;
  background: white;
  border: 8px solid #737473;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.episodeCard:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.episodeCard.completed {
  border-color: #638d2e;
  box-shadow: 0 4px 15px rgba(74, 222, 128, 0.2);
}

.episodeCard.completed:hover {
  box-shadow: 0 8px 25px rgba(74, 222, 128, 0.3);
}

.episodeCard.pending {
  /* opacity: 0.7; */
  pointer-events: none;
}

.episodeCard.pending:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.episodeCard.pending .image {
  filter: grayscale(50%);
}

.episodeCard.accessible {
  border-color: #737473;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  pointer-events: auto; /* ให้คลิกได้ */
}

.episodeCard.accessible:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.episodeImage {
  position: relative;
  width: 100%;
  height: 240px;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.episodeCard:hover .image {
  transform: scale(1.05);
}

/* =================================================================
   EPISODE NUMBERS
   ================================================================= */
.episodeNumber {
  position: absolute;
  background: white;
  border: 2px solid #638d2e;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Kanit', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #638d2e;
  box-shadow: 0 2px 8px rgba(99, 141, 46, 0.2);
  z-index: 20;
}

.episodeNumber.first {
  left: 25px;
  top: 39%;
  transform: translateY(-50%);
}

.episodeNumber.middle {
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.episodeNumber.last {
  right: -20px;
  top: 55%;
  transform: translateY(-50%);
}

.episodeNumber.top {
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.episodeNumber.bottom {
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.episodeNumber.pending {
  display: none;
}

/* =================================================================
   STATUS INDICATORS
   ================================================================= */
.statusIndicator {
  position: absolute;
  bottom: -45px;
  left: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.finishedBadge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #545454;
  font-family: Kanit;
  font-size: 20px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
}

.progressBadge {
  color: #92bf1f;
  font-family: Kanit;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-align: start;
}

/* =================================================================
   LOCK OVERLAY
   ================================================================= */
.lockOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #00000033;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15;
}

.lockIcon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* =================================================================
   CONNECTION LINES
   ================================================================= */
.connectionLine {
  position: absolute;
  z-index: 1;
  transition: background-color 0.3s ease;
}

.connectionRight {
  right: -10rem;
  top: 50%;
  width: 10rem;
  height: 26px;
  transform: translateY(-50%);
  z-index: 1;
}

.connectionLeft {
  left: -10rem;
  top: 50%;
  width: 10rem;
  height: 26px;
  z-index: 1;
}

.connectionDown {
  bottom: -5rem;
  left: 50%;
  width: 26px;
  height: 5rem;
  transform: translateX(-50%);
  z-index: 1;
}

.connectionLine.completed {
  background: #638d2e;
}

.connectionLine.pending {
  background: #d1d5db;
}

/* Vertical Lines */
.verticalLine {
  z-index: 1;
  width: 26px;
  height: 5rem;
  margin: 0 auto;
  transition: background-color 0.3s ease;
}

.verticalLine.completed {
  background: #638d2e;
}

.verticalLine.pending {
  background: #d1d5db;
}

/* =================================================================
   ANIMATIONS
   ================================================================= */
@keyframes progressFill {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

@keyframes progressFillFromBottom {
  from {
    transform: scaleY(0);
  }
  to {
    transform: scaleY(1);
  }
}

.connectionLine.completed {
  animation: progressFill 0.5s ease-out;
  transform-origin: left;
}

.verticalLine.completed {
  animation: progressFill 0.5s ease-out;
  transform-origin: top;
}

/* =================================================================
   FORMAT 2 SPECIFIC STYLES
   ================================================================= */
.format2 .columnStart {
  gap: 0rem;
}

.format2 .columnStart .startCard {
  margin-bottom: 5rem;
  gap: 0rem;
}

.format2 .columnStart .startCard .connectionRight {
  transform: translateX(-50%) rotate(90deg);
  transform-origin: center top;
  top: calc(100% + 5rem);
  left: 54%;
  right: auto;
  width: 10rem;
  height: 26px;
  z-index: 1;
}

.format2 .columnStart .episodeNumber {
  top: -20px;
  left: 50%;
  right: auto;
  transform: translateX(-50%);
  z-index: 30;
}

.format2 .columnContent {
  gap: 0;
  padding: 0rem 0;
  justify-content: flex-end;
}

.format2 .columnStart .episodeWrapper:last-child::after {
  content: '';
  position: absolute;
  right: -5rem;
  top: 50%;
  width: 10rem;
  height: 26px;
  background: #638d2e;
  z-index: 1;
  transform: translateY(-50%);
}

.format2 .columnStart .episodeWrapper:last-child.pending::after {
  background: #d1d5db;
}

.format2 .columnContent .endCard {
  margin-bottom: 5rem;
}

.format2 .columnContent .endCard .connectionDown {
  bottom: -5rem;
  left: 50%;
  width: 26px;
  height: 5rem;
  transform: translateX(-50%);
  z-index: 1;
}

.format2 .columnContent .verticalLine.completed {
  animation: progressFillFromBottom 0.5s ease-out;
  transform-origin: bottom;
}

.format2 .columnContent .episodeNumber.bottom {
  bottom: 55px;
  left: 50%;
  transform: translateX(-50%);
}

.format2 .columnContent .episodeWrapper:last-child .episodeNumber {
  left: 25px;
  top: 50%;
  bottom: auto;
  transform: translateY(-50%);
}

/* =================================================================
   FORMAT 3 SPECIFIC STYLES
   ================================================================= */
.format3 .columnContent {
  gap: 0;
  padding: 0rem 0;
  justify-content: flex-start;
  padding-top: calc(2rem + 250px + 5rem);
  padding-bottom: 0;
}

.format3 .columnEnd {
  justify-content: flex-start;
  padding-top: calc(2rem + 250px + 5rem);
  padding-bottom: 0;
  z-index: 99;
}

.format3 .columnStart .startCard .connectionRight {
  transform: translateX(-50%) rotate(90deg);
  transform-origin: center top;
  top: calc(100% + 2.5rem);
  left: 54%;
  right: auto;
  width: 5rem;
  height: 26px;
}

.format3 .columnStart .episodeNumber {
  top: -20px;
  left: 50%;
  right: auto;
  transform: translateX(-50%);
}

.format3 .columnStart .episodeWrapper:last-child::after {
  content: '';
  position: absolute;
  right: -5rem;
  top: 50%;
  width: 10rem;
  height: 26px;
  background: #638d2e;
  z-index: 1;
  transform: translateY(-50%);
}

.format3 .columnStart .episodeWrapper:last-child.pending::after {
  background: #d1d5db;
}

.format3 .columnContent .episodeNumber.bottom {
  bottom: 55px;
  left: 50%;
  transform: translateX(-50%);
}

.format3 .columnContent .episodeWrapper:last-child .episodeNumber {
  left: 25px;
  top: 51%;
  bottom: auto;
  transform: translateY(-50%);
}

.format3 .columnContent .verticalLine.completed {
  animation: progressFillFromBottom 0.5s ease-out;
  transform-origin: bottom;
}

.format3 .columnContent .episodeWrapper:first-child::after {
  content: '';
  position: absolute;
  right: -5rem;
  top: 39%;
  width: 10rem;
  height: 26px;
  background: var(--horizontal-connection-color, #d1d5db);
  z-index: 2;
  transform: translateY(-50%);
}

.format3 .columnContent .episodeWrapper:first-child.horizontalCompleted::after {
  background: #638d2e;
}

.format3 .columnEnd .episodeWrapper:last-child::after {
  content: '';
  position: absolute;
  bottom: -5rem;
  left: 50%;
  width: 26px;
  height: 5rem;
  background: #d1d5db;
  z-index: 1;
  transform: translateX(-50%);
}

.format3 .columnEnd .episodeWrapper:last-child.completed::after {
  background: #638d2e;
}

.format3 .columnEnd .episodeWrapper:last-child .episodeNumber {
  top: -20px !important;
  left: 50% !important;
  right: auto !important;
  bottom: auto !important;
  transform: translateX(-50%) !important;
}

/* .format3 .episodeNumber.first {
  left: 25px;
  top: 50%;
  transform: translateY(-50%);
} */

/* =================================================================
   RESPONSIVE DESIGN
   ================================================================= */
@media only screen and (min-device-width: 1000px) and (max-device-width: 1500px) {
  /* .format2 .columnContent .curriculumCardContainer {
    position: relative;
    width: 100%;
    z-index: 10;
  } */

  /* =================================================================
   Format 3 RESPONSIVE DESIGN
   ================================================================= */
  .format3 .columnContent .curriculumCardContainer {
    position: relative;
    width: 100%;
    z-index: 10;
  }

  .format3 .columnContent .episodeWrapper:first-child::after {
    content: '';
    position: absolute;
    right: -8rem;
    top: 39%;
    width: 10rem;
    height: 26px;
    background: var(--horizontal-connection-color, #d1d5db);
    z-index: 2;
    transform: translateY(-50%);
  }

  .startCard svg,
  .endCard svg {
    width: 80px !important;
    height: 80px !important;
  }

  .startCard p,
  .startCard h3,
  .endCard p,
  .endCard h3 {
    font-size: 18px;
    text-align: start;
  }

  .startCard,
  .endCard {
    padding: 1rem;
    min-height: 200px;
    width: 80%;
  }

  .format3 .columnEnd {
    gap: 0;
    padding: 0rem 0;
    justify-content: flex-start;
    padding-top: calc(2rem + 195px + 5rem);
    padding-bottom: 0;
  }

  .format3 .columnContent {
    gap: 0;
    padding: 0rem 0;
    justify-content: flex-start;
    padding-top: calc(2rem + 195px + 5rem);
    padding-bottom: 0;
  }
}

@media only screen and (min-device-width: 481px) and (max-device-width: 999px) {
  .curriculumCardContainer {
    position: relative;
    width: 95%;
    z-index: 10;
  }

  /* =================================================================
   Format 3 RESPONSIVE DESIGN
   ================================================================= */
  .format3 .columnContent .episodeWrapper:first-child::after {
    content: '';
    position: absolute;
    right: -8rem;
    top: 39%;
    width: 10rem;
    height: 26px;
    background: var(--horizontal-connection-color, #d1d5db);
    z-index: 2;
    transform: translateY(-50%);
  }

  .startCard svg,
  .endCard svg {
    width: 50px !important;
    height: 50px !important;
  }

  .startCard p,
  .startCard h3,
  .endCard p,
  .endCard h3 {
    font-size: 12px;
    text-align: start;
  }

  .startCard,
  .endCard {
    padding: 0.2rem;
    min-height: 100px;
    width: 80%;
  }

  .format3 .columnEnd {
    gap: 0;
    padding: 0rem 0;
    justify-content: flex-start;
    padding-top: calc(2rem + 95px + 5rem);
    padding-bottom: 0;
  }

  .format3 .columnContent {
    gap: 0;
    padding: 0rem 0;
    justify-content: flex-start;
    padding-top: calc(2rem + 95px + 5rem);
    padding-bottom: 0;
  }
}

@media only screen and (max-device-width: 480px) {
  .flowContainer {
    padding: 1.5rem 0.5rem;
  }

  .startCard,
  .endCard {
    padding: 1.5rem;
    min-height: 200px;
    width: 100%;
  }

  .curriculumCardContainer {
    position: relative;
    width: 100% !important;
    z-index: 10;
  }

  .cardTitle {
    font-size: 16px;
  }

  .episodeImage {
    height: 180px;
  }
}

/* =================================================================
   CURRICULUM CARD CONTAINER - ไม่กระทบ CardProgramSw
   ================================================================= */
.curriculumCardContainer {
  position: relative;
  width: 80%;
  /* max-width: 400px; */
  z-index: 10;
  /* ปล่อยให้ CardProgramSw ใช้ CSS ของตัวเองตามปกติ */
}

/* Image preloader ซ่อนไว้ */
.imagePreloader {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  pointer-events: none;
  z-index: 1;
}

/* บังคับให้รูปใน CardProgramSw โหลดทันที */
.curriculumCardContainer :global(.swiper-lazy) {
  opacity: 1 !important;
}

.curriculumCardContainer :global(.swiper-lazy-preloader) {
  display: none !important;
}

/* Override lazy loading behavior */
.curriculumCardContainer :global(.BlockImgSize img) {
  opacity: 1 !important;
}

/* บังคับให้ CardProgramSw เต็มพื้นที่และไม่มี hover transform */
.curriculumCardContainer :global(.card.card-course) {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  transform: scale(1) !important;
  transition: transform 0.3s ease !important;
  z-index: 10 !important;
  position: relative !important;
  border: 8px solid #737473 !important;
  border-radius: 22px !important;
}

.curriculumCardContainer :global(.card.card-course:hover) {
  transform: scale(1.1) !important;
  z-index: 1000 !important;
}

.curriculumCardContainer :global(.card.card-course-hover) {
  transform: scale(1) !important;
  z-index: 10 !important;
  position: relative !important;
  border: 8px solid #737473 !important;
  border-radius: 22px;
}

.episodeWrapper.completed .curriculumCardContainer :global(.card.card-course) {
  border-color: #638c1c !important;
}

.episodeWrapper.completed
  .curriculumCardContainer
  :global(.card.card-course-hover) {
  border-color: #638c1c !important;
}

.curriculumCardContainer :global(.card.card-course-hover:hover) {
  transform: scale(1.1) !important;
  z-index: 1000 !important;
}

/* เพิ่ม z-index สำหรับ curriculum card container ตอน hover */
.curriculumCardContainer:hover {
  z-index: 1000 !important;
}

/* เฉพาะ curriculum overlays เท่านั้น - ไม่กระทบ component */
.curriculumLockOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 500;
  border-radius: 22px;
  opacity: 0.5;
  /* backdrop-filter: blur(2px); */
}

.curriculumLockIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  padding: 1rem;
}

.curriculumProgressBadge {
  position: absolute;
  bottom: -45px;
  left: 60%;
  color: #92bf1f;
  font-family: Kanit, sans-serif;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  z-index: 101;
  padding: 0.25rem 0.5rem;
}

.curriculumFinishedBadge {
  position: absolute;
  bottom: -45px;
  left: 60%;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #545454;
  font-family: Kanit, sans-serif;
  font-size: 20px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  z-index: 101;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

/* Episode completed style - ใช้ pseudo-element ไม่กระทบ CardProgramSw */
.episodeWrapper.completed .curriculumCardContainer::after {
  content: '';
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  border: 3px solid #638d2e;
  border-radius: 22px;
  z-index: 5;
  pointer-events: none;
  box-shadow: 0 0 20px rgba(99, 141, 46, 0.3);
}

/* Episode pending style */
.episodeWrapper.pending .curriculumCardContainer::after {
  content: '';
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  border: 3px solid #d1d5db;
  border-radius: 22px;
  z-index: 5;
  pointer-events: none;
}

.card.card-course-hover {
  transform: scale(1) !important;
}

/* เพิ่ม style สำหรับ Continue Learning button */
.curriculumContinueButton {
  position: absolute;
  bottom: -45px;
  left: 60%;
  z-index: 101;
}

.continueBtn {
  background: #92bf1f;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-family: Kanit, sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.continueBtn:hover {
  background: #638d2e;
  transform: scale(1.05);
}

/* =================================================================
   MOBILE RESPONSIVE DESIGN
   ================================================================= */
@media only screen and (max-device-width: 480px) {
  .flowContainer {
    padding: 1.5rem 0.5rem;
  }

  .gridContainer {
    display: flex;
    flex-direction: column;
    gap: 0;
    max-width: 100%;
    margin: 0 auto;
    position: relative;
    padding: 0 1rem;
  }

  .columnStart:empty,
  .columnEnd:empty {
    display: none;
  }

  .columnStart,
  .columnContent,
  .columnEnd {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    padding: 0;
    gap: 0;
    width: 100%;
  }

  .startCard {
    width: 100%;
    max-width: 350px;
    min-height: 230px;
    padding: 1rem;
    margin-bottom: 0;
    position: relative;
  }

  .startCard .cardTitle {
    font-size: 16px;
    text-align: center;
  }

  .startCard svg {
    width: 80px !important;
    height: 80px !important;
  }

  .endCard {
    width: 100%;
    max-width: 350px;
    min-height: 230px;
    padding: 1rem;
    margin-top: 0;
    position: relative;
  }

  .endCard .cardTitle,
  .endCard .cardDescription {
    font-size: 16px;
    text-align: center;
  }

  .endCard svg {
    width: 80px !important;
    height: 80px !important;
  }

  .connectionLine.connectionRight,
  .connectionLine.connectionLeft {
    display: none;
  }

  .connectionLine.connectionDown {
    display: none;
  }

  .mobileVerticalConnection {
    width: 4px;
    height: 3rem;
    margin: 0 auto;
    transition: background-color 0.3s ease;
    background: #d1d5db;
  }

  .mobileVerticalConnection.completed {
    background: #638d2e;
  }

  .startCard::after {
    content: '';
    position: absolute;
    bottom: -3rem;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 3rem;
    background: #638d2e;
    z-index: 1;
  }

  .mobileLastEpisodeConnection {
    width: 20px;
    height: 3rem;
    margin: 0 auto;
    background: #d1d5db;
    transition: background-color 0.3s ease;
  }

  .mobileLastEpisodeConnection.completed {
    background: #638d2e;
  }

  .curriculumCardContainer {
    width: 100% !important;
    max-width: 350px;
  }

  .episodeImage {
    height: 200px;
  }

  .episodeNumber {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .episodeNumber.first,
  .episodeNumber.middle,
  .episodeNumber.last,
  .episodeNumber.top,
  .episodeNumber.bottom {
    top: -15px;
    left: 50%;
    right: auto;
    bottom: auto;
    transform: translateX(-50%);
  }

  .curriculumProgressBadge,
  .curriculumFinishedBadge,
  .curriculumContinueButton {
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 16px;
  }

  .continueBtn {
    padding: 0.4rem 0.8rem;
    font-size: 14px;
  }

  .verticalLine {
    width: 20px;
    height: 3rem;
    margin: 0 auto;
  }

  .format2 .columnStart .startCard::after,
  .format3 .columnStart .startCard::after {
    display: block;
  }

  .format2 .columnContent .connectionDown,
  .format3 .columnContent .connectionDown {
    display: none;
  }

  .episodeWrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* .episodeWrapper:last-child {
    margin-bottom: 2rem;
  } */

  .format2 .columnStart .episodeWrapper:last-child::after,
  .format3 .columnStart .episodeWrapper:last-child::after,
  .format3 .columnContent .episodeWrapper:first-child::after,
  .format3 .columnEnd .episodeWrapper:last-child::after {
    display: none;
  }

  .curriculumCardContainer :global(.card.card-course:hover),
  .curriculumCardContainer :global(.card.card-course-hover:hover) {
    transform: scale(1.02) !important;
  }
}

/* =================================================================
   TABLET RESPONSIVE DESIGN  
   ================================================================= */
@media only screen and (min-device-width: 481px) and (max-device-width: 999px) {
  .curriculumCardContainer {
    position: relative;
    width: 95%;
    z-index: 10;
  }

  .format3 .columnContent .episodeWrapper:first-child::after {
    content: '';
    position: absolute;
    right: -8rem;
    top: 39%;
    width: 10rem;
    height: 26px;
    background: var(--horizontal-connection-color, #d1d5db);
    z-index: 2;
    transform: translateY(-50%);
  }

  .startCard svg,
  .endCard svg {
    width: 50px !important;
    height: 50px !important;
  }

  .startCard p,
  .startCard h3,
  .endCard p,
  .endCard h3 {
    font-size: 12px;
    text-align: start;
  }

  .startCard,
  .endCard {
    padding: 0.2rem;
    min-height: 100px;
    width: 80%;
  }

  .format3 .columnEnd {
    gap: 0;
    padding: 0rem 0;
    justify-content: flex-start;
    padding-top: calc(2rem + 95px + 5rem);
    padding-bottom: 0;
  }

  .format3 .columnContent {
    gap: 0;
    padding: 0rem 0;
    justify-content: flex-start;
    padding-top: calc(2rem + 95px + 5rem);
    padding-bottom: 0;
  }
}

/* =================================================================
   NO DATA STYLES
   ================================================================= */
.noDataContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  min-height: 400px;
}

.noDataIcon {
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.noDataTitle {
  color: #374151;
  font-family: 'Kanit', sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 0;
}

.noDataDescription {
  color: #6b7280;
  font-family: 'Kanit', sans-serif;
  font-size: 1rem;
  font-weight: 400;
  margin: 0;
  max-width: 400px;
}

/* Mobile responsive for no data */
@media only screen and (max-device-width: 480px) {
  .noDataContainer {
    padding: 2rem 1rem;
    min-height: 300px;
  }

  .noDataIcon svg {
    width: 60px;
    height: 60px;
  }

  .noDataTitle {
    font-size: 1.25rem;
  }

  .noDataDescription {
    font-size: 0.875rem;
  }
}
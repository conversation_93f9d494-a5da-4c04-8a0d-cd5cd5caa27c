import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import React from "react";
import { useState, useEffect, useContext } from "react";

import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
  TextArea,
} from "semantic-ui-react";

import Link from "next/link";
import Image from "next/image";

import styles from "/public/assets/css/component/formPop.module.css";

import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import AppContext from "/libs/contexts/AppContext";
import Swal from "sweetalert2";

export default function formPopLoginEn(props) {
  const router = useRouter();
  const { locale, pathname, asPath } = router;
  const appContext = useContext(AppContext);
  const { register, setValue, getValues, handleSubmit } = useForm({
    defaultValues: {},
  });
  const [errorArray, setErrorArray] = useState([]);
  const cookies = parseCookies();

  return (
    <div className={`${styles.block_pop_fm}`}>
      <div className={`${styles.inner_pop_fm}`}>
        <div className={`${styles.logo}`}>
          <div className={`${styles.thumb}`}>
            <div className="blockImgHeaderLogin">
              <Image src="/assets/images/header-logo.png" 
                layout="fill"
                objectFit="contain"
                className="ItemsImgHeaderLogin"
                alt=""
              />
            </div>
          </div>
        </div>
        {/* ====== */}
        <div className={`${styles.col_fm}`}>
          <div className="item-fm fm-w-icon">
            <i className="icon-ic-f-user"></i>
            <Input type="text" className="fm-control" placeholder="Email">
              <input
                {...register("email")}
                maxLength={100}
                data-type="email"
                onInput={appContext.diFormPattern}
              />
            </Input>
          </div>
        </div>
        {/* ====== */}
        <div className={`${styles.col_fm}`}>
          <div className="item-fm fm-w-icon">
            <i className="icon-ic-f-lock"></i>
            <Input
              id={props.name}
              type="password"
              className="fm-control"
              placeholder="Password"
            >
              <input
                {...register("password")}
                maxLength={25}
                data-type="password"
                onInput={appContext.diFormPattern}
              />
            </Input>

            <div className="eye-button">
              <i
                id="input_eye_pop"
                className="eye icon"
                onClick={() => {
                  var attr_pass = document
                    .getElementById(props.name)
                    .getAttribute("type");
                  if (attr_pass == "password") {
                    document
                      .getElementById(props.name)
                      .setAttribute("type", "text");
                  } else {
                    document
                      .getElementById(props.name)
                      .setAttribute("type", "password");
                  }
                  document
                    .getElementById("input_eye_pop")
                    .classList.toggle("slash");
                }}
              ></i>
            </div>
          </div>
        </div>
        {/* ====== */}
        <div className={`${styles.col_fm_btn}`}>
          <button
            className={`${styles.btn_submit}`}
            onClick={() => {
              setValue("login_type", "email");
              let obj = getValues();
              let bol = true;
              var errarr = [];
              if (!appContext.isEmail(obj["email"])) {
                bol = false;
                errarr.push("email");
              }
              if (!appContext.isPassword(obj["password"])) {
                bol = false;
                errarr.push("password");
              }
              setErrorArray(errarr);

              if (bol) {
                appContext.setOpen(false);
                appContext.loadApi(
                  process.env.NEXT_PUBLIC_API + "/api/user/login",
                  obj,
                  (data) => {
                    if (data["status"] == "success") {
                      router.replace({
                        pathname: "/auth/callback/token",
                        query: { utoken: data["utoken"] },
                      });
                    } else {
                      if (data["status"] == "unverify") {
                        Swal.fire({
                          text: "Please verify your identity from your registered email address.",
                          icon: "error",
                          confirmButtonText: "Close",
                          confirmButtonColor: "#648d2f",
                        });
                      } else {
                        Swal.fire({
                          text: "Incorrect password",
                          icon: "error",
                          confirmButtonText: "Close",
                          confirmButtonColor: "#648d2f",
                        });
                      }
                    }
                  }
                );
              } else {
                Swal.fire({
                  text: "Please confirm your information",
                  icon: "error",
                  confirmButtonText: "Close",
                  confirmButtonColor: "#648d2f",
                });
              }
            }}
          >
            <span>Login</span>
          </button>
          <button
            className={`${styles.btn_forget}`}
            onClick={() => props.callback("forgot")}
          >
            <span>Forget Password</span>
          </button>
        </div>
        {/* ====== */}
        <div className={`${styles.or}`}>
          <div className={`${styles.inner}`}>
            <span>OR</span>
          </div>
        </div>
        {/* ====== */}
        <div className={`${styles.way_group_btn}`}>
          <a href={"/auth/signin/facebook"}>
            <button className={`${styles.btn_way}`}>
              <div className="blockImgSocialSize">
                <Image src="/assets/images/way_facebook.png" 
                  layout="fill"
                  objectFit="cover"
                  className="ItemsImgSocialSize"
                  alt=""
                />
              </div>
            </button>
          </a>
          <a href={"/auth/signin/line"}>
            <button className={`${styles.btn_way}`}>
            <div className="blockImgSocialSize">
              <Image src="/assets/images/way_line.png" 
                layout="fill"
                objectFit="cover"
                className="ItemsImgSocialSize"
                alt=""
              />
            </div>
            </button>
          </a>
          <a href={"/auth/signin/google"}>
            <button className={`${styles.btn_way}`}>
            <div className="blockImgSocialSize">
              <Image src="/assets/images/way_google.png" 
                layout="fill"
                objectFit="cover"
                className="ItemsImgSocialSize"
                alt=""
              />
            </div>
            </button>
          </a>
        </div>
        {/* ====== */}
      </div>
    </div>
  );
}

import React, { Component } from "react";
import { render } from "react-dom";
import { Progress } from 'semantic-ui-react'
import styles from "/public/assets/css/component/cardVideo.module.css";
import Image from "next/image";

export default class index extends Component {
  render() {
    return (
      <div className="card card-video radius">
        <div className="inner">
            <div className="card-img">
              <div className={styles.blockVideoImgSize}>
                {this.props.data && this.props.data.cover && this.props.data.cover!=null && this.props.data.cover!='' && this.props.data.cover!='null' ?(
                  <Image layout="fill" className="img-thumb ItemsVideoImgSize" src={this.props.data.cover} alt="" />
                ):null}
              </div>
              {this.props.data.link!=null&&this.props.data.link!='null'&&this.props.data.link!='' ? (
                <div className="card-icon video">
                  <i onClick={() => this.props.callback()} className="icon-ic-circle-play"></i>
                </div>
              ):null}
              {/* <div className="card-img-progress">
                <Progress percent={this.props.data.learned_percent} />
              </div> */}
            </div>
        </div>
      </div>
    );
  }
}

import React, { Component } from "react";
import {
  Input,
  Select,
  TextArea,
  Icon,
  Dropdown,
  Label,
} from "semantic-ui-react";

import styles from "/public/assets/css/component/chooseTypeDataSelect.module.css";

const dataOptions = [
  { value: 'op_1', text: 'ใบประกาศนียบัตร' },
  { value: 'op_2', text: 'รายการโปรด' },
  { value: 'op_3', text: 'ประวัติการสั่งซื้อ' },
  { value: 'op_4', text: 'คอร์สของฉัน' },
]


export default class index extends Component {

  
  render() {
    return (
      <div className="choose_type_data_select">
        <div className="inner_data_select">
            <Select className="data_select"  
            options={dataOptions}  
            defaultValue={dataOptions[0].value}
            />
        </div>
      </div> 
    );
  }
}

import React, { Component } from "react";
import Link from "next/link";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Zoom, FreeMode, Pagination, Controller, Navigation,Lazy,Mousewheel } from "swiper";

import "swiper/css/lazy";
import Swal from 'sweetalert2'
export default class index extends Component {
  render() {
    if(this.props.type=='desktop'){
      return (
        <div className="card card-default radius">
          <div className="inner card-list-row">
            {this.props.data.map((val, key) => (
              <div key={key} className="col-6 col-md-6 col-lg-3 col-xl-3 card-item">
                {this.props.haveName ? (
                  <a href={`${val.link}`} target="_blank" rel="noreferrer">
                    <div className="card-img">
                      <div className="img-thumb cursor-pointer certificate-img-list">
                        {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                          <Image src={val.image_th} alt={val.title_th} 
                            layout='fill'
                            objectFit="cover"
                            objectPosition='center'
                            
                          />
                        ):null}
                      </div>
                      {val.lang_type=='th' ? (
                        <div className="cert-lang">
                          <p>TH</p>
                        </div>
                      ):(
                        <div className="cert-lang">
                          <p>EN</p>
                        </div>
                      )}
                    </div>
                  </a>
                ):(
                  <a onClick={() => 
                    Swal.fire({
                      text: 'กรุณากรอกชื่อที่ต้องการให้แสดงบนใบประกาศนียบัตร',
                      icon: 'error',
                      confirmButtonText: 'ตกลง',
                      confirmButtonColor: "#648d2f"
                    }).then((result) => {
                      this.props.alert();
                    })
                  }>
                    <div className="card-img">
                      <div className="img-thumb cursor-pointer certificate-img-list">
                      {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                        <Image src={val.image_th} alt={val.title_th} 
                          layout="fill"
                          objectFit="cover"
                          objectPosition="center"
                          
                        />
                      ):null}
                      </div>
                      {val.lang_type=='th' ? (
                        <div className="cert-lang">
                          <p>TH</p>
                        </div>
                      ):(
                        <div className="cert-lang">
                          <p>EN</p>
                        </div>
                      )}
                    </div>
                  </a>
                )}
                
              </div>
            ))}
            
          </div>
        </div>
      );
    }else{
      return (
        <div>
          <div className="category-item-slide-big">
            <Swiper
              freeMode={true}
              lazy={true}
              mousewheel={{
                forceToAxis: true,
                enabled: true,
              }}
              modules={[FreeMode,Lazy,Pagination,Mousewheel]}
              spaceBetween={30}
              pagination={{
                clickable: true,
              }}
            >
              {this.props.data.map((val, key) => (
                key<3?(
                  <SwiperSlide key={key}>
                    {this.props.haveName ? (
                      <a href={`${val.link}`} target="_blank" rel="noreferrer">
                        <div className="card-img">
                          <div className="img-thumb cursor-pointer swiper-lazy certificate-img-list">
                          {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                            <Image data-src={val.image_th} src={val.image_th} alt={val.title_th} 
                                layout='fill'
                                objectFit="cover"
                                objectPosition="center"
                                
                            />
                          ):null}
                          </div>
                          {val.lang_type=='th' ? (
                            <div className="cert-lang">
                              <p>TH</p>
                            </div>
                          ):(
                            <div className="cert-lang">
                              <p>EN</p>
                            </div>
                          )}
                          <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                        </div>
                      </a>
                    ):(
                      <a onClick={() => 
                        Swal.fire({
                          text: 'กรุณากรอกชื่อที่ต้องการให้แสดงบนใบประกาศนียบัตร',
                          icon: 'error',
                          confirmButtonText: 'ตกลง',
                          confirmButtonColor: "#648d2f"
                        }).then((result) => {
                          this.props.alert();
                        })
                      }>
                        <div className="card-img">
                          <div className="img-thumb cursor-pointer swiper-lazy certificate-img-list">
                          {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                            <Image data-src={val.image_th} src={val.image_th} alt={val.title_th} 
                                layout='fill'
                                objectFit="cover"
                                objectPosition="center"
                                
                            />
                          ):null}
                          </div>
                          {val.lang_type=='th' ? (
                            <div className="cert-lang">
                              <p>TH</p>
                            </div>
                          ):(
                            <div className="cert-lang">
                              <p>EN</p>
                            </div>
                          )}
                          <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                        </div>
                      </a>
                    )}
                    
                  </SwiperSlide>
                ):(
                  null
                )
              ))}
            </Swiper>
          </div>
          <div className="category-item-slide">
            <Swiper
              freeMode={true}
              lazy={true}
              mousewheel={{
                forceToAxis: true,
                enabled: true,
              }}
              modules={[FreeMode,Lazy,Mousewheel]}
              spaceBetween={10}
              breakpoints={{
                0: {
                  slidesPerView: 2.5,

                }
              }}
            >
              {this.props.data.map((val, key) => (
                key>=3?(
                  <SwiperSlide key={key}>
                    {this.props.haveName ? (
                      <a href={`${val.link}`} target="_blank" rel="noreferrer">
                        <div className="card-img">
                          <div className="img-thumb cursor-pointer swiper-lazy certificate-img-list">
                          {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                            <Image data-src={val.image_th} src={val.image_th} alt={val.title_th}
                                layout='fill'
                                objectFit="cover"
                                objectPosition="center"
                                
                            />
                          ):null}
                          </div>
                          {val.lang_type=='th' ? (
                            <div className="cert-lang">
                              <p>TH</p>
                            </div>
                          ):(
                            <div className="cert-lang">
                              <p>EN</p>
                            </div>
                          )}
                          <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                        </div>
                      </a>
                    ):(
                      <a onClick={() => 
                        Swal.fire({
                          text: 'กรุณากรอกชื่อที่ต้องการให้แสดงบนใบประกาศนียบัตร',
                          icon: 'error',
                          confirmButtonText: 'ตกลง',
                          confirmButtonColor: "#648d2f"
                        }).then((result) => {
                          this.props.alert();
                        })
                      }>
                        <div className="card-img">
                          <div className="img-thumb cursor-pointer swiper-lazy certificate-img-list">
                          {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                            <Image data-src={val.image_th} src={val.image_th} alt={val.title_th} 
                                layout='fill'
                                objectFit="cover"
                                objectPosition="center"
                                
                            />
                          ):null}
                          </div>
                          {val.lang_type=='th' ? (
                            <div className="cert-lang">
                              <p>TH</p>
                            </div>
                          ):(
                            <div className="cert-lang">
                              <p>EN</p>
                            </div>
                          )}
                          <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                        </div>
                      </a>
                    )}
                    
                  </SwiperSlide>
                ):(
                  null
                )
              ))}
            </Swiper>
          </div>
        </div>
      );
    }
  }
}

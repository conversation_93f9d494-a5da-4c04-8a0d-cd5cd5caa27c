import React, { Component } from "react";
import Link from "next/link";
import Image from 'next/image'
const truncate = (input, limit) => {
  if (input && input.length > limit) return input.substring(0, limit) + " ...";
  else return input;
};
export default class index extends Component {
  render() {
    return (
      <div className="card card-news">
        <div className="inner cursor">
          <a href={`/article/${this.props.data.slug}`}>
            <div className="card-news-inner">
                <div className="card-news-img">
                  <div className="BlockImgSize">
                    {this.props.data && this.props.data.image && this.props.data.image!=null && this.props.data.image!='' && this.props.data.image!='null' ?(
                      <Image className="news-thumb swiper-lazy ItemsImgSize" src={this.props.data.image} alt={this.props.data.title_th} 
                      layout='fill' 
                      objectFit="contain"
                      />
                    ):null}
                  </div>
                  <div className="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
                </div>
                <div className="card-news-description">
                  <h3>
                    {truncate(this.props.data.title_th,55)}
                  </h3>
                  <p>
                    {truncate(this.props.data.subtitle_th,90)}
                  </p>
                  <div className="detail">
                    <div className="date"><i className="icon-ic-clock"></i> <span>{this.props.data.date}</span></div>
                    <a href={`/article/${this.props.data.slug}`} className="more">
                      <span>อ่านเพิ่มเติม</span>
                      <i className="icon-ic-right"></i>
                      <i className="icon-ic-right"></i>
                    </a>
                  </div>
                </div>
            </div>
          </a>
        </div>
      </div>
    );
  }
}

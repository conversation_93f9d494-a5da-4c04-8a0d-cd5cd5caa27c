import React, { Component } from "react";
import Link from "next/link";
import Image from "next/image";
import moment from "moment";
import CardProgram from "../cardProgram";
const truncate = (input,limit) => {
  if (input && input.length > limit)
      return input.substring(0, limit) + ' ...';
  else
      return input;
};
export default class index extends Component {
  render() {
    return this.props.data.map((val, key) => (
      val.topic_type==1?(
        <div key={key} className="col-6 col-md-6 col-lg-4 col-xl-4 pd-t-b">
          <div className="card card-seminar radius">
            <div className="inner cursor">
              <a href={`/course/${val.slug}`}>
                <div className="card-seminar-inner">
                  <div className="seminar-img">
                    <div className="seminar-thumb">
                      {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                        <Image src={val.image_th} alt="" 
                          layout="fill"
                          objectFit="cover"
                          objectPosition="center"
                        />
                      ):null}
                    </div>
                  </div>
                  <div className="seminar-description">
                    <div className="description-content content-top">
                      <h3>{truncate(val.title_th,35)}</h3>
                      <p>{truncate(val.subtitle_th,50)}</p>
                    </div>
                    <div className="description-content content-bottom">
                      <div className="date">
                        <i className="icon-ic-clock"></i> <span>{moment(val.created_at).format("MMMM Do YYYY")}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      ):(
        <div key={key} className="col-6 col-md-6 col-lg-4 col-xl-4 pd-t-b">
          <CardProgram
            type="normal"
            callback={this.props.callback}
            data={val}
          ></CardProgram>
        </div>
      )
    ));
  }
}

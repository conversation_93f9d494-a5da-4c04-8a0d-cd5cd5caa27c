import React, { Component } from "react";
import { Input, Select, Icon, Dropdown, <PERSON><PERSON>, Menu } from "semantic-ui-react";
import { render } from "react-dom";
import styles from "/public/assets/css/component/courseDetailScholarship.module.css";
import Image from "next/image";

import Swal from 'sweetalert2'
const options = [
  { key: 1, text: "Choice 1", value: 1 },
  { key: 2, text: "Choice 2", value: 2 },
  { key: 3, text: "Choice 3", value: 3 },
];

export default class index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      accept: false,
      consent: false,
      code: '',
    };
  }

  setAccept() {
    if(this.state.accept){
      this.setState({
        accept: false,
      });
    }else{
      this.setState({
        accept: true,
      });
    }
  }
  setConsent() {
    if(this.state.consent){
      this.setState({
        consent: false,
      });
    }else{
      this.setState({
        consent: true,
      });
    }
  }
  setCode(_code) {
    this.setState({
      code: _code,
    });
  }
  submit(){
    if(this.state.code!=''){
      if(this.state.accept&&this.state.consent){
        this.props.callback(this.state.code,'sponsor')
      }else{
        Swal.fire({
          text: 'กรุณายอมรับการสนับสนุน',
          icon: 'error',
          confirmButtonText: 'ปิด',
          confirmButtonColor: "#648d2f"
        })
      }
    }else{
      Swal.fire({
        text: 'กรุณาเลือกผู้สนับสนุน',
        icon: 'error',
        confirmButtonText: 'ปิด',
        confirmButtonColor: "#648d2f"
      })
    }
  }
  render() {
    return (
      <div className="block-course-detail-scholarship">
        <div className="inner">
          <Menu compact className="btn-dropdown-scholarship">
            <Dropdown text="รับการสนับสนุน" icon="caret down">
              <Dropdown.Menu className="inner-dropdown-custom">
                <Dropdown.Item onClick={(e) => e.stopPropagation()}>
                  <div className="scholarship-list">
                    <div className="ilist scholarship-brand">
                      {this.props.data.map((val, key) => (
                        <div key={key} className="item-choose" onClick={() => this.setCode(val.code)}>
                          <div className="fm-check">
                            <input name={this.props.name} type="radio" />
                            <div className="text">
                              <div className="i_remark">
                                <i className="icon-ic-circle" />
                              </div>
                              <div className={styles.imgThumbDiv}>
                                {val && val.image && val.image!=null && val.image!='' && val.image!='null' ?(
                                  <Image layout="fill" className="img-thumb" src={val.image} alt="" />
                                ):null}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="ilist scholarship-text">
                      {/* ====== */}
                      <div className="item-choose" onClick={() => this.setAccept()}>
                        <div className="fm-check">
                          <input type="checkbox" />
                          <div className="text">
                            <div className="i_remark">
                              <i className="icon-ic-circle" />
                            </div>
                            <p>รับการสนับสนุนนี้</p>
                          </div>
                        </div>
                      </div>
                      {/* ====== */}
                      <div className="item-choose" onClick={() => this.setConsent()}>
                        <div className="fm-check">
                          <input type="checkbox" />
                          <div className="text">
                            <div className="i_remark">
                              <i className="icon-ic-circle" />
                            </div>
                            <p>ยินดีเปิดเผยข้อมูลให้กับผู้สนับสนุน</p>
                          </div>
                        </div>
                      </div>
                      {/* ====== */}
                    </div>
                  </div>
                  <div className="scholarship-action">
                    <button onClick={() => this.submit()} className="btn-scholarship-action">
                      <span>ตกลง</span>
                    </button>
                  </div>
                </Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          </Menu>
        </div>
      </div>
    );
  }
}

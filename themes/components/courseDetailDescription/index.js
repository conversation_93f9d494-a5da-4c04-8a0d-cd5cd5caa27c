import React, { Component } from "react";
import Image from "next/image";
import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
} from "semantic-ui-react";
import ReactStars from "react-rating-stars-component";
import { render } from "react-dom";
 

import styles from "/public/assets/css/component/courseDetailDescription.module.css";
function convertSecond(second) {
  if (second != null && second != "") {
    var hours = parseInt(
      new Date(second * 1000).toISOString().substring(11, 13)
    );
    var minutes = parseInt(
      new Date(second * 1000).toISOString().substring(14, 16)
    );
    var seconds = parseInt(
      new Date(second * 1000).toISOString().substring(17, 19)
    );
    if(hours==0){
      return minutes+'m';
    }else{
      return hours + "h:" + minutes+'m';
    }
    
  } else {
    return '0m';
  }
}
export default class index extends Component {
  constructor(props) {
    super(props);
    if(this.props.lang){
      this.state = {
        lang:this.props.lang
      };
    }else{
      this.state = {
        lang:'th'
      };
    }
  }
  translateEng(_value) {
    if(this.state.lang=='en'){
      if(_value=='นาที'){
        return "Minutes";
      }else if(_value=='ชั่วโมง'){
        return "Hours";
      }else if(_value=='วิทยากร'){
        return "Speaker";
      }else if(_value=='ผู้เขียน'){
        return "Writer";
      }else if(_value=='ภาควิชา'){
        return "Department";
      }else if(_value=='วันที่บรรยาย'){
        return "Lecture date";
      }else if(_value=='วันที่เผยแพร่'){
        return "Release date";
      }else if(_value=='วันที่เรียน'){
        return "Class date";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  render() {
    const ratingThisStar = {
      size: 24,
      count: 5,
      color: "#FFC130",
      activeColor: "#FFC130",
      value: this.props.data.rate,
      a11y: false,
      isHalf: true,
      emptyIcon: <i className="icon-ic-star-light" />,
      halfIcon: <i className="icon-ic-star-half-light" />,
      filledIcon: <i className="icon-ic-star" />,
      onChange: newValue => {
        this.props.callback(newValue);
      }
    };
    return (
        <div className="block-course-detail-description">
          {this.props.data.trailer_media==7 ? (
            <>
              <div className="item-description-live">
                {new Date(this.props.data.started_time) <= new Date() ?(
                  new Date(this.props.data.end_time) >= new Date() ?(
                    <div className="live-status active">
                      <div className="live-dot"></div><p>LIVE NOW</p>
                    </div>
                  ):(
                    <div className="live-status">
                      <div className="live-dot"></div><p>LIVE END</p>
                    </div>
                  )
                ):(
                  <div className="live-soon-section">
                    <div className="live-status">
                      <div className="live-dot"></div><p>LIVE SOON</p>
                    </div>
                    {this.props.countdownDay && this.props.countdownHour && this.props.countdownMin && this.props.countdownSec && (this.props.countdownDay!='00' || this.props.countdownHour!='00' || this.props.countdownMin!='00' || this.props.countdownSec!='00') ?(
                      <div className="live-soon-time">
                        {/* <div className="countdown-title">
                          <h3>Live In</h3>
                        </div> */}
                        <div className="countdown-time">
                          <div className="time-item">
                            <div className="time-bg">
                              <span>{this.props.countdownDay}</span>
                            </div>
                            <h3>DAYS</h3>
                          </div>
                          <div className="time-dot">
                            <span>:</span>
                          </div>
                          <div className="time-item">
                            <div className="time-bg">
                              <span>{this.props.countdownHour}</span>
                            </div>
                            <h3>HOURS</h3>
                          </div>
                          <div className="time-dot">
                            <span>:</span>
                          </div>
                          <div className="time-item">
                            <div className="time-bg">
                              <span>{this.props.countdownMin}</span>
                            </div>
                            <h3>MINUTES</h3>
                          </div>
                          <div className="time-dot">
                            <span>:</span>
                          </div>
                          <div className="time-item">
                            <div className="time-bg">
                              <span>{this.props.countdownSec}</span>
                            </div>
                            <h3>SECONDS</h3>
                          </div>
                          {/* <span>{this.props.countdownDay}:{this.props.countdownHour}:{this.props.countdownMin}:{this.props.countdownSec}</span> */}
                        </div>
                      </div>
                    ):null}
                  </div>
                )}
              </div>
              {this.props.liveLogo?(
                <div className="live-logo">
                  <Image
                    className=""
                    src={this.props.data.live_logo}
                    alt=""
                    layout="fill"
                    objectFit="contain"
                  />
                </div>
              ):null}
            </>
          ):null}
          <div className="item-description-title">
            <h1>{this.props.data.title}</h1>
          </div>
          {this.props.data.trailer_media!=8 ? (
            <div className="item-description">
              <ReactStars {...ratingThisStar} />
              <div className="info-rating">{Number(this.props.data.rate).toFixed(1)} ({this.props.data.rating} rating)</div>
            </div>
          ):null}
          {this.props.data.trailer_media!=2 && this.props.data.trailer_media!=6 && this.props.data.trailer_media!=7 && this.props.data.trailer_media!=8 && this.props.data.trailer_media!=9 && this.props.data.trailer_media!=10 && this.props.data.trailer_media!=11 ? (
            <div className="item-description-ex">
              <i className="icon-ic-lessons"></i>
              <span>{this.props.data.lesson} lessons</span>
            </div>
          ):null}
          {this.props.data.trailer_media!=6 && this.props.data.trailer_media!=7 && this.props.data.trailer_media!=8 && this.props.data.trailer_media!=9 && this.props.data.trailer_media!=10 && this.props.data.trailer_media!=11 ? (
            <div className="item-description-ex">
                <i className="icon-ic-time"></i>
                {this.props.data.duration ? (<span>{convertSecond(this.props.data.duration)}</span>):(<span>0m</span>)}
            </div>
          ):null}
          {this.props.data.level!=''?(
            <div className="item-description-ex">
              <span>Level : {this.props.data.level}</span>
            </div>
          ):(null)}
          {this.props.data.speaker_name!=null && this.props.data.speaker_name!='' && this.props.data.speaker_name!='null' ? (
            this.props.data.trailer_media!=6 ?(
              <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.translateEng('วิทยากร')+' : '+this.props.data.speaker_name}}>
              </div>
            ):(
              <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: 'by '+this.props.data.speaker_name+' (Author)'}}>
              </div>
            )
          ):null}
          {this.props.data.department_name!=null && this.props.data.department_name!='' && this.props.data.department_name!='null' && this.props.data.trailer_media!=6 ? (
            <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.translateEng('ภาควิชา')+' : '+this.props.data.department_name}}>
            </div>
          ):null}
          {this.props.data.type=='seminar' ? (
            this.props.data.conference_date_th!=null && this.props.data.conference_date_th!='' && this.props.data.conference_date_th!='null' ? (
              <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.translateEng('วันที่บรรยาย')+' : '+this.props.data.conference_date_th}}>
              </div>
            ):null
          ):(
            this.props.data.publish_date_th!=null && this.props.data.publish_date_th!='' && this.props.data.publish_date_th!='null' && this.props.data.trailer_media!=8 ? (
              this.props.data.trailer_media==2 ? (
                <>
                  <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.translateEng('วันที่เผยแพร่')+' : '+this.props.data.publish_date_th}}>
                  </div>
                  <div className={`${styles.description} started_time`} dangerouslySetInnerHTML={{__html: this.translateEng('วันที่เรียน')+' : '+this.props.data.started_time_th}}>
                  </div>
                </>
              ):(
                this.props.data.trailer_media==7 ? (
                  <>
                    <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.translateEng('วันที่ Live')+' : '+this.props.data.live_date}}></div>
                    <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.translateEng('เวลา')+' : '+this.props.data.live_time}}></div>
                  </>
                ):(
                  <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.translateEng('วันที่เผยแพร่')+' : '+this.props.data.publish_date_th}}></div>
                )
              )
            ):null
          )}
          {this.props.data.trailer_media==6 ?(
            <>
              {this.props.data.publisher!=null && this.props.data.publisher!='' && this.props.data.publisher!='null' ? (
                <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.translateEng('สำนักพิมพ์')+' : '+this.props.data.publisher}}></div>
              ):null}
              {this.props.data.pages!=null && this.props.data.pages!='' && this.props.data.pages!='null' ? (
                <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.translateEng('จำนวนหน้า')+' : '+this.props.data.pages}}></div>
              ):null}
              {this.props.data.isbn_no!=null && this.props.data.isbn_no!='' && this.props.data.isbn_no!='null' ? (
                <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.translateEng('ISBN')+' : '+this.props.data.isbn_no}}></div>
              ):null}
              {this.props.data.category_name!=null && this.props.data.category_name!='' && this.props.data.category_name!='null' ? (
                <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.translateEng('หมวดหมู่หนังสือ')+' : '+this.props.data.category_name}}></div>
              ):null}
            </>
          ):null}
          <div className={`${styles.description}`} dangerouslySetInnerHTML={{__html: this.props.data.description}}>
          </div>
        </div>
    );
  }
}

import React, { Component } from "react";
import Image from "next/image";
import styles from "/public/assets/css/component/homeSponsors.module.css";

export default class index extends Component {
  render() {
    return ( 
        this.props.data.length>0 ?
          <div className={`${styles.homeSponsors}`}>
          <div className="container custom-container">
            <div className="row">
              <div className={`${styles.inner}`}>
                <div className={`${styles.title}`}>
                  <h3>ร่วมสนับสนุนโครงการ</h3>
                </div>
                <div className={`${styles.logos}`}>
                  {this.props.data.map((val, key) =>
                  val && val.image && val.image!=null && val.image!='' && val.image!='null' ?(
                    <div className={`${styles.imgDiv}`} key={key}><Image src={val.image} alt="" 
                    layout="fill"
                    objectFit="cover"
                    objectPosition="center"
                    /></div>
                  ):null
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        :null 
    );
  }
}

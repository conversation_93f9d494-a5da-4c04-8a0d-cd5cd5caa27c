import React from 'react';
import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
  TextArea,
} from "semantic-ui-react";
import { render } from "react-dom";

import styles from "/public/assets/css/component/formPop.module.css";

const Index = () => {
  return (
    <div className={`${styles.block_pop_fm}`}>
      <div className={`${styles.inner_pop_fm}`}>
        <div className={`${styles.logo}`}>
          <div className={`${styles.thumb}`}>
            <img src="/assets/images/header-logo.png" />
          </div>          
        </div>
        {/* ====== */}
        <div className={`${styles.col_fm}`}>
          <div className="item-fm fm-w-icon">
            <i className='icon-ic-f-user'></i>
            <Input type="text" className="fm-control" placeholder='ชื่อ'></Input>
          </div>
        </div>
        {/* ====== */}
        <div className={`${styles.col_fm}`}>
          <div className="item-fm fm-w-icon">
            <i className='icon-ic-f-user'></i>
            <Input type="text" className="fm-control" placeholder='อีเมล์'></Input>
          </div>
        </div>
        {/* ====== */}
        <div className={`${styles.col_fm}`}>
          <div className="item-fm fm-w-icon">
            <i className='icon-ic-f-lock'></i>
            <Input type="password" className="fm-control" placeholder='รหัสผ่าน'></Input>
          </div>
        </div>
        {/* ====== */}
        <div className={`${styles.col_fm_btn}`}>
          <button className={`${styles.btn_submit}`}>
            <span>กรอกข้อมูลส่วนตัว</span>
          </button>
        </div>
        {/* ====== */}
        <div className={`${styles.or}`}>
          <div className={`${styles.inner}`}>
            <span>หรือ</span>
          </div>
        </div>
        {/* ====== */}
        <div className={`${styles.way_group_btn}`}>
            <a href={'/auth/signin/facebook'}>
            <button className={`${styles.btn_way}`}>
              <img src="/assets/images/way_facebook.png" />
            </button>
            </a>
            <a href={'/auth/signin/line'}>
            <button className={`${styles.btn_way}`}>
              <img src="/assets/images/way_line.png" />
            </button>
            </a>
            <a href={'/auth/signin/google'}>
            <button className={`${styles.btn_way}`}>
              <img src="/assets/images/way_google.png" />
            </button>
            </a>
        </div>
        {/* ====== */}
      </div>
    </div>
  );
}

export default Index;

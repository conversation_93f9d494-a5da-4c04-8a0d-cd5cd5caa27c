import React, { Component } from "react";

import styles from "/public/assets/css/component/itemListCourse.module.css";

import Link from "next/link";
import Image from "next/image";

export default class index extends Component {
  render() {
    return (
      this.props.data.map((val, key) => (
        <div key={key} className={`${styles.item_list_course}`}>
          <div className={`${styles.item_course_inner}`}>
            <div className={`${styles.item_course_img}`}>
              <div className={`${styles.blockLisCourseImgSize}`}>
                {val && val.image_th && val.image_th!=null && val.image_th!='' && val.image_th!='null' ?(
                  <Image className={`${styles.thumb}`} key={key} src={val.image_th} 
                  layout="fill"
                  objectFit="cover"
                  alt=""
                  />
                ):null}
              </div>
            </div>
            <div className={`${styles.item_course_text}`}>
              <h3>{val.title_th}</h3>
              <h4>{val.subtitle_th}</h4>
            </div>
            <div className={`${styles.item_course_action}`}>
              <a href={`/course/${val.slug}`}>
                <button className={`${styles.action}`}>
                  <i className="icon-ic-play"></i>
                  <span>เข้าเรียน</span>
                </button>
              </a>
              <button className={`${styles.action} oculus-pin-btn`} onClick={() => this.props.callback(val.id,val.ep_id)}>
                <i className="icon-ic-qrcode"></i>
                <span>Oculus Pin</span>
              </button>
            </div>
          </div>
        </div>
      ))
    );
  }
}

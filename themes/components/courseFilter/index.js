import React, { Component } from "react";
import { Accordion, I<PERSON> } from "semantic-ui-react";

import Link from "next/link";
import "swiper/css";

function btn_close() {
  document.getElementById("block_filter").classList.remove("on_show");
  document.body.classList.remove("open_nav");
}

function filterActive(_data,_filter) {
  const filter = _filter;
  const data = _data;
  const arr = [];
  for (var i = 0; i < filter.length; i++) {
    for (var j = 0; j < data.length; j++) {
      if(filter[i].category_key==data[j].category_key){
        arr.push(j);
      }
    }
  }
  return arr;
}
class Index extends Component {
  
  state = { activeIndexs: filterActive(this.props.data,this.props.filter) };

  handleClick = (e, titleProps) => {
    const { index } = titleProps;
    const { activeIndexs } = this.state;
    const newIndex = activeIndexs;

    const currentIndexPosition = activeIndexs.indexOf(index);
    if (currentIndexPosition > -1) {
      newIndex.splice(currentIndexPosition, 1);
    } else {
      newIndex.push(index);
    }

    this.setState({ activeIndexs: newIndex });
  };
  render() {
    const { activeIndexs } = this.state
    setTimeout(() => {
      var checkboxes = document.getElementsByName('checkbox_filter');
      for (var checkbox of checkboxes) {
          checkbox.checked = false;
      }
      for (var i = 0; i < this.props.filter.length; i++) {
        for (var k = 0; k < this.props.filter[i]['cate'].length; k++) {
          try {
            document.getElementById(this.props.filter[i].category_key+'_'+this.props.filter[i]['cate'][k]).checked = true;
          }
          catch(err) {
          }
        }
      }
    }, "0");
    return (
      <div className="block-course-filter">
        <div className="inner">
          <div className="filter-option">
            <h2>{this.props.title}</h2>
            <h3>Navigation Filtering </h3>
            
            {/* Update Filter Style */}
            <div className="group-filter-check">
              <Accordion>
                {this.props.data.map((val, key) => (
                  <div key={key}>
                      <Accordion.Title
                        active={activeIndexs.includes(key)}
                        index={key}
                        onClick={this.handleClick}
                      >
                        <span>
                          {val.category_name} 
                          {/* <span className="small">{this.props.countNumFilter(val.category_key)}</span> */}
                        </span>
                        
                        <i className="icon-ic-down"></i>
                      </Accordion.Title>
                      <Accordion.Content active={activeIndexs.includes(key)}>
                        <div className="list-filter-check">
                          {val.cate.map((val_cate, key_cate) => (
                            <div key={key_cate} className="item-filter-check">
                              <div className="item-choose">
                                <div className="fm-check" onClick={() => this.props.selectCate(val.category_key, val_cate.id)}>
                                  <input name="checkbox_filter" id={`${val.category_key}_${val_cate.id}`} type="checkbox" />
                                  <div className="text">
                                    <div className="i_remark">
                                      <i className="icon-ic-tick" />
                                    </div>
                                    <p>{val_cate.title_th}</p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </Accordion.Content>
                  </div>
                ))}
              </Accordion>
            </div>
            {/* Update Filter Style */}
          </div>
          <div className="category-search-btn pd-t-b clear-filter">
            <button onClick={() => this.props.clearFilter()} className="btn-default">
              <span>ล้างผลการค้นหา</span>
            </button>
          </div>
          {this.props.tagData && this.props.tagData.length > 0 ? (
            <div className="filter-tags">
              <h2>Tag</h2>
              <div className="group-filter-tags">
                <div className="tags">
                  {this.props.tagData.map((val, key) => (
                    <div key={key} className="tag">
                      <span onClick={() => location.href=this.props.prefixUrl+'?tag_id='+val.id+'&tag='+val.title}>{val.title}</span>
                      <i
                        onClick={() => this.props.removeTag(key)}
                        className="remove icon-ic-close"
                      ></i>
                    </div>
                  ))}
                  {/* <button className="add-tag"><i className="add icon-ic-plus"></i></button> */}
                </div>
              </div>
            </div>
          ) : null}
        </div>
        <div className="btn-close" onClick={() => btn_close()}>
          <span className="close-1"></span>
          <span className="close-2"></span>
        </div>
      </div>
    );
  }
}

export default Index;

import React from "react";
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, } from "chart.js";
import { Bar } from "react-chartjs-2";
import Link from "next/link";
import { useState, useEffect, useContext } from "react";


import { Menu, Button, Modal, Select, Input, Icon, Dropdown, Label, } from "semantic-ui-react";

ChartJS.register(CategoryScale,LinearScale,BarElement,Title,Tooltip,Legend);

export const options = {
  responsive: true,
  scales: {
    yAxis: {
      min: 0,
      max: 10,
    },
    xAxis: {
      pointLabelFontSize:18,
      ticks: {
        size: 40
      }
    }
  },
  plugins: {
    legend: {
      display: false,
      labels: {},
    },
    title: {
      display: false,
    },
  },
};

const labels = ['คะแนนสูงสุด', 'คะแนนที่ทำได้', 'คะแนนต่ำสุด'];

export const data = {
  labels,
  datasets: [
    {
      data: [9, 5, 3,],
      backgroundColor: [
        'rgba(75, 192, 192, 0.5)',
        'rgba(54, 162, 235, 0.5)',
        'rgba(255, 99, 132, 0.5)',
      ],
    }
  ],
};
const Index = () => {
  return (
    <div className="statisticViewAverage">
        <div className="statisticViewAverage-description">
          <div className="item-description">เฉลี่ยคะแนนที่ทำได้</div>
        </div>
        <div className="statisticViewAverage-chart">
          <div className="chart-inner">
          <Bar options={options} data={data} />
          </div>
        </div>
    </div>
  );
};

export default Index;

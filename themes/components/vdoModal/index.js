import React, { Component, useState} from "react";
import Link from "next/link";
import Image from "next/image";
import ReactPlayer from "react-player";
import styles from "/public/assets/css/component/listVdoModal.module.css";
import {
  Menu,
  Button,
  Modal,
  Input,
  Icon,
  Dropdown,
  Label,
  List,
} from "semantic-ui-react";
import "semantic-ui-css/semantic.min.css";

import CardCourse from "../cardCourse";
import CardLock from "../cardLock";
import Swal from "sweetalert2";
export default class index extends Component {
  render() {
    return (
      <Modal
        className={`${styles.modalVdo}`}
        onClose={() => this.props.callback(false)}
        onOpen={() => this.props.callback(true)}
        open={true}
      >
        <Modal.Content className={`${styles.modaVdoContent}`}>
          <div className={`${styles.blockPlayer}`}>
            <div className="modal-video-block">
              <ReactPlayer
                playsinline
                className="video-player"
                url={this.props.vdoData}
                controls={true}
                width={680}
                height={Number(680 / (16 / 9))}
              />
            </div>
          </div>
        </Modal.Content>
      </Modal>
    );
  }
}

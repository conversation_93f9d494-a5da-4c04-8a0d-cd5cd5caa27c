import React, { Component, useState } from "react";
import ReactPlayer from "react-player";
import styles from "/public/assets/css/component/courseDetailIntro.module.css";
import Image from "next/image";

import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
  Progress,
} from "semantic-ui-react";
export default class index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      
    };
  }

  render() {
    if (this.props.type == "normal") {
      return (
        <div className="block-course-detail-intro">
          <div className="inner"> 
              <div>
                <div className="intro-img">
                  <div className={styles.imgThumbDiv}>
                    {this.props.data && this.props.data.cover && this.props.data.cover!=null && this.props.data.cover!='' && this.props.data.cover!='null' ?(
                      <Image layout="fill" className="img-thumb d-none d-xl-block d-md-block" src={this.props.data.cover} alt="" />
                    ):null}
                    {this.props.data && this.props.data.cover_m && this.props.data.cover_m!=null && this.props.data.cover_m!='' && this.props.data.cover_m!='null' ?(
                      <Image layout="fill" className="img-thumb d-block d-md-none d-xl-none" src={this.props.data.cover_m} alt="" />
                    ):null}
                  </div>
                </div>
                <div className="intro-icon">
                  <i onClick={() => this.props.callback(this.props.ep-1)} className="icon-ic-circle-play"></i>
                </div>
              </div>
              {/* {this.props.data.allowed ? (
                null
              ) : (
                <div className="intro-buy">
                  <div className="container custom-container">
                    <button onClick={() => this.props.addCart('cart',this.props.data.id)} className="btn-default btn-buy">
                      <span>ซื้อคอร์สนี้</span>
                    </button>
                  </div>
                </div> 
              )} */}
            <div className="intro-progress">
              <Progress percent={this.props.data.learned_percent} />
            </div>
          </div>
        </div>
      );
    }else if (this.props.type == "zoom") {
      return (
        <div className="block-course-detail-intro">
          <div className="inner"> 
              <div>
                <div className="intro-img">
                  <div className={styles.imgThumbDiv}>
                    {this.props.data && this.props.data.cover && this.props.data.cover!=null && this.props.data.cover!='' && this.props.data.cover!='null' ?(
                      <Image layout="fill" className="img-thumb d-none d-xl-block d-md-block" src={this.props.data.cover} alt="" />
                    ):null}
                    {this.props.data && this.props.data.cover_m && this.props.data.cover_m!=null && this.props.data.cover_m!='' && this.props.data.cover_m!='null' ?(
                      <Image layout="fill" className="img-thumb d-block d-md-none d-xl-none" src={this.props.data.cover_m} alt="" />
                    ):null}
                  </div>
                  <div className="container custom-container">
                    <div className={styles.imgLogoDiv}>
                      <Image layout="intrinsic" className="img-logo" src="/assets/images/zoom.png" width={208} height={208} alt="" />
                    </div>
                  </div>
                </div>
                <div className="intro-icon">
                  <i onClick={() => this.props.callback(this.props.ep-1)} className="icon-ic-circle-play"></i>
                </div>
              </div>
              {/* {this.props.data.allowed ? (
                null
              ) : (
                <div className="intro-buy">
                  <div className="container custom-container">
                    <button onClick={() => this.props.addCart('cart',this.props.data.id)} className="btn-default btn-buy">
                      <span>ซื้อคอร์สนี้</span>
                    </button>
                  </div>
                </div> 
              )} */}
            <div className="intro-progress">
              <Progress percent={this.props.data.learned_percent} />
            </div>
          </div>
        </div>
      );
    }else if (this.props.type == "ticket") {
      return (
        <div className="block-course-detail-intro">
          <div className="inner"> 
              <div>
                <div className="intro-img">
                  <div className={styles.imgThumbDiv}>
                    {this.props.data && this.props.data.cover && this.props.data.cover!=null && this.props.data.cover!='' && this.props.data.cover!='null' ?(
                      <Image layout="fill" className="img-thumb d-none d-xl-block d-md-block" src={this.props.data.cover} alt="" />
                    ):null}
                    {this.props.data && this.props.data.cover_m && this.props.data.cover_m!=null && this.props.data.cover_m!='' && this.props.data.cover_m!='null' ?(
                      <Image layout="fill" className="img-thumb d-block d-md-none d-xl-none" src={this.props.data.cover_m} alt="" />
                    ):null}
                  </div>
                </div>
                <div className="intro-icon"></div>
              </div>
          </div>
        </div>
      );
    }else{
      return (
        <div className="block-course-detail-intro">
          <div className="inner"> 
            <div>
              <div className="intro-img">
                <div className={styles.imgThumbDiv}>
                  {this.props.data && this.props.data.cover && this.props.data.cover!=null && this.props.data.cover!='' && this.props.data.cover!='null' ?(
                    <Image layout="fill" className="img-thumb d-none d-xl-block d-md-block" src={this.props.data.cover} alt="" />
                  ):null}
                  {this.props.data && this.props.data.cover_m && this.props.data.cover_m!=null && this.props.data.cover_m!='' && this.props.data.cover_m!='null' ?(
                    <Image layout="fill" className="img-thumb d-block d-md-none d-xl-none" src={this.props.data.cover_m} alt="" />
                  ):null}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
  }
}

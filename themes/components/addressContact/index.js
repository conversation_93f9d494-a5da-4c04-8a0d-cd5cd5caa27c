import React, { Component } from "react";
import styles from "/public/assets/css/component/addressContact.module.css";
import Link from "next/link";

export default class index extends Component {
  render() {
    return (
      <div className={`${styles.block_address_contact}`}>
        <div className={`${styles.inner}`}>
          {/* ===== */}
          <div className={`${styles.item_address_contact}`}>
            <div className={`${styles.title_address_contact}`}>
              ข้อมูลติดต่อ
            </div>
            <div className={`${styles.data_address_contact}`}>
              {this.props.data.email}
            </div>
            <div className={`${styles.action_address_contact}`}>
              <Link href={"/profile/edit"}>
                <button>เปลี่ยน</button>
              </Link>
            </div>
          </div>
          {/* ===== */}
          <div className={`${styles.item_address_contact}`}>
            <div className={`${styles.title_address_contact}`}>
              ที่อยู่สำหรับ <br></br>
              ออกใบเสร็จ
            </div>
            {this.props.data.address != "" &&
            this.props.data.subdistrict_name != "" &&
            this.props.data.district_name != "" &&
            this.props.data.province_name != "" &&
            this.props.data.postcode != "" &&
            this.props.data.address != null &&
            this.props.data.subdistrict_name != null &&
            this.props.data.district_name != null &&
            this.props.data.province_name != null &&
            this.props.data.postcode != null ? (
              this.props.data.province_name == "กรุงเทพมหานคร" ? (
                <div className={`${styles.data_address_contact}`}>
                  {this.props.data.address} แขวง
                  {this.props.data.subdistrict_name}, เขต
                  {this.props.data.district_name},{" "}
                  {this.props.data.province_name} {this.props.data.postcode},
                  ไทย
                </div>
              ) : (
                <div className={`${styles.data_address_contact}`}>
                  {this.props.data.address} ตำบล
                  {this.props.data.subdistrict_name}, อำเภอ
                  {this.props.data.district_name},{" "}
                  {this.props.data.province_name} {this.props.data.postcode},
                  ไทย
                </div>
              )
            ) : (
              <div className={`${styles.data_address_contact}`}></div>
            )}

            <div className={`${styles.action_address_contact}`}>
              <Link href={"/profile/edit"}>
                <button>เปลี่ยน</button>
              </Link>
            </div>
          </div>
          {/* ===== */}
        </div>
      </div>
    );
  }
}

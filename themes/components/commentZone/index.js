import React, { Component } from "react";
import Image from "next/image";
import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
  TextArea,
} from "semantic-ui-react";
import NumberFormat from 'react-number-format';
import { render } from "react-dom";

import styles from "/public/assets/css/component/commentZone.module.css";

import Swal from 'sweetalert2'
export default class index extends Component {
  constructor(props) {
    super(props);
    if(this.props.lang){
      this.state = {
        comment: '',
        lang:this.props.lang
      };
    }else{
      this.state = {
        comment: '',
        lang:'th'
      };
    }
  }
  translateEng(_value) {
    if(this.state.lang=='en'){
      if(_value=='กรุณากรอกความคิดเห็น'){
        return "Please enter a comment";
      }else if(_value=='ปิด'){
        return "Close";
      }else if(_value=='พิมพ์คำถามที่ต้องการสอบถาม'){
        return "Type the question you want to ask";
      }else if(_value=='แสดงความคิดเห็น'){
        return "Comment";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  setReply(_name) {
    this.setState({
      comment: '@'+_name+' ',
    });
    setTimeout(() => {
      document.getElementById('comment_box').value = this.state.comment;
      document.getElementById("comment_box").focus();
    }, "0");
  }
  submit() {
    this.setState({
      comment: document.getElementById("comment_box").value,
    });
    setTimeout(() => {
      if(this.state.comment!=''){
        this.props.addComment(this.state.comment)
      }else{
        Swal.fire({
          text: this.translateEng('กรุณากรอกความคิดเห็น'),
          icon: 'error',
          confirmButtonText: this.translateEng('ปิด'),
          confirmButtonColor: "#648d2f"
        })
      }
      this.setState({
        comment: "",
      });
      document.getElementById("comment_box").value = "";
    }, "0");
  }
  render() {
    return (
      <div className="block-commnet-zone">
        <div className="inner">
          <div className="commnet-zone-list">
            {this.props.data.map((val, key) => (
              <div key={key}>
                <div className="commnet-item commnet-question">
                  <div className="commnet-avatar">
                    {
                      val.user_avatar && val.user_avatar!=null && val.user_avatar!='' && val.user_avatar!='null'?
                      <div className="BlockImgSize">
                        <Image className="thumb ItemsImgSize" src={val.user_avatar} 
                          layout="fill"
                          objectFit="contain"
                          alt=""
                        />
                      </div>
                      :
                      <div className="BlockImgSize">
                        <Image className="thumb ItemsImgSize" src="/assets/images/user-key.png" 
                          layout="fill"
                          objectFit="contain"
                          alt=""
                        />
                      </div>
                    }
                  </div>
                  <div className="commnet-info">
                    <h3>{val.user_name}</h3>
                    <h4 dangerouslySetInnerHTML={{__html: val.comment}}></h4>
                    <div className="action">
                      {val.liked ?(
                        <button onClick={() => this.props.addLike(val.id)} className="btn-action like">
                          <div className="blockLike2ImgSize">
                            <Image className="itemLike2ImgSize" src="/assets/images/like1.png" 
                                layout="fill"
                                objectFit="contain"
                                alt=""
                            />
                          </div>
                        </button>
                      ):(
                        <button onClick={() => this.props.addLike(val.id)} className="btn-action like">
                          <div className="blockLike2ImgSize">
                            <Image className="itemLike2ImgSize" src="/assets/images/like2.png" 
                              layout="fill"
                              objectFit="contain"
                              alt=""
                            />
                          </div>
                        </button>
                      )}
                      <NumberFormat
                        value={val.like_count}
                        displayType={'text'}
                        thousandSeparator={true}
                        renderText={(value, props) => <span {...props}>{value}</span>}
                      />
                      <button onClick={() => this.setReply(val.user_name)} className="btn-action ans">
                        <div className="blockLike2ImgSize">
                          <Image className="itemLike2ImgSize" src="/assets/images/commen_icon.png" 
                              layout="fill"
                              objectFit="contain"
                              alt=""
                          />
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
                {val.reply.map((val_re, key_re) => (
                  <div className="commnet-item commnet-answer" key={key_re}>
                    <div className="commnet-avatar">
                      {val_re.user_avatar && val_re.user_avatar!=null && val_re.user_avatar!='' && val_re.user_avatar!='null' ? (
                           <div className="BlockImgSize">
                              <Image className="thumb ItemsImgSize" src={val_re.user_avatar} 
                                layout="fill"
                                objectFit="contain"
                                alt=""
                              />
                           </div>
                      ):(
                        <div className="BlockImgSize">
                          <Image className="thumb ItemsImgSize" src="/assets/images/user-key.png" 
                            layout="fill"
                            objectFit="contain"
                            alt=""
                          />
                        </div>
                      )}
                    </div>
                    <div className="commnet-info">
                      <h3>{val_re.user_name}</h3>
                      <h4 dangerouslySetInnerHTML={{__html: val_re.comment}}></h4>
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
          <div className="commnet-zone-fm">
            {this.props.user && this.props.user.avatar && this.props.user.avatar!=null && this.props.user.avatar!='' && this.props.user.avatar!='null' ? (
                <Image className="thumb itemCommnetImgSize" src={this.props.user.avatar} 
                  layout="fill"
                  objectFit="contain"
                  alt=""
                />
            ):(
              <Image className="thumb itemCommnetImgSize" src="/assets/images/iuser.png" 
                  layout="fill"
                  objectFit="contain"
                  alt=""
              />
            )}
            <div className="item-fm">
              <TextArea id="comment_box"
                placeholder={this.translateEng('พิมพ์คำถามที่ต้องการสอบถาม')}
                className="fm-control"
              />
            </div>
          </div>
          <div className="commnet-zone-fm">
            {this.props.user && this.props.user.avatar && this.props.user.avatar!=null && this.props.user.avatar!='' && this.props.user.avatar!='null' ? (
              <Image className="thumb opacity-0 itemCommnetImgSize" src={this.props.user.avatar} 
              layout="fill"
              objectFit="contain"
              alt=""
              />
            ):(
              <Image className="thumb opacity-0 itemCommnetImgSize" src="/assets/images/iuser.png" 
              layout="fill"
              objectFit="contain"
              alt=""
              />
            )}
            <div className="item-fm">
              <div className="comment-button">
                <button onClick={() => this.submit()} className="btn-default">
                  <span>{this.translateEng('แสดงความคิดเห็น')}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

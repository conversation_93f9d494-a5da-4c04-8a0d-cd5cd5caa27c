import React, { Component } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Zoom, FreeMode, Pagination, Controller, Navigation,Lazy,Mousewheel } from "swiper";

import CardCourse from "../cardCourse";
import CardProgram from "../cardProgram";
import CardProgramSw from "../cardProgramSw";
import CardProgramGroup from "../cardProgramGroup";
import CardProgramPop from "../cardProgramPop";
import CardSpeaker from "../cardSpeaker";
import CardContinue from "../cardContinue";
import CardContinueSw from "../cardContinueSw";
import CardProgramInfo from "../cardProgramInfo";
import CardSeminar from "../cardSeminar";
import CardInfographic from "../cardInfographic";
import CardInfographicSw from "../cardInfographicSw";
import CardNews from "../cardNews";
import CardNewsSw from "../cardNewsSw";
import CardLock from "../cardLock";
import <PERSON> from "next/link";
import Image from "next/image";

import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/lazy";
import styles from "/public/assets/css/component/groupCategory.module.css";
export default class index extends Component {
  render() {
    if (this.props.type == "normal") {
      return (
        <div
          className={`container custom-container space-between-content ${this.props.bg&&this.props.bg!=null&&this.props.bg!='null'&&this.props.bg!=''?'is_banner':''}`}
          style={{ zIndex: this.props.index,
            backgroundImage: `url(${this.props.bg})`}}
        >
          <div className="row">
            <div className="col-12">
              <h2
                className={`${styles.title}`}
                style={{ color: this.props.color }}
              >
                {this.props.name}
              </h2>
            </div>
          </div>

          <Swiper
              freeMode={true}
              loop={this.props.isLoop}
              lazy={{
                enabled: true,
                loadPrevNext: true,
                loadPrevNextAmount:6
              }}
              mousewheel={{
                forceToAxis: true,
                enabled: true,
              }}
              modules={[FreeMode,Lazy,Zoom,Mousewheel]}
              spaceBetween={0}
              zoom={true}
              className={`${styles.mySlide}`}
              breakpoints={{
                0: {
                  slidesPerView: 1.5,
                },
                768: {
                  slidesPerView: 3,
                },
                1200: {
                  slidesPerView: this.props.size,
                },
              }}
            >
              {this.props.data.map((val, key) => (
                <SwiperSlide key={key}>
                  <CardProgramSw
                    type="normal"
                    callback={this.props.callback}
                    key={key}
                    data={val}
                  ></CardProgramSw>
                </SwiperSlide>
              ))}
          </Swiper>
        </div>
      );
    } else if (this.props.type == "zoom") {
      return (
        <div
          className={`container custom-container space-between-content`}
          style={{ zIndex: this.props.index }}
        >
          <div className="row">
            <div className="col-12">
              <h2
                className={`${styles.title}`}
                style={{ color: this.props.color }}
              >
                {this.props.name}
              </h2>
            </div>
          </div>
          <Swiper
              freeMode={true}
              loop={this.props.isLoop}
              lazy={{
                enabled: true,
                loadPrevNext: true,
                loadPrevNextAmount:6
              }}
              mousewheel={{
                forceToAxis: true,
                enabled: true,
              }}
              modules={[FreeMode,Lazy,Zoom,Mousewheel]}
              spaceBetween={0}
              zoom={true}
              className={`${styles.mySlide}`}
              breakpoints={{
                0: {
                  slidesPerView: 1.5,
                },
                768: {
                  slidesPerView: this.props.size,
                },
              }}
            >
              {this.props.data.map((val, key) => (
                <SwiperSlide key={key}>
                  <CardProgramSw
                    type="zoom"
                    callback={this.props.callback}
                    key={key}
                    data={val}
                  ></CardProgramSw>
                </SwiperSlide>
              ))}
          </Swiper>
        </div>
      );
    } else if (this.props.type == "series") {
      return (
        <div
          className={`container-fluid space-between-content ${styles.backgroundSeries}`}
          style={{
            zIndex: this.props.index,
            backgroundImage: `url(${this.props.backgroundImage})`,
          }}
        >
          <div className="container custom-container">
            <div className="row">
              <div className="col-12">
                <h2
                  className={`${styles.seriestitle}`}
                  style={{ color: this.props.color }}
                >
                  {this.props.title}
                </h2>
                <p
                  className={`space-between-content-bottom ${styles.subtitle}`}
                  style={{ color: this.props.color }}
                >
                  {this.props.subtitle}
                </p>

                <Swiper
                  freeMode={true}
                  loop={this.props.isLoop}
                  lazy={{
                    enabled: true,
                    loadPrevNext: true,
                    loadPrevNextAmount:6
                  }}
                  mousewheel={{
                    forceToAxis: true,
                    enabled: true,
                  }}
                  modules={[FreeMode,Lazy,Zoom,Mousewheel]}
                  spaceBetween={0}
                  zoom={true}
                  navigation={{}}
                  className={`${styles.mySlide}`}
                  breakpoints={{
                    0: {
                      slidesPerView: 2,
                    },
                    768: {
                      slidesPerView: this.props.size,
                    },
                  }}
                >
                  {this.props.data.map((val, key) => (
                    <SwiperSlide key={key}>
                      <CardProgramSw
                        type="normal"
                        callback={this.props.callback}
                        key={key}
                        data={val}
                      ></CardProgramSw>
                    </SwiperSlide>
                  ))}
                </Swiper>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (this.props.type == "seriesBanner") {
      return (
        <div
          className={`container-fluid space-between-content ${styles.backgroundSeries}`}
          style={{
            zIndex: this.props.index,
            backgroundImage: `url(${this.props.backgroundImage})`,
          }}
        >
          <div className="container custom-container">
            <div className="row">
              <div className="col-12">
                <h2
                  className={`${styles.seriestitle}`}
                  style={{ color: this.props.color }}
                >
                  {this.props.title}
                </h2>
                <p
                  className={`space-between-content-bottom ${styles.subtitle}`}
                  style={{ color: this.props.color }}
                  dangerouslySetInnerHTML={{__html: this.props.subtitle}}
                >
                </p>
                <p
                  className={`space-between-content-bottom ${styles.subtitle}`}
                >
                  {!this.props.isBuy ? (
                    this.props.price==0||this.props.isInternal ? (
                      <a
                        className={`${styles.btnBuyNow}`}
                        style={{
                          color: this.props.buttonColor,
                          backgroundColor: this.props.buttonBackgroundColor,
                        }}
                        onClick={() =>
                          this.props.callback("group", this.props.groupId, "")
                        }
                      >
                        ฟรี
                      </a>
                    ) : (
                      <a
                        className={`${styles.btnBuyNow}`}
                        style={{
                          color: this.props.buttonColor,
                          backgroundColor: this.props.buttonBackgroundColor,
                        }}
                        onClick={() =>
                          this.props.callback("group", this.props.groupId, "")
                        }
                      >
                        ซื้อ Group Course นี้ {this.props.price.toLocaleString()} บาท
                      </a>
                    )
                  ):null}
                </p>
                <Swiper
                  freeMode={true}
                  loop={this.props.isLoop}
                  lazy={{
                    enabled: true,
                    loadPrevNext: true,
                    loadPrevNextAmount:6
                  }}
                  mousewheel={{
                    forceToAxis: true,
                    enabled: true,
                  }}
                  modules={[FreeMode,Lazy,Zoom,Mousewheel]}
                  spaceBetween={0}
                  zoom={true}
                  navigation={{}}
                  className={`${styles.mySlide}`}
                  breakpoints={{
                    0: {
                      slidesPerView: 2,
                    },
                    768: {
                      slidesPerView: this.props.size,
                    },
                  }}
                >
                  {this.props.data.map((val, key) => (
                    <SwiperSlide key={key}>
                      <CardProgramSw
                        type="normal"
                        callback={this.props.callback}
                        key={key}
                        data={val}
                      ></CardProgramSw>
                    </SwiperSlide>
                  ))}
                </Swiper>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (this.props.type == "seminar") {
      return (
        <div
          className={`container custom-container space-between-content`}
          style={{ zIndex: this.props.index }}
        >
          <div className="row">
            <div className="col-12">
              <h2
                className={`${styles.title}`}
                style={{ color: this.props.color }}
              >
                {this.props.name}
              </h2>
            </div>
          </div>
          <Swiper
            freeMode={true}
            loop={this.props.isLoop}
            lazy={{
              enabled: true,
              loadPrevNext: true,
              loadPrevNextAmount:6
            }}
            mousewheel={{
              forceToAxis: true,
              enabled: true,
            }}
            modules={[FreeMode,Lazy,Zoom,Mousewheel]}
            spaceBetween={20}
            zoom={true}
            className={`${styles.mySlide}`}
            breakpoints={{
              0: {
                slidesPerView: 1.2,
              },
              768: {
                slidesPerView: 3,
              },
              1200: {
                slidesPerView: this.props.size,
              },
            }}
          >
            {this.props.data.map((val, key) =>
              <SwiperSlide key={key}>
                <CardProgramSw
                  type="normal"
                  callback={this.props.callback}
                  key={key}
                  data={val}
                ></CardProgramSw>
              </SwiperSlide>
            )}
          </Swiper>
        </div>
      );
    } else if (this.props.type == "infographic") {
      return (
        <div
          className={`container custom-container space-between-content ${this.props.bg&&this.props.bg!=null&&this.props.bg!='null'&&this.props.bg!=''?'is_banner':''}`}
          style={{ zIndex: this.props.index,
            backgroundImage: `url(${this.props.bg})` }}
        >
          <div className="row">
            <div className="col-12">
              <h2
                className={`${styles.title}`}
                style={{ color: this.props.color }}
              >
                {this.props.name}
              </h2>
            </div>
          </div>

          <Swiper
            freeMode={true}
            loop={this.props.isLoop}
            lazy={{
              enabled: true,
              loadPrevNext: true,
              loadPrevNextAmount:6
            }}
            mousewheel={{
              forceToAxis: true,
              enabled: true,
            }}
            modules={[FreeMode,Lazy,Zoom,Mousewheel]}
            spaceBetween={20}
            zoom={true}
            className={`${styles.mySlide}`}
            breakpoints={{
              0: {
                slidesPerView: 1.2,
              },
              768: {
                slidesPerView: 3,
              },
              1200: {
                slidesPerView: this.props.size,
              },
            }}
          >
            {this.props.data.map((val, key) =>
              <SwiperSlide key={key}>
                <CardInfographicSw key={key} data={val}></CardInfographicSw>
              </SwiperSlide>
            )}
          </Swiper>
        </div>
      );
    } else if (this.props.type == "news") {
      return (
        <div
          className={`container custom-container space-between-content home-news ${this.props.bg&&this.props.bg!=null&&this.props.bg!='null'&&this.props.bg!=''?'is_banner':''}`}
          style={{ zIndex: this.props.index,
            backgroundImage: `url(${this.props.bg})` }}
        >
          <div className="row">
            <div className="col-12">
              <h2
                className={`${styles.title}`}
                style={{ color: this.props.color }}>
                {this.props.name}
              </h2>
            </div>
          </div>

          <Swiper
            freeMode={true}
            loop={this.props.isLoop}
            lazy={{
              enabled: true,
              loadPrevNext: true,
              loadPrevNextAmount:6
            }}
            mousewheel={{
              forceToAxis: true,
              enabled: true,
            }}
            modules={[FreeMode,Lazy,Zoom,Mousewheel]}
            spaceBetween={20}
            zoom={true}
            className={`${styles.mySlide}`}
            breakpoints={{
              0: {
                slidesPerView: 1,
                // noSwiping: true,
                // noSwipingClass: "swiper-slide",
              },
              768: {
                slidesPerView: 3,
              },
              1200: {
                slidesPerView: this.props.size,
              },
            }}
          >
            {this.props.data.map((val, key) => (
              <SwiperSlide className="CardNews_mobile" key={key}>
                <CardNewsSw  key={key} data={val}></CardNewsSw>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      );
    } else if (this.props.type == "continue") {
      return (
        <div
          className={`container custom-container space-between-content ${this.props.bg&&this.props.bg!=null&&this.props.bg!='null'&&this.props.bg!=''?'is_banner':''}`}
          style={{ zIndex: this.props.index,
            backgroundImage: `url(${this.props.bg})` }}
        >
          <div className="row">
            <div className="col-12">
              <h2
                className={`${styles.title}`}
                style={{ color: this.props.color }}
              >
                {this.props.name}
              </h2>
            </div>
          </div>

          <Swiper
              freeMode={true}
              loop={this.props.isLoop}
              lazy={{
                enabled: true,
                loadPrevNext: true,
                loadPrevNextAmount:6
              }}
              mousewheel={{
                forceToAxis: true,
                enabled: true,
              }}
              modules={[FreeMode,Lazy,Zoom,Mousewheel]}
              spaceBetween={0}
              zoom={true}
              className={`${styles.mySlide}`}
              breakpoints={{
                0: {
                  slidesPerView: 1.5,
                },
                768: {
                  slidesPerView: 3,
                },
                1200: {
                  slidesPerView: this.props.size,
                },
              }}
            >
              {this.props.data.map((val, key) => (
                <SwiperSlide key={key}>
                  {val.learning ? (
                    <CardContinueSw
                      type="normal"
                      callback={this.props.callback}
                      key={key}
                      data={val}
                    ></CardContinueSw> 
                  ):(
                    <CardProgramSw
                      type="normal"
                      callback={this.props.callback}
                      key={key}
                      data={val}
                    ></CardProgramSw> 
                  )}
                  
                </SwiperSlide>
              ))}
            </Swiper>
        </div>
      );
    } else if (this.props.type == "popular") {
      return (
        <div
          className={`container custom-container space-between-content ${this.props.bg&&this.props.bg!=null&&this.props.bg!='null'&&this.props.bg!=''?'is_banner':''}`}
          style={{ zIndex: this.props.index,
            backgroundImage: `url(${this.props.bg})` }}
        >
          <div className="row">
            <div className="col-12">
              <h2
                className={`${styles.title}`}
                style={{ color: this.props.color }}
              >
                {this.props.name}
              </h2>
            </div>
          </div>

          <Swiper
              freeMode={true}
              loop={this.props.isLoop}
              lazy={{
                enabled: true,
                loadPrevNext: true,
                loadPrevNextAmount:6
              }}
              mousewheel={{
                forceToAxis: true,
                enabled: true,
              }}
              modules={[FreeMode,Lazy,Zoom,Mousewheel]}
              spaceBetween={0}
              zoom={true}
              className={`${styles.mySlide}`}
              breakpoints={{
                0: {
                  slidesPerView: 1.5,
                },
                768: {
                  slidesPerView: 3,
                },
                1200: {
                  slidesPerView: this.props.size,
                },
              }}
            >
              {this.props.data.map((val, key) => (
                <SwiperSlide key={key}>
                  <CardProgramPop
                    type="normal"
                    callback={this.props.callback}
                    key={key}
                    data={val}
                  ></CardProgramPop>
                </SwiperSlide>
              ))}
          </Swiper>
        </div>
      );
    } else if (this.props.type == "speaker") {
      return (
        <div
          className={`container custom-container space-between-content home-news ${this.props.bg&&this.props.bg!=null&&this.props.bg!='null'&&this.props.bg!=''?'is_banner':''}`}
          style={{ zIndex: this.props.index,
            backgroundImage: `url(${this.props.bg})` }}
        >
          <div className="row">
            <div className="col-12">
              <h2
                className={`${styles.title}`}
                style={{ color: this.props.color }}>
                {this.props.name}
              </h2>
            </div>
          </div>

          <Swiper
            freeMode={true}
            loop={this.props.isLoop}
            lazy={{
              enabled: true,
              loadPrevNext: true,
              loadPrevNextAmount:6
            }}
            mousewheel={{
              forceToAxis: true,
              enabled: true,
            }}
            modules={[FreeMode,Lazy,Zoom,Mousewheel]}
            spaceBetween={20}
            zoom={true}
            className={`${styles.mySlide}`}
            breakpoints={{
              0: {
                slidesPerView: 1,
                // noSwiping: true,
                // noSwipingClass: "swiper-slide",
              },
              768: {
                slidesPerView: 3,
              },
              1200: {
                slidesPerView: this.props.size,
              },
            }}
          >
            {this.props.data.map((val, key) => (
              <SwiperSlide className="CardNews_mobile" key={key}>
                <CardSpeaker  key={key} data={val}></CardSpeaker>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      );
    } else if (this.props.type == "group") {
      return (
        <div
          className={`container custom-container space-between-content ${this.props.bg&&this.props.bg!=null&&this.props.bg!='null'&&this.props.bg!=''?'is_banner':''}`}
          style={{ zIndex: this.props.index,
            backgroundImage: `url(${this.props.bg})`}}
        >
          <div className="row">
            <div className="col-12">
              <h2
                className={`${styles.title}`}
                style={{ color: this.props.color }}
              >
                {this.props.name}
              </h2>
            </div>
          </div>

          <Swiper
              freeMode={true}
              loop={this.props.isLoop}
              lazy={{
                enabled: true,
                loadPrevNext: true,
                loadPrevNextAmount:6
              }}
              mousewheel={{
                forceToAxis: true,
                enabled: true,
              }}
              modules={[FreeMode,Lazy,Zoom,Mousewheel]}
              spaceBetween={0}
              zoom={true}
              className={`${styles.mySlide}`}
              breakpoints={{
                0: {
                  slidesPerView: 1.5,
                },
                768: {
                  slidesPerView: 3,
                },
                1200: {
                  slidesPerView: this.props.size,
                },
              }}
            >
              {this.props.data.map((val, key) => (
                <SwiperSlide key={key}>
                  <CardProgramGroup
                    type="normal"
                    callback={this.props.callback}
                    key={key}
                    data={val}
                  ></CardProgramGroup>
                </SwiperSlide>
              ))}
          </Swiper>
        </div>
      );
    } else if (this.props.type == "relate") {
      return (
        <div style={{backgroundColor: '#f2f2f2',width:'100%'}}>
          <div
            className={`container custom-container space-between-content ${this.props.bg&&this.props.bg!=null&&this.props.bg!='null'&&this.props.bg!=''?'is_banner':''}`}
            style={{ zIndex: this.props.index,
              backgroundImage: `url(${this.props.bg})`}}
          >
            <div className="row">
              <div className="col-12">
                <h2
                  className={`${styles.title}`}
                  style={{ color: this.props.color }}
                >
                  {this.props.name}
                </h2>
              </div>
            </div>

            <Swiper
                freeMode={true}
                loop={this.props.isLoop}
                lazy={{
                  enabled: true,
                  loadPrevNext: true,
                  loadPrevNextAmount:6
                }}
                mousewheel={{
                  forceToAxis: true,
                  enabled: true,
                }}
                modules={[FreeMode,Lazy,Zoom,Mousewheel]}
                spaceBetween={0}
                zoom={true}
                className={`${styles.mySlide}`}
                breakpoints={{
                  0: {
                    slidesPerView: 1.5,
                  },
                  768: {
                    slidesPerView: 3,
                  },
                  1200: {
                    slidesPerView: this.props.size,
                  },
                }}
              >
                {this.props.data.map((val, key) => (
                  <SwiperSlide key={key}>
                    <CardProgramSw
                      type="normal"
                      callback={this.props.callback}
                      key={key}
                      data={val}
                    ></CardProgramSw>
                  </SwiperSlide>
                ))}
            </Swiper>
          </div>
        </div>
      );
    } else {
      return null;
    }
  }
}

import React, { Component, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import styles from "/public/assets/css/component/tableAudioList.module.css";

import {
  Menu,
  Button,
  Modal,
  Input,
  Icon,
  Dropdown,
  Label,
  List,
} from "semantic-ui-react";
import "semantic-ui-css/semantic.min.css";
function convertSecond(second) {
  if (second != null && second != "") {
    var hours = parseInt(
      new Date(second * 1000).toISOString().substring(11, 13)
    );
    var minutes = parseInt(
      new Date(second * 1000).toISOString().substring(14, 16)
    );
    var seconds = parseInt(
      new Date(second * 1000).toISOString().substring(17, 19)
    );
    if(hours==0){
      return minutes+'m';
    }else{
      return hours + "h:" + minutes+'m';
    }
    
  } else {
    return '0m';
  }
}
export default class index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      keySelect: this.props.keySelect,
      audioData: this.props.audioData,
      isPause: 0,
    };
  }

  selectAudio(_keySelect) {
    this.setState({
      keySelect: _keySelect,
    });
    this.setState({
      isPause: 0,
    });
    this.props.callback(_keySelect)
  }
  pauseAudio(_keySelect) {
    if(this.props.audioData[_keySelect]['vdo']!=null&&this.props.audioData[_keySelect]['vdo']!=''){
      if(this.state.isPause==0){
        this.setState({
          isPause: 1,
        });
        setTimeout(() => {
          document.querySelector('#audio_id audio').pause();
        }, "500");
      }else{
        this.setState({
          isPause: 0,
        });
        setTimeout(() => {
          document.querySelector('#audio_id audio').play();
        }, "500");
      }
    }
  }
  render() {
    return (
      <div className={`${styles.block_table_audio}`}>
        <div className={`${styles.inner_table_audio}`}>
          <div className={`${styles.scroll}`}>
          <table>
            <thead>
              <tr>
                <th scope="col" >ชื่อ</th>
                <th scope="col">เวลา</th>
                <th scope="col">ราคา</th>
              </tr>
            </thead>
            <tbody>
            {this.props.audioData && this.props.audioData.length>0 ? (
              this.props.audioData.map((val, key) =>
              val.lock ? (
                  <tr key={key}>
                    <td className={`${styles.status}`}>
                      <span>{key+1}. {val.title}</span>
                      <i className="icon-ic-audio-active"></i>
                    </td>
                    <td className={`${styles.time}`}>
                      {convertSecond(val.duration)}
                    </td>
                    {/* <td className={`${styles.action}`}>
                      <span>{val.price} บาท</span>
                    </td> */}
                    <td className={`${styles.action}`}>
                      <i className="icon-ic-circle-lock-full"></i>
                    </td>
                  </tr>
              ) : (
                this.state.keySelect == key ? 
                (
                  <tr key={key} className={`${styles.audio_active}`}>
                    <td className={`${styles.status}`}>
                      <span>{key+1}. {val.title}</span>
                      <i className="icon-ic-audio-active"></i>
                    </td>
                    <td className={`${styles.time}`}>
                      {convertSecond(val.duration)}
                    </td>
                    <td className={`${styles.action}`}>
                      {this.state.isPause == 0 ? 
                        (
                          <i onClick={() => this.pauseAudio(key)} className="pause circle outline icon"></i>
                        ) : (
                          <i onClick={() => this.pauseAudio(key)} className="play circle outline icon"></i>
                        )
                      }
                    </td>
                  </tr>
                ) : (
                  <tr key={key}>
                    <td className={`${styles.status}`}>
                      <span>{key+1}. {val.title}</span>
                    </td>
                    <td className={`${styles.time}`}>
                      {convertSecond(val.duration)}
                    </td>
                    <td className={`${styles.action}`}>
                      <i onClick={() => this.selectAudio(key)} className="play circle outline icon"></i>
                    </td>
                  </tr>
                )
              )
              )
            ):null}
            </tbody>
          </table>
          {this.props.data.allowed ? (
            null
          ):(
            this.props.data.allowed ||
            this.props.data.order_status == 4 ||
            this.props.data.order_status == 1 || this.props.data.is_soon==true ? null : (
              <div className="block-to-buy-course">
                {this.props.data.price == 0 || (this.props.data.is_promotion == 1 && this.props.data.pro_price == 0) || this.props.data.is_internal || this.props.data.is_subscription || this.props.data.is_volume ? (
                  this.props.data.is_subscription ? (
                    <div className="comment-button right">
                      <button onClick={() => this.props.addCart('vip',this.props.data.id,'')} className="btn-default">
                        <span>เริ่มเรียน</span>
                      </button>
                    </div>
                  ):(
                    this.props.data.is_volume ? (
                      <div className="comment-button right">
                        <button onClick={() => this.props.addCart('volume',this.props.data.id,'')} className="btn-default">
                          <span>เริ่มเรียน</span>
                        </button>
                      </div>
                    ):(
                      this.props.data.is_internal&&this.props.data.user_internal ? (
                        <div className="comment-button right">
                          <button onClick={() => this.props.addCart('internal',this.props.data.id,'')} className="btn-default">
                            <span>เริ่มเรียน</span>
                          </button>
                        </div>
                      ):(
                        <div className="comment-button right">
                          <button onClick={() => this.props.addCart('free',this.props.data.id,'')} className="btn-default">
                            <span>เริ่มเรียน</span>
                          </button>
                        </div>
                      )
                    )
                  )
                ) : (
                  <div className="comment-button right">
                    <button onClick={() => this.props.addCart('cart',this.props.data.id,'')} className="btn-default">
                      <span>ซื้อคอร์สนี้</span>
                    </button>
                  </div>
                )}
              </div>
            )
          )}
          </div>
        </div>
      </div>
    );
  }
}

import React, { Component } from "react";
import {
  Input,
  Select,
  Icon,
  Dropdown,
  Button,
} from "semantic-ui-react";
export default class index extends Component {
  constructor(props) {
    super(props);
    if(this.props.lang){
      this.state = {
        lang:this.props.lang
      };
    }else{
      this.state = {
        lang:'th'
      };
    }
  }
  translateEng(_value) {
    if(this.state.lang=='en'){
      if(_value=='กรอกโค้ดส่วนลด'){
        return "Enter the discount code";
      }else if(_value=='นำไปใช้'){
        return "Apply";
      }else{
        return _value;
      }
    }else{
      return _value;
    }
  }
  setInputCode() {
    this.props.callback(document.getElementById("input_code").value,'code')
  }

  render() {
    return (
      <div className="block-course-detail-code">
        <div className="inner">
          <div className="item-fm item-fm-w-btn">
            <Input id="input_code" type="text" className="fm-control" placeholder={this.translateEng('กรอกโค้ดส่วนลด')}></Input>
            <Button onClick={() => this.setInputCode()} className="fm-btn-control" content={this.translateEng('นำไปใช้')}/>
          </div>
        </div>
      </div>
    );
  }
}

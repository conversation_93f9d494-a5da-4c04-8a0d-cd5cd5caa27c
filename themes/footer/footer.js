import React from 'react'
import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import moment from 'moment'
import { useRouter } from 'next/router'
// import { Button, Header, Image, Modal } from 'semantic-ui-react'

const Footer = () => {
  const router = useRouter()
  const { locale, pathname, asPath } = router
  const [lang, setLang] = useState(locale)
  const [accCookie, setAccCookie] = React.useState(true)
  const [visible, setVisible] = useState(false)
  const toTop = React.useRef(null)

  function checkLocal() {
    if (window.localStorage.getItem('mdcuconsent')) {
      setAccCookie(true)
    } else {
      setAccCookie(false)
    }
  }

  function acceptCookie() {
    window.localStorage.setItem('mdcuconsent', moment())
    setAccCookie(true)
  }

  const toggleVisible = () => {
    const scrolled = document.documentElement.scrollTop
    if (scrolled > 100) {
      setVisible(true)
      try {
        toTop.current.classList.add('active')
      } catch (e) {}
    } else if (scrolled <= 100) {
      setVisible(false)
      try {
        toTop.current.classList.remove('active')
      } catch (e) {}
    }
  }

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }

  const scrollAtBottom = () => {
    const mainAllHeight = document.getElementsByClassName('main-all')
    const toTopButton = document.getElementsByClassName('block-to-top')
    if (window.innerHeight + window.scrollY == mainAllHeight[0].offsetHeight) {
      try {
        toTopButton[0].classList.add('bottom')
      } catch (e) {}
    } else {
      try {
        toTopButton[0].classList.remove('bottom')
      } catch (e) {}
    }
  }

  useEffect(() => {
    checkLocal()
    window.addEventListener('scroll', () => {
      toggleVisible()
      scrollAtBottom()
    })
  }, [])

  return (
    <div id="footer_position" className="main-footer">
      <div ref={toTop} className="block-to-top">
        <button className="btn-to-top" onClick={scrollToTop}>
          <i className="icon-ic-up"></i>
        </button>
      </div>
      <div className="footer-inner">
        {/* <div className="footer-copy">
          <p className="link">
            <Link href="/page/privacy">
              <a>Privacy Policy</a>
            </Link>
            |
            <Link href="/page/term">
              <a>Term and Condition</a>
            </Link>
            |
            <Link href="/page/consent">
              <a>Consent</a>
            </Link>
            |
            <a href="https://line.me/R/ti/p/@562tfgma" target="_blank">
              แจ้งปัญหาการใช้งาน
            </a>
            |
            <Link href="/faq">
              <a>FAQ</a>
            </Link>
          </p>
        </div> */}
        <div className="footer-content">
          <div className="container custom-container">
            <div className="footer-remark">
              {lang == 'en' ? (
                <h3>
                  Images, audio, videos, or any content materials on the MDCU
                  MedUMore website are not to be reproduced, distributed,
                  republished, downloaded, or modified without written consents
                  from the Academic Service Affairs, Faculty of Medicine,
                  Chulalongkorn University. Any violations of this Copyright
                  Policy will be prosecuted accordingly.{' '}
                </h3>
              ) : (
                <h3>
                  ภาพ เสียง หรือเนื้อหาในสื่อ MedUMore นี้ ห้ามมิให้ผู้ใดทำซ้ำ
                  ดัดแปลง หรือเผยแพร่โดยมิได้รับอนุญาต อย่างเป็นลายลักษณ์อักษร
                  ผู้ที่กระทำการฝ่าฝืนจะถูกดำเนินคดีตามกฎหมาย
                </h3>
              )}
            </div>
            <div className="row align-items-start fix-footer-space">
              <div className="col-f-content f-content-img footer-sponsor">
                <Link href="/">
                  <a>
                    <Image
                      src="/assets/images/footer-logo.png"
                      alt=""
                      layout="fill"
                      objectFit="cover"
                      objectPosition="center"
                      className="ItemSizeImgFooter"
                    />
                  </a>
                </Link>
              </div>
              {lang == 'en' ? (
                <div className="col-f-content f-content-address f-content-address-en">
                  <p>Academic Service Affairs</p>
                  <p>Faculty of Medicine, Chulalongkorn University</p>
                  <p>1873 Rama IV road, Pathumwan</p>
                  <p>Bangkok 10330</p>
                  {/* <p>
                  <i className="icon-ic-tel"></i>0 2256 4183 ,0 2256 4000
                  (รพ.จุฬาฯ)
                </p> */}
                  <p>
                    <i className="icon-ic-mail-bd"></i><EMAIL>
                  </p>
                </div>
              ) : (
                <div className="col-f-content f-content-address f-content-address-en">
                  <p>ฝ่ายบริการวิชาการ</p>
                  <p>คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย</p>
                  <p>1873 ถนนพระราม4 แขวงปทุมวัน เขตปทุมวัน</p>
                  <p>กรุงเทพฯ 10330</p>
                  {/* <p>
                  <i className="icon-ic-tel"></i>0 2256 4183 ,0 2256 4000
                  (รพ.จุฬาฯ)
                </p> */}
                  <p>
                    <i className="icon-ic-mail-bd"></i><EMAIL>
                  </p>
                </div>
              )}

              <div className="col-f-content f-content-address f-group-link">
                <Link href="/page/privacy">
                  <a>Privacy Policy</a>
                </Link>
                <Link href="/page/term">
                  <a>Term and Condition</a>
                </Link>
                <Link href="/page/consent">
                  <a>Consent</a>
                </Link>
              </div>
              <div className="col-f-content f-content-address">
                <div className="follow-item-top f-group-link">
                  {lang == 'en' ? (
                    <a
                      href="https://line.me/R/ti/p/@562tfgma"
                      target="_blank"
                      rel="noreferrer"
                    >
                      Technical Support
                    </a>
                  ) : (
                    <a
                      href="https://line.me/R/ti/p/@562tfgma"
                      target="_blank"
                      rel="noreferrer"
                    >
                      แจ้งปัญหาการใช้งาน
                    </a>
                  )}

                  <Link href="/faq">
                    <a>FAQ</a>
                  </Link>
                </div>
                <div className="follow-item-bottom">
                  <h3>Follow</h3>
                  <div className="group-connet">
                    <a
                      target="_blank"
                      rel="noreferrer"
                      href="https://www.facebook.com/MDCUMedUMore"
                    >
                      <i className="icon-ic-circle-social-facebook"></i>
                    </a>
                    <a
                      target="_blank"
                      href="https://twitter.com/MDCU_MedUmore"
                      rel="noreferrer"
                    >
                      <i className="icon-ic-circle-social-twitter"></i>
                    </a>
                    <a
                      target="_blank"
                      href="https://www.instagram.com/mdcu.medumore/"
                      rel="noreferrer"
                    >
                      <i className="icon-ic-circle-social-instagram"></i>
                    </a>
                    <a
                      target="_blank"
                      href="https://line.me/R/ti/p/@562tfgma"
                      rel="noreferrer"
                    >
                      <i className="icon-ic-circle-social-line"></i>
                    </a>
                    {/* <a href="#">
                      <i className="icon-ic-circle-social-facebook"></i>
                    </a>
                    <a href="#">
                      <i className="icon-ic-circle-social-twitter"></i>
                    </a>
                    <a href="#">
                      <i className="icon-ic-circle-social-instagram"></i>
                    </a>
                    <a href="#">
                      <i className="icon-ic-circle-social-line"></i>
                    </a> */}
                  </div>
                </div>
              </div>
              <div className="col-f-content f-content-connet">
                <Image
                  src="/assets/images/main-spon.png"
                  alt=""
                  layout="fill"
                  objectFit="cover"
                  objectPosition="center"
                  className="ItemSizeImg"
                />
              </div>
              <div className="header-hidden">
                <Link href={'/category/course'}>คอร์สออนไลน์</Link>
                <Link href={'/category/seminar'}>ประชุมวิชาการ</Link>
                <Link href={'/article'}>ข่าวสาร</Link>
                <Link href={'/infographic'}>Infographic</Link>
                <Link href={'/yearly-member'}>Package</Link>
                <Link href={'/page/about'}>About Us</Link>
              </div>
            </div>
            <div className="row justify-content-center align-items-start sponsor-list-footer">
              <div className="col-f-content f-content-img footer-sponsor">
                {lang == 'en' ? (
                  <h2 className="main-sponsor-text">Network Partners :</h2>
                ) : (
                  <h2 className="main-sponsor-text">ภาคีเครือข่าย :</h2>
                )}
                <div className="group-sponsor sponsor-group-first">
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/associate1.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/associate2.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/associate3.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/associate4.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/associate5.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/associate6.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/associate7.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/associate8.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/associate9.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/associate10.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                </div>
                <div className="group-sponsor sponsor-group-first">
                  <Link href="/">
                    <a className="sponsor-item sponsor-item-width-custom custom-m-t">
                      <Image
                        src="/assets/images/associate11.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item sponsor-item-width-custom-second custom-m-t">
                      <Image
                        src="/assets/images/associate12-width.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item sponsor-item-width-custom-third custom-m-t">
                      <Image
                        src="/assets/images/associate13.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                </div>
              </div>
            </div>
            <div className="row justify-content-center align-items-start sponsor-list-footer">
              <div className="col-f-content f-content-img footer-sponsor">
                {lang == 'en' ? (
                  <h2 className="main-sponsor-text">Main sponsor :</h2>
                ) : (
                  <h2 className="main-sponsor-text">ผู้สนับสนุนหลัก :</h2>
                )}
                <div className="group-sponsor">
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/co-spon-1.png"
                        alt=""
                        width={88}
                        height={52}
                        objectFit="contain"
                      />
                    </a>
                  </Link>
                  {/* <Link href="/">
                    <a className="sponsor-item">
                      <Image src="/assets/images/co-spon-2.png" alt="" 
                      layout="fill"
                      objectFit="contain"
                      objectPosition='center'
                      className="ItemSizeImg2"
                      />
                    </a>
                  </Link> */}
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/co-spon-3.png"
                        alt=""
                        width={98}
                        height={52}
                        objectFit="contain"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/co-spon-4.png"
                        alt=""
                        width={108}
                        height={52}
                        objectFit="contain"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/co-spon-5.png"
                        alt=""
                        width={108}
                        height={52}
                        objectFit="contain"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/co-spon-6.png"
                        alt=""
                        width={108}
                        height={52}
                        objectFit="contain"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/co-spon-7.png"
                        alt=""
                        width={108}
                        height={52}
                        objectFit="contain"
                      />
                    </a>
                  </Link>
                </div>
              </div>
            </div>
            <div className="row justify-content-center align-items-start sponsor-list-footer">
              <div className="col-f-content f-content-img footer-sponsor">
                {lang == 'en' ? (
                  <h2 className="main-sponsor-text">Alliance :</h2>
                ) : (
                  <h2 className="main-sponsor-text">พันธมิตร :</h2>
                )}
                <div className="group-sponsor">
                  {/* <Link href="/">
                    <a className="sponsor-item">
                      <Image src="/assets/images/alliance1.png" alt="" 
                      layout="fill"
                      objectFit="cover"
                      objectPosition='center'
                      className="ItemSizeImg2"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image src="/assets/images/alliance2.png" alt="" 
                      layout="fill"
                      objectFit="cover"
                      objectPosition='center'
                      className="ItemSizeImg5"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image src="/assets/images/alliance3.png" alt="" 
                      layout="fill"
                      objectFit="cover"
                      objectPosition='center'
                      className="ItemSizeImg4"
                      />
                    </a>
                  </Link> */}
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/alliance4.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg3"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/alliance5.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg3"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item">
                      <Image
                        src="/assets/images/alliance6.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg3"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item sponsor-item-width-custom">
                      <Image
                        src="/assets/images/alliance7-width.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg3"
                      />
                    </a>
                  </Link>
                  <Link href="/">
                    <a className="sponsor-item sponsor-item-width-custom">
                      <Image
                        src="/assets/images/alliance8-width.png"
                        alt=""
                        layout="fill"
                        objectFit="contain"
                        objectPosition="center"
                        className="ItemSizeImg3"
                      />
                    </a>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* {!accCookie ? (
        <div className="block-cookie active">
          <div className="container custom-container cookie">
            <div className="row">
              <button
                className="btn btn-cookie-close"
                onClick={() => {
                  setAccCookie(true);
                }}
              >
                <i className="icon-ic-close-cart"></i>
              </button>
              <div className="col-12 col-lg-10 offset-lg-1">
                <span className="line"></span>
                <div className="in-cookie-content">
                  <p>
                    เว็บไซต์นี้มีการใช้คุ้กกี้ในการเก็บข้อมูล
                    เพื่อให้ท่านได้รับประสบการณ์และความพึงพอใจในการใช้งานที่ดีขึ้น
                    โปรดศึกษารายละเอียดเพิ่มเติมเกี่ยวกับ
                    <div className="group-link">
                      <div className="i-link">
                        <Link href={"/page/privacy"}>
                          นโยบายความเป็นส่วนตัว
                        </Link>{" "}
                      </div>
                      <div className="i-link">
                        <Link href={"/page/term"}>
                          เงื่อนไขและข้อกำหนดการใช้เว็บไซต์
                        </Link>{" "}
                      </div>
                      <div className="i-link">
                        <Link href={"/page/consent"}> การขอความยินยอม</Link>
                      </div>
                    </div>
                  </p>
                  <div className="btn_accept">
                    <span
                      className="accept"
                      onClick={() => {
                        acceptCookie();
                      }}
                    >
                      {" "}
                      ยอมรับและดำเนินการต่อ{" "}
                      <i className="icon-ic-right-bd-arrow"></i>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : null} */}

      {/* <div className="all-snow">
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
  <div className="snow">❅</div>
</div> */}
    </div>
  )
}

export default Footer

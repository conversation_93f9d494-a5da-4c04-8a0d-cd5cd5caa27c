import React from 'react';
import { useRouter } from 'next/router';
import Swal from 'sweetalert2';

/**
 * Component สำหรับการชำระเงินผ่านระบบ 2C2P
 * @param {Object} props - Properties ที่ส่งเข้ามา
 * @param {Function} props.appContext - Context ของแอปพลิเคชัน
 * @param {Object} props.cartData - ข้อมูลตะกร้าสินค้า
 * @param {Function} props.setCartData - Function สำหรับ update ข้อมูลตะกร้าสินค้า
 * @param {Function} props.setValue - Function จาก useForm สำหรับกำหนดค่าในฟอร์ม
 * @param {Function} props.getValues - Function จาก useForm สำหรับดึงค่าจากฟอร์ม
 * @param {Number} props.taxType - ประเภทภาษี
 * @param {Function} props.setDataSendComplete - Function สำหรับตั้งค่าสถานะการส่งข้อมูลเสร็จสมบูรณ์
 * @param {String} props.envNextPublicApi - URL ของ API จาก environment variables
 */
const Payment2c2p = ({
  appContext,
  cartData,
  setCartData,
  setValue,
  getValues,
  taxType,
  setDataSendComplete,
  envNextPublicApi
}) => {
  const router = useRouter();

  /**
   * ส่งข้อมูลการชำระเงินไปยังระบบ 2C2P
   * ฟังก์ชันนี้ทำหน้าที่:
   * 1. ดึงข้อมูลตะกร้าสินค้าและคำนวณภาษี
   * 2. สร้างคำสั่งซื้อในระบบ
   * 3. สร้าง JWT token สำหรับการชำระเงิน
   * 4. ดำเนินการตามประเภทการชำระเงิน (QR Code หรือ Credit/Debit Card)
   */
  const submit2c2p = async () => {
    try {
      appContext.setLoading(true);

      // 1. ดึงข้อมูลตะกร้าสินค้าและคำนวณภาษี
      const formData = new URLSearchParams();
      formData.append("tax_type", taxType);

      // ส่งคำขอข้อมูล checkout โดยใช้ appContext.sendApi
      appContext.sendApi(
        envNextPublicApi + "/api/mdcu/getCheckout",
        formData,
        (jsonData) => {
          // ตรวจสอบสถานะและข้อมูลตะกร้า
          if (jsonData["status"] !== "success") {
            appContext.setLoading(false);
            location.href = "/";
            return;
          }

          // บันทึกข้อมูลตะกร้า
          setCartData(jsonData);
          setValue("cart_data", JSON.stringify(jsonData));

          // เพิ่มข้อมูล UTM source (ถ้ามี)
          if (!appContext.isNull(appContext.getLocal("utm_source"))) {
            setValue("utm_source", appContext.getLocal("utm_source"));
          }

          // 2. สร้างคำสั่งซื้อ
          const formValues = getValues();
      
          // ใช้ appContext.loadApi เพื่อสร้างคำสั่งซื้อ
          appContext.loadApi(
            envNextPublicApi + "/api/mdcu/addOrder",
            formValues,
            (orderData) => {

              if (orderData["status"] === "success") {
                // ลบข้อมูล UTM source หลังจากสร้างคำสั่งซื้อสำเร็จ
                appContext.removeLocal("utm_source");
                appContext.removeLocal("utm_time");
                setDataSendComplete(true);

                // 3. สร้าง payload สำหรับขอ JWT token
                const jwtRequestData = new URLSearchParams();

                // ส่งเพียง order_id เท่านั้น เพราะ backend จะดึงข้อมูลอื่นๆ เอง
                jwtRequestData.append("order_id", orderData["order_id_encrypt"]);

                // เพิ่ม payment_type ตามที่ผู้ใช้เลือก (CC หรือ QRPROM)
                const selectedPayment = parseInt(formValues["payment"]);
                
                // ตรวจสอบว่าค่า selectedPayment มีค่าจริงหรือไม่
                handlePaymentTypeSelection(selectedPayment, jwtRequestData);

                // สร้าง JSON data ตามที่ 2C2P ต้องการ
                const requestJSON = {
                  merchantID: process.env.NEXT_PUBLIC_2C2P_MERCHANT_ID, // ใช้ค่าจาก environment variables
                  invoiceNo: orderData.order_id_encrypt,
                  description: "MDCU Purchase",
                  amount: parseFloat(jsonData.total_price).toFixed(2), // ต้องส่งเป็นทศนิยม 2 ตำแหน่ง
                  currencyCode: "THB"
                };

                // เพิ่ม paymentChannel สำหรับ QR Code
                if (selectedPayment === 1) {
                  requestJSON.paymentChannel = ["QR"];
                }

                // แปลง JSON เป็น string และส่งไปยัง backend
                jwtRequestData.append("request_data", JSON.stringify(requestJSON));

                // ส่งข้อมูลไปยัง API เพื่อสร้าง JWT token
                processPaymentWithJwt(jwtRequestData, orderData, selectedPayment, jsonData);
              } else {
                // กรณีไม่สามารถสร้างคำสั่งซื้อได้
                handleOrderFailure(orderData);
              }
            }
          );
        }
      );
    } catch (error) {
      handlePaymentError(error);
    }
  };

  /**
   * จัดการกับการเลือกประเภทการชำระเงิน
   * @param {Number} selectedPayment - ประเภทการชำระเงินที่เลือก
   * @param {URLSearchParams} jwtRequestData - ข้อมูลที่จะส่งไปขอ JWT token
   */
  const handlePaymentTypeSelection = (selectedPayment, jwtRequestData) => {
    if (!selectedPayment) {
      // พยายามตรวจสอบจาก radio button ที่ถูกเลือกโดยตรง
      const radioButtons = document.querySelectorAll('input[name="payment"]');
      let selectedRadioValue = null;
      radioButtons.forEach((radio) => {
        if (radio.checked) {
          selectedRadioValue = parseInt(radio.value);
        }
      });

      if (selectedRadioValue) {
        // ใช้ค่าจาก radio button แทน
        if (selectedRadioValue === 2) {
          jwtRequestData.append("payment_type", "CC");
        } else if (selectedRadioValue === 1) {
          jwtRequestData.append("payment_type", "QRCODE"); // ใช้ QRCODE ตาม API ของคุณ
          jwtRequestData.append("paymentChannel", JSON.stringify(["QR"])); // เพิ่ม paymentChannel ตามเอกสาร 2C2P
          jwtRequestData.append("qrType", "PROMPTPAY");
        }
      } else {
        // ถ้าไม่มีการเลือกเลย เช็คจากอีเลเมนท์ที่มีคลาส .selected
        const selectedContainer = document.querySelector(".item-choose-payment.selected");
        if (selectedContainer) {
          const index = Array.from(document.querySelectorAll(".item-choose-payment")).indexOf(selectedContainer);
          const paymentValue = index + 1; // 1-based index

          if (paymentValue === 2) {
            jwtRequestData.append("payment_type", "CC");
          } else if (paymentValue === 1) {
            jwtRequestData.append("payment_type", "QRCODE"); // เปลี่ยนจาก QRPROM เป็น QRCODE
            jwtRequestData.append("qrType", "PROMPTPAY");
          }
        } else {
          // ถ้าไม่มีการเลือกเลย ใช้ default
          jwtRequestData.append("payment_type", "QR"); // เปลี่ยนจาก QRPROM เป็น QRCODE
          jwtRequestData.append("qrType", "PROMPTPAY");
        }
      }
    } else {
      // ใช้ค่าจาก form value
      if (selectedPayment === 2) {
        jwtRequestData.append("payment_type", "CC");
      } else if (selectedPayment === 1) {
        jwtRequestData.append("payment_type", "QR"); // เปลี่ยนจาก QRPROM เป็น QRCODE
        jwtRequestData.append("qrType", "PROMPTPAY");
      }
    }
  };

  /**
   * ดำเนินการชำระเงินโดยใช้ JWT token
   * @param {URLSearchParams} jwtRequestData - ข้อมูลที่จะส่งไปขอ JWT token
   * @param {Object} orderData - ข้อมูลคำสั่งซื้อ
   * @param {Number} selectedPayment - ประเภทการชำระเงินที่เลือก
   * @param {Object} jsonData - ข้อมูลตะกร้าสินค้า
   */
  const processPaymentWithJwt = (jwtRequestData, orderData, selectedPayment, jsonData) => {
    appContext.sendApi(
      envNextPublicApi + "/api/2c2p/jwt",
      jwtRequestData,
      (jwtData) => {
        
        if (jwtData && jwtData.status === "success") {
          // ดำเนินการต่อเมื่อได้รับ JWT token สำเร็จ
          const tokenData = new URLSearchParams();
          tokenData.append("payload", jwtData.payload);
          tokenData.append("payment_order_id", orderData.order_id_encrypt);
          
          // เรียก API ต่อไปเพื่อสร้าง payment token
          appContext.sendApi(
            envNextPublicApi + "/api/2c2p/token",
            tokenData,
            (tokenResponse) => {
              appContext.setLoading(false);
              
              // ตรวจสอบการตอบกลับและดำเนินการตามประเภทการชำระเงิน
              if (tokenResponse && tokenResponse.status === "success") {
                
                // ตรวจสอบประเภทการชำระเงิน
                if (selectedPayment === 1) { // QR Code
                  handleQrCodePayment(tokenResponse, orderData);
                } else { // Credit/Debit Card
                  handleCreditCardPayment(tokenResponse);
                }
              } else {
                // แสดงข้อความแจ้งเตือนกรณีเกิดข้อผิดพลาด
                Swal.fire({
                  text: tokenResponse?.message || "เกิดข้อผิดพลาดในการเชื่อมต่อกับระบบชำระเงิน กรุณาลองใหม่อีกครั้ง",
                  icon: "error",
                  confirmButtonText: "ปิด",
                  confirmButtonColor: "#648d2f",
                });
              }
            }
          );
        } else {
          appContext.setLoading(false);
          Swal.fire({
            text: jwtData?.message || "เกิดข้อผิดพลาดในการเชื่อมต่อกับระบบชำระเงิน กรุณาลองใหม่อีกครั้ง",
            icon: "error",
            confirmButtonText: "ปิด",
            confirmButtonColor: "#648d2f",
          });
        }
      }
    );
  };

  /**
   * จัดการการชำระเงินด้วย QR Code
   * @param {Object} tokenResponse - ข้อมูลการตอบกลับจาก API payment token
   * @param {Object} orderData - ข้อมูลคำสั่งซื้อ
   */
  const handleQrCodePayment = (tokenResponse, orderData) => {
    // Redirect ไปยังหน้าชำระเงิน 2C2P เช่นเดียวกับ Credit Card
    if (tokenResponse.decode && tokenResponse.decode.webPaymentUrl) {
      window.location.href = tokenResponse.decode.webPaymentUrl;
    }
    // ถ้าไม่มี webPaymentUrl แต่มี paymentOptionUrl ให้ redirect ไปยังหน้า QR Code โดยตรง
    else if (tokenResponse.paymentOptionUrl) {
      const separator = tokenResponse.paymentOptionUrl.includes('?') ? '&' : '?';
      window.location.href = `${tokenResponse.paymentOptionUrl}${separator}paymentChannel=QR`;
    }
    else {
      // ถ้าไม่มีทั้ง webPaymentUrl และ paymentOptionUrl
      Swal.fire({
        text: "ไม่พบข้อมูลสำหรับการชำระเงิน กรุณาลองใหม่อีกครั้ง",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f",
      });
    }
  };

  /**
   * จัดการการชำระเงินด้วยบัตรเครดิต/เดบิต
   * @param {Object} tokenResponse - ข้อมูลการตอบกลับจาก API payment token
   */
  const handleCreditCardPayment = (tokenResponse) => {
    // Redirect ไปยังหน้าชำระเงิน - ตรวจสอบ paymentOptionUrl ก่อน
    if (tokenResponse.paymentOptionUrl) {
      window.location.href = tokenResponse.decode.webPaymentUrl;
    }
    // ถ้าไม่มี paymentOptionUrl ใช้ webPaymentUrl แทน
    else if (tokenResponse.decode && tokenResponse.decode.webPaymentUrl) {
      window.location.href = tokenResponse.decode.webPaymentUrl;
    }
    else {
      // ถ้าไม่มีข้อมูลสำหรับ redirect
      Swal.fire({
        text: "ไม่พบ URL สำหรับการชำระเงิน กรุณาลองใหม่อีกครั้ง",
        icon: "error",
        confirmButtonText: "ปิด",
        confirmButtonColor: "#648d2f",
      });
    }
  };

  /**
   * จัดการกรณีการสร้างคำสั่งซื้อล้มเหลว
   * @param {Object} orderData - ข้อมูลการตอบกลับจาก API addOrder
   */
  const handleOrderFailure = (orderData) => {
    appContext.setLoading(false);
    Swal.fire({
      text: orderData["message"] || "ไม่สามารถสร้างคำสั่งซื้อได้ กรุณาลองใหม่อีกครั้ง",
      icon: "error",
      confirmButtonText: "ปิด",
      confirmButtonColor: "#648d2f",
    });
  };

  /**
   * จัดการกรณีเกิดข้อผิดพลาดในกระบวนการชำระเงิน
   * @param {Error} error - ข้อผิดพลาดที่เกิด
   */
  const handlePaymentError = (error) => {
    console.error("Error in payment process:", error);
    appContext.setLoading(false);
    Swal.fire({
      text: "เกิดข้อผิดพลาดในการชำระเงิน กรุณาลองใหม่อีกครั้ง",
      icon: "error",
      confirmButtonText: "ปิด",
      confirmButtonColor: "#648d2f",
    });
  };

  // ส่งคืนฟังก์ชันที่จะใช้ภายนอก component
  return { submit2c2p };
};

export default Payment2c2p;
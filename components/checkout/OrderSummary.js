import React from 'react';
import Image from 'next/image';
import NumberFormat from 'react-number-format';
import CourseDetailCode from '../../themes/components/courseDetailCode';

/**
 * Component สำหรับแสดงสรุปรายการสั่งซื้อ
 * @param {Object} props - Properties ที่ส่งเข้ามา
 * @param {Object} props.cartData - ข้อมูลตะกร้าสินค้า
 * @param {Object} props.appContext - Context ของแอพพลิเคชัน
 * @param {Function} props.addDiscountCode - ฟังก์ชันสำหรับเพิ่มคูปองส่วนลด
 * @param {Boolean} props.dataSendComplete - สถานะการส่งข้อมูลเสร็จสมบูรณ์
 * @param {Boolean} props.leftShow - สถานะการแสดงส่วนซ้าย (ใช้สำหรับเปลี่ยนข้อความปุ่ม)
 * @param {Function} props.onSubmit - ฟังก์ชันเมื่อกดปุ่มยืนยัน
 */
const OrderSummary = ({ 
  cartData, 
  appContext, 
  addDiscountCode, 
  dataSendComplete, 
  leftShow, 
  onSubmit 
}) => {
  // ถ้าไม่มีข้อมูลตะกร้า ไม่ต้องแสดงอะไร
  if (!cartData) return null;

  return (
    <div className="right">
      <h2 className="form-title m-bot">สรุปรายการสั่งซื้อ</h2>
      <div>
        {/* แสดงรายการสินค้าในตะกร้า */}
        {cartData["data"].map((val, key) => (
          <div key={key}>
            <div className="item-purchase-report">
              {/* ส่วนแสดงรูปภาพสินค้า */}
              <div className="purchase-report-img">
                <div className="purchase-thumb">
                  <div className="imgResponsiveCustom">
                    {val &&
                    val.image &&
                    val.image != null &&
                    val.image != "" &&
                    val.image != "null" ? (
                      <Image
                        src={val.image}
                        layout="fill"
                        objectFit="contain"
                        sizes="100%"
                        alt=""
                      />
                    ) : null}
                  </div>
                </div>
              </div>
              
              {/* ส่วนแสดงข้อมูลสินค้า */}
              <div className="purchase-report-content">
                <h3>{val.title}</h3>
                <p
                  dangerouslySetInnerHTML={{
                    __html: appContext.truncate(val.description, 80),
                  }}
                ></p>
              </div>
              
              {/* ส่วนแสดงราคาสินค้า */}
              <NumberFormat
                decimalScale={2}
                value={val.price}
                displayType={"text"}
                thousandSeparator={true}
                renderText={(value, props) => (
                  <div className="purchase-report-price" {...props}>
                    {value} บาท
                  </div>
                )}
              />
            </div>
            
            {/* ส่วนแสดงรายละเอียดราคา */}
            <div className="item-purchase-report">
              {/* ส่วนแสดงส่วนลด (ถ้ามี) */}
              {val.discount_code != "" &&
              val.discount_code != null ? (
                <div className="item-report-price">
                  <div className="purchase-report-name">
                    <h4>
                      สวนลด{" "}
                      <span>
                        <i className="icon-ic-tikket"></i>{" "}
                        {val.discount_code}
                      </span>
                    </h4>
                  </div>
                  <NumberFormat
                    decimalScale={2}
                    value={val.discount_price}
                    displayType={"text"}
                    thousandSeparator={true}
                    renderText={(value, props) => (
                      <div
                        className="purchase-report-price"
                        {...props}
                      >
                        -{value} บาท
                      </div>
                    )}
                  />
                </div>
              ) : null}
              
              {/* ส่วนแสดงราคาก่อน VAT และยอด VAT */}
              <div className="width-100">
                <div className="item-report-price">
                  <div className="purchase-report-name">
                    <h4>ราคาก่อน VAT</h4>
                  </div>
                  <NumberFormat
                    decimalScale={2}
                    value={val.pre_vat}
                    displayType={"text"}
                    thousandSeparator={true}
                    renderText={(value, props) => (
                      <div
                        className="purchase-report-price"
                        {...props}
                      >
                        {value} บาท
                      </div>
                    )}
                  />
                </div>
                <div className="item-report-price">
                  <div className="purchase-report-name">
                    <h4>ยอด VAT</h4>
                  </div>
                  <NumberFormat
                    decimalScale={2}
                    value={val.vat}
                    displayType={"text"}
                    thousandSeparator={true}
                    renderText={(value, props) => (
                      <div
                        className="purchase-report-price"
                        {...props}
                      >
                        {value} บาท
                      </div>
                    )}
                  />
                </div>
              </div>
              
              {/* ส่วนแสดงหักภาษี ณ ที่จ่าย (ถ้ามี) */}
              {cartData["tax_type"] == 1 ? (
                <div className="item-report-price">
                  <div className="purchase-report-name">
                    <h4>หักภาษี ณ ที่จ่าย 3%</h4>
                  </div>
                  <NumberFormat
                    decimalScale={2}
                    value={val.tax}
                    displayType={"text"}
                    thousandSeparator={true}
                    renderText={(value, props) => (
                      <div
                        className="purchase-report-price"
                        {...props}
                      >
                        {value} บาท
                      </div>
                    )}
                  />
                </div>
              ) : null}
              
              {/* ส่วนแสดงยอดรวมย่อย */}
              <div className="item-report-price">
                <div className="purchase-report-name">
                  <h3>ยอดรวมย่อย</h3>
                </div>
                <NumberFormat
                  decimalScale={2}
                  value={val.total}
                  displayType={"text"}
                  thousandSeparator={true}
                  renderText={(value, props) => (
                    <div className="purchase-report-price" {...props}>
                      {value} บาท
                    </div>
                  )}
                />
              </div>
            </div>
          </div>
        ))}
        
        {/* ส่วนแสดงสรุปรวม */}
        <div className="item-purchase-report">
          {/* ส่วนแสดงส่วนลดคูปองเงินสด (ถ้ามี) */}
          {cartData["discount_web"] > 0 ? (
            <div className="item-report-price">
              <div className="purchase-report-name">
                <h4>
                  ส่วนลดคูปองเงินสด{" "}
                  <span>
                    <i className="icon-ic-tikket"></i>{" "}
                    {cartData["code_web"]}
                  </span>
                </h4>
              </div>
              <NumberFormat
                decimalScale={2}
                value={cartData["discount_web"]}
                displayType={"text"}
                thousandSeparator={true}
                renderText={(value, props) => (
                  <div className="purchase-report-price" {...props}>
                    - {value} บาท
                  </div>
                )}
              />
            </div>
          ) : null}
          
          {/* ส่วนแสดงยอดรวมทั้งสิ้น */}
          <div className="item-report-price">
            <div className="purchase-report-name">
              <h3>ยอดรวม</h3>
              {cartData["tax_type"] == 1 ? (
                <NumberFormat
                  decimalScale={2}
                  value={cartData.total_tax}
                  displayType={"text"}
                  thousandSeparator={true}
                  renderText={(value, props) => (
                    <p {...props}>รวมภาษี ฿{value}</p>
                  )}
                />
              ) : null}
            </div>
            <NumberFormat
              decimalScale={2}
              value={cartData.total_price}
              displayType={"text"}
              thousandSeparator={true}
              renderText={(value, props) => (
                <div className="purchase-report-price" {...props}>
                  <b>{value} บาท</b>
                </div>
              )}
            />
          </div>
        </div>
        
        {/* ส่วนแสดงช่องกรอกคูปองเงินสด */}
        <div className="coupon-checkout">
          <p className="space-between-content-top title-button-group">
            คูปองเงินสด
          </p>
          <CourseDetailCode
            callback={addDiscountCode}
          ></CourseDetailCode>
        </div>
        
        {/* ส่วนแสดงปุ่มยืนยัน */}
        {!dataSendComplete ? (
          <div className="item-purchase-report border0">
            <div className="purchase-report-action">
              <button
                onClick={onSubmit}
                className="btn-default remove_submit_btn"
              >
                {leftShow ? (
                  <span>ดำเนินการชำระเงิน</span>
                ) : (
                  <span>ยืนยัน</span>
                )}
              </button>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default OrderSummary;
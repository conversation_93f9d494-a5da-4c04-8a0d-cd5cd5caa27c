import React, { useEffect } from 'react';
import Image from 'next/image';

/**
 * Component แสดงตัวเลือกวิธีการชำระเงิน
 * @param {Object} props - Properties ที่ส่งเข้ามา
 * @param {Function} props.setPayment - ฟังก์ชันสำหรับตั้งค่าวิธีการชำระเงิน
 * @param {Number} props.selectedPayment - วิธีการชำระเงินที่เลือกปัจจุบัน
 * @param {Function} props.setValue - ฟังก์ชันจาก useForm สำหรับกำหนดค่าในฟอร์ม
 * @param {Function} props.getValues - ฟังก์ชันจาก useForm สำหรับดึงค่าจากฟอร์ม
 */
const PaymentOptions = ({ setPayment, selectedPayment, setValue, getValues }) => {
  // ใช้ useEffect เพื่อตรวจสอบการเลือกตั้งแต่โหลด component
  useEffect(() => {
    if (selectedPayment) {
      updateVisualFeedback(selectedPayment);
    }
  }, [selectedPayment]);

  /**
   * อัปเดตสถานะการเลือกและสร้าง visual feedback
   * @param {Number} paymentValue - ค่าวิธีการชำระเงินที่เลือก
   */
  const updateVisualFeedback = (paymentValue) => {
    // ปรับ radio buttons
    const radioButtons = document.querySelectorAll('input[name="payment"]');
    radioButtons.forEach((radio, index) => {
      if (index === parseInt(paymentValue) - 1) {
        radio.checked = true;
      } else {
        radio.checked = false;
      }
    });

    // ปรับ CSS classes สำหรับ visual feedback
    const paymentContainers = document.querySelectorAll(".item-choose-payment");
    paymentContainers.forEach((container, index) => {
      if (index === parseInt(paymentValue) - 1) {
        container.classList.add("selected");
      } else {
        container.classList.remove("selected");
      }
    });
  };

  /**
   * จัดการการเลือกวิธีการชำระเงิน
   * @param {Number} paymentValue - ค่าวิธีการชำระเงินที่เลือก
   */
  const handlePaymentSelection = (paymentValue) => {
    // เรียกใช้ฟังก์ชัน setPayment ที่ส่งมาจาก parent component
    setPayment(paymentValue);
    
    // อัปเดตค่าในฟอร์ม
    setValue("payment", parseInt(paymentValue));
    
    // อัปเดต visual feedback
    updateVisualFeedback(paymentValue);
  };

  return (
    <div className="payment-options-container">
      <h2 className="form-title m-top">การชำระเงิน</h2>
      <h3 className="form-description m-bot">
        ธุรกรรมทั้งหมดมีความปลอดภัยและได้รับการเข้ารหัส
      </h3>
      
      <div className="block-payment-list">
        {/* QR Code Payment Option */}
        <div className="item-payment">
          <div
            className={`item-choose-payment ${selectedPayment === 1 ? 'selected' : ''}`}
            onClick={() => handlePaymentSelection(1)}
          >
            <div className="fm-check">
              <input
                type="radio"
                id="payment_qr"
                name="payment"
                value="1"
                checked={selectedPayment === 1}
                onChange={() => {}} // เพิ่ม empty handler เพื่อป้องกัน React warning
              />
              <div className="text">
                <div className="i_remark">
                  <i className="icon-ic-circle" />
                </div>
                <div className="content">
                  <p>QR Code</p>
                  <div className="imgResponsiveQr">
                    <Image
                      src={"/assets/images/qr.png"}
                      layout="fill"
                      objectFit="contain"
                      sizes="100%"
                      alt="QR Code Payment"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Credit/Debit Card Payment Option */}
        <div className="item-payment">
          <div
            className={`item-choose-payment ${selectedPayment === 2 ? 'selected' : ''}`}
            onClick={() => handlePaymentSelection(2)}
          >
            <div className="fm-check">
              <input
                type="radio"
                id="payment_cc"
                name="payment"
                value="2"
                checked={selectedPayment === 2}
                onChange={() => {}} // เพิ่ม empty handler เพื่อป้องกัน React warning
              />
              <div className="text">
                <div className="i_remark">
                  <i className="icon-ic-circle" />
                </div>
                <div className="content">
                  <p>Credit/Debit</p>
                  <div className="imgResponsiveVisa">
                    <Image
                      src={"/assets/images/visa.jpg"}
                      layout="fill"
                      objectFit="contain"
                      sizes="100%"
                      alt="Credit Card Payment"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* ถ้าต้องการเพิ่มตัวเลือกการชำระเงินอื่นๆ สามารถเพิ่มที่นี่ได้ */}
      </div>
    </div>
  );
};

export default PaymentOptions;
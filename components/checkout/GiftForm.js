import React from 'react';
import { Input, TextArea } from 'semantic-ui-react';

/**
 * Component สำหรับฟอร์มข้อมูลการส่งเป็นของขวัญ
 * @param {Object} props - Properties ที่ส่งเข้ามา
 * @param {Number} props.giftType - ประเภทของขวัญ (1: ส่งเป็นของขวัญ, 2: ไม่ส่ง)
 * @param {Function} props.setGiftType - ฟังก์ชันสำหรับเปลี่ยนประเภทของขวัญ
 * @param {Function} props.register - ฟังก์ชันจาก react-hook-form สำหรับลงทะเบียนฟิลด์
 * @param {Function} props.getValues - ฟังก์ชันจาก react-hook-form สำหรับดึงค่า
 * @param {Function} props.setValue - ฟังก์ชันจาก react-hook-form สำหรับตั้งค่า
 * @param {Object} props.appContext - Context ของแอปพลิเคชัน
 */
const GiftForm = ({ 
  giftType, 
  setGiftType, 
  register, 
  getValues, 
  setValue, 
  appContext 
}) => {
  /**
   * สลับสถานะการส่งเป็นของขวัญ
   */
  const toggleGiftOption = () => {
    if (giftType === 1) {
      setGiftType(2);
      setValue("gift", 2);
    } else {
      setGiftType(1);
      setValue("gift", 1);
    }
  };

  return (
    <>
      <h2 className="form-title m-top">ซื้อเป็นของขวัญ</h2>
      <div className="item-choose row">
        <div
          className="fm-check"
          onClick={toggleGiftOption}
        >
          <input
            id="checkbox_gift"
            type="checkbox"
            name="checkbox_gift"
            checked={giftType === 1}
            readOnly
          />
          <div className="text">
            <div className="i_remark">
              <i className="icon-ic-circle" />
            </div>
            <p>ส่งของขวัญ</p>
          </div>
        </div>
      </div>
      
      {giftType === 1 && (
        <>
          <div className="col-fm-register col-12 col-md-6 mg-top-5">
            <div className="item-fm">
              <Input
                type="email"
                className="fm-control"
                placeholder="อีเมล์ผู้รับ"
              >
                <input
                  {...register("gift_email")}
                  maxLength={100}
                  data-type="email"
                  onInput={appContext.diFormPattern}
                />
              </Input>
            </div>
          </div>
          <div className="col-fm-register col-12 col-md-12 mg-top-5">
            <div className="item-fm">
              <TextArea
                placeholder="ข้อความถึงผู้รับ"
                defaultValue={getValues("gift_message")}
                data-type="textaddress"
                onInput={appContext.diFormPattern}
                onChange={(event) =>
                  setValue("gift_message", event.target.value)
                }
                className={"fm-control"}
              />
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default GiftForm;
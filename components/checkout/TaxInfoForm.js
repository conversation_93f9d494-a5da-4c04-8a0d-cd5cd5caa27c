import React from 'react';
import GroupRadio from "../../themes/components/groupRadio";
import AddressContact from "../../themes/components/addressContact";
import { Input, TextArea, Select } from 'semantic-ui-react';

/**
 * Component สำหรับแสดงฟอร์มข้อมูลใบกำกับภาษี
 * @param {Object} props - Properties ที่ส่งเข้ามา
 * @param {Object} props.user - ข้อมูลผู้ใช้
 * @param {Number} props.getReceipt - สถานะต้องการใบกำกับภาษีหรือไม่ (1: ต้องการ, 2: ไม่ต้องการ)
 * @param {Function} props.setReceipt - ฟังก์ชันตั้งค่าสถานะต้องการใบกำกับภาษี
 * @param {Number} props.formType - ประเภทฟอร์ม (1: บุคคลธรรมดา, 2: นิติบุคคล)
 * @param {Function} props.setReceiptType - ฟังก์ชันตั้งค่าประเภทฟอร์ม
 * @param {Number} props.addressType - ประเภทที่อยู่ (1: ที่อยู่ที่ใช้ลงทะเบียน, 2: กรอกที่อยู่)
 * @param {Number} props.companyType - ประเภทบริษัท (1: สำนักงานใหญ่, 2: สาขา)
 * @param {Function} props.setCompany - ฟังก์ชันตั้งค่าประเภทบริษัท
 * @param {Function} props.setTax - ฟังก์ชันตั้งค่าหัก ณ ที่จ่าย
 * @param {Number} props.taxType - ประเภทภาษี (1: ต้องการหัก ณ ที่จ่าย, 2: ไม่ต้องการ)
 * @param {Array} props.provinceOptions - ตัวเลือกจังหวัด
 * @param {Array} props.districtOptions - ตัวเลือกอำเภอ
 * @param {Array} props.subdistrictOptions - ตัวเลือกตำบล
 * @param {Function} props.setReloadDistrict - ฟังก์ชันโหลดข้อมูลอำเภอใหม่
 * @param {Function} props.setReloadSubDistrict - ฟังก์ชันโหลดข้อมูลตำบลใหม่
 * @param {Function} props.register - ฟังก์ชันจาก react-hook-form สำหรับลงทะเบียนฟิลด์
 * @param {Function} props.getValues - ฟังก์ชันจาก react-hook-form สำหรับดึงค่า
 * @param {Function} props.setValue - ฟังก์ชันจาก react-hook-form สำหรับตั้งค่า
 * @param {Object} props.appContext - Context ของแอปพลิเคชัน
 */
const TaxInfoForm = ({
  user,
  getReceipt,
  setReceipt,
  formType,
  setReceiptType,
  addressType,
  companyType,
  setCompany,
  setTax,
  taxType,
  provinceOptions,
  districtOptions,
  subdistrictOptions,
  setReloadDistrict,
  setReloadSubDistrict,
  register,
  getValues,
  setValue,
  appContext,
  setAddress
}) => {
  return (
    <>
      <GroupRadio
        callback={setReceipt}
        title="ข้อมูลใบเสร็จ"
        name="receipt"
        value_1="ต้องการใบเสร็จ"
        value_2="ไม่ต้องการ"
      ></GroupRadio>
      
      {getReceipt == 1 && (
        <div>
          <GroupRadio
            callback={setReceiptType}
            title="ออกในนาม"
            name="receipt_type"
            value_1="บุคคลธรรมดา"
            value_2="นิติบุคคล"
          ></GroupRadio>
          {/* <GroupRadio
            callback={setAddress}
            title="ที่อยู่"
            name="address_type"
            value_1="ที่อยู่ที่ใช้ลงทะเบียน"
            value_2="กรอกที่อยู่"
          ></GroupRadio> */}
        </div>
      )}
      
      {getReceipt == 1 && (
        formType == 1 ? (
          addressType == 1 ? (
            <AddressContact data={user}></AddressContact>
          ) : (
            <div className="row-fm-register-checkout row">
              <div className="col-fm-register col-12">
                <div className="item-fm">
                  <p className="fm-title checkout">บุคคลธรรมดา</p>
                </div>
              </div>
              <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                <div className="item-fm">
                  <Input
                    type="text"
                    className="fm-control"
                    placeholder="ชื่อ"
                  >
                    <input
                      {...register("name")}
                      maxLength={100}
                      data-type="thaionly"
                      onInput={appContext.diFormPattern}
                    />
                  </Input>
                </div>
              </div>
              <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                <div className="item-fm">
                  <Input
                    type="text"
                    className="fm-control"
                    placeholder="นามสกุล"
                  >
                    <input
                      {...register("lastname")}
                      maxLength={100}
                      data-type="thaionly"
                      onInput={appContext.diFormPattern}
                    />
                  </Input>
                </div>
              </div>
              <div className="col-fm-register col-12 col-md-12 checkout-register-mgb">
                <div className="item-fm">
                  <Input
                    type="text"
                    className="fm-control"
                    placeholder="เลขประจำตัวประชาชน"
                  >
                    <input
                      {...register("iden_no")}
                      maxLength={13}
                      data-type="number"
                      onInput={appContext.diFormPattern}
                    />
                  </Input>
                </div>
              </div>
              <div className="col-fm-register col-12 checkout-register-mgb">
                <div className="item-fm">
                  <TextArea
                    placeholder="ที่อยู่"
                    className="fm-control"
                    defaultValue={getValues("address")}
                    data-type="textaddressthai"
                    onInput={appContext.diFormPattern}
                    onChange={(event) =>
                      setValue("address", event.target.value)
                    }
                  />
                </div>
              </div>
              <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                <div className="item-fm">
                  <Select
                    className="fm-control"
                    placeholder="กรุณาเลือกจังหวัด"
                    options={provinceOptions}
                    defaultValue={user.province}
                    onChange={(event, data) => {
                      setReloadDistrict(true);
                      setValue("province", data.value);
                      setValue(
                        "province_name",
                        event.target.innerText
                      );
                    }}
                  />
                </div>
              </div>
              <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                <div className="item-fm">
                  <Select
                    className="fm-control"
                    placeholder="กรุณาเลือกเขต/อำเภอ"
                    options={districtOptions}
                    defaultValue={user.district}
                    onChange={(event, data) => {
                      setReloadSubDistrict(true);
                      setValue("district", data.value);
                      setValue(
                        "district_name",
                        event.target.innerText
                      );
                    }}
                  />
                </div>
              </div>
              <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                <div className="item-fm">
                  <Select
                    className="fm-control"
                    placeholder="กรุณาเลือกแขวง/ตำบล"
                    options={subdistrictOptions}
                    defaultValue={user.subdistrict}
                    onChange={(event, data) => {
                      setValue("subdistrict", data.value);
                      setValue(
                        "subdistrict_name",
                        event.target.innerText
                      );
                    }}
                  />
                </div>
              </div>
              <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
                <div className="item-fm">
                  <Input
                    type="tel"
                    className="fm-control"
                    placeholder="รหัสไปรษณีย์"
                  >
                    <input
                      {...register("postcode")}
                      maxLength={5}
                      data-type="number"
                      onInput={appContext.diFormPattern}
                      defaultValue={register["postcode"]}
                    />
                  </Input>
                </div>
              </div>
              <div className="col-fm-register col-12 checkout-register-mgb">
                <div className="item-fm">
                  <TextArea
                    placeholder="โน้ตเพิ่มเติม"
                    className="fm-control"
                    defaultValue={getValues("note")}
                    data-type="textaddress"
                    onInput={appContext.diFormPattern}
                    onChange={(event) =>
                      setValue("note", event.target.value)
                    }
                  />
                </div>
              </div>
            </div>
          )
        ) : addressType == 1 ? (
          <div>
            <AddressContact data={user}></AddressContact>
            {/* <GroupRadio
              callback={setTax}
              title=""
              name="tax"
              value_1="ต้องการหัก ณ ที่จ่าย"
              value_2="ไม่ต้องการ หัก ณ ที่จ่าย"
            ></GroupRadio> */}
          </div>
        ) : (
          <div className="row-fm-register-checkout row">
            <div className="col-fm-register col-12">
              <div className="item-fm">
                <p className="fm-title checkout">นิติบุคคล</p>
              </div>
            </div>
            <div className="col-fm-register col-12 col-md-12 checkout-register-mgb">
              <div className="item-fm">
                <Input
                  type="text"
                  className="fm-control"
                  placeholder="ชื่อบริษัท"
                >
                  <input
                    {...register("name")}
                    maxLength={100}
                    data-type="thaionly"
                    onInput={appContext.diFormPattern}
                  />
                </Input>
              </div>
            </div>
            <div className="col-fm-register col-12 col-md-12 checkout-register-mgb">
              <GroupRadio
                callback={setCompany}
                title=""
                name="company_type"
                value_1="สำนักงานใหญ่"
                value_2="สาขา ระบุ..."
              ></GroupRadio>
            </div>
            {companyType == 2 ? (
              <div className="col-fm-register col-12 col-md-12 checkout-register-mgb">
                <div className="item-fm">
                  <Input
                    type="text"
                    className="fm-control"
                    placeholder="ชื่อสาขา"
                  >
                    <input
                      {...register("company_branch")}
                      maxLength={100}
                      data-type="thaionly"
                      onInput={appContext.diFormPattern}
                    />
                  </Input>
                </div>
              </div>
            ) : null}
            <div className="col-fm-register col-12 col-md-12 checkout-register-mgb">
              <div className="item-fm">
                <Input
                  type="text"
                  className="fm-control"
                  placeholder="เลขประจำตัวผู้เสียภาษีอากร"
                >
                  <input
                    {...register("iden_no")}
                    maxLength={13}
                    data-type="number"
                    onInput={appContext.diFormPattern}
                  />
                </Input>
              </div>
            </div>
            <div className="col-fm-register col-12 checkout-register-mgb">
              <div className="item-fm">
                <TextArea
                  placeholder="ที่อยู่"
                  className="fm-control"
                  defaultValue={getValues("address")}
                  data-type="textaddressthai"
                  onInput={appContext.diFormPattern}
                  onChange={(event) =>
                    setValue("address", event.target.value)
                  }
                />
              </div>
            </div>
            <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
              <div className="item-fm">
                <Select
                  className="fm-control"
                  placeholder="กรุณาเลือกจังหวัด"
                  options={provinceOptions}
                  defaultValue={user.province}
                  onChange={(event, data) => {
                    setReloadDistrict(true);
                    setValue("province", data.value);
                    setValue(
                      "province_name",
                      event.target.innerText
                    );
                  }}
                />
              </div>
            </div>
            <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
              <div className="item-fm">
                <Select
                  className="fm-control"
                  placeholder="กรุณาเลือกเขต/อำเภอ"
                  options={districtOptions}
                  defaultValue={user.district}
                  onChange={(event, data) => {
                    setReloadSubDistrict(true);
                    setValue("district", data.value);
                    setValue(
                      "district_name",
                      event.target.innerText
                    );
                  }}
                />
              </div>
            </div>
            <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
              <div className="item-fm">
                <Select
                  className="fm-control"
                  placeholder="กรุณาเลือกแขวง/ตำบล"
                  options={subdistrictOptions}
                  defaultValue={user.subdistrict}
                  onChange={(event, data) => {
                    setValue("subdistrict", data.value);
                    setValue(
                      "subdistrict_name",
                      event.target.innerText
                    );
                  }}
                />
              </div>
            </div>
            <div className="col-fm-register col-12 col-md-6 checkout-register-mgb">
              <div className="item-fm">
                <Input
                  type="tel"
                  className="fm-control"
                  placeholder="รหัสไปรษณีย์"
                >
                  <input
                    {...register("postcode")}
                    maxLength={5}
                    data-type="number"
                    onInput={appContext.diFormPattern}
                    defaultValue={register["postcode"]}
                  />
                </Input>
              </div>
            </div>
            <div className="col-fm-register col-12 checkout-register-mgb">
              <div className="item-fm">
                <TextArea
                  placeholder="โน้ตเพิ่มเติม"
                  className="fm-control"
                  defaultValue={getValues("note")}
                  data-type="textaddress"
                  onInput={appContext.diFormPattern}
                  onChange={(event) =>
                    setValue("note", event.target.value)
                  }
                />
              </div>
            </div>
            {/* <GroupRadio
              callback={setTax}
              title=""
              name="tax"
              value_1="ต้องการหัก ณ ที่จ่าย"
              value_2="ไม่ต้องการ หัก ณ ที่จ่าย"
            ></GroupRadio> */}
          </div>
        )
      )}
    </>
  );
};

export default TaxInfoForm;
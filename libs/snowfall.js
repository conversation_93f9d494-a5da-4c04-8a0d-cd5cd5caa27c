// libs/snowfall.js

// กลมๆ
// export const createSnowfall = () => {
//   if (typeof window !== 'undefined') {
//     const snow = () => {
//       const snowContainer = document.createElement('div');
//       snowContainer.style.position = 'fixed';
//       snowContainer.style.top = 0;
//       snowContainer.style.left = 0;
//       snowContainer.style.width = '100%';
//       snowContainer.style.height = '100%';
//       snowContainer.style.pointerEvents = 'none';
//       snowContainer.style.zIndex = 9999;
//       document.body.appendChild(snowContainer);

//       let flakes = [];
//       let numFlakes = 100;

//       const createFlake = () => {
//         const flake = document.createElement('div');
//         flake.style.position = 'absolute';
//         flake.style.width = `${Math.random() * 10 + 5}px`;
//         flake.style.height = `${Math.random() * 10 + 5}px`;
//         flake.style.backgroundColor = 'white';
//         flake.style.borderRadius = '50%';
//         flake.style.animation = 'falling 5s linear infinite';
//         snowContainer.appendChild(flake);

//         flakes.push(flake);
//       };

//       for (let i = 0; i < numFlakes; i++) {
//         createFlake();
//       }

//       const keyframes = `@keyframes falling {
//         to {
//           transform: translateY(100vh);
//           opacity: 0;
//         }
//       }`;

//       const styleSheet = document.createElement('style');
//       styleSheet.innerText = keyframes;
//       document.head.appendChild(styleSheet);

//       flakes.forEach(flake => {
//         flake.style.left = `${Math.random() * 100}%`;
//         flake.style.animationDuration = `${Math.random() * 5 + 5}s`;
//         flake.style.animationDelay = `${Math.random() * 5}s`;
//       });
//     };

//     snow(); // เรียกฟังก์ชั่นหิมะ
//   }
// };


// 6 เหลี่ยม ลง ไม่หมุน

// export const createSnowfall = () => {
//     if (typeof window !== 'undefined') {
//       const snow = () => {
//         const snowContainer = document.createElement('div');
//         snowContainer.style.position = 'fixed';
//         snowContainer.style.top = 0;
//         snowContainer.style.left = 0;
//         snowContainer.style.width = '100%';
//         snowContainer.style.height = '100%';
//         snowContainer.style.pointerEvents = 'none';
//         snowContainer.style.zIndex = 9999;
//         document.body.appendChild(snowContainer);
  
//         let flakes = [];
//         // let numFlakes = 200;  // จำนวนหิมะ มาก
//         let numFlakes = 80;  // จำนวนหิมะ น้อย
  
//         const createFlake = () => {
//           const flake = document.createElement('div');
//           flake.style.position = 'absolute';
          
//           // สุ่มขนาดหิมะจาก 10px ถึง 80px
//           const size = Math.random() * 45 + 10; // ขนาดระหว่าง 10px ถึง 45px
//           flake.style.fontSize = `${size}px`;
//           flake.style.color = 'white';
//           flake.style.animation = 'falling 8s linear infinite';  // เพิ่มความช้าของแอนิเมชั่น
//           flake.textContent = "❄";  // ใช้สัญลักษณ์หิมะ ❄
//           snowContainer.appendChild(flake);
  
//           flakes.push(flake);
//         };
  
//         for (let i = 0; i < numFlakes; i++) {
//           createFlake();
//         }
  
//         const keyframes = `@keyframes falling {
//           to {
//             transform: translateY(100vh);
//             opacity: 0;
//           }
//         }`;
  
//         const styleSheet = document.createElement('style');
//         styleSheet.innerText = keyframes;
//         document.head.appendChild(styleSheet);
  
//         flakes.forEach(flake => {
//           flake.style.left = `${Math.random() * 100}%`;
//           flake.style.animationDuration = `${Math.random() * 10 + 10}s`;  // ความเร็วของการตก
//           flake.style.animationDelay = `${Math.random() * 5}s`;
//         });
//       };
  
//       snow(); // เรียกฟังก์ชั่นหิมะ
//     }
//   };


// 6 เหลี่ยม ลงและหมุน

export const createSnowfall = () => {
  if (typeof window !== 'undefined') {
    const snow = () => {
      const snowContainer = document.createElement('div');
      snowContainer.style.position = 'fixed';
      snowContainer.style.top = 0;
      snowContainer.style.left = 0;
      snowContainer.style.width = '100%';
      snowContainer.style.height = '100%';
      snowContainer.style.pointerEvents = 'none';
      snowContainer.style.zIndex = 9999;
      document.body.appendChild(snowContainer);

      let flakes = [];
      let numFlakes = 80; // จำนวนหิมะน้อย

      const createFlake = () => {
        const flake = document.createElement('div');
        flake.style.position = 'absolute';

        // ตรวจสอบว่าเป็นหน้าจอ Desktop หรือ Mobile
        const isMobile = window.innerWidth <= 768;

        // กำหนดขนาดหิมะสำหรับ Desktop และ Mobile
        const size = isMobile ? Math.random() * 20 + 5 : Math.random() * 35 + 10; // Mobile: 5px-25px, Desktop: 10px-45px
        flake.style.fontSize = `${size}px`;
        flake.style.color = 'white';
        flake.style.animation = 'falling 8s linear infinite'; // เพิ่มความช้าของแอนิเมชั่น
        flake.textContent = "❄"; // ใช้สัญลักษณ์หิมะ ❄
        snowContainer.appendChild(flake);

        flakes.push(flake);
      };

      for (let i = 0; i < numFlakes; i++) {
        createFlake();
      }

      const keyframes = `@keyframes falling {
        to {
          transform: translateY(100vh);
          opacity: 0;
        }
      }`;

      const styleSheet = document.createElement('style');
      styleSheet.innerText = keyframes;
      document.head.appendChild(styleSheet);

      flakes.forEach(flake => {
        flake.style.left = `${Math.random() * 100}%`;
        flake.style.animationDuration = `${Math.random() * 10 + 10}s`; // ความเร็วของการตก
        flake.style.animationDelay = `${Math.random() * 5}s`;
      });
    };

    snow(); // เรียกฟังก์ชั่นหิมะ
  }
};

  
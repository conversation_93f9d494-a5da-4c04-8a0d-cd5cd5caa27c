@import '../var';
 
.titleButtonGroup{
    font-family: $font-kanit;
    font-weight: 300;
    font-size: 1.042vw; 
    @media only screen and (min-width: 1200px) and (max-width: 1479px) {
        font-size: 1.35vw;
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        font-size: 1.65vw;
    }   
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        font-size: 1.95vw;
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        font-size: 4.6vw;
    }
} 
.iconThumb{
    
}
.iconDiv {
    display: inline;
    span {
        width: 1.406vw !important;
    }
    @media only screen and (min-width: 1200px) and (max-width: 1479px) {
        span {
            width: 1.826vw !important;
        }
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        span {
            width: 2.252vw !important;
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        span {
            width: 2.725vw !important;
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        span {
            width: 5.05vw !important;
        }
    }
}
.iconTestDiv {
    display: flex;
    span {
        width: 2vw !important;
        margin-right: 0.5vw !important;
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        span {
            width: 2.5vw !important;
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        span {
            width: 3.5vw !important;
            margin-right: 1.2vw !important;
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        span {
            width: 6.2vw !important;
            margin-right: 2vw !important;
        }
    }
}
.questionImageDiv {
    span {
        position: relative !important;
        max-width: 100% !important;
        img {
            width: 100% !important;
            position: relative !important;
            height: auto !important;
        }
    }
}
.answerImageResultDiv {
    margin-bottom: 10px;
    span {
        position: relative !important;
        max-width: 100% !important;
        img {
            width: 100% !important;
            position: relative !important;
            height: auto !important;
        }
    }
}
.answerImageDiv {
    margin: 5px 5px 5px 35px;
    span {
        position: relative !important;
        max-width: 100% !important;
        img {
            width: 100% !important;
            position: relative !important;
            height: auto !important;
        }
    }
}
.unlockCertIconDiv {
    display: flex;
    span {
        width: 20vw !important;
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        span {
            width: 25vw !important;
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        span {
            width: 40vw !important;
        }
    }
}
.assessmentIconDiv {
    display: flex;
    span {
        width: 50px !important;
    }
}
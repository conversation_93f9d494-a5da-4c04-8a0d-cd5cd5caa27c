@import '../var';
 .mobile{
    @media only screen and (min-width:768px) and (max-width:5000px) {
        display: none!important;
    }
 }
.titleListCourse{
    display: flex;
    align-items: center;
    justify-content:space-between;
    @media only screen and (min-width:290px) and (max-width:767px) {
        flex-wrap: wrap;
    }
    h3{
        margin:1vw 0 1vw 0;
        font-family: $font-kanit;
        span{
            font-family: $font-kanit;
            color:#648D2F;
            font-size:1.25vw;
            font-weight: 500;
            &.small{
                font-size:1.05vw;
                margin-left: 0.5vw;
                font-weight: 300;
            }
        }
    }
    @media only screen and (min-width:992px) and (max-width:1199px) {
        h3 {
            span {
                font-size: 1.6vw;
                &.small{
                    font-size:1.3vw;
                    margin-left: 0.5vw;
                    font-weight: 300;
                }
            }
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        h3 {
            span {
                font-size: 1.9vw;
                &.small{
                    font-size:1.6vw;
                    margin-left: 0.5vw;
                    font-weight: 300;
                }
            }
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        h3 {
            margin: 1vw 0 1vw 0;
            span {
                font-size: 6.6vw;
                &.small{
                    font-size:4.5vw;
                    margin-left: 0vw;
                    line-height: 1.5;
                    font-weight: 300;
                }
            }
        }
    }
}
.mb_0{
    h3{
        margin-bottom:0;
    }
}
.titleListCourseGreenLine{
    display: flex;
    align-items: center;
    justify-content:space-between;
    padding:1vw 0 1vw 0;
    margin:0 0 1vw 0;
    border-bottom: 2px solid #648D2F;
    h3 {
        flex:1;
    }
    button{
        margin-right:20px!important;
    }
    .my_point {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        p {
            font-size: 1vw;
            color: #638d2E;
            margin: 0 0 0 0;
            font-family: $font-kanit;
            padding-right: 10px;
        }
        h3 {
            font-size: 1.25vw;
            color: #638d2E;
            margin: 0 0 0 0;
            font-family: $font-kanit;
            padding-left: 10px;
        }
        .coin {
            width: 32px;
            height: 32px;
        }
    }
    @media only screen and (min-width:290px) and (max-width:767px) {
        flex-wrap: wrap;
    }
    h3{
        font-family: $font-kanit;
        margin: 0;
        span{
            font-family: $font-kanit;
            color:#648D2F;
            font-size:1.25vw;
            font-weight: 500;
            &.small{
                font-size:1.05vw;
                margin-left: 0.5vw;
                font-weight: 300;
            }
        }
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        padding: 1.3vw 0 1.3vw 0;
        margin: 0 0 2vw 0;
        h3 {
            span {
                font-size: 1.6vw;
                &.small{
                    font-size:1.3vw;
                    margin-left: 0.5vw;
                    font-weight: 300;
                }
            }
        }
        .my_point {
            p {
                font-size: 1.2vw;
                font-weight: 500;
            }
            h3 {
                font-size: 1.6vw;
            }
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        padding: 1.3vw 0 1.3vw 0;
        margin: 0 0 2vw 0;
        h3 {
            span {
                font-size: 1.9vw;
                &.small{
                    font-size:1.6vw;
                    margin-left: 0.5vw;
                    font-weight: 300;
                }
            }
        }
        .my_point {
            p {
                font-size: 1.5vw;
            }
            h3 {
                font-size: 1.9vw;
            }
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        padding: 4vw 0;
        margin: 0 0 15px 0;
        h3 {
            margin: 0 0 0 0;
            span {
                font-size: 6vw;
                &.small{
                    font-size:4.5vw;
                    margin-left: 0vw;
                    line-height: 1.5;
                    font-weight: 300;
                }
            }
        }
        .my_point {
            p {
                font-size: 3.2vw;
                padding-right: 2vw;
            }
            h3 {
                font-size: 5vw;
                padding-left: 2vw;
            }
            .coin {
                width: 5vw;
                height: 5vw;
            }
        }
    }
}
.cmeReportDataHead{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: wrap;
    flex-direction: row;
}

.cmeReportDataBody{
    padding: .5vw 0 1.5vw 0;
}
.tableReport{
    width: 100%;
    border-spacing: 0!important;
    border: 1px solid #B5B5B5 ;
    thead{
        tr{
            background-color: #fff;
            th{
                text-align: left;
                font-size: 1.029vw;
                color: #303030;
                padding: 1vw;
                border-right: 1px solid #B5B5B5 ;
                &:last-child{
                    text-align: center;
                    border-right: 1px solid transparent ;
                }
            }
        }
    }   
    tbody{
        tr{
            &:nth-child(even){
                background-color: #fff;
            }
            &:nth-child(odd){
                background-color: #F8F8F8;
            }
            td{
                font-size: .920vw;
                color: #303030;
                padding: 1vw;
                border-right: 1px solid #B5B5B5 ;
                &:last-child{
                    text-align: center;
                    border-right: 1px solid transparent ;
                }
            }
        }
    }  
    &.total{
        border: 1px solid transparent;
        thead{
            tr{
                th{
                    text-align: center;
                    border-right: 1px solid #B5B5B5!important;
                    border-bottom: 1px solid #B5B5B5!important;
                    &:first-child{
                        border-bottom: 0!important;;
                    }
                }
            }
        }
    } 
}

.cmeReportGroupBtn{
    .cmeReportBtn{
        font-family: "Kanit";
        font-weight: normal;
        font-size: 1vw;
        color: #fff!important;
        margin-left: 15px;
        margin: 0 0 0 10px!important;
        min-height: auto;
        padding: .5vw .5vw;
        min-width: 110px;
        background-color: #B5B5B5!important;
        &.cmePrint{
            background-color: #638D2E!important;
        }
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {

}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cmeReportDataBody{
        padding: .5vw 0 1.5vw 0;
    }
    .tableReport{
        thead{
            tr{
                th{
                    font-size: 1.229vw;
                    padding: 1.2vw .5vw; 
                }
            }
        }   
        tbody{
            tr{
                td{
                    font-size: 1.220vw;
                    padding: 1.2vw .5vw;
                }
            }
        }  
    }
    .cmeReportGroupBtn{
        .cmeReportBtn{
            padding: .75vw .75vw;
            min-width: 80px;
        }
    }
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
    .cmeReportDataBody{
        padding: .5vw 0 1.5vw 0;
    }
    .tableReport{
        thead{
            tr{
                th{
                    font-size: 3.2vw;
                    padding: 1.5vw 1.5vw; 
                }
            }
        }   
        tbody{
            tr{
                td{
                    font-size: 3.2vw;
                    padding: 1.5vw 1.5vw;
                }
            }
        }  
    }
    .cmeReportGroupBtn{
        .cmeReportBtn{
            font-size: 3.2vw;
            padding: 1.5vw .75vw;
            min-width: 70px;
        }
    }
}
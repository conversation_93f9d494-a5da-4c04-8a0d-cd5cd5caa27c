@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

@media only screen and (min-width: 768px) and (max-width: 5000px) {
  .mobile {
    display: none !important;
  }
}

.titleListCourse {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
  .titleListCourse {
    flex-wrap: wrap;
  }
}

.titleListCourse h3 {
  margin: 1vw 0 1vw 0;
  font-family: "Kanit";
}

.titleListCourse h3 span {
  font-family: "Kanit";
  color: #648D2F;
  font-size: 1.25vw;
  font-weight: 500;
}

.titleListCourse h3 span.small {
  font-size: 1.05vw;
  margin-left: 0.5vw;
  font-weight: 300;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .titleListCourse h3 span {
    font-size: 1.6vw;
  }
  .titleListCourse h3 span.small {
    font-size: 1.3vw;
    margin-left: 0.5vw;
    font-weight: 300;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .titleListCourse h3 span {
    font-size: 1.9vw;
  }
  .titleListCourse h3 span.small {
    font-size: 1.6vw;
    margin-left: 0.5vw;
    font-weight: 300;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .titleListCourse h3 {
    margin: 1vw 0 1vw 0;
  }
  .titleListCourse h3 span {
    font-size: 6.6vw;
  }
  .titleListCourse h3 span.small {
    font-size: 4.5vw;
    margin-left: 0vw;
    line-height: 1.5;
    font-weight: 300;
  }
}

.mb_0 h3 {
  margin-bottom: 0;
}

.titleListCourseGreenLine {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1vw 0 1vw 0;
  margin: 0 0 1vw 0;
  border-bottom: 2px solid #648D2F;
}

.titleListCourseGreenLine h3 {
  flex: 1;
}

.titleListCourseGreenLine button {
  margin-right: 20px !important;
}

.titleListCourseGreenLine .my_point {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.titleListCourseGreenLine .my_point p {
  font-size: 1vw;
  color: #638d2E;
  margin: 0 0 0 0;
  font-family: "Kanit";
  padding-right: 10px;
}

.titleListCourseGreenLine .my_point h3 {
  font-size: 1.25vw;
  color: #638d2E;
  margin: 0 0 0 0;
  font-family: "Kanit";
  padding-left: 10px;
}

.titleListCourseGreenLine .my_point .coin {
  width: 32px;
  height: 32px;
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
  .titleListCourseGreenLine {
    flex-wrap: wrap;
  }
}

.titleListCourseGreenLine h3 {
  font-family: "Kanit";
  margin: 0;
}

.titleListCourseGreenLine h3 span {
  font-family: "Kanit";
  color: #648D2F;
  font-size: 1.25vw;
  font-weight: 500;
}

.titleListCourseGreenLine h3 span.small {
  font-size: 1.05vw;
  margin-left: 0.5vw;
  font-weight: 300;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .titleListCourseGreenLine {
    padding: 1.3vw 0 1.3vw 0;
    margin: 0 0 2vw 0;
  }
  .titleListCourseGreenLine h3 span {
    font-size: 1.6vw;
  }
  .titleListCourseGreenLine h3 span.small {
    font-size: 1.3vw;
    margin-left: 0.5vw;
    font-weight: 300;
  }
  .titleListCourseGreenLine .my_point p {
    font-size: 1.2vw;
    font-weight: 500;
  }
  .titleListCourseGreenLine .my_point h3 {
    font-size: 1.6vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .titleListCourseGreenLine {
    padding: 1.3vw 0 1.3vw 0;
    margin: 0 0 2vw 0;
  }
  .titleListCourseGreenLine h3 span {
    font-size: 1.9vw;
  }
  .titleListCourseGreenLine h3 span.small {
    font-size: 1.6vw;
    margin-left: 0.5vw;
    font-weight: 300;
  }
  .titleListCourseGreenLine .my_point p {
    font-size: 1.5vw;
  }
  .titleListCourseGreenLine .my_point h3 {
    font-size: 1.9vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .titleListCourseGreenLine {
    padding: 4vw 0;
    margin: 0 0 15px 0;
  }
  .titleListCourseGreenLine h3 {
    margin: 0 0 0 0;
  }
  .titleListCourseGreenLine h3 span {
    font-size: 6vw;
  }
  .titleListCourseGreenLine h3 span.small {
    font-size: 4.5vw;
    margin-left: 0vw;
    line-height: 1.5;
    font-weight: 300;
  }
  .titleListCourseGreenLine .my_point p {
    font-size: 3.2vw;
    padding-right: 2vw;
  }
  .titleListCourseGreenLine .my_point h3 {
    font-size: 5vw;
    padding-left: 2vw;
  }
  .titleListCourseGreenLine .my_point .coin {
    width: 5vw;
    height: 5vw;
  }
}

.cmeReportDataHead {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: wrap;
  flex-direction: row;
}

.cmeReportDataBody {
  padding: .5vw 0 1.5vw 0;
}

.tableReport {
  width: 100%;
  border-spacing: 0 !important;
  border: 1px solid #B5B5B5;
}

.tableReport thead tr {
  background-color: #fff;
}

.tableReport thead tr th {
  text-align: left;
  font-size: 1.029vw;
  color: #303030;
  padding: 1vw;
  border-right: 1px solid #B5B5B5;
}

.tableReport thead tr th:last-child {
  text-align: center;
  border-right: 1px solid transparent;
}

.tableReport tbody tr:nth-child(even) {
  background-color: #fff;
}

.tableReport tbody tr:nth-child(odd) {
  background-color: #F8F8F8;
}

.tableReport tbody tr td {
  font-size: .920vw;
  color: #303030;
  padding: 1vw;
  border-right: 1px solid #B5B5B5;
}

.tableReport tbody tr td:last-child {
  text-align: center;
  border-right: 1px solid transparent;
}

.tableReport.total {
  border: 1px solid transparent;
}

.tableReport.total thead tr th {
  text-align: center;
  border-right: 1px solid #B5B5B5 !important;
  border-bottom: 1px solid #B5B5B5 !important;
}

.tableReport.total thead tr th:first-child {
  border-bottom: 0 !important;
}

.cmeReportGroupBtn .cmeReportBtn {
  font-family: "Kanit";
  font-weight: normal;
  font-size: 1vw;
  color: #fff !important;
  margin-left: 15px;
  margin: 0 0 0 10px !important;
  min-height: auto;
  padding: .5vw .5vw;
  min-width: 110px;
  background-color: #B5B5B5 !important;
}

.cmeReportGroupBtn .cmeReportBtn.cmePrint {
  background-color: #638D2E !important;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cmeReportDataBody {
    padding: .5vw 0 1.5vw 0;
  }
  .tableReport thead tr th {
    font-size: 1.229vw;
    padding: 1.2vw .5vw;
  }
  .tableReport tbody tr td {
    font-size: 1.220vw;
    padding: 1.2vw .5vw;
  }
  .cmeReportGroupBtn .cmeReportBtn {
    padding: .75vw .75vw;
    min-width: 80px;
  }
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
  .cmeReportDataBody {
    padding: .5vw 0 1.5vw 0;
  }
  .tableReport thead tr th {
    font-size: 3.2vw;
    padding: 1.5vw 1.5vw;
  }
  .tableReport tbody tr td {
    font-size: 3.2vw;
    padding: 1.5vw 1.5vw;
  }
  .cmeReportGroupBtn .cmeReportBtn {
    font-size: 3.2vw;
    padding: 1.5vw .75vw;
    min-width: 70px;
  }
}
/*# sourceMappingURL=profile.module.css.map */
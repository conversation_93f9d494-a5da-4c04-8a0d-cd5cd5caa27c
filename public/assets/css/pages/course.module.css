@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.titleButtonGroup {
  font-family: "Kanit";
  font-weight: 300;
  font-size: 1.042vw;
}

@media only screen and (min-width: 1200px) and (max-width: 1479px) {
  .titleButtonGroup {
    font-size: 1.35vw;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .titleButtonGroup {
    font-size: 1.65vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .titleButtonGroup {
    font-size: 1.95vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .titleButtonGroup {
    font-size: 4.6vw;
  }
}

.iconDiv {
  display: inline;
}

.iconDiv span {
  width: 1.406vw !important;
}

@media only screen and (min-width: 1200px) and (max-width: 1479px) {
  .iconDiv span {
    width: 1.826vw !important;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .iconDiv span {
    width: 2.252vw !important;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .iconDiv span {
    width: 2.725vw !important;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .iconDiv span {
    width: 5.05vw !important;
  }
}

.iconTestDiv {
  display: flex;
}

.iconTestDiv span {
  width: 2vw !important;
  margin-right: 0.5vw !important;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .iconTestDiv span {
    width: 2.5vw !important;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .iconTestDiv span {
    width: 3.5vw !important;
    margin-right: 1.2vw !important;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .iconTestDiv span {
    width: 6.2vw !important;
    margin-right: 2vw !important;
  }
}

.questionImageDiv span {
  position: relative !important;
  max-width: 100% !important;
}

.questionImageDiv span img {
  width: 100% !important;
  position: relative !important;
  height: auto !important;
}

.answerImageResultDiv {
  margin-bottom: 10px;
}

.answerImageResultDiv span {
  position: relative !important;
  max-width: 100% !important;
}

.answerImageResultDiv span img {
  width: 100% !important;
  position: relative !important;
  height: auto !important;
}

.answerImageDiv {
  margin: 5px 5px 5px 35px;
}

.answerImageDiv span {
  position: relative !important;
  max-width: 100% !important;
}

.answerImageDiv span img {
  width: 100% !important;
  position: relative !important;
  height: auto !important;
}

.unlockCertIconDiv {
  display: flex;
}

.unlockCertIconDiv span {
  width: 20vw !important;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .unlockCertIconDiv span {
    width: 25vw !important;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .unlockCertIconDiv span {
    width: 40vw !important;
  }
}

.assessmentIconDiv {
  display: flex;
}

.assessmentIconDiv span {
  width: 50px !important;
}
/*# sourceMappingURL=course.module.css.map */
@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?udfc94');
  src:  url('fonts/icomoon.eot?udfc94#iefix') format('embedded-opentype'),
    url('fonts/icomoon.woff2?udfc94') format('woff2'),
    url('fonts/icomoon.ttf?udfc94') format('truetype'),
    url('fonts/icomoon.woff?udfc94') format('woff'),
    url('fonts/icomoon.svg?udfc94#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-ic-doctor-bag:before {
  content: "\e950";
}
.icon-ic-download-2:before {
  content: "\e94a";
}
.icon-ic-download-1:before {
  content: "\e94b";
}
.icon-ic-download-3:before {
  content: "\e94c";
}
.icon-ic-trash-1:before {
  content: "\e94d";
}
.icon-ic-trash-2:before {
  content: "\e94e";
}
.icon-ic-document:before {
  content: "\e94f";
}
.icon-ic-time_2:before {
  content: "\e948";
}
.icon-ic-time_3:before {
  content: "\e949";
}
.icon-ic-search-x:before {
  content: "\e946";
}
.icon-ic-search-no-results:before {
  content: "\e947";
}
.icon-ic-dot-nav:before {
  content: "\e945";
}
.icon-ic-circle-lock-full:before {
  content: "\e944";
}
.icon-ic-close-cart:before {
  content: "\e942";
}
.icon-ic-remove-cart:before {
  content: "\e943";
}
.icon-ic-audio-active:before {
  content: "\e941";
}
.icon-ic-star-half-light:before {
  content: "\e940";
}
.icon-ic-star-light:before {
  content: "\e93f";
}
.icon-ic-star-half:before {
  content: "\e93d";
}
.icon-ic-star:before {
  content: "\e93e";
}
.icon-ic-circle-minus:before {
  content: "\e93a";
}
.icon-ic-circle-plus:before {
  content: "\e93b";
}
.icon-ic-circle-heart:before {
  content: "\e93c";
}
.icon-ic-arr-left:before {
  content: "\e936";
}
.icon-ic-arr-right:before {
  content: "\e937";
}
.icon-ic-arr-up:before {
  content: "\e938";
}
.icon-ic-arr-down:before {
  content: "\e939";
}
.icon-ic-circle-social-twitter:before {
  content: "\e935";
}
.icon-ic-circle-social-youtube:before {
  content: "\e931";
}
.icon-ic-circle-social-instagram:before {
  content: "\e932";
}
.icon-ic-circle-social-facebook:before {
  content: "\e933";
}
.icon-ic-circle-social-line:before {
  content: "\e934";
}
.icon-ic-time:before {
  content: "\e92a";
}
.icon-ic-circle-play:before {
  content: "\e900";
}
.icon-ic-clock:before {
  content: "\e901";
}
.icon-ic-coin-point:before {
  content: "\e902";
}
.icon-ic-up:before {
  content: "\e903";
}
.icon-ic-down:before {
  content: "\e904";
}
.icon-ic-left:before {
  content: "\e905";
}
.icon-ic-right:before {
  content: "\e906";
}
.icon-ic-close:before {
  content: "\e907";
}
.icon-ic-plus:before {
  content: "\e908";
}
.icon-ic-play:before {
  content: "\e909";
}
.icon-ic-file-p:before {
  content: "\e90a";
}
.icon-ic-file-pdf:before {
  content: "\e90b";
}
.icon-ic-file-x:before {
  content: "\e90c";
}
.icon-ic-f-lock:before {
  content: "\e90d";
}
.icon-ic-f-user:before {
  content: "\e90e";
}
.icon-ic-heart:before {
  content: "\e90f";
}
.icon-ic-heart-full:before {
  content: "\e910";
}
.icon-ic-image:before {
  content: "\e911";
}
.icon-ic-edit:before {
  content: "\e912";
}
.icon-ic-lessons:before {
  content: "\e913";
}
.icon-ic-lock:before {
  content: "\e914";
}
.icon-ic-mail:before {
  content: "\e915";
}
.icon-ic-mail-bd:before {
  content: "\e917";
}
.icon-ic-tel:before {
  content: "\e916";
}
.icon-ic-pc-click:before {
  content: "\e918";
}
.icon-ic-pc-course:before {
  content: "\e919";
}
.icon-ic-pc-world:before {
  content: "\e91a";
}
.icon-ic-qrcode:before {
  content: "\e91b";
}
.icon-ic-search:before {
  content: "\e91c";
}
.icon-ic-shape-down:before {
  content: "\e91d";
}
.icon-ic-shape-left:before {
  content: "\e91e";
}
.icon-ic-shape-right:before {
  content: "\e91f";
}
.icon-ic-shape-up:before {
  content: "\e920";
}
.icon-ic-share:before {
  content: "\e921";
}
.icon-ic-social-facebook:before {
  content: "\e922";
}
.icon-ic-social-instagram:before {
  content: "\e923";
}
.icon-ic-social-line:before {
  content: "\e924";
}
.icon-ic-social-tiktok:before {
  content: "\e925";
}
.icon-ic-social-twitter:before {
  content: "\e926";
}
.icon-ic-tick:before {
  content: "\e927";
}
.icon-ic-tick-thanks:before {
  content: "\e928";
}
.icon-ic-tikket:before {
  content: "\e929";
}
.icon-ic-user-key:before {
  content: "\e92b";
}
.icon-ic-youtube:before {
  content: "\e92c";
}
.icon-ic-bell:before {
  content: "\e92d";
}
.icon-ic-cart:before {
  content: "\e92e";
}
.icon-ic-circle:before {
  content: "\e92f";
}
.icon-ic-circle-lock:before {
  content: "\e930";
}

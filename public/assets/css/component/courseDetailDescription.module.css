@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.description {
  font-family: "K2D";
  font-weight: 100;
  font-size: 0.833vw;
  padding: 10px 0;
  width: 100%;
}

@media only screen and (min-width: 1200px) and (max-width: 1479px) {
  .description {
    font-size: 1.2vw;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .description {
    font-size: 1.55vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .description {
    font-size: 1.85vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .description {
    font-size: 4vw;
  }
}
/*# sourceMappingURL=courseDetailDescription.module.css.map */
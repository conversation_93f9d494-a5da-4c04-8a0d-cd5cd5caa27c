@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.choose_type_data_list {
  padding: 0;
  background-color: #fff;
  border-radius: 12px;
}

.choose_type_data_list ul {
  list-style: none;
  padding: 0.8vw 0;
  margin: 0 0 0 0;
}

.choose_type_data_list ul li {
  position: relative;
  padding: 5px 15px 5px 30px;
  margin: 20px 0;
  cursor: pointer;
}

.choose_type_data_list ul li::before {
  opacity: 0;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0.52vw;
  height: 100%;
  background-color: #648D2F;
  transition: .3s;
}

.choose_type_data_list ul li span {
  font-family: "Kanit";
  font-weight: 500;
  font-size: 1vw;
  color: #BCBCBC;
  transition: .3s;
}

.choose_type_data_list ul li.active::before, .choose_type_data_list ul li:hover::before {
  opacity: 1;
}

.choose_type_data_list ul li.active span, .choose_type_data_list ul li:hover span {
  color: #648D2F;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .choose_type_data_list ul li {
    margin: 15px 0;
  }
  .choose_type_data_list ul li span {
    font-size: 1.4vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .choose_type_data_list ul li span {
    font-size: 1.6vw;
  }
}
/*# sourceMappingURL=chooseTypeDataList.module.css.map */
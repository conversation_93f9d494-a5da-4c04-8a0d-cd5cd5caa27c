@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.block_table_history_course .inner_table_history_course .scroll table {
  width: 100%;
  border-collapse: collapse;
}

.block_table_history_course .inner_table_history_course .scroll table thead tr th {
  font-family: "Kanit";
  text-align: left;
  font-weight: 500;
  font-size: 1vw;
  color: #648D2F;
  padding: 15px 5px;
  border-top: 1px solid #648D2F !important;
  border-bottom: 1px solid #648D2F !important;
}

.block_table_history_course .inner_table_history_course .scroll table thead tr th:nth-last-child(1), .block_table_history_course .inner_table_history_course .scroll table thead tr th:nth-last-child(2) {
  text-align: center;
}

.block_table_history_course .inner_table_history_course .scroll table tbody tr {
  background-color: rgba(148, 193, 32, 0);
  transition: .3s;
}

.block_table_history_course .inner_table_history_course .scroll table tbody tr td {
  font-family: "Kanit";
  font-weight: 400;
  font-size: 1vw;
  color: #656565;
  padding: 10px 5px;
  max-width: 250px;
}

.block_table_history_course .inner_table_history_course .scroll table tbody tr td a {
  font-size: 1vw;
  color: #648D2F;
}

.block_table_history_course .inner_table_history_course .scroll table tbody tr td a svg {
  font-size: 1.4vw;
  position: relative;
  top: 0.15vw;
}

.block_table_history_course .inner_table_history_course .scroll table tbody tr td:nth-last-child(1), .block_table_history_course .inner_table_history_course .scroll table tbody tr td:nth-last-child(2) {
  text-align: center;
}

.block_table_history_course .inner_table_history_course .scroll table tbody tr:nth-child(even) {
  background-color: rgba(148, 193, 32, 0.1);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .block_table_history_course .inner_table_history_course .scroll table {
    width: 100%;
    border-collapse: collapse;
  }
  .block_table_history_course .inner_table_history_course .scroll table thead tr th {
    font-size: 1.4vw;
    width: 16.66%;
    padding: 15px 10px;
  }
  .block_table_history_course .inner_table_history_course .scroll table tbody tr td {
    font-size: 1.2vw;
    padding: 10px;
  }
  .block_table_history_course .inner_table_history_course .scroll table tbody tr td a {
    font-size: 1.2vw;
  }
  .block_table_history_course .inner_table_history_course .scroll table tbody tr td a svg {
    font-size: 2vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .block_table_history_course .inner_table_history_course .scroll table {
    width: 100%;
    border-collapse: collapse;
  }
  .block_table_history_course .inner_table_history_course .scroll table thead tr th {
    font-size: 1.6vw;
    width: 16.66%;
    padding: 15px 10px;
  }
  .block_table_history_course .inner_table_history_course .scroll table tbody tr td {
    font-size: 1.4vw;
    padding: 10px;
  }
  .block_table_history_course .inner_table_history_course .scroll table tbody tr td a {
    font-size: 1.4vw;
  }
  .block_table_history_course .inner_table_history_course .scroll table tbody tr td a svg {
    font-size: 2.3vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .block_table_history_course .inner_table_history_course {
    overflow: auto;
  }
  .block_table_history_course .inner_table_history_course .scroll {
    width: 600px;
  }
  .block_table_history_course .inner_table_history_course .scroll table {
    width: 100%;
    border-collapse: collapse;
  }
  .block_table_history_course .inner_table_history_course .scroll table thead tr th {
    font-size: 4vw;
    width: 16.66%;
  }
  .block_table_history_course .inner_table_history_course .scroll table tbody tr td {
    font-size: 3.2vw;
    padding: 10px 5px;
  }
  .block_table_history_course .inner_table_history_course .scroll table tbody tr td a {
    font-size: 3.2vw;
  }
  .block_table_history_course .inner_table_history_course .scroll table tbody tr td a svg {
    font-size: 4.8vw;
  }
}
/*# sourceMappingURL=tableHistoryCourse.module.css.map */
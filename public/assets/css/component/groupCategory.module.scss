@import '../var';

.divider{
  padding-top: 1.042vw;
} 
.title {
  font-family: $font-kanit;
  font-size: 1.563vw;
  padding-bottom: 1.042vw;
  font-weight: 400;
  @media only screen and (min-width: 992px) and (max-width: 1199px) {
    font-size: 2.1vw;
  }
  @media only screen and (min-width: 768px) and (max-width: 991px) {
    font-size: 2.6vw;
  }
  @media only screen and (min-width: 250px) and (max-width: 767px) {
    font-size: 6vw;
    padding: 0 0 6vw 0;
  }
 }
 .seriestitle{
  font-family: $font-kanit;
  font-size: 1.563vw;
  margin: 0 0 5px 0;
  font-weight: 400;
  @media only screen and (min-width: 1200px) and (max-width: 1479px) {
    font-size: 1.563vw;
  }
  @media only screen and (min-width: 992px) and (max-width: 1199px) {
    font-size: 2.1vw;
  }
  @media only screen and (min-width: 768px) and (max-width: 991px) {
    font-size: 2.6vw;
  }
  @media only screen and (min-width: 250px) and (max-width: 767px) {
    font-size: 6vw;
  }
 }
 .subtitle{
  font-family: $font-k2d;
  font-weight: 300;
  font-size: 0.833vw;
  margin: 0 0 5px 0;
  width: 50%;
  @media only screen and (min-width: 1200px) and (max-width: 1479px) {
    font-size: 1.15vw;
  }
  @media only screen and (min-width: 992px) and (max-width: 1199px) {
    font-size: 1.45vw;
  }
  @media only screen and (min-width: 768px) and (max-width: 991px) {
    font-size: 1.85vw;
  }
  @media only screen and (min-width: 250px) and (max-width: 767px) {
    font-size: 4vw;
    width: 100%;
  }
 }
.mySlide {
  transition: all .25s ease-out;
  opacity: 1;
  overflow: visible !important;
  margin-bottom: 5px;
  .swiper-slide-active {
    transform: scale(1);
  }
} 
.mySlideSeries{
  transition: all .25s ease-out;
  .swiper-slide-active {
    transform: scale(1);
  }
}
.backgroundSeries{
  padding: 30px 15px 35px 15px;
  margin: 0;
  background-size: cover;
  background-position: center;
  // box-shadow: inset 0 0 0 1000px rgba(0,0,0,.4);
  .subtitle{
    margin-bottom: 10px;
  }
}
.btnBuyNow{
  padding: 10px 30px; 
  text-align: center;
  border-radius: 14px;
  cursor: pointer;
}
 
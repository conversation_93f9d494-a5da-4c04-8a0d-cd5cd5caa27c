@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default, .block_address_contact .inner, .block_address_contact .inner .item_address_contact {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center, .block_address_contact .inner .item_address_contact .action_address_contact {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.block_address_contact {
  width: 100%;
  margin-top: 0.8vw;
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .block_address_contact {
    margin-top: 2.4vw;
  }
}

.block_address_contact .inner {
  flex-direction: column;
  border: 1px solid #648D2F;
  border-radius: 12px;
  padding: 5px 15px;
}

.block_address_contact .inner .item_address_contact {
  align-items: stretch;
  width: 100%;
  padding: 10px 5px;
  border-top: 1px solid #648D2F;
}

.block_address_contact .inner .item_address_contact:first-child {
  border: 0;
}

.block_address_contact .inner .item_address_contact .title_address_contact {
  font-family: "Kanit";
  font-weight: 500;
  font-size: .9vw;
  white-space: normal;
  color: #676767;
  margin: 0 0 0 0;
}

.block_address_contact .inner .item_address_contact .data_address_contact {
  flex: 1;
  font-family: "K2D";
  font-weight: 300;
  font-size: .85vw;
  white-space: normal;
  color: #676767;
  padding: 0 15px;
  margin: 0 0 0 0;
}

.block_address_contact .inner .item_address_contact .action_address_contact button {
  background-color: transparent;
  border: none;
  font-family: "K2D";
  font-weight: 300;
  font-size: .85vw;
  white-space: normal;
  color: #676767;
  padding: 0 0;
  margin: 0 0 0 0;
  cursor: pointer;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .block_address_contact .inner {
    padding: 5px 10px;
  }
  .block_address_contact .inner .item_address_contact .title_address_contact {
    font-size: 1.6vw;
  }
  .block_address_contact .inner .item_address_contact .data_address_contact {
    font-size: 1.4vw;
  }
  .block_address_contact .inner .item_address_contact .action_address_contact button {
    font-size: 1.4vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .block_address_contact .inner {
    padding: 5px 10px;
  }
  .block_address_contact .inner .item_address_contact .title_address_contact {
    font-size: 4vw;
    width: 100%;
  }
  .block_address_contact .inner .item_address_contact .data_address_contact {
    font-size: 3.2vw;
    padding: 0 15px 0 0;
  }
  .block_address_contact .inner .item_address_contact .action_address_contact button {
    font-size: 3.2vw;
  }
}
/*# sourceMappingURL=addressContact.module.css.map */
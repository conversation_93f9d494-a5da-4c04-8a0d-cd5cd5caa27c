@import '../var';
 
.block_address_contact{
    width: 100%;    
    margin-top:0.8vw;
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        margin-top:2.4vw;
    }
    .inner{
        @extend .d-flex-default;
        flex-direction: column;
        border: 1px solid #648D2F;
        border-radius: 12px;
        padding: 5px 15px;
        .item_address_contact{
            @extend .d-flex-default;
            align-items: stretch;
            width: 100%;
            padding: 10px 5px;
            border-top:1px solid #648D2F;
            &:first-child{
                border: 0;
            }
            .title_address_contact{
                font-family: $font-kanit;
                font-weight: 500;
                font-size: .9vw;
                white-space: normal;
                color: #676767;
                margin: 0 0 0 0;
            }
            .data_address_contact{
                flex: 1;
                font-family: $font-k2d;
                font-weight: 300;
                font-size: .85vw;
                white-space: normal;
                color: #676767;
                padding: 0 15px;
                margin: 0 0 0 0;
            }
            .action_address_contact{
                @extend .d-flex-all-center;
                button{
                    background-color: transparent;
                    border: none;
                    font-family: $font-k2d;
                    font-weight: 300;
                    font-size: .85vw;
                    white-space: normal;
                    color: #676767;
                    padding: 0 0;
                    margin: 0 0 0 0;
                    cursor: pointer;
                }
            }
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        .inner {
            padding: 5px 10px;
            .item_address_contact {
                .title_address_contact {
                    font-size: 1.6vw;
                }
                .data_address_contact {
                    font-size: 1.4vw;
                }
                .action_address_contact {
                    button {
                        font-size: 1.4vw;
                    }
                }
            }
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        .inner {
            padding: 5px 10px;
            .item_address_contact {
                .title_address_contact {
                    font-size: 4vw;
                    width: 100%;
                }
                .data_address_contact {
                    font-size: 3.2vw;
                    padding: 0 15px 0 0;
                }
                .action_address_contact {
                    button {
                        font-size: 3.2vw;
                    }
                }
            }
        }
    }
}




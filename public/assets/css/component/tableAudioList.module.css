@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.block_table_audio .inner_table_audio .scroll table {
  width: 100%;
  border-collapse: collapse;
}

.block_table_audio .inner_table_audio .scroll table thead tr th {
  font-family: "K2D";
  text-align: left;
  font-weight: 500;
  font-size: 1vw;
  color: #648D2F;
  padding: 10px 10px;
  border-top: 1px solid #648D2F !important;
  border-bottom: 1px solid #648D2F !important;
  text-align: center;
}

.block_table_audio .inner_table_audio .scroll table thead tr th:nth-child(1) {
  text-align: left;
}

.block_table_audio .inner_table_audio .scroll table tbody tr {
  border-bottom: 1px solid #648D2F !important;
  vertical-align: middle;
  transition: .3s;
}

.block_table_audio .inner_table_audio .scroll table tbody tr td {
  font-family: "Kanit";
  font-weight: 400;
  font-size: .9vw;
  color: #656565;
  padding: 10px 10px;
}

.block_table_audio .inner_table_audio .scroll table tbody tr td.status {
  text-align: left;
  display: flex;
  align-items: center;
}

.block_table_audio .inner_table_audio .scroll table tbody tr td.status i {
  font-size: 1.4vw;
  margin: 0 0 0 5px;
  opacity: 0;
}

.block_table_audio .inner_table_audio .scroll table tbody tr td.time {
  text-align: center;
}

.block_table_audio .inner_table_audio .scroll table tbody tr td.action {
  text-align: center;
}

.block_table_audio .inner_table_audio .scroll table tbody tr td.action i {
  font-size: 1.4vw;
}

.block_table_audio .inner_table_audio .scroll table tbody tr td.action span {
  background-color: #94C120;
  font-size: 1vw;
  text-align: center;
  color: #fff;
  border-radius: 8px;
  padding: 2px 5px;
}

.block_table_audio .inner_table_audio .scroll table tbody tr.audio_active td.status {
  color: #648D2F;
}

.block_table_audio .inner_table_audio .scroll table tbody tr.audio_active td.status i {
  opacity: 1;
}

.block_table_audio .inner_table_audio .scroll table tbody tr.audio_active td.time {
  color: #94C120;
}

.block_table_audio .inner_table_audio .scroll table tbody tr.audio_active td.action {
  color: #94C120;
}

@media only screen and (min-width: 1200px) and (max-width: 1479px) {
  .block_table_audio .inner_table_audio .scroll table thead tr th {
    font-size: 1.2vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td {
    font-size: 1.2vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.status i {
    font-size: 1.6vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.action i {
    font-size: 1.6vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.action span {
    font-size: 1.2vw;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .block_table_audio .inner_table_audio .scroll table thead tr th {
    font-size: 1.55vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td {
    font-size: 1.55vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.status i {
    font-size: 1.95vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.action i {
    font-size: 1.95vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.action span {
    font-size: 1.55vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .block_table_audio .inner_table_audio .scroll table thead tr th {
    font-size: 1.85vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td {
    font-size: 1.85vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.status i {
    font-size: 2.35vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.action i {
    font-size: 2.35vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.action span {
    font-size: 1.85vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .block_table_audio .inner_table_audio .scroll table thead tr th {
    font-size: 4vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td {
    font-size: 4vw;
    padding: 10px 0;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.status i {
    font-size: 4.3vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.action i {
    font-size: 4.3vw;
  }
  .block_table_audio .inner_table_audio .scroll table tbody tr td.action span {
    font-size: 4vw;
  }
}
/*# sourceMappingURL=tableAudioList.module.css.map */
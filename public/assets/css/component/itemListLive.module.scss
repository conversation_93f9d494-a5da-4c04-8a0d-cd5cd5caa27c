@import '../var';
.item_action_title{
    p{
      text-align: center;
      font-family: $font-kanit;
      font-weight: 400;
      font-size: .9vw;
      white-space: normal;
      margin: 0 0 0 0;
      color:#000;
      @media only screen and (min-width: 992px) and (max-width: 1199px) {
        font-size: 1vw;
        margin-bottom: 0.8vw;
      }
      @media only screen and (min-width: 768px) and (max-width: 991px) {
        font-size: 1.5vw;
        margin-bottom: 1vw;
      }
      @media only screen and (min-width: 250px) and (max-width: 767px) {
        font-size: 3.5vw;
      }
    }
}
.item_action_time{
    p{
      font-family: $font-kanit;
      font-weight: 400;
      font-size: .9vw;
      white-space: normal;
      margin: 0 0 0 0;
      color:#000;
      text-align: center;
      @media only screen and (min-width: 992px) and (max-width: 1199px) {
        font-size: 1vw;
        margin-top: 0.8vw;
      }
      @media only screen and (min-width: 768px) and (max-width: 991px) {
        font-size: 1.5vw;
        margin-top: 1vw;
      }
      @media only screen and (min-width: 250px) and (max-width: 767px) {
        font-size: 2.8vw;
      }
    }
}
.item_list_course{
    padding: 15px 0;
    width: 100%;
    // border-top: 1px solid #648D2F;
    .item_course_inner{
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        .item_course_img{
            .thumb{
                width: 12vw;
                height: 8vw;
                object-fit: cover;
                border-radius: 8px;
            }
            .blockLisCourseImgSize{
                width: 100%;
                height: 100%;
                span{
                    position: relative !important;
                    .thumb{
                        width: 12vw !important;
                        height: auto !important;
                        position: relative !important;
                    }
                }
            }
        }
        
        .item_course_text{
            flex: 1;
            color: #648D2F;
            padding: 25px;
            h3{
                font-family: $font-kanit;
                font-weight: 500;
                font-size: 1.1vw;
                white-space: normal;
                margin: 0 0 10px 0;
            }
            h4{
                font-family: $font-kanit;
                font-weight: 400;
                font-size: .9vw;
                white-space: normal;
                margin: 0 0 0 0;
                a{
                    color: #94C120;
                    text-decoration: underline;
                }
            }
        }
        .item_course_action{
            .action{
                display: flex;
                align-items: center;
                justify-content: center;
                flex-wrap: wrap;
                background-color: transparent;
                color: #648D2F;
                box-shadow: none;
                outline: none;
                border: none;
                cursor: pointer;
                span{
                    font-family: $font-kanit;
                    font-weight: 500;
                    font-size: 1.2vw;
                }
                i{
                    font-size: 1vw;
                    margin: 0 10px 0 0;
                }
            }
            .percent{
                font-family: $font-kanit;
                font-weight: 500;
                font-size: 1.2vw;
                color: #648D2F;
                text-align: right;
                margin-top:0.5vw;
            }
        }
        .item_live_action{
            @media only screen and (min-width: 290px) and (max-width: 767px) {
                margin-top:3vw;
            }
            .action{
                display: flex;
                // align-items: center;
                justify-content: center;
                flex-wrap: wrap;
                flex-direction: column;
                background-color: transparent;
                box-shadow: none;
                outline: none;
                border: none;
                cursor: pointer;
                width: fit-content;
                margin:0.5vw auto 0.5vw auto;
                border-radius: 0.55vw;
                color:#fff;
                background-color: #9fa09e;
                padding:0 0.8vw 0.2vw 2.5vw;
                @media only screen and (min-width: 992px) and (max-width: 1199px) {
                    padding:0 0.8vw 0.2vw 3.8vw;
                }
                @media only screen and (min-width: 768px) and (max-width: 991px) {
                    padding:0 0.8vw 0.2vw 3.5vw;
                }
                @media only screen and (min-width: 768px) and (max-width: 991px) {
                    padding:0 0.8vw 0.2vw 4.7vw;
                }
                @media only screen and (min-width: 290px) and (max-width: 767px) {
                    margin:1.25vw auto 1.25vw auto;
                    border-radius: 1.25vw;
                    padding:0 0.8vw 0.2vw 7vw;
                }
                position: relative;
                .play_stream{
                    border:2px solid #fff;
                    border-radius: 50%;
                    position: absolute;
                    left: -0.5vw;
                    width: 2.5vw;
                    height: 2.5vw;
                    @media only screen and (min-width: 992px) and (max-width: 1199px) {
                        width: 3.7vw;
                        height: 3.7vw;
                    }
                    @media only screen and (min-width: 768px) and (max-width: 991px) {
                        width: 3.5vw;
                        height: 3.5vw;
                    }
                    @media only screen and (min-width: 768px) and (max-width: 991px) {
                        width: 4.5vw;
                        height: 4.5vw;
                    }
                    @media only screen and (min-width: 290px) and (max-width: 767px) {
                        width: 7.5vw;
                        height: 7.5vw;
                        left: -1.5vw;
                    }
                    background-color: #9fa09e;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    top: 0;
                    bottom: 0;
                    margin: auto 0;
                    i{
                        margin:0;
                    }
                }
                &.active{
                    background-color: #d00;
                    .play_stream{
                        background-color: #d00;
                    }
                }
                h3{
                    font-family: $font-kanit;
                    font-weight: 500;
                    font-size: 1.2vw;
                    @media only screen and (min-width: 992px) and (max-width: 1199px) {
                        font-size: 1.8vw;
                    }
                    @media only screen and (min-width: 768px) and (max-width: 991px) {
                        font-size: 2vw;
                    }
                    margin:0;
                    line-height: 1;
                    @media only screen and (min-width: 290px) and (max-width: 767px) {
                        font-size: 3.5vw;
                    }
                }
                p{
                    font-family: $font-kanit;
                    font-weight: 500;
                    font-size: 0.2vw;
                    margin:0;
                    line-height: 1;
                    @media only screen and (min-width: 290px) and (max-width: 767px) {
                        font-size: 0.5vw;
                    }
                }
                i{
                    font-size: 1vw;
                    margin: 0 10px 0 0;
                    @media only screen and (min-width: 290px) and (max-width: 767px) {
                        font-size: 3vw;
                    }
                }
            }
        }
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        .item_course_inner {
            .item_course_img {
                .thumb {
                    width: 16vw;
                    height: 10vw;
                }
                .blockLisCourseImgSize{
                    span{
                        .thumb{
                            width: 16vw !important;
                        }
                    }
                }
            }
            .item_course_text {
                padding: 20px;
                h3 {
                    font-size: 1.4vw;
                }
                h4 {
                    font-size: 1.2vw;
                }
            }
            .item_course_action {
                .action {
                    span {
                        font-size: 1.6vw;
                    }
                    i {
                        font-size: 1.4vw;
                    }
                }
                .percent{
                    font-size: 1.6vw;
                    margin-top:0.8vw;
                }
            }
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        .item_course_inner {
            .item_course_img {
                .thumb {
                    width: 16vw;
                    height: 10vw;
                }
                .blockLisCourseImgSize{
                    span{
                        .thumb{
                            width: 16vw !important;
                        }
                    }
                }
            }
            .item_course_text {
                padding: 20px;
                h3 {
                    font-size: 1.8vw;
                }
                h4 {
                    font-size: 1.6vw;
                }
            }
            .item_course_action {
                .action {
                    span {
                        font-size: 1.6vw;
                    }
                    i {
                        font-size: 1.4vw;
                    }
                }
                .percent{
                    font-size: 1.6vw;
                    margin-top:0.8vw;
                }
            }
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        padding: 3vw 0;
        .item_course_inner {
            .item_course_img {
                .thumb {
                    width: 26vw;
                    height: 20vw;
                }
                .blockLisCourseImgSize{
                    span{
                        .thumb{
                            width: 26vw !important;
                        }
                    }
                }
            }
            .item_course_text {
                padding: 0 0 0 4vw;
                h3 {
                    font-size: 4vw;
                    margin: 0 0 5px 0;
                }
                h4 {
                    font-size: 3.2vw;
                }
            }
            .item_course_action {
                .action {
                    padding: 0 0 0 3vw;
                    span {
                        font-size: 3vw;
                    }
                    i {
                        font-size: 3vw;
                    }
                }
                .percent{
                    font-size: 3vw;
                    margin-top:1.5vw;
                }
            }
        }
    }
}
@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center, .block_pop_fm, .block_pop_fm .inner_pop_fm, .block_pop_fm .inner_pop_fm .col_fm_btn, .block_pop_fm .inner_pop_fm .col_fm_btn .btn_submit, .block_pop_fm .inner_pop_fm .way_group_btn, .block_pop_fm .inner_pop_fm .way_group_btn .btn_way {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.block_pop_fm {
  width: 100%;
}

.block_pop_fm .inner_pop_fm {
  width: 100%;
  max-width: 60%;
  flex-direction: column;
}

.block_pop_fm .inner_pop_fm .logo {
  margin: 0 0 15px 0;
}

.block_pop_fm .inner_pop_fm .logo .thumb img {
  height: 6.8vw;
}

.block_pop_fm .inner_pop_fm .logo .icon i {
  font-size: 7.8vw;
  color: #648D2F;
}

.block_pop_fm .inner_pop_fm .col_fm {
  width: 100%;
  margin: 5px 0;
}

.block_pop_fm .inner_pop_fm .col_fm_btn {
  width: 100%;
  flex-wrap: wrap;
  padding: 0 15px;
}

.block_pop_fm .inner_pop_fm .col_fm_btn .btn_submit {
  background-color: #648D2F;
  width: 100%;
  font-family: "Kanit";
  font-size: 0.938vw;
  font-weight: normal;
  color: #fff;
  border: none;
  border-radius: 12px;
  padding: 10px;
  margin: 15px 0 5px 0;
  cursor: pointer;
}

.block_pop_fm .inner_pop_fm .col_fm_btn .btn_forget {
  background-color: transparent;
  font-family: "Kanit";
  font-size: 0.938vw;
  color: #648D2F;
  margin: 0 0 0 auto;
  box-shadow: none;
  border: none;
  cursor: pointer;
}

.block_pop_fm .inner_pop_fm .or {
  position: relative;
  width: 100%;
  text-align: center;
  padding: 0 15px;
  margin: 10px 0 0 0;
}

.block_pop_fm .inner_pop_fm .or .inner {
  width: 100%;
  position: relative;
}

.block_pop_fm .inner_pop_fm .or .inner::before {
  top: 50%;
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #648D2F;
}

.block_pop_fm .inner_pop_fm .or span {
  position: relative;
  text-align: center;
  font-family: "Kanit";
  font-size: 1vw;
  color: #648D2F;
  background-color: #fff;
  padding: 0 15px;
  z-index: 2;
}

.block_pop_fm .inner_pop_fm .way_group_btn {
  width: 100%;
  margin: 15px 0 15px 0;
}

.block_pop_fm .inner_pop_fm .way_group_btn .btn_way {
  border: 0;
  background-color: transparent;
  margin: 0 0 0 0;
  cursor: pointer;
}

.block_pop_fm .inner_pop_fm .way_group_btn .btn_way img {
  height: 2.4vw;
  border-radius: 50%;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .block_pop_fm .inner_pop_fm {
    max-width: 60%;
  }
  .block_pop_fm .inner_pop_fm .logo {
    margin: 0 0 15px 0;
  }
  .block_pop_fm .inner_pop_fm .logo .thumb img {
    height: 9vw;
  }
  .block_pop_fm .inner_pop_fm .logo .icon i {
    font-size: 10vw;
  }
  .block_pop_fm .inner_pop_fm .col_fm {
    width: 100%;
    margin: 5px 0;
  }
  .block_pop_fm .inner_pop_fm .col_fm_btn {
    padding: 0 15px;
  }
  .block_pop_fm .inner_pop_fm .col_fm_btn .btn_submit {
    font-size: 1.8vw;
    border-radius: 12px;
    padding: 10px;
    margin: 15px 0 5px 0;
  }
  .block_pop_fm .inner_pop_fm .col_fm_btn .btn_forget {
    font-size: 1.6vw;
  }
  .block_pop_fm .inner_pop_fm .or {
    padding: 0 15px;
    margin: 10px 0 0 0;
  }
  .block_pop_fm .inner_pop_fm .or .inner::before {
    top: 50%;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
  }
  .block_pop_fm .inner_pop_fm .or span {
    font-size: 1.6vw;
    padding: 0 15px;
  }
  .block_pop_fm .inner_pop_fm .way_group_btn {
    margin: 15px 0 15px 0;
  }
  .block_pop_fm .inner_pop_fm .way_group_btn .btn_way {
    border: 0;
    margin: 0 0 0 0;
  }
  .block_pop_fm .inner_pop_fm .way_group_btn .btn_way img {
    height: 3.6vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .block_pop_fm .inner_pop_fm {
    max-width: 70%;
  }
  .block_pop_fm .inner_pop_fm .logo {
    margin: 0 0 15px 0;
  }
  .block_pop_fm .inner_pop_fm .logo .thumb img {
    height: 9vw;
  }
  .block_pop_fm .inner_pop_fm .logo .icon i {
    font-size: 10vw;
  }
  .block_pop_fm .inner_pop_fm .col_fm {
    width: 100%;
    margin: 5px 0;
  }
  .block_pop_fm .inner_pop_fm .col_fm_btn {
    padding: 0 15px;
  }
  .block_pop_fm .inner_pop_fm .col_fm_btn .btn_submit {
    font-size: 1.8vw;
    border-radius: 8px;
    padding: 10px;
    margin: 15px 0 5px 0;
  }
  .block_pop_fm .inner_pop_fm .col_fm_btn .btn_forget {
    font-size: 1.6vw;
  }
  .block_pop_fm .inner_pop_fm .or {
    padding: 0 15px;
    margin: 10px 0 0 0;
  }
  .block_pop_fm .inner_pop_fm .or .inner::before {
    top: 50%;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
  }
  .block_pop_fm .inner_pop_fm .or span {
    font-size: 1.6vw;
    padding: 0 15px;
  }
  .block_pop_fm .inner_pop_fm .way_group_btn {
    margin: 15px 0 15px 0;
  }
  .block_pop_fm .inner_pop_fm .way_group_btn .btn_way {
    border: 0;
    margin: 0 0 0 0;
  }
  .block_pop_fm .inner_pop_fm .way_group_btn .btn_way img {
    height: 3.6vw;
  }
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
  .block_pop_fm .inner_pop_fm {
    max-width: 85%;
  }
  .block_pop_fm .inner_pop_fm .logo {
    margin: 0 0 15px 0;
  }
  .block_pop_fm .inner_pop_fm .logo .thumb img {
    height: 24vw;
  }
  .block_pop_fm .inner_pop_fm .logo .icon i {
    font-size: 22vw;
  }
  .block_pop_fm .inner_pop_fm .col_fm {
    width: 100%;
    margin: 5px 0;
  }
  .block_pop_fm .inner_pop_fm .col_fm_btn {
    padding: 0 15px;
  }
  .block_pop_fm .inner_pop_fm .col_fm_btn .btn_submit {
    font-size: 3.8vw;
    border-radius: 8px;
    padding: 10px;
    margin: 15px 0 5px 0;
  }
  .block_pop_fm .inner_pop_fm .col_fm_btn .btn_forget {
    font-size: 3.6vw;
  }
  .block_pop_fm .inner_pop_fm .or {
    padding: 0 15px;
    margin: 10px 0 0 0;
  }
  .block_pop_fm .inner_pop_fm .or .inner::before {
    top: 50%;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
  }
  .block_pop_fm .inner_pop_fm .or span {
    font-size: 3.6vw;
    padding: 0 10px;
  }
  .block_pop_fm .inner_pop_fm .way_group_btn {
    margin: 15px 0 15px 0;
  }
  .block_pop_fm .inner_pop_fm .way_group_btn .btn_way {
    border: 0;
    margin: 0 0 0 0;
  }
  .block_pop_fm .inner_pop_fm .way_group_btn .btn_way img {
    height: 8vw;
  }
}
/*# sourceMappingURL=formPop.module.css.map */
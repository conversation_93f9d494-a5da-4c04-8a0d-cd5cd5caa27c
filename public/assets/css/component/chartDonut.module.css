@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.block_chart_info {
  width: 100%;
}

.block_chart_info .inner_chart {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
  flex-wrap: wrap;
}

.block_chart_info .inner_chart .chart_info_img {
  position: relative;
  width: 12vw;
  display: flex;
  align-items: center;
  justify-content: center;
}

.block_chart_info .inner_chart .chart_info_img .chart_info_number {
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
}

.block_chart_info .inner_chart .chart_info_img .chart_info_number .number {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  position: relative;
  top: 0.35vw;
  border: 0.4vw solid #9f9f9f;
  width: 82%;
  height: 82%;
  border-radius: 50%;
}

.block_chart_info .inner_chart .chart_info_img .chart_info_number .number span {
  font-family: "Kanit";
  font-weight: 500;
  font-size: 2.6vw;
  color: #648D2F;
}

.block_chart_info .inner_chart .chart_info_img canvas {
  pointer-events: none;
}

.block_chart_info .inner_chart .chart_info_text {
  flex: 1;
  padding: 0 30px;
  color: #676767;
}

.block_chart_info .inner_chart .chart_info_text h3 {
  font-family: "Kanit";
  font-weight: 500;
  font-size: 1.1vw;
  white-space: normal;
  margin: 0 0 10px 0;
}

.block_chart_info .inner_chart .chart_info_text h4 {
  font-family: "K2D";
  font-weight: 400;
  font-size: .9vw;
  white-space: normal;
  margin: 0 0 0 0;
}

@media only screen and (min-width: 1200px) and (max-width: 1479px) {
  .block_chart_info .inner_chart .chart_info_text h3 {
    font-size: 2vw;
  }
  .block_chart_info .inner_chart .chart_info_text h4 {
    font-size: 1.1vw;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .block_chart_info .inner_chart .chart_info_img {
    width: 17vw;
  }
  .block_chart_info .inner_chart .chart_info_img .chart_info_number .number span {
    font-size: 4vw;
  }
  .block_chart_info .inner_chart .chart_info_text h3 {
    font-size: 2.5vw;
  }
  .block_chart_info .inner_chart .chart_info_text h4 {
    font-size: 1.6vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .block_chart_info .inner_chart .chart_info_img {
    width: 25vw;
  }
  .block_chart_info .inner_chart .chart_info_img .chart_info_number .number span {
    font-size: 4.5vw;
  }
  .block_chart_info .inner_chart .chart_info_text h3 {
    font-size: 3vw;
  }
  .block_chart_info .inner_chart .chart_info_text h4 {
    font-size: 2vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .block_chart_info .inner_chart {
    flex-direction: column;
  }
  .block_chart_info .inner_chart .chart_info_img {
    width: 40vw;
    margin-bottom: 20px;
  }
  .block_chart_info .inner_chart .chart_info_img .chart_info_number .number {
    top: 1.1vw;
    width: 75%;
    height: 75%;
  }
  .block_chart_info .inner_chart .chart_info_img .chart_info_number .number span {
    font-size: 7.8vw;
  }
  .block_chart_info .inner_chart .chart_info_text {
    padding: 0;
  }
  .block_chart_info .inner_chart .chart_info_text h3 {
    font-size: 5vw;
  }
  .block_chart_info .inner_chart .chart_info_text h4 {
    font-size: 4vw;
  }
}
/*# sourceMappingURL=chartDonut.module.css.map */
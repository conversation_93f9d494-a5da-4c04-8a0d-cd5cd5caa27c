@import '../var';
 

.choose_type_data_list{
    padding: 0;
    background-color: #fff;
    border-radius: 12px;
    ul{
        list-style: none;
        padding: 0.8vw 0;
        margin: 0 0 0 0;
        li{
            position: relative;
            padding: 5px 15px 5px 30px;
            margin:  20px 0;
            cursor: pointer;
            &::before{
                opacity: 0;
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 0.52vw;
                height: 100%;
                background-color: #648D2F;
                transition: .3s;
            }
            span{
                font-family: $font-kanit;
                font-weight: 500;
                font-size: 1vw;
                color: #BCBCBC;
                transition: .3s;
            }
            &.active, &:hover{
                &::before{
                    opacity: 1;
                }
                span{
                    color: #648D2F;
                }
            }
        }
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        ul {
            li {
                margin: 15px 0;
                span {
                    font-size: 1.4vw;
                }
            }
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        ul {
            li {
                span {
                    font-size: 1.6vw;
                }
            }
        }
    }
}


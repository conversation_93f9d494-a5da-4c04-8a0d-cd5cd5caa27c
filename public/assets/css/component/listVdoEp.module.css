@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.groupTitle {
  padding-bottom: 20px;
}

.blockPlayer {
  margin: 0 auto;
  justify-content: center;
  display: flex;
}

.list {
  padding-top: 15px;
  cursor: pointer;
}

.list.active {
  background-color: #f4f4f4;
}

.list .description {
  font-size: 0.8vw !important;
  font-family: "K2D";
  font-weight: 100;
}

.list .text_right {
  display: flex;
  justify-content: end;
}

@media only screen and (min-width: 1200px) and (max-width: 1479px) {
  .list .description {
    font-size: 1.0vw !important;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .list .description {
    font-size: 1.2vw !important;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .list .description {
    font-size: 1.5vw !important;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .list .description {
    font-size: 3.2vw !important;
  }
}

.videoThumbDiv span {
  position: relative !important;
  max-width: 100% !important;
}

.videoThumbDiv span img {
  width: 100% !important;
  position: relative !important;
  height: auto !important;
}

.iconTestDiv {
  display: flex;
}

.iconTestDiv span {
  width: 1.5vw !important;
  margin-right: 0.3vw !important;
}

@media only screen and (min-width: 1200px) and (max-width: 1479px) {
  .iconTestDiv span {
    width: 1.8vw !important;
    margin-right: 0.5vw !important;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .iconTestDiv span {
    width: 2vw !important;
    margin-right: 0.5vw !important;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .iconTestDiv span {
    width: 2.6vw !important;
    margin-right: 0.7vw !important;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .iconTestDiv span {
    width: 6.2vw !important;
    margin-right: 2vw !important;
  }
}
/*# sourceMappingURL=listVdoEp.module.css.map */
@import '../var';
 

.item_list_chapter{
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-wrap: wrap;
    width: 100%;
    font-family: $font-kanit;
    color: #9F9F9F;
    font-weight: 400;
    font-size: 1vw;
    white-space: normal;
    margin: 0 0 0 0;
    .icon_chapter{
        margin: 3px 5px 0 0;
        i{
            font-size: 1.2vw;
            color: #9F9F9F;
        }
    }
    .number_chapter{
        color: #9F9F9F;
        margin: 0 5px 0 0;
    }
    .title_chapter{
        flex: 1;
    }
    .time_chapter{
    }
    @media only screen and (min-width: 1200px) and (max-width: 1479px) {
        font-size: 1.35vw;
        .icon_chapter {
            i {
                font-size: 1.35vw;
            }
        }
        .number_chapter {
            font-size: 1.35vw;
        }
        .title_chapter {
            font-size: 1.35vw;
        }
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        font-size: 1.7vw;
        .icon_chapter {
            i {
                font-size: 1.7vw;
            }
        }
        .number_chapter {
            font-size: 1.7vw;
        }
        .title_chapter {
            font-size: 1.7vw;
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        font-size: 2vw;
        .icon_chapter {
            i {
                font-size: 2vw;
            }
        }
        .number_chapter {
            font-size: 2vw;
        }
        .title_chapter {
            font-size: 2vw;
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        font-size: 3.7vw;
        .icon_chapter {
            margin: 1.867vw 10px 0 0;
            i {
                font-size: 3.9vw;
            }
        }
        .number_chapter {
            font-size: 5vw;
            width: 80%;
            margin-bottom: 5px;
        }
        .title_chapter {
            font-size: 4.2vw;
            margin: 0 0 10px 6.667vw;
        }
    }
}

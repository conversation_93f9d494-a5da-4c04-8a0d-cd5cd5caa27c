@import '../var';

.homeSponsors {
  background-color: #FFFFFF;
  padding: 30px 0;
  @media only screen and (min-width: 250px) and (max-width: 767px) {
    margin: 0 -15px;
  }
}

.inner {

}

.title {
  padding: 0 20px;
  margin-bottom: 25px;
  h3 {
    color: #6E953D;
    font-size: 1.563vw;
    font-family: "Kanit";
    font-weight: 500;
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
      font-size: 2.1vw;
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
      font-size: 2.6vw;
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
      font-size: 6vw;
    }
  }
}

.logos {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  @media only screen and (min-width: 250px) and (max-width: 767px) {
    padding: 0 15px;
  }
}

.imgDiv {
  margin: 0 1.5vw 15px 1.5vw;
  @media only screen and (min-width: 250px) and (max-width: 767px) {
    margin: 0 2vw 10px 2vw; 
  }
  img {
    width: 100%;
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
      max-height: 4.5vw;
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
      max-height: 5vw;
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
      
    }
  }
}
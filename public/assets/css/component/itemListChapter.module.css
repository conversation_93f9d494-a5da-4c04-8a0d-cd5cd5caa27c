@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.item_list_chapter {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  width: 100%;
  font-family: "Kanit";
  color: #676767;
  font-weight: 400;
  font-size: 1vw;
  white-space: normal;
  margin: 0 0 0 0;
}

.item_list_chapter .icon_chapter {
  margin: 3px 5px 0 0;
}

.item_list_chapter .icon_chapter i {
  font-size: 1.2vw;
  color: #648D2F;
}

.item_list_chapter .number_chapter {
  color: #648D2F;
  margin: 0 5px 0 0;
}

.item_list_chapter .title_chapter {
  flex: 1;
}

@media only screen and (min-width: 1200px) and (max-width: 1479px) {
  .item_list_chapter {
    font-size: 1.35vw;
  }
  .item_list_chapter .icon_chapter i {
    font-size: 1.35vw;
  }
  .item_list_chapter .number_chapter {
    font-size: 1.35vw;
  }
  .item_list_chapter .title_chapter {
    font-size: 1.35vw;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .item_list_chapter {
    font-size: 1.7vw;
  }
  .item_list_chapter .icon_chapter i {
    font-size: 1.7vw;
  }
  .item_list_chapter .number_chapter {
    font-size: 1.7vw;
  }
  .item_list_chapter .title_chapter {
    font-size: 1.7vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .item_list_chapter {
    font-size: 2vw;
  }
  .item_list_chapter .icon_chapter i {
    font-size: 2vw;
  }
  .item_list_chapter .number_chapter {
    font-size: 2vw;
  }
  .item_list_chapter .title_chapter {
    font-size: 2vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .item_list_chapter {
    font-size: 3.7vw;
  }
  .item_list_chapter .icon_chapter {
    margin: 1.867vw 10px 0 0;
  }
  .item_list_chapter .icon_chapter i {
    font-size: 3.9vw;
  }
  .item_list_chapter .number_chapter {
    font-size: 5vw;
    width: 80%;
    margin-bottom: 5px;
  }
  .item_list_chapter .title_chapter {
    font-size: 4.2vw;
    margin: 0 0 10px 6.667vw;
  }
}
/*# sourceMappingURL=itemListChapter.module.css.map */
@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.homeSponsors {
  background-color: #FFFFFF;
  padding: 30px 0;
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .homeSponsors {
    margin: 0 -15px;
  }
}

.title {
  padding: 0 20px;
  margin-bottom: 25px;
}

.title h3 {
  color: #6E953D;
  font-size: 1.563vw;
  font-family: "Kanit";
  font-weight: 500;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .title h3 {
    font-size: 2.1vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .title h3 {
    font-size: 2.6vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .title h3 {
    font-size: 6vw;
  }
}

.logos {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .logos {
    padding: 0 15px;
  }
}

.imgDiv {
  margin: 0 1.5vw 15px 1.5vw;
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .imgDiv {
    margin: 0 2vw 10px 2vw;
  }
}

.imgDiv img {
  width: 100%;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .imgDiv img {
    max-height: 4.5vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .imgDiv img {
    max-height: 5vw;
  }
}
/*# sourceMappingURL=homeSponsors.module.css.map */
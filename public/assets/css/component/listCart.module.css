@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.block_list_cart .list_inner_cart {
  border-bottom: 1px solid #648D2F;
}

.block_list_cart .list_inner_cart .item_cart {
  display: flex;
  justify-content: center;
  align-items: stretch;
  flex-wrap: nowrap;
  flex-direction: row;
  padding: 15px 0;
  border-top: 1px solid #648D2F;
}

.block_list_cart .list_inner_cart .item_cart .cart_img {
  width: 30%;
}

.block_list_cart .list_inner_cart .item_cart .cart_img img {
  max-width: 100%;
}

.block_list_cart .list_inner_cart .item_cart .cart_content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  padding: 0 15px;
}

.block_list_cart .list_inner_cart .item_cart .cart_content h3 {
  font-family: "Kanit";
  font-weight: 500;
  font-size: 1.2vw;
  color: #648D2F;
  white-space: normal;
  margin: 0 0 0 0;
}

.block_list_cart .list_inner_cart .item_cart .cart_content h4 {
  font-family: "K2D";
  font-weight: 500;
  font-size: .85vw;
  color: #648D2F;
  white-space: normal;
  margin: 0 0 0 0;
}

.block_list_cart .list_inner_cart .item_cart .cart_content p {
  font-family: "Kanit";
  font-weight: 400;
  font-size: 1.1vw;
  color: #648D2F;
  white-space: normal;
  margin: 0 0 0 0;
}

.block_list_cart .list_inner_cart .item_cart .cart_action {
  display: flex;
  align-items: center;
}

.block_list_cart .list_inner_cart .item_cart .cart_action .btn_cart_action {
  background-color: transparent;
  border: none;
  cursor: pointer;
}

.block_list_cart .list_inner_cart .item_cart .cart_action .btn_cart_action i {
  font-size: 1.2vw;
  color: #9F9F9F;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .block_list_cart .list_inner_cart .item_cart {
    padding: 15px 0;
    border-top-width: 1px;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_img {
    width: 30%;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_img img {
    max-width: 100%;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content {
    padding: 0 15px;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content h3 {
    font-size: 1.8vw;
    margin: 0 0 0 0;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content h4 {
    font-size: 1.6vw;
    margin: 0 0 0 0;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content p {
    font-size: 1.4vw;
    margin: 0 0 0 0;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_action .btn_cart_action i {
    font-size: 1.8vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .block_list_cart .list_inner_cart .item_cart {
    padding: 15px 0;
    border-top-width: 1px;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_img {
    width: 30%;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_img img {
    max-width: 100%;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content {
    padding: 0 15px;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content h3 {
    font-size: 1.8vw;
    margin: 0 0 0 0;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content h4 {
    font-size: 1.6vw;
    margin: 0 0 0 0;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content p {
    font-size: 1.4vw;
    margin: 0 0 0 0;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_action .btn_cart_action i {
    font-size: 1.8vw;
  }
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
  .block_list_cart .list_inner_cart .item_cart {
    padding: 15px 0;
    border-top-width: 1px;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_img {
    width: 30%;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_img img {
    max-width: 100%;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content {
    padding: 0 10px;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content h3 {
    font-size: 4.4vw;
    margin: 0 0 0 0;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content h4 {
    font-size: 4.2vw;
    margin: 0 0 0 0;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_content p {
    font-size: 4vw;
    margin: 0 0 0 0;
  }
  .block_list_cart .list_inner_cart .item_cart .cart_action .btn_cart_action i {
    font-size: 5.8vw;
  }
}
/*# sourceMappingURL=listCart.module.css.map */
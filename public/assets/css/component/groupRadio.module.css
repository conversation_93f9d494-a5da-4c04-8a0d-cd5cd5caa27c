@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.radio_title {
  font-family: "Kanit";
  font-size: 1.563vw;
  font-weight: 400;
  color: #648D2F;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .radio_title {
    font-size: 1.8vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .radio_title {
    font-size: 4vw;
  }
}

.group_margin {
  margin-top: 1vw;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .group_margin {
    margin-top: 2vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .group_margin {
    margin-top: 4vw;
  }
}
/*# sourceMappingURL=groupRadio.module.css.map */
@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.item_action_title p {
  text-align: center;
  font-family: "Kanit";
  font-weight: 400;
  font-size: .9vw;
  white-space: normal;
  margin: 0 0 0 0;
  color: #000;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .item_action_title p {
    font-size: 1vw;
    margin-bottom: 0.8vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .item_action_title p {
    font-size: 1.5vw;
    margin-bottom: 1vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .item_action_title p {
    font-size: 3.5vw;
  }
}

.item_action_time p {
  font-family: "Kanit";
  font-weight: 400;
  font-size: .9vw;
  white-space: normal;
  margin: 0 0 0 0;
  color: #000;
  text-align: center;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .item_action_time p {
    font-size: 1vw;
    margin-top: 0.8vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .item_action_time p {
    font-size: 1.5vw;
    margin-top: 1vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .item_action_time p {
    font-size: 2.8vw;
  }
}

.item_list_course {
  padding: 15px 0;
  width: 100%;
}

.item_list_course .item_course_inner {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.item_list_course .item_course_inner .item_course_img .thumb {
  width: 12vw;
  height: 8vw;
  object-fit: cover;
  border-radius: 8px;
}

.item_list_course .item_course_inner .item_course_img .blockLisCourseImgSize {
  width: 100%;
  height: 100%;
}

.item_list_course .item_course_inner .item_course_img .blockLisCourseImgSize span {
  position: relative !important;
}

.item_list_course .item_course_inner .item_course_img .blockLisCourseImgSize span .thumb {
  width: 12vw !important;
  height: auto !important;
  position: relative !important;
}

.item_list_course .item_course_inner .item_course_text {
  flex: 1;
  color: #648D2F;
  padding: 25px;
}

.item_list_course .item_course_inner .item_course_text h3 {
  font-family: "Kanit";
  font-weight: 500;
  font-size: 1.1vw;
  white-space: normal;
  margin: 0 0 10px 0;
}

.item_list_course .item_course_inner .item_course_text h4 {
  font-family: "Kanit";
  font-weight: 400;
  font-size: .9vw;
  white-space: normal;
  margin: 0 0 0 0;
}

.item_list_course .item_course_inner .item_course_text h4 a {
  color: #94C120;
  text-decoration: underline;
}

.item_list_course .item_course_inner .item_course_action .action {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  background-color: transparent;
  color: #648D2F;
  box-shadow: none;
  outline: none;
  border: none;
  cursor: pointer;
}

.item_list_course .item_course_inner .item_course_action .action span {
  font-family: "Kanit";
  font-weight: 500;
  font-size: 1.2vw;
}

.item_list_course .item_course_inner .item_course_action .action i {
  font-size: 1vw;
  margin: 0 10px 0 0;
}

.item_list_course .item_course_inner .item_course_action .percent {
  font-family: "Kanit";
  font-weight: 500;
  font-size: 1.2vw;
  color: #648D2F;
  text-align: right;
  margin-top: 0.5vw;
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
  .item_list_course .item_course_inner .item_live_action {
    margin-top: 3vw;
  }
}

.item_list_course .item_course_inner .item_live_action .action {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  flex-direction: column;
  background-color: transparent;
  box-shadow: none;
  outline: none;
  border: none;
  cursor: pointer;
  width: fit-content;
  margin: 0.5vw auto 0.5vw auto;
  border-radius: 0.55vw;
  color: #fff;
  background-color: #9fa09e;
  padding: 0 0.8vw 0.2vw 2.5vw;
  position: relative;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .item_list_course .item_course_inner .item_live_action .action {
    padding: 0 0.8vw 0.2vw 3.8vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .item_list_course .item_course_inner .item_live_action .action {
    padding: 0 0.8vw 0.2vw 3.5vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .item_list_course .item_course_inner .item_live_action .action {
    padding: 0 0.8vw 0.2vw 4.7vw;
  }
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
  .item_list_course .item_course_inner .item_live_action .action {
    margin: 1.25vw auto 1.25vw auto;
    border-radius: 1.25vw;
    padding: 0 0.8vw 0.2vw 7vw;
  }
}

.item_list_course .item_course_inner .item_live_action .action .play_stream {
  border: 2px solid #fff;
  border-radius: 50%;
  position: absolute;
  left: -0.5vw;
  width: 2.5vw;
  height: 2.5vw;
  background-color: #9fa09e;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0;
  bottom: 0;
  margin: auto 0;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .item_list_course .item_course_inner .item_live_action .action .play_stream {
    width: 3.7vw;
    height: 3.7vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .item_list_course .item_course_inner .item_live_action .action .play_stream {
    width: 3.5vw;
    height: 3.5vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .item_list_course .item_course_inner .item_live_action .action .play_stream {
    width: 4.5vw;
    height: 4.5vw;
  }
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
  .item_list_course .item_course_inner .item_live_action .action .play_stream {
    width: 7.5vw;
    height: 7.5vw;
    left: -1.5vw;
  }
}

.item_list_course .item_course_inner .item_live_action .action .play_stream i {
  margin: 0;
}

.item_list_course .item_course_inner .item_live_action .action.active {
  background-color: #d00;
}

.item_list_course .item_course_inner .item_live_action .action.active .play_stream {
  background-color: #d00;
}

.item_list_course .item_course_inner .item_live_action .action h3 {
  font-family: "Kanit";
  font-weight: 500;
  font-size: 1.2vw;
  margin: 0;
  line-height: 1;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .item_list_course .item_course_inner .item_live_action .action h3 {
    font-size: 1.8vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .item_list_course .item_course_inner .item_live_action .action h3 {
    font-size: 2vw;
  }
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
  .item_list_course .item_course_inner .item_live_action .action h3 {
    font-size: 3.5vw;
  }
}

.item_list_course .item_course_inner .item_live_action .action p {
  font-family: "Kanit";
  font-weight: 500;
  font-size: 0.2vw;
  margin: 0;
  line-height: 1;
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
  .item_list_course .item_course_inner .item_live_action .action p {
    font-size: 0.5vw;
  }
}

.item_list_course .item_course_inner .item_live_action .action i {
  font-size: 1vw;
  margin: 0 10px 0 0;
}

@media only screen and (min-width: 290px) and (max-width: 767px) {
  .item_list_course .item_course_inner .item_live_action .action i {
    font-size: 3vw;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .item_list_course .item_course_inner .item_course_img .thumb {
    width: 16vw;
    height: 10vw;
  }
  .item_list_course .item_course_inner .item_course_img .blockLisCourseImgSize span .thumb {
    width: 16vw !important;
  }
  .item_list_course .item_course_inner .item_course_text {
    padding: 20px;
  }
  .item_list_course .item_course_inner .item_course_text h3 {
    font-size: 1.4vw;
  }
  .item_list_course .item_course_inner .item_course_text h4 {
    font-size: 1.2vw;
  }
  .item_list_course .item_course_inner .item_course_action .action span {
    font-size: 1.6vw;
  }
  .item_list_course .item_course_inner .item_course_action .action i {
    font-size: 1.4vw;
  }
  .item_list_course .item_course_inner .item_course_action .percent {
    font-size: 1.6vw;
    margin-top: 0.8vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .item_list_course .item_course_inner .item_course_img .thumb {
    width: 16vw;
    height: 10vw;
  }
  .item_list_course .item_course_inner .item_course_img .blockLisCourseImgSize span .thumb {
    width: 16vw !important;
  }
  .item_list_course .item_course_inner .item_course_text {
    padding: 20px;
  }
  .item_list_course .item_course_inner .item_course_text h3 {
    font-size: 1.8vw;
  }
  .item_list_course .item_course_inner .item_course_text h4 {
    font-size: 1.6vw;
  }
  .item_list_course .item_course_inner .item_course_action .action span {
    font-size: 1.6vw;
  }
  .item_list_course .item_course_inner .item_course_action .action i {
    font-size: 1.4vw;
  }
  .item_list_course .item_course_inner .item_course_action .percent {
    font-size: 1.6vw;
    margin-top: 0.8vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .item_list_course {
    padding: 3vw 0;
  }
  .item_list_course .item_course_inner .item_course_img .thumb {
    width: 26vw;
    height: 20vw;
  }
  .item_list_course .item_course_inner .item_course_img .blockLisCourseImgSize span .thumb {
    width: 26vw !important;
  }
  .item_list_course .item_course_inner .item_course_text {
    padding: 0 0 0 4vw;
  }
  .item_list_course .item_course_inner .item_course_text h3 {
    font-size: 4vw;
    margin: 0 0 5px 0;
  }
  .item_list_course .item_course_inner .item_course_text h4 {
    font-size: 3.2vw;
  }
  .item_list_course .item_course_inner .item_course_action .action {
    padding: 0 0 0 3vw;
  }
  .item_list_course .item_course_inner .item_course_action .action span {
    font-size: 3vw;
  }
  .item_list_course .item_course_inner .item_course_action .action i {
    font-size: 3vw;
  }
  .item_list_course .item_course_inner .item_course_action .percent {
    font-size: 3vw;
    margin-top: 1.5vw;
  }
}
/*# sourceMappingURL=itemListCourse.module.css.map */
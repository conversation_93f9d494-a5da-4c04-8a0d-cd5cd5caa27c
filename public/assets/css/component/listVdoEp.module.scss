@import '../var';
 

.groupTitle{
    padding-bottom: 20px;
}
.blockPlayer{
    margin: 0 auto;
    justify-content: center;
    display: flex;
} 
.list{
    padding-top: 15px;
    cursor: pointer;
    &.active{
        background-color: #f4f4f4;
    }
    .imgThumb{
    
    }
    .title{

    }
    .description{
        font-size: 0.8vw !important;
        font-family: $font-k2d;
        font-weight: 100;
    }
    .calendar{

    } 
    .text_right{
        display: flex;
        justify-content: end;
    }

    @media only screen and (min-width: 1200px) and (max-width: 1479px) {
        .description{
            font-size: 1.0vw !important;
        }
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        .description{
            font-size: 1.2vw !important;
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        .description{
            font-size: 1.5vw !important;
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        .description{
            font-size: 3.2vw !important;
        }
    }
}
.videoThumbDiv {
    span {
        position: relative !important;
        max-width: 100% !important;
        img {
            width: 100% !important;
            position: relative !important;
            height: auto !important;
        }
    }
}
.iconTestDiv {
    display: flex;
    span {
        width: 1.5vw !important;
        margin-right: 0.3vw !important;
    }
    @media only screen and (min-width: 1200px) and (max-width: 1479px) {
        span {
            width: 1.8vw !important;
            margin-right: 0.5vw !important;
        }
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        span {
            width: 2vw !important;
            margin-right: 0.5vw !important;
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        span {
            width: 2.6vw !important;
            margin-right: 0.7vw !important;
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        span {
            width: 6.2vw !important;
            margin-right: 2vw !important;
        }
    }
}




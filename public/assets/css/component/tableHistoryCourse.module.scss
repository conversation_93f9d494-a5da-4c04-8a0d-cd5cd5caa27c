@import '../var';
 

.block_table_history_course{
    .inner_table_history_course{
        .scroll{
            table{
                width: 100%;
                border-collapse: collapse;
                thead{
                    tr{
                        th{
                            font-family: $font-kanit;
                            text-align: left;
                            font-weight: 500;
                            font-size: 1vw;
                            color: #648D2F;
                            padding: 15px 5px;
                            border-top: 1px solid #648D2F!important;
                            border-bottom: 1px solid #648D2F!important;
                            &:nth-last-child(1),&:nth-last-child(2) {
                                text-align: center;
                            }
                        }
                    }
                }
                tbody{
                    tr{
                        background-color: rgba(148, 193, 32,.0);
                        transition: .3s;
                        td{
                            font-family: $font-kanit;
                            font-weight: 400;
                            font-size: 1vw;
                            color: #656565;
                            padding: 10px 5px;
                            max-width: 250px;
                            a{
                                font-size: 1vw;
                                color: #648D2F;
                                svg {
                                    font-size: 1.4vw;
                                    position: relative;
                                    top: 0.15vw;
                                }
                            }
                            &:nth-last-child(1),
                            &:nth-last-child(2) {
                                text-align: center;
                            }
                        }
                        &:nth-child(even){
                            background-color: rgba(148, 193, 32,.1);
                        }
                    }
                }
            }
        }
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        .inner_table_history_course {
            .scroll {
                table {
                    width: 100%;
                    border-collapse: collapse;
    
                    thead {
                        tr {
                            th {
                                font-size: 1.4vw;
                                width: 16.66%;
                                padding: 15px 10px;
                            }
                        }
                    }
    
                    tbody {
                        tr {
                            td {
                                font-size: 1.2vw;
                                padding: 10px;
                                a {
                                    font-size: 1.2vw;
                                    svg {
                                        font-size: 2vw;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        .inner_table_history_course {
            .scroll {
                table {
                    width: 100%;
                    border-collapse: collapse;
    
                    thead {
                        tr {
                            th {
                                font-size: 1.6vw;
                                width: 16.66%;
                                padding: 15px 10px;
                            }
                        }
                    }
    
                    tbody {
                        tr {
                            td {
                                font-size: 1.4vw;
                                padding: 10px;
                                a {
                                    font-size: 1.4vw;
                                    svg {
                                        font-size: 2.3vw;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        .inner_table_history_course {
            overflow: auto;
            .scroll {
                width: 600px;
                table {
                    width: 100%;
                    border-collapse: collapse;
                    thead {
                        tr {
                            th {
                                font-size: 4vw;
                                width: 16.66%;
                            }
                        }
                    }
                    tbody {
                        tr {
                            td {
                                font-size: 3.2vw;
                                padding: 10px 5px;
                                a {
                                    font-size: 3.2vw;
                                    svg {
                                        font-size: 4.8vw;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
@import url("https://fonts.googleapis.com/css2?family=K2D:wght@100;200;300;400;500&family=Kanit:wght@100;200;300;400;500&display=swap");
.nowrap {
  white-space: nowrap;
}

.d-flex-default {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-a-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
}

.d-flex-j-center {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: center;
  flex-direction: row;
}

.d-flex-all-center {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.divider {
  padding-top: 1.042vw;
}

.title {
  font-family: "Kanit";
  font-size: 1.563vw;
  padding-bottom: 1.042vw;
  font-weight: 400;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .title {
    font-size: 2.1vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .title {
    font-size: 2.6vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .title {
    font-size: 6vw;
    padding: 0 0 6vw 0;
  }
}

.seriestitle {
  font-family: "Kanit";
  font-size: 1.563vw;
  margin: 0 0 5px 0;
  font-weight: 400;
}

@media only screen and (min-width: 1200px) and (max-width: 1479px) {
  .seriestitle {
    font-size: 1.563vw;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .seriestitle {
    font-size: 2.1vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .seriestitle {
    font-size: 2.6vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .seriestitle {
    font-size: 6vw;
  }
}

.subtitle {
  font-family: "K2D";
  font-weight: 300;
  font-size: 0.833vw;
  margin: 0 0 5px 0;
  width: 50%;
}

@media only screen and (min-width: 1200px) and (max-width: 1479px) {
  .subtitle {
    font-size: 1.15vw;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .subtitle {
    font-size: 1.45vw;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .subtitle {
    font-size: 1.85vw;
  }
}

@media only screen and (min-width: 250px) and (max-width: 767px) {
  .subtitle {
    font-size: 4vw;
    width: 100%;
  }
}

.mySlide {
  transition: all .25s ease-out;
  opacity: 1;
  overflow: visible !important;
  margin-bottom: 5px;
}

.mySlide .swiper-slide-active {
  transform: scale(1);
}

.mySlideSeries {
  transition: all .25s ease-out;
}

.mySlideSeries .swiper-slide-active {
  transform: scale(1);
}

.backgroundSeries {
  padding: 30px 15px 35px 15px;
  margin: 0;
  background-size: cover;
  background-position: center;
}

.backgroundSeries .subtitle {
  margin-bottom: 10px;
}

.btnBuyNow {
  padding: 10px 30px;
  text-align: center;
  border-radius: 14px;
  cursor: pointer;
}
/*# sourceMappingURL=groupCategory.module.css.map */
.imgThumbDiv {
    span {
        position: relative !important;
        min-width: 100% !important;
        height: 500px !important;
        img {
            width: 100% !important;
            position: relative !important;
            height: auto !important;
        }
    }
    @media only screen and (min-width: 768px) {
        span {
            &:nth-child(2) {
                display: none !important;
            }
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        span {
            height: 380px !important;
            &:nth-child(1) {
                display: none !important;
            }
        }
    }
}
.imgLogoDiv {
    display: flex;
    position: absolute !important;
    right: 0 !important;
    bottom: 30px !important;
    span {
        width: 5vw !important;
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        bottom: -13vw !important;
        right: 15px !important;
        span {
            width: 10vw !important;
        }
    }
}
@import '../var';
 .modalBackground{
    width:100%;
    height:100vh;
    position: fixed;
    left:0;
    right:0;
    top:0;
    bottom:0;
    margin: auto;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, .8);
    .vdo_right{
        flex:1;
        height: 80vh;
    }
    .vdo_left{
        flex:1;
        height: 80vh;
    }
    .modalVdo{
        display: flex;
        flex-direction: column;
        justify-content: stretch;
        max-width: 960px;
        width: 100%;
        height: 100vh;
        .vdo_bottom{
            flex:1;
            width: 100%;
            height: 10vh;
        }
        .vdo_top{
            flex:1;
            width: 100%;
            height: 10vh;
        }
        .modaVdoContent{
            background-color: #000 !important;
            overflow-y:auto;
            overflow-x:hidden;
            height: 80vh;
            color: #FFF;
            background-color: #000 !important;
            .groupTitle{
                padding: 20px 15px;
            }
            .blockPlayer{
                margin: 0 auto;
                justify-content: center;
                display: flex;
                position: relative;
            } 
            .list{
                padding-top: 10px;
                padding-bottom: 10px;
                cursor: pointer;
                &.active{
                    background-color: #282828;
                    img{
                        border:2px solid red;
                    }
                }
                .imgThumb{
                
                }
                .title{
        
                }
                .description{
        
                }
                .calendar{
        
                } 
                .text_right{
                    display: flex;
                    justify-content: end;
                }
            }
        }
    }
 }





.block_list_cart{
    .list_inner_cart{
        border-bottom: 1px solid #648D2F;
        .item_cart{
            display: flex;
            justify-content: center;
            align-items: stretch;
            flex-wrap: nowrap;
            flex-direction: row;
            padding: 15px 0;
            border-top: 1px solid #648D2F;
            .cart_img{
                width: 30%;
                img{
                    max-width: 100%;

                }
            }
            .cart_content{
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: flex-start;
                flex-direction: column;
                padding: 0 15px;
                h3{
                    font-family: $font-kanit;
                    font-weight: 500;
                    font-size: 1.2vw;
                    color: #648D2F;
                    white-space: normal;
                    margin: 0 0 0 0;
                }
                h4{
                    font-family: $font-k2d;
                    font-weight: 500;
                    font-size: .85vw;
                    color: #648D2F;
                    white-space: normal;
                    margin: 0 0 0 0;
                }
                p{
                    font-family: $font-kanit;
                    font-weight: 400;
                    font-size: 1.1vw;
                    color: #648D2F;
                    white-space: normal;
                    margin: 0 0 0 0;
                }
            }
            .cart_action{
                display: flex;
                align-items: center;
                .btn_cart_action{
                    background-color: transparent;
                    border: none;
                    cursor: pointer;
                    i{
                        font-size: 1.2vw;
                        color: #9F9F9F;
                    }
                }
            }
        }
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        .list_inner_cart{
            .item_cart{
                padding: 15px 0;
                border-top-width: 1px;
                .cart_img{
                    width: 30%;
                    img{
                        max-width: 100%;
                    }
                }
                .cart_content{
                    padding: 0 15px;
                    h3{
                        font-size: 1.8vw;
                        margin: 0 0 0 0;
                    }
                    h4{
                        font-size: 1.6vw;
                        margin: 0 0 0 0;
                    }
                    p{
                        font-size: 1.4vw;
                        margin: 0 0 0 0;
                    }
                }
                .cart_action{
                    .btn_cart_action{
                        i{
                            font-size: 1.8vw;
                        }
                    }
                }
            }
        }
    }
    
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        .list_inner_cart{
            .item_cart{
                padding: 15px 0;
                border-top-width: 1px;
                .cart_img{
                    width: 30%;
                    img{
                        max-width: 100%;
                    }
                }
                .cart_content{
                    padding: 0 15px;
                    h3{
                        font-size: 1.8vw;
                        margin: 0 0 0 0;
                    }
                    h4{
                        font-size: 1.6vw;
                        margin: 0 0 0 0;
                    }
                    p{
                        font-size: 1.4vw;
                        margin: 0 0 0 0;
                    }
                }
                .cart_action{
                    .btn_cart_action{
                        i{
                            font-size: 1.8vw;
                        }
                    }
                }
            }
        }
    }
    
    @media only screen and (min-width: 290px) and (max-width: 767px) {
        .list_inner_cart{
            .item_cart{
                padding: 15px 0;
                border-top-width: 1px;
                .cart_img{
                    width: 30%;
                    img{
                        max-width: 100%;
                    }
                }
                .cart_content{
                    padding: 0 10px;
                    h3{
                        font-size: 4.4vw;
                        margin: 0 0 0 0;
                    }
                    h4{
                        font-size: 4.2vw;
                        margin: 0 0 0 0;
                    }
                    p{
                        font-size: 4vw;
                        margin: 0 0 0 0;
                    }
                }
                .cart_action{
                    .btn_cart_action{
                        i{
                            font-size: 5.8vw;
                        }
                    }
                }
            }
        }
    }
    
}
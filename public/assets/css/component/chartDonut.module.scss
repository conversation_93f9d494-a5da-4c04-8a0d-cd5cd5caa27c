@import '../var';
 
.block_chart_info{
    width: 100%;
    .inner_chart{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction:row;
        flex-wrap: wrap;
        .chart_info_img{
            position: relative;
            width: 12vw;
            display: flex;
            align-items: center;
            justify-content: center;
            .chart_info_number{
                width: 100%;
                height: 100%;
                position: absolute;
                display: flex;
                align-items: center;
                justify-content: center;
                .number{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: #fff;
                    position: relative;
                    top: 0.35vw;
                    border: 0.4vw solid #9f9f9f;
                    width: 82%;
                    height: 82%;
                    border-radius: 50%;
                    span{
                        font-family: $font-kanit;
                        font-weight: 500;
                        font-size: 2.6vw;
                        color: #648D2F;
                    }
                }

            }
            canvas{
                pointer-events: none;
            }
        }
        .chart_info_text{
            flex: 1;
            padding: 0 30px;
            color: #676767;
            h3{
                font-family: $font-kanit;
                font-weight: 500;
                font-size: 1.1vw;
                white-space: normal;
                margin: 0 0 10px 0;
            }
            h4{
                font-family: $font-k2d;
                font-weight: 400;
                font-size: .9vw;
                white-space: normal;
                margin: 0 0 0 0;
            }
        }
    }
    @media only screen and (min-width: 1200px) and (max-width: 1479px) {
        .inner_chart{
            .chart_info_text{
                h3{
                    font-size: 2vw;
                }
                h4{
                    font-size: 1.1vw;
                }
            }
        }
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        .inner_chart{
            .chart_info_img{
                width: 17vw;
                .chart_info_number{
                    .number{
                        span{
                            font-size: 4vw;
                        }
                    }
                }
            }
            .chart_info_text{
                h3{
                    font-size: 2.5vw;
                }
                h4{
                    font-size: 1.6vw;
                }
            }
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        .inner_chart{
            .chart_info_img{
                width: 25vw;
                .chart_info_number{
                    .number{
                        span{
                            font-size: 4.5vw;
                        }
                    }
                }
            }
            .chart_info_text{
                h3{
                    font-size: 3vw;
                }
                h4{
                    font-size: 2vw;
                }
            }
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        .inner_chart{
            flex-direction: column;
            .chart_info_img{
                width: 40vw;
                margin-bottom: 20px;
                .chart_info_number{
                    .number{
                        top: 1.1vw;
                        width: 75%;
                        height: 75%;
                        span{
                            font-size: 7.8vw;
                        }
                    }
                }
            }
            .chart_info_text{
                padding: 0;
                h3{
                    font-size: 5vw;
                }
                h4{
                    font-size: 4vw;
                }
            }
        }
    }
}




@import '../var';
 


.block_table_audio{
    .inner_table_audio{
        .scroll{
            table{
                width: 100%;
                border-collapse: collapse;
                thead{
                    tr{
                        th{
                            font-family: $font-k2d;
                            text-align: left;
                            font-weight: 500;
                            font-size: 1vw;
                            color: #648D2F;
                            padding: 10px 10px;
                            border-top: 1px solid #648D2F!important;
                            border-bottom: 1px solid #648D2F!important;
                            text-align: center;
                            &:nth-child(1){
                                text-align: left;
                            }
                        }
                    }
                }
                tbody{
                    tr{
                        border-bottom: 1px solid #648D2F!important;
                        vertical-align: middle;
                        transition: .3s;
                        td{
                            font-family: $font-kanit;
                            font-weight: 400;
                            font-size: .9vw;
                            color: #656565;
                            padding: 10px 10px;
                            &.status{
                                text-align: left;
                                display: flex;
                                align-items: center;
                                i{
                                    font-size: 1.4vw;
                                    margin: 0 0 0 5px;
                                    opacity: 0;
                                }
                            }
                            &.time{
                                text-align: center;
                            }
                            &.action{
                                text-align: center;
                                i{
                                    font-size: 1.4vw;
                                }
                                span{
                                    background-color: #94C120;
                                    font-size: 1vw;
                                    text-align: center;
                                    color: #fff;
                                    border-radius: 8px;
                                    padding: 2px 5px;
                                }
                            }
                        }
                        &.audio_active{
                            td{
                                &.status{
                                    color: #648D2F;
                                    i{
                                        opacity: 1;
                                    }
                                }
                                &.time{
                                    color: #94C120;
                                }
                                &.action{
                                    color: #94C120;
                                }
                            }

                        }
                    }
                }
            }
        }
    }
    @media only screen and (min-width: 1200px) and (max-width: 1479px) {
        .inner_table_audio{
            .scroll{
                table{
                    thead{
                        tr{
                            th{
                                font-size: 1.2vw;
                            }
                        }
                    }
                    tbody{
                        tr{
                            td{
                                font-size: 1.2vw;
                                &.status{
                                    i{
                                        font-size: 1.6vw;
                                    }
                                }
                                &.action{
                                    i{
                                        font-size: 1.6vw;
                                    }
                                    span{
                                        font-size: 1.2vw;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }  
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        .inner_table_audio{
            .scroll{
                table{
                    thead{
                        tr{
                            th{
                                font-size: 1.55vw;
                            }
                        }
                    }
                    tbody{
                        tr{
                            td{
                                font-size: 1.55vw;
                                &.status{
                                    i{
                                        font-size: 1.95vw;
                                    }
                                }
                                &.action{
                                    i{
                                        font-size: 1.95vw;
                                    }
                                    span{
                                        font-size: 1.55vw;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        .inner_table_audio{
            .scroll{
                table{
                    thead{
                        tr{
                            th{
                                font-size: 1.85vw;
                            }
                        }
                    }
                    tbody{
                        tr{
                            td{
                                font-size: 1.85vw;
                                &.status{
                                    i{
                                        font-size: 2.35vw;
                                    }
                                }
                                &.action{
                                    i{
                                        font-size: 2.35vw;
                                    }
                                    span{
                                        font-size: 1.85vw;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    @media only screen and (min-width: 250px) and (max-width: 767px) {
        .inner_table_audio{
            .scroll{
                table{
                    thead{
                        tr{
                            th{
                                font-size: 4vw;
                            }
                        }
                    }
                    tbody{
                        tr{
                            td{
                                font-size: 4vw;
                                padding: 10px 0;
                                &.status{
                                    i{
                                        font-size: 4.3vw;
                                    }
                                }
                                &.action{
                                    i{
                                        font-size: 4.3vw;
                                    }
                                    span{
                                        font-size: 4vw;

                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}


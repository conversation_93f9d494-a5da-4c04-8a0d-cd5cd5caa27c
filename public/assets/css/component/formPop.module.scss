@import '../var';
 
.block_pop_fm{
    width: 100%;
    @extend .d-flex-all-center;
    .inner_pop_fm{
        width: 100%;
        max-width: 60%;
        @extend .d-flex-all-center;
        flex-direction: column;
        .logo{
            margin: 0 0 15px 0;
            .thumb{
                img{
                    height: 6.8vw;
                }
            }
            .icon{
                i{
                    font-size: 7.8vw;
                    color: #648D2F;
                }
            }
        }
        .col_fm{
            width: 100%;
            margin: 5px 0;
        }
        .col_fm_btn{
            width: 100%;
            @extend .d-flex-all-center;
            flex-wrap: wrap;
            padding: 0 15px;
            .btn_submit{
                background-color: #648D2F;
                width: 100%;
                @extend .d-flex-all-center;
                font-family: $font-kanit;
                font-size: 0.938vw;
                font-weight: normal;
                color: #fff;
                border: none;
                border-radius: 12px;
                padding: 10px;
                margin: 15px 0 5px 0;
                cursor: pointer;
            }
            .btn_forget{
                background-color: transparent;
                font-family: $font-kanit;
                font-size: 0.938vw;
                color: #648D2F;
                margin: 0 0 0 auto;
                box-shadow: none;
                border: none;
                cursor: pointer;
            }
        }
        .or{
            position: relative;
            width: 100%;
            text-align: center;
            padding: 0 15px;
            margin: 10px 0 0 0;
            .inner{
                width: 100%;
                position: relative;
                &::before{
                    top: 50%;
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 0;
                    width: 100%;
                    height: 1px;
                    background-color: #648D2F;
                }
            }
            span{
                position: relative;
                text-align: center;
                font-family: $font-kanit;
                font-size: 1vw;
                color: #648D2F;
                background-color: #fff;
                padding: 0 15px;
                z-index: 2;
      
            }
 
        }
        .way_group_btn{
            width: 100%;
            @extend .d-flex-all-center;
            margin:  15px 0 15px 0;
            .btn_way{
                @extend .d-flex-all-center;
                border: 0;
                background-color: transparent;
                margin: 0 0 0 0;
                cursor: pointer;
                img{
                    height: 2.4vw;
                    border-radius: 50%;
                }
            }
        }
    }

    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        .inner_pop_fm{
            max-width: 60%;
            .logo{
                margin: 0 0 15px 0;
                .thumb{
                    img{
                        height: 9vw;
                    }
                }
                .icon{
                    i{
                        font-size: 10vw;
                    }
                }
            }
            .col_fm{
                width: 100%;
                margin: 5px 0;
            }
            .col_fm_btn{
                padding: 0 15px;
                .btn_submit{
                    font-size: 1.8vw;
                    border-radius: 12px;
                    padding: 10px;
                    margin: 15px 0 5px 0;
                }
                .btn_forget{
                    font-size: 1.6vw;
                }
            }
            .or{
                padding: 0 15px;
                margin: 10px 0 0 0;
                .inner{
                    &::before{
                        top: 50%;
                        top: 50%;
                        left: 0;
                        width: 100%;
                        height: 1px;
                    }
                }
                span{
                    font-size: 1.6vw;
                    padding: 0 15px;
                }
     
            }
            .way_group_btn{
                margin:  15px 0 15px 0;
                .btn_way{
                    border: 0;
                    margin: 0 0 0 0;
                    img{
                        height: 3.6vw;
                    }
                }
            }
        }
    }
    
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        .inner_pop_fm{
            max-width: 70%;
            .logo{
                margin: 0 0 15px 0;
                .thumb{
                    img{
                        height: 9vw;
                    }
                }
                .icon{
                    i{
                        font-size: 10vw;
                    }
                }
            }
            .col_fm{
                width: 100%;
                margin: 5px 0;
            }
            .col_fm_btn{
                padding: 0 15px;
                .btn_submit{
                    font-size: 1.8vw;
                    border-radius: 8px;
                    padding: 10px;
                    margin: 15px 0 5px 0;
                }
                .btn_forget{
                    font-size: 1.6vw;
                }
            }
            .or{
                padding: 0 15px;
                margin: 10px 0 0 0;
                .inner{
                    &::before{
                        top: 50%;
                        top: 50%;
                        left: 0;
                        width: 100%;
                        height: 1px;
                    }
                }
                span{
                    font-size: 1.6vw;
                    padding: 0 15px;
                }
     
            }
            .way_group_btn{
                margin:  15px 0 15px 0;
                .btn_way{
                    border: 0;
                    margin: 0 0 0 0;
                    img{
                        height: 3.6vw;
                    }
                }
            }
        }
    }
    
    @media only screen and (min-width: 290px) and (max-width: 767px) {
        .inner_pop_fm{
            max-width: 85%;
            .logo{
                margin: 0 0 15px 0;
                .thumb{
                    img{
                        height: 24vw;
                    }
                }
                .icon{
                    i{
                        font-size: 22vw;
                    }
                }
            }
            .col_fm{
                width: 100%;
                margin: 5px 0;
            }
            .col_fm_btn{
                padding: 0 15px;
                .btn_submit{
                    font-size: 3.8vw;
                    border-radius: 8px;
                    padding: 10px;
                    margin: 15px 0 5px 0;
                }
                .btn_forget{
                    font-size: 3.6vw;
                }
            }
            .or{
                padding: 0 15px;
                margin: 10px 0 0 0;
                .inner{
                    &::before{
                        top: 50%;
                        top: 50%;
                        left: 0;
                        width: 100%;
                        height: 1px;
                    }
                }
                span{
                    font-size: 3.6vw;
                    padding: 0 10px;
                }
     
            }
            .way_group_btn{
                margin:  15px 0 15px 0;
                .btn_way{
                    border: 0;
                    margin: 0 0 0 0;
                    img{
                        height: 8vw;
                    }
                }
            }
        }
    }

}



